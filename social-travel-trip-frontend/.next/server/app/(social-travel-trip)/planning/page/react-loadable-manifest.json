{"[project]/src/features/planning/InteractiveScheduleChart.tsx [app-client] (ecmascript, next/dynamic entry)": {"id": "[project]/src/features/planning/InteractiveScheduleChart.tsx [app-client] (ecmascript, next/dynamic entry)", "files": ["static/chunks/node_modules_react-leaflet_lib_index_fd0c06f0.js", "static/chunks/src_e9016e46._.js", "static/chunks/node_modules_7d7f8bda._.js", "static/chunks/node_modules_leaflet_dist_leaflet_88e19fd3.css", "static/chunks/src_features_planning_InteractiveScheduleChart_tsx_96187dab._.js"]}}