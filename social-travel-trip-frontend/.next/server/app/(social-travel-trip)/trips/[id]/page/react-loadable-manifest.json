{"[project]/node_modules/emoji-picker-react/dist/emoji-picker-react.esm.js [app-client] (ecmascript, next/dynamic entry)": {"id": "[project]/node_modules/emoji-picker-react/dist/emoji-picker-react.esm.js [app-client] (ecmascript, next/dynamic entry)", "files": ["static/chunks/node_modules_962c6964._.js", "static/chunks/node_modules_emoji-picker-react_dist_emoji-picker-react_esm_48a07640.js"]}, "[project]/src/features/planning/InteractiveScheduleChart.tsx [app-client] (ecmascript, next/dynamic entry)": {"id": "[project]/src/features/planning/InteractiveScheduleChart.tsx [app-client] (ecmascript, next/dynamic entry)", "files": ["static/chunks/node_modules_react-leaflet_lib_index_c0a7f44b.js", "static/chunks/src_08a13073._.js", "static/chunks/node_modules_42a5bc84._.js", "static/chunks/node_modules_leaflet_dist_leaflet_88e19fd3.css", "static/chunks/src_features_planning_InteractiveScheduleChart_tsx_48a07640._.js"]}}