(()=>{var e={};e.id=276,e.ids=[276],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},27999:(e,r,t)=>{"use strict";t.d(r,{default:()=>i});var s=t(60687);let a=(0,t(30036).default)(async()=>{},{loadableGenerated:{modules:["app/(social-travel-trip)/blog/create/client-form.tsx -> @/features/blog/create-post-form"]},ssr:!1,loading:()=>(0,s.jsx)("div",{className:"h-[600px] w-full flex items-center justify-center bg-gray-100",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-500 mx-auto"}),(0,s.jsx)("p",{className:"mt-4 text-emerald-600",children:"Đang tải form..."})]})})});function i(){return(0,s.jsx)(a,{})}},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},45074:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>i.default,__next_app__:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>l});var s=t(65239),a=t(48088),i=t(31369),o=t(30893),n={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);t.d(r,n);let l={children:["",{children:["(social-travel-trip)",{children:["blog",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,80604)),"/workspaces/social-travel-trip/social-travel-trip-frontend/src/app/(social-travel-trip)/blog/create/page.tsx"]}]},{}]},{}]},{forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"/workspaces/social-travel-trip/social-travel-trip-frontend/src/app/layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,20392)),"/workspaces/social-travel-trip/social-travel-trip-frontend/src/app/loading.tsx"],"global-error":[()=>Promise.resolve().then(t.bind(t,31369)),"/workspaces/social-travel-trip/social-travel-trip-frontend/src/app/global-error.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"/workspaces/social-travel-trip/social-travel-trip-frontend/src/app/not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["/workspaces/social-travel-trip/social-travel-trip-frontend/src/app/(social-travel-trip)/blog/create/page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(social-travel-trip)/blog/create/page",pathname:"/blog/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73024:e=>{"use strict";e.exports=require("node:fs")},74075:e=>{"use strict";e.exports=require("zlib")},76760:e=>{"use strict";e.exports=require("node:path")},77598:e=>{"use strict";e.exports=require("node:crypto")},79551:e=>{"use strict";e.exports=require("url")},80604:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o,metadata:()=>i});var s=t(37413),a=t(94925);let i={title:"Tạo b\xe0i viết mới",description:"Tạo b\xe0i viết mới cho blog du lịch của bạn"};function o(){return(0,s.jsxs)("main",{className:"min-h-screen",children:[(0,s.jsx)("header",{className:"bg-emerald-600 text-white p-6",children:(0,s.jsxs)("div",{className:"container mx-auto",children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Tạo b\xe0i viết mới"}),(0,s.jsx)("p",{className:"text-emerald-100",children:"Th\xeam địa điểm mới v\xe0o bản đồ du lịch của bạn"})]})}),(0,s.jsx)("div",{className:"container mx-auto py-8 px-4",children:(0,s.jsx)(a.default,{})})]})}},84976:(e,r,t)=>{Promise.resolve().then(t.bind(t,94925))},94704:(e,r,t)=>{Promise.resolve().then(t.bind(t,27999))},94925:(e,r,t)=>{"use strict";t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"/workspaces/social-travel-trip/social-travel-trip-frontend/src/app/(social-travel-trip)/blog/create/client-form.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/workspaces/social-travel-trip/social-travel-trip-frontend/src/app/(social-travel-trip)/blog/create/client-form.tsx","default")},95294:(e,r,t)=>{"use strict";t.r(r),t.d(r,{"7f2f748a7846e11603b4f0ab56663f5d9fa9b83953":()=>s.ai,"7f5d007482e15f593024fa1ec6121dc675b59f8752":()=>s.at,"7fcd875a496e2e32a91a07e8d3fdfe19bd5d36d4e9":()=>a.y,"7fdd66e7c310ebab442b218570eddadf8e5a36af05":()=>s.ot});var s=t(11958),a=t(3937)}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[447,181,958,99],()=>t(45074));module.exports=s})();