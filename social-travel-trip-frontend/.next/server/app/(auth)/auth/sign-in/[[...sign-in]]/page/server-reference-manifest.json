{"node": {"7f681c5099cdbe2c0378e5094e9cee37f110ae7433": {"workers": {"app/(auth)/auth/sign-in/[[...sign-in]]/page": {"moduleId": "[project]/.next-internal/server/app/(auth)/auth/sign-in/[[...sign-in]]/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(auth)/auth/sign-in/[[...sign-in]]/page": "action-browser"}}, "7f5954d68e805c001b0ba5ad09a599aabb194bb741": {"workers": {"app/(auth)/auth/sign-in/[[...sign-in]]/page": {"moduleId": "[project]/.next-internal/server/app/(auth)/auth/sign-in/[[...sign-in]]/page/actions.js { ACTIONS_MODULE0 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/server-actions.js [app-rsc] (ecmascript)\", ACTIONS_MODULE1 => \"[project]/node_modules/@clerk/nextjs/dist/esm/app-router/keyless-actions.js [app-rsc] (ecmascript)\" } [app-rsc] (server actions loader, ecmascript)", "async": false}}, "layer": {"app/(auth)/auth/sign-in/[[...sign-in]]/page": "action-browser"}}}, "edge": {}}