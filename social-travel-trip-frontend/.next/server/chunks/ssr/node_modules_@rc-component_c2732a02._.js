module.exports = {

"[project]/node_modules/@rc-component/trigger/es/Popup/Arrow.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Arrow)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
;
function Arrow(props) {
    var prefixCls = props.prefixCls, align = props.align, arrow = props.arrow, arrowPos = props.arrowPos;
    var _ref = arrow || {}, className = _ref.className, content = _ref.content;
    var _arrowPos$x = arrowPos.x, x = _arrowPos$x === void 0 ? 0 : _arrowPos$x, _arrowPos$y = arrowPos.y, y = _arrowPos$y === void 0 ? 0 : _arrowPos$y;
    var arrowRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])();
    // Skip if no align
    if (!align || !align.points) {
        return null;
    }
    var alignStyle = {
        position: 'absolute'
    };
    // Skip if no need to align
    if (align.autoArrow !== false) {
        var popupPoints = align.points[0];
        var targetPoints = align.points[1];
        var popupTB = popupPoints[0];
        var popupLR = popupPoints[1];
        var targetTB = targetPoints[0];
        var targetLR = targetPoints[1];
        // Top & Bottom
        if (popupTB === targetTB || ![
            't',
            'b'
        ].includes(popupTB)) {
            alignStyle.top = y;
        } else if (popupTB === 't') {
            alignStyle.top = 0;
        } else {
            alignStyle.bottom = 0;
        }
        // Left & Right
        if (popupLR === targetLR || ![
            'l',
            'r'
        ].includes(popupLR)) {
            alignStyle.left = x;
        } else if (popupLR === 'l') {
            alignStyle.left = 0;
        } else {
            alignStyle.right = 0;
        }
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("div", {
        ref: arrowRef,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("".concat(prefixCls, "-arrow"), className),
        style: alignStyle
    }, content);
}
}}),
"[project]/node_modules/@rc-component/trigger/es/Popup/Mask.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>Mask)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$motion$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-motion/es/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$motion$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-motion/es/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
;
;
;
function Mask(props) {
    var prefixCls = props.prefixCls, open = props.open, zIndex = props.zIndex, mask = props.mask, motion = props.motion;
    if (!mask) {
        return null;
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$motion$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({}, motion, {
        motionAppear: true,
        visible: open,
        removeOnLeave: true
    }), function(_ref) {
        var className = _ref.className;
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("div", {
            style: {
                zIndex: zIndex
            },
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("".concat(prefixCls, "-mask"), className)
        });
    });
}
}}),
"[project]/node_modules/@rc-component/trigger/es/Popup/PopupContent.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
var PopupContent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])(function(_ref) {
    var children = _ref.children;
    return children;
}, function(_, next) {
    return next.cache;
});
if ("TURBOPACK compile-time truthy", 1) {
    PopupContent.displayName = 'PopupContent';
}
const __TURBOPACK__default__export__ = PopupContent;
}}),
"[project]/node_modules/@rc-component/trigger/es/Popup/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/slicedToArray.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$motion$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-motion/es/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$motion$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-motion/es/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$resize$2d$observer$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-resize-observer/es/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$resize$2d$observer$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-resize-observer/es/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useLayoutEffect.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/ref.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$Popup$2f$Arrow$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/trigger/es/Popup/Arrow.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$Popup$2f$Mask$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/trigger/es/Popup/Mask.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$Popup$2f$PopupContent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/trigger/es/Popup/PopupContent.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
var Popup = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(function(props, ref) {
    var popup = props.popup, className = props.className, prefixCls = props.prefixCls, style = props.style, target = props.target, _onVisibleChanged = props.onVisibleChanged, open = props.open, keepDom = props.keepDom, fresh = props.fresh, onClick = props.onClick, mask = props.mask, arrow = props.arrow, arrowPos = props.arrowPos, align = props.align, motion = props.motion, maskMotion = props.maskMotion, forceRender = props.forceRender, getPopupContainer = props.getPopupContainer, autoDestroy = props.autoDestroy, Portal = props.portal, zIndex = props.zIndex, onMouseEnter = props.onMouseEnter, onMouseLeave = props.onMouseLeave, onPointerEnter = props.onPointerEnter, onPointerDownCapture = props.onPointerDownCapture, ready = props.ready, offsetX = props.offsetX, offsetY = props.offsetY, offsetR = props.offsetR, offsetB = props.offsetB, onAlign = props.onAlign, onPrepare = props.onPrepare, stretch = props.stretch, targetWidth = props.targetWidth, targetHeight = props.targetHeight;
    var childNode = typeof popup === 'function' ? popup() : popup;
    // We can not remove holder only when motion finished.
    var isNodeVisible = open || keepDom;
    // ======================= Container ========================
    var getPopupContainerNeedParams = (getPopupContainer === null || getPopupContainer === void 0 ? void 0 : getPopupContainer.length) > 0;
    var _React$useState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(!getPopupContainer || !getPopupContainerNeedParams), _React$useState2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_React$useState, 2), show = _React$useState2[0], setShow = _React$useState2[1];
    // Delay to show since `getPopupContainer` need target element
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function() {
        if (!show && getPopupContainerNeedParams && target) {
            setShow(true);
        }
    }, [
        show,
        getPopupContainerNeedParams,
        target
    ]);
    // ========================= Render =========================
    if (!show) {
        return null;
    }
    // >>>>> Offset
    var AUTO = 'auto';
    var offsetStyle = {
        left: '-1000vw',
        top: '-1000vh',
        right: AUTO,
        bottom: AUTO
    };
    // Set align style
    if (ready || !open) {
        var _experimental;
        var points = align.points;
        var dynamicInset = align.dynamicInset || ((_experimental = align._experimental) === null || _experimental === void 0 ? void 0 : _experimental.dynamicInset);
        var alignRight = dynamicInset && points[0][1] === 'r';
        var alignBottom = dynamicInset && points[0][0] === 'b';
        if (alignRight) {
            offsetStyle.right = offsetR;
            offsetStyle.left = AUTO;
        } else {
            offsetStyle.left = offsetX;
            offsetStyle.right = AUTO;
        }
        if (alignBottom) {
            offsetStyle.bottom = offsetB;
            offsetStyle.top = AUTO;
        } else {
            offsetStyle.top = offsetY;
            offsetStyle.bottom = AUTO;
        }
    }
    // >>>>> Misc
    var miscStyle = {};
    if (stretch) {
        if (stretch.includes('height') && targetHeight) {
            miscStyle.height = targetHeight;
        } else if (stretch.includes('minHeight') && targetHeight) {
            miscStyle.minHeight = targetHeight;
        }
        if (stretch.includes('width') && targetWidth) {
            miscStyle.width = targetWidth;
        } else if (stretch.includes('minWidth') && targetWidth) {
            miscStyle.minWidth = targetWidth;
        }
    }
    if (!open) {
        miscStyle.pointerEvents = 'none';
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(Portal, {
        open: forceRender || isNodeVisible,
        getContainer: getPopupContainer && function() {
            return getPopupContainer(target);
        },
        autoDestroy: autoDestroy
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$Popup$2f$Mask$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
        prefixCls: prefixCls,
        open: open,
        zIndex: zIndex,
        mask: mask,
        motion: maskMotion
    }), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$resize$2d$observer$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"], {
        onResize: onAlign,
        disabled: !open
    }, function(resizeObserverRef) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$motion$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({
            motionAppear: true,
            motionEnter: true,
            motionLeave: true,
            removeOnLeave: false,
            forceRender: forceRender,
            leavedClassName: "".concat(prefixCls, "-hidden")
        }, motion, {
            onAppearPrepare: onPrepare,
            onEnterPrepare: onPrepare,
            visible: open,
            onVisibleChanged: function onVisibleChanged(nextVisible) {
                var _motion$onVisibleChan;
                motion === null || motion === void 0 || (_motion$onVisibleChan = motion.onVisibleChanged) === null || _motion$onVisibleChan === void 0 || _motion$onVisibleChan.call(motion, nextVisible);
                _onVisibleChanged(nextVisible);
            }
        }), function(_ref, motionRef) {
            var motionClassName = _ref.className, motionStyle = _ref.style;
            var cls = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(prefixCls, motionClassName, className);
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("div", {
                ref: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["composeRef"])(resizeObserverRef, ref, motionRef),
                className: cls,
                style: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({
                    '--arrow-x': "".concat(arrowPos.x || 0, "px"),
                    '--arrow-y': "".concat(arrowPos.y || 0, "px")
                }, offsetStyle), miscStyle), motionStyle), {}, {
                    boxSizing: 'border-box',
                    zIndex: zIndex
                }, style),
                onMouseEnter: onMouseEnter,
                onMouseLeave: onMouseLeave,
                onPointerEnter: onPointerEnter,
                onClick: onClick,
                onPointerDownCapture: onPointerDownCapture
            }, arrow && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$Popup$2f$Arrow$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                prefixCls: prefixCls,
                arrow: arrow,
                arrowPos: arrowPos,
                align: align
            }), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$Popup$2f$PopupContent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                cache: !open && !fresh
            }, childNode));
        });
    }));
});
if ("TURBOPACK compile-time truthy", 1) {
    Popup.displayName = 'Popup';
}
const __TURBOPACK__default__export__ = Popup;
}}),
"[project]/node_modules/@rc-component/trigger/es/TriggerWrapper.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/ref.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
;
var TriggerWrapper = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(function(props, ref) {
    var children = props.children, getTriggerDOMNode = props.getTriggerDOMNode;
    var canUseRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supportRef"])(children);
    // When use `getTriggerDOMNode`, we should do additional work to get the real dom
    var setRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(function(node) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fillRef"])(ref, getTriggerDOMNode ? getTriggerDOMNode(node) : node);
    }, [
        getTriggerDOMNode
    ]);
    var mergedRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useComposeRef"])(setRef, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getNodeRef"])(children));
    return canUseRef ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cloneElement"])(children, {
        ref: mergedRef
    }) : children;
});
if ("TURBOPACK compile-time truthy", 1) {
    TriggerWrapper.displayName = 'TriggerWrapper';
}
const __TURBOPACK__default__export__ = TriggerWrapper;
}}),
"[project]/node_modules/@rc-component/trigger/es/context.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
var TriggerContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(null);
const __TURBOPACK__default__export__ = TriggerContext;
}}),
"[project]/node_modules/@rc-component/trigger/es/hooks/useAction.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useAction)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
function toArray(val) {
    return val ? Array.isArray(val) ? val : [
        val
    ] : [];
}
function useAction(mobile, action, showAction, hideAction) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(function() {
        var mergedShowAction = toArray(showAction !== null && showAction !== void 0 ? showAction : action);
        var mergedHideAction = toArray(hideAction !== null && hideAction !== void 0 ? hideAction : action);
        var showActionSet = new Set(mergedShowAction);
        var hideActionSet = new Set(mergedHideAction);
        if (mobile) {
            if (showActionSet.has('hover')) {
                showActionSet.delete('hover');
                showActionSet.add('click');
            }
            if (hideActionSet.has('hover')) {
                hideActionSet.delete('hover');
                hideActionSet.add('click');
            }
        }
        return [
            showActionSet,
            hideActionSet
        ];
    }, [
        mobile,
        action,
        showAction,
        hideAction
    ]);
}
}}),
"[project]/node_modules/@rc-component/trigger/es/util.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "collectScroller": (()=>collectScroller),
    "getAlignPopupClassName": (()=>getAlignPopupClassName),
    "getMotion": (()=>getMotion),
    "getVisibleArea": (()=>getVisibleArea),
    "getWin": (()=>getWin),
    "toNum": (()=>toNum)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-ssr] (ecmascript)");
;
function isPointsEq() {
    var a1 = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];
    var a2 = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];
    var isAlignPoint = arguments.length > 2 ? arguments[2] : undefined;
    if (isAlignPoint) {
        return a1[0] === a2[0];
    }
    return a1[0] === a2[0] && a1[1] === a2[1];
}
function getAlignPopupClassName(builtinPlacements, prefixCls, align, isAlignPoint) {
    var points = align.points;
    var placements = Object.keys(builtinPlacements);
    for(var i = 0; i < placements.length; i += 1){
        var _builtinPlacements$pl;
        var placement = placements[i];
        if (isPointsEq((_builtinPlacements$pl = builtinPlacements[placement]) === null || _builtinPlacements$pl === void 0 ? void 0 : _builtinPlacements$pl.points, points, isAlignPoint)) {
            return "".concat(prefixCls, "-placement-").concat(placement);
        }
    }
    return '';
}
function getMotion(prefixCls, motion, animation, transitionName) {
    if (motion) {
        return motion;
    }
    if (animation) {
        return {
            motionName: "".concat(prefixCls, "-").concat(animation)
        };
    }
    if (transitionName) {
        return {
            motionName: transitionName
        };
    }
    return null;
}
function getWin(ele) {
    return ele.ownerDocument.defaultView;
}
function collectScroller(ele) {
    var scrollerList = [];
    var current = ele === null || ele === void 0 ? void 0 : ele.parentElement;
    var scrollStyle = [
        'hidden',
        'scroll',
        'clip',
        'auto'
    ];
    while(current){
        var _getWin$getComputedSt = getWin(current).getComputedStyle(current), overflowX = _getWin$getComputedSt.overflowX, overflowY = _getWin$getComputedSt.overflowY, overflow = _getWin$getComputedSt.overflow;
        if ([
            overflowX,
            overflowY,
            overflow
        ].some(function(o) {
            return scrollStyle.includes(o);
        })) {
            scrollerList.push(current);
        }
        current = current.parentElement;
    }
    return scrollerList;
}
function toNum(num) {
    var defaultValue = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;
    return Number.isNaN(num) ? defaultValue : num;
}
function getPxValue(val) {
    return toNum(parseFloat(val), 0);
}
function getVisibleArea(initArea, scrollerList) {
    var visibleArea = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({}, initArea);
    (scrollerList || []).forEach(function(ele) {
        if (ele instanceof HTMLBodyElement || ele instanceof HTMLHtmlElement) {
            return;
        }
        // Skip if static position which will not affect visible area
        var _getWin$getComputedSt2 = getWin(ele).getComputedStyle(ele), overflow = _getWin$getComputedSt2.overflow, overflowClipMargin = _getWin$getComputedSt2.overflowClipMargin, borderTopWidth = _getWin$getComputedSt2.borderTopWidth, borderBottomWidth = _getWin$getComputedSt2.borderBottomWidth, borderLeftWidth = _getWin$getComputedSt2.borderLeftWidth, borderRightWidth = _getWin$getComputedSt2.borderRightWidth;
        var eleRect = ele.getBoundingClientRect();
        var eleOutHeight = ele.offsetHeight, eleInnerHeight = ele.clientHeight, eleOutWidth = ele.offsetWidth, eleInnerWidth = ele.clientWidth;
        var borderTopNum = getPxValue(borderTopWidth);
        var borderBottomNum = getPxValue(borderBottomWidth);
        var borderLeftNum = getPxValue(borderLeftWidth);
        var borderRightNum = getPxValue(borderRightWidth);
        var scaleX = toNum(Math.round(eleRect.width / eleOutWidth * 1000) / 1000);
        var scaleY = toNum(Math.round(eleRect.height / eleOutHeight * 1000) / 1000);
        // Original visible area
        var eleScrollWidth = (eleOutWidth - eleInnerWidth - borderLeftNum - borderRightNum) * scaleX;
        var eleScrollHeight = (eleOutHeight - eleInnerHeight - borderTopNum - borderBottomNum) * scaleY;
        // Cut border size
        var scaledBorderTopWidth = borderTopNum * scaleY;
        var scaledBorderBottomWidth = borderBottomNum * scaleY;
        var scaledBorderLeftWidth = borderLeftNum * scaleX;
        var scaledBorderRightWidth = borderRightNum * scaleX;
        // Clip margin
        var clipMarginWidth = 0;
        var clipMarginHeight = 0;
        if (overflow === 'clip') {
            var clipNum = getPxValue(overflowClipMargin);
            clipMarginWidth = clipNum * scaleX;
            clipMarginHeight = clipNum * scaleY;
        }
        // Region
        var eleLeft = eleRect.x + scaledBorderLeftWidth - clipMarginWidth;
        var eleTop = eleRect.y + scaledBorderTopWidth - clipMarginHeight;
        var eleRight = eleLeft + eleRect.width + 2 * clipMarginWidth - scaledBorderLeftWidth - scaledBorderRightWidth - eleScrollWidth;
        var eleBottom = eleTop + eleRect.height + 2 * clipMarginHeight - scaledBorderTopWidth - scaledBorderBottomWidth - eleScrollHeight;
        visibleArea.left = Math.max(visibleArea.left, eleLeft);
        visibleArea.top = Math.max(visibleArea.top, eleTop);
        visibleArea.right = Math.min(visibleArea.right, eleRight);
        visibleArea.bottom = Math.min(visibleArea.bottom, eleBottom);
    });
    return visibleArea;
}
}}),
"[project]/node_modules/@rc-component/trigger/es/hooks/useAlign.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useAlign)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/slicedToArray.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$findDOMNode$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/Dom/findDOMNode.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$isVisible$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/Dom/isVisible.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useEvent.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useLayoutEffect.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/trigger/es/util.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
function getUnitOffset(size) {
    var offset = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
    var offsetStr = "".concat(offset);
    var cells = offsetStr.match(/^(.*)\%$/);
    if (cells) {
        return size * (parseFloat(cells[1]) / 100);
    }
    return parseFloat(offsetStr);
}
function getNumberOffset(rect, offset) {
    var _ref = offset || [], _ref2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_ref, 2), offsetX = _ref2[0], offsetY = _ref2[1];
    return [
        getUnitOffset(rect.width, offsetX),
        getUnitOffset(rect.height, offsetY)
    ];
}
function splitPoints() {
    var points = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';
    return [
        points[0],
        points[1]
    ];
}
function getAlignPoint(rect, points) {
    var topBottom = points[0];
    var leftRight = points[1];
    var x;
    var y;
    // Top & Bottom
    if (topBottom === 't') {
        y = rect.y;
    } else if (topBottom === 'b') {
        y = rect.y + rect.height;
    } else {
        y = rect.y + rect.height / 2;
    }
    // Left & Right
    if (leftRight === 'l') {
        x = rect.x;
    } else if (leftRight === 'r') {
        x = rect.x + rect.width;
    } else {
        x = rect.x + rect.width / 2;
    }
    return {
        x: x,
        y: y
    };
}
function reversePoints(points, index) {
    var reverseMap = {
        t: 'b',
        b: 't',
        l: 'r',
        r: 'l'
    };
    return points.map(function(point, i) {
        if (i === index) {
            return reverseMap[point] || 'c';
        }
        return point;
    }).join('');
}
function useAlign(open, popupEle, target, placement, builtinPlacements, popupAlign, onPopupAlign) {
    var _React$useState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        ready: false,
        offsetX: 0,
        offsetY: 0,
        offsetR: 0,
        offsetB: 0,
        arrowX: 0,
        arrowY: 0,
        scaleX: 1,
        scaleY: 1,
        align: builtinPlacements[placement] || {}
    }), _React$useState2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_React$useState, 2), offsetInfo = _React$useState2[0], setOffsetInfo = _React$useState2[1];
    var alignCountRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(0);
    var scrollerList = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(function() {
        if (!popupEle) {
            return [];
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collectScroller"])(popupEle);
    }, [
        popupEle
    ]);
    // ========================= Flip ==========================
    // We will memo flip info.
    // If size change to make flip, it will memo the flip info and use it in next align.
    var prevFlipRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])({});
    var resetFlipCache = function resetFlipCache() {
        prevFlipRef.current = {};
    };
    if (!open) {
        resetFlipCache();
    }
    // ========================= Align =========================
    var onAlign = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function() {
        if (popupEle && target && open) {
            var _popupElement$parentE, _popupRect$x, _popupRect$y, _popupElement$parentE2;
            var popupElement = popupEle;
            var doc = popupElement.ownerDocument;
            var win = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getWin"])(popupElement);
            var _win$getComputedStyle = win.getComputedStyle(popupElement), width = _win$getComputedStyle.width, height = _win$getComputedStyle.height, popupPosition = _win$getComputedStyle.position;
            var originLeft = popupElement.style.left;
            var originTop = popupElement.style.top;
            var originRight = popupElement.style.right;
            var originBottom = popupElement.style.bottom;
            var originOverflow = popupElement.style.overflow;
            // Placement
            var placementInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({}, builtinPlacements[placement]), popupAlign);
            // placeholder element
            var placeholderElement = doc.createElement('div');
            (_popupElement$parentE = popupElement.parentElement) === null || _popupElement$parentE === void 0 || _popupElement$parentE.appendChild(placeholderElement);
            placeholderElement.style.left = "".concat(popupElement.offsetLeft, "px");
            placeholderElement.style.top = "".concat(popupElement.offsetTop, "px");
            placeholderElement.style.position = popupPosition;
            placeholderElement.style.height = "".concat(popupElement.offsetHeight, "px");
            placeholderElement.style.width = "".concat(popupElement.offsetWidth, "px");
            // Reset first
            popupElement.style.left = '0';
            popupElement.style.top = '0';
            popupElement.style.right = 'auto';
            popupElement.style.bottom = 'auto';
            popupElement.style.overflow = 'hidden';
            // Calculate align style, we should consider `transform` case
            var targetRect;
            if (Array.isArray(target)) {
                targetRect = {
                    x: target[0],
                    y: target[1],
                    width: 0,
                    height: 0
                };
            } else {
                var _rect$x, _rect$y;
                var rect = target.getBoundingClientRect();
                rect.x = (_rect$x = rect.x) !== null && _rect$x !== void 0 ? _rect$x : rect.left;
                rect.y = (_rect$y = rect.y) !== null && _rect$y !== void 0 ? _rect$y : rect.top;
                targetRect = {
                    x: rect.x,
                    y: rect.y,
                    width: rect.width,
                    height: rect.height
                };
            }
            var popupRect = popupElement.getBoundingClientRect();
            popupRect.x = (_popupRect$x = popupRect.x) !== null && _popupRect$x !== void 0 ? _popupRect$x : popupRect.left;
            popupRect.y = (_popupRect$y = popupRect.y) !== null && _popupRect$y !== void 0 ? _popupRect$y : popupRect.top;
            var _doc$documentElement = doc.documentElement, clientWidth = _doc$documentElement.clientWidth, clientHeight = _doc$documentElement.clientHeight, scrollWidth = _doc$documentElement.scrollWidth, scrollHeight = _doc$documentElement.scrollHeight, scrollTop = _doc$documentElement.scrollTop, scrollLeft = _doc$documentElement.scrollLeft;
            var popupHeight = popupRect.height;
            var popupWidth = popupRect.width;
            var targetHeight = targetRect.height;
            var targetWidth = targetRect.width;
            // Get bounding of visible area
            var visibleRegion = {
                left: 0,
                top: 0,
                right: clientWidth,
                bottom: clientHeight
            };
            var scrollRegion = {
                left: -scrollLeft,
                top: -scrollTop,
                right: scrollWidth - scrollLeft,
                bottom: scrollHeight - scrollTop
            };
            var htmlRegion = placementInfo.htmlRegion;
            var VISIBLE = 'visible';
            var VISIBLE_FIRST = 'visibleFirst';
            if (htmlRegion !== 'scroll' && htmlRegion !== VISIBLE_FIRST) {
                htmlRegion = VISIBLE;
            }
            var isVisibleFirst = htmlRegion === VISIBLE_FIRST;
            var scrollRegionArea = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getVisibleArea"])(scrollRegion, scrollerList);
            var visibleRegionArea = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getVisibleArea"])(visibleRegion, scrollerList);
            var visibleArea = htmlRegion === VISIBLE ? visibleRegionArea : scrollRegionArea;
            // When set to `visibleFirst`,
            // the check `adjust` logic will use `visibleRegion` for check first.
            var adjustCheckVisibleArea = isVisibleFirst ? visibleRegionArea : visibleArea;
            // Record right & bottom align data
            popupElement.style.left = 'auto';
            popupElement.style.top = 'auto';
            popupElement.style.right = '0';
            popupElement.style.bottom = '0';
            var popupMirrorRect = popupElement.getBoundingClientRect();
            // Reset back
            popupElement.style.left = originLeft;
            popupElement.style.top = originTop;
            popupElement.style.right = originRight;
            popupElement.style.bottom = originBottom;
            popupElement.style.overflow = originOverflow;
            (_popupElement$parentE2 = popupElement.parentElement) === null || _popupElement$parentE2 === void 0 || _popupElement$parentE2.removeChild(placeholderElement);
            // Calculate scale
            var _scaleX = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toNum"])(Math.round(popupWidth / parseFloat(width) * 1000) / 1000);
            var _scaleY = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toNum"])(Math.round(popupHeight / parseFloat(height) * 1000) / 1000);
            // No need to align since it's not visible in view
            if (_scaleX === 0 || _scaleY === 0 || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$findDOMNode$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDOM"])(target) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$isVisible$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(target)) {
                return;
            }
            // Offset
            var offset = placementInfo.offset, targetOffset = placementInfo.targetOffset;
            var _getNumberOffset = getNumberOffset(popupRect, offset), _getNumberOffset2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_getNumberOffset, 2), popupOffsetX = _getNumberOffset2[0], popupOffsetY = _getNumberOffset2[1];
            var _getNumberOffset3 = getNumberOffset(targetRect, targetOffset), _getNumberOffset4 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_getNumberOffset3, 2), targetOffsetX = _getNumberOffset4[0], targetOffsetY = _getNumberOffset4[1];
            targetRect.x -= targetOffsetX;
            targetRect.y -= targetOffsetY;
            // Points
            var _ref3 = placementInfo.points || [], _ref4 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_ref3, 2), popupPoint = _ref4[0], targetPoint = _ref4[1];
            var targetPoints = splitPoints(targetPoint);
            var popupPoints = splitPoints(popupPoint);
            var targetAlignPoint = getAlignPoint(targetRect, targetPoints);
            var popupAlignPoint = getAlignPoint(popupRect, popupPoints);
            // Real align info may not same as origin one
            var nextAlignInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({}, placementInfo);
            // Next Offset
            var nextOffsetX = targetAlignPoint.x - popupAlignPoint.x + popupOffsetX;
            var nextOffsetY = targetAlignPoint.y - popupAlignPoint.y + popupOffsetY;
            // ============== Intersection ===============
            // Get area by position. Used for check if flip area is better
            function getIntersectionVisibleArea(offsetX, offsetY) {
                var area = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : visibleArea;
                var l = popupRect.x + offsetX;
                var t = popupRect.y + offsetY;
                var r = l + popupWidth;
                var b = t + popupHeight;
                var visibleL = Math.max(l, area.left);
                var visibleT = Math.max(t, area.top);
                var visibleR = Math.min(r, area.right);
                var visibleB = Math.min(b, area.bottom);
                return Math.max(0, (visibleR - visibleL) * (visibleB - visibleT));
            }
            var originIntersectionVisibleArea = getIntersectionVisibleArea(nextOffsetX, nextOffsetY);
            // As `visibleFirst`, we prepare this for check
            var originIntersectionRecommendArea = getIntersectionVisibleArea(nextOffsetX, nextOffsetY, visibleRegionArea);
            // ========================== Overflow ===========================
            var targetAlignPointTL = getAlignPoint(targetRect, [
                't',
                'l'
            ]);
            var popupAlignPointTL = getAlignPoint(popupRect, [
                't',
                'l'
            ]);
            var targetAlignPointBR = getAlignPoint(targetRect, [
                'b',
                'r'
            ]);
            var popupAlignPointBR = getAlignPoint(popupRect, [
                'b',
                'r'
            ]);
            var overflow = placementInfo.overflow || {};
            var adjustX = overflow.adjustX, adjustY = overflow.adjustY, shiftX = overflow.shiftX, shiftY = overflow.shiftY;
            var supportAdjust = function supportAdjust(val) {
                if (typeof val === 'boolean') {
                    return val;
                }
                return val >= 0;
            };
            // Prepare position
            var nextPopupY;
            var nextPopupBottom;
            var nextPopupX;
            var nextPopupRight;
            function syncNextPopupPosition() {
                nextPopupY = popupRect.y + nextOffsetY;
                nextPopupBottom = nextPopupY + popupHeight;
                nextPopupX = popupRect.x + nextOffsetX;
                nextPopupRight = nextPopupX + popupWidth;
            }
            syncNextPopupPosition();
            // >>>>>>>>>> Top & Bottom
            var needAdjustY = supportAdjust(adjustY);
            var sameTB = popupPoints[0] === targetPoints[0];
            // Bottom to Top
            if (needAdjustY && popupPoints[0] === 't' && (nextPopupBottom > adjustCheckVisibleArea.bottom || prevFlipRef.current.bt)) {
                var tmpNextOffsetY = nextOffsetY;
                if (sameTB) {
                    tmpNextOffsetY -= popupHeight - targetHeight;
                } else {
                    tmpNextOffsetY = targetAlignPointTL.y - popupAlignPointBR.y - popupOffsetY;
                }
                var newVisibleArea = getIntersectionVisibleArea(nextOffsetX, tmpNextOffsetY);
                var newVisibleRecommendArea = getIntersectionVisibleArea(nextOffsetX, tmpNextOffsetY, visibleRegionArea);
                if (// Of course use larger one
                newVisibleArea > originIntersectionVisibleArea || newVisibleArea === originIntersectionVisibleArea && (!isVisibleFirst || // Choose recommend one
                newVisibleRecommendArea >= originIntersectionRecommendArea)) {
                    prevFlipRef.current.bt = true;
                    nextOffsetY = tmpNextOffsetY;
                    popupOffsetY = -popupOffsetY;
                    nextAlignInfo.points = [
                        reversePoints(popupPoints, 0),
                        reversePoints(targetPoints, 0)
                    ];
                } else {
                    prevFlipRef.current.bt = false;
                }
            }
            // Top to Bottom
            if (needAdjustY && popupPoints[0] === 'b' && (nextPopupY < adjustCheckVisibleArea.top || prevFlipRef.current.tb)) {
                var _tmpNextOffsetY = nextOffsetY;
                if (sameTB) {
                    _tmpNextOffsetY += popupHeight - targetHeight;
                } else {
                    _tmpNextOffsetY = targetAlignPointBR.y - popupAlignPointTL.y - popupOffsetY;
                }
                var _newVisibleArea = getIntersectionVisibleArea(nextOffsetX, _tmpNextOffsetY);
                var _newVisibleRecommendArea = getIntersectionVisibleArea(nextOffsetX, _tmpNextOffsetY, visibleRegionArea);
                if (// Of course use larger one
                _newVisibleArea > originIntersectionVisibleArea || _newVisibleArea === originIntersectionVisibleArea && (!isVisibleFirst || // Choose recommend one
                _newVisibleRecommendArea >= originIntersectionRecommendArea)) {
                    prevFlipRef.current.tb = true;
                    nextOffsetY = _tmpNextOffsetY;
                    popupOffsetY = -popupOffsetY;
                    nextAlignInfo.points = [
                        reversePoints(popupPoints, 0),
                        reversePoints(targetPoints, 0)
                    ];
                } else {
                    prevFlipRef.current.tb = false;
                }
            }
            // >>>>>>>>>> Left & Right
            var needAdjustX = supportAdjust(adjustX);
            // >>>>> Flip
            var sameLR = popupPoints[1] === targetPoints[1];
            // Right to Left
            if (needAdjustX && popupPoints[1] === 'l' && (nextPopupRight > adjustCheckVisibleArea.right || prevFlipRef.current.rl)) {
                var tmpNextOffsetX = nextOffsetX;
                if (sameLR) {
                    tmpNextOffsetX -= popupWidth - targetWidth;
                } else {
                    tmpNextOffsetX = targetAlignPointTL.x - popupAlignPointBR.x - popupOffsetX;
                }
                var _newVisibleArea2 = getIntersectionVisibleArea(tmpNextOffsetX, nextOffsetY);
                var _newVisibleRecommendArea2 = getIntersectionVisibleArea(tmpNextOffsetX, nextOffsetY, visibleRegionArea);
                if (// Of course use larger one
                _newVisibleArea2 > originIntersectionVisibleArea || _newVisibleArea2 === originIntersectionVisibleArea && (!isVisibleFirst || // Choose recommend one
                _newVisibleRecommendArea2 >= originIntersectionRecommendArea)) {
                    prevFlipRef.current.rl = true;
                    nextOffsetX = tmpNextOffsetX;
                    popupOffsetX = -popupOffsetX;
                    nextAlignInfo.points = [
                        reversePoints(popupPoints, 1),
                        reversePoints(targetPoints, 1)
                    ];
                } else {
                    prevFlipRef.current.rl = false;
                }
            }
            // Left to Right
            if (needAdjustX && popupPoints[1] === 'r' && (nextPopupX < adjustCheckVisibleArea.left || prevFlipRef.current.lr)) {
                var _tmpNextOffsetX = nextOffsetX;
                if (sameLR) {
                    _tmpNextOffsetX += popupWidth - targetWidth;
                } else {
                    _tmpNextOffsetX = targetAlignPointBR.x - popupAlignPointTL.x - popupOffsetX;
                }
                var _newVisibleArea3 = getIntersectionVisibleArea(_tmpNextOffsetX, nextOffsetY);
                var _newVisibleRecommendArea3 = getIntersectionVisibleArea(_tmpNextOffsetX, nextOffsetY, visibleRegionArea);
                if (// Of course use larger one
                _newVisibleArea3 > originIntersectionVisibleArea || _newVisibleArea3 === originIntersectionVisibleArea && (!isVisibleFirst || // Choose recommend one
                _newVisibleRecommendArea3 >= originIntersectionRecommendArea)) {
                    prevFlipRef.current.lr = true;
                    nextOffsetX = _tmpNextOffsetX;
                    popupOffsetX = -popupOffsetX;
                    nextAlignInfo.points = [
                        reversePoints(popupPoints, 1),
                        reversePoints(targetPoints, 1)
                    ];
                } else {
                    prevFlipRef.current.lr = false;
                }
            }
            // ============================ Shift ============================
            syncNextPopupPosition();
            var numShiftX = shiftX === true ? 0 : shiftX;
            if (typeof numShiftX === 'number') {
                // Left
                if (nextPopupX < visibleRegionArea.left) {
                    nextOffsetX -= nextPopupX - visibleRegionArea.left - popupOffsetX;
                    if (targetRect.x + targetWidth < visibleRegionArea.left + numShiftX) {
                        nextOffsetX += targetRect.x - visibleRegionArea.left + targetWidth - numShiftX;
                    }
                }
                // Right
                if (nextPopupRight > visibleRegionArea.right) {
                    nextOffsetX -= nextPopupRight - visibleRegionArea.right - popupOffsetX;
                    if (targetRect.x > visibleRegionArea.right - numShiftX) {
                        nextOffsetX += targetRect.x - visibleRegionArea.right + numShiftX;
                    }
                }
            }
            var numShiftY = shiftY === true ? 0 : shiftY;
            if (typeof numShiftY === 'number') {
                // Top
                if (nextPopupY < visibleRegionArea.top) {
                    nextOffsetY -= nextPopupY - visibleRegionArea.top - popupOffsetY;
                    // When target if far away from visible area
                    // Stop shift
                    if (targetRect.y + targetHeight < visibleRegionArea.top + numShiftY) {
                        nextOffsetY += targetRect.y - visibleRegionArea.top + targetHeight - numShiftY;
                    }
                }
                // Bottom
                if (nextPopupBottom > visibleRegionArea.bottom) {
                    nextOffsetY -= nextPopupBottom - visibleRegionArea.bottom - popupOffsetY;
                    if (targetRect.y > visibleRegionArea.bottom - numShiftY) {
                        nextOffsetY += targetRect.y - visibleRegionArea.bottom + numShiftY;
                    }
                }
            }
            // ============================ Arrow ============================
            // Arrow center align
            var popupLeft = popupRect.x + nextOffsetX;
            var popupRight = popupLeft + popupWidth;
            var popupTop = popupRect.y + nextOffsetY;
            var popupBottom = popupTop + popupHeight;
            var targetLeft = targetRect.x;
            var targetRight = targetLeft + targetWidth;
            var targetTop = targetRect.y;
            var targetBottom = targetTop + targetHeight;
            var maxLeft = Math.max(popupLeft, targetLeft);
            var minRight = Math.min(popupRight, targetRight);
            var xCenter = (maxLeft + minRight) / 2;
            var nextArrowX = xCenter - popupLeft;
            var maxTop = Math.max(popupTop, targetTop);
            var minBottom = Math.min(popupBottom, targetBottom);
            var yCenter = (maxTop + minBottom) / 2;
            var nextArrowY = yCenter - popupTop;
            onPopupAlign === null || onPopupAlign === void 0 || onPopupAlign(popupEle, nextAlignInfo);
            // Additional calculate right & bottom position
            var offsetX4Right = popupMirrorRect.right - popupRect.x - (nextOffsetX + popupRect.width);
            var offsetY4Bottom = popupMirrorRect.bottom - popupRect.y - (nextOffsetY + popupRect.height);
            if (_scaleX === 1) {
                nextOffsetX = Math.round(nextOffsetX);
                offsetX4Right = Math.round(offsetX4Right);
            }
            if (_scaleY === 1) {
                nextOffsetY = Math.round(nextOffsetY);
                offsetY4Bottom = Math.round(offsetY4Bottom);
            }
            var nextOffsetInfo = {
                ready: true,
                offsetX: nextOffsetX / _scaleX,
                offsetY: nextOffsetY / _scaleY,
                offsetR: offsetX4Right / _scaleX,
                offsetB: offsetY4Bottom / _scaleY,
                arrowX: nextArrowX / _scaleX,
                arrowY: nextArrowY / _scaleY,
                scaleX: _scaleX,
                scaleY: _scaleY,
                align: nextAlignInfo
            };
            setOffsetInfo(nextOffsetInfo);
        }
    });
    var triggerAlign = function triggerAlign() {
        alignCountRef.current += 1;
        var id = alignCountRef.current;
        // Merge all align requirement into one frame
        Promise.resolve().then(function() {
            if (alignCountRef.current === id) {
                onAlign();
            }
        });
    };
    // Reset ready status when placement & open changed
    var resetReady = function resetReady() {
        setOffsetInfo(function(ori) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({}, ori), {}, {
                ready: false
            });
        });
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(resetReady, [
        placement
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function() {
        if (!open) {
            resetReady();
        }
    }, [
        open
    ]);
    return [
        offsetInfo.ready,
        offsetInfo.offsetX,
        offsetInfo.offsetY,
        offsetInfo.offsetR,
        offsetInfo.offsetB,
        offsetInfo.arrowX,
        offsetInfo.arrowY,
        offsetInfo.scaleX,
        offsetInfo.scaleY,
        offsetInfo.align,
        triggerAlign
    ];
}
}}),
"[project]/node_modules/@rc-component/trigger/es/hooks/useWatch.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useWatch)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useLayoutEffect.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/trigger/es/util.js [app-ssr] (ecmascript)");
;
;
;
function useWatch(open, target, popup, onAlign, onScroll) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function() {
        if (open && target && popup) {
            var targetElement = target;
            var popupElement = popup;
            var targetScrollList = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collectScroller"])(targetElement);
            var popupScrollList = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collectScroller"])(popupElement);
            var win = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getWin"])(popupElement);
            var mergedList = new Set([
                win
            ].concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(targetScrollList), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(popupScrollList)));
            function notifyScroll() {
                onAlign();
                onScroll();
            }
            mergedList.forEach(function(scroller) {
                scroller.addEventListener('scroll', notifyScroll, {
                    passive: true
                });
            });
            win.addEventListener('resize', notifyScroll, {
                passive: true
            });
            // First time always do align
            onAlign();
            return function() {
                mergedList.forEach(function(scroller) {
                    scroller.removeEventListener('scroll', notifyScroll);
                    win.removeEventListener('resize', notifyScroll);
                });
            };
        }
    }, [
        open,
        target,
        popup
    ]);
}
}}),
"[project]/node_modules/@rc-component/trigger/es/hooks/useWinClick.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useWinClick)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$shadow$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/Dom/shadow.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/warning.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/trigger/es/util.js [app-ssr] (ecmascript)");
;
;
;
;
function useWinClick(open, clickToHide, targetEle, popupEle, mask, maskClosable, inPopupOrChild, triggerOpen) {
    var openRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(open);
    openRef.current = open;
    var popupPointerDownRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(false);
    // Click to hide is special action since click popup element should not hide
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(function() {
        if (clickToHide && popupEle && (!mask || maskClosable)) {
            var onPointerDown = function onPointerDown() {
                popupPointerDownRef.current = false;
            };
            var onTriggerClose = function onTriggerClose(e) {
                var _e$composedPath;
                if (openRef.current && !inPopupOrChild(((_e$composedPath = e.composedPath) === null || _e$composedPath === void 0 || (_e$composedPath = _e$composedPath.call(e)) === null || _e$composedPath === void 0 ? void 0 : _e$composedPath[0]) || e.target) && !popupPointerDownRef.current) {
                    triggerOpen(false);
                }
            };
            var win = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getWin"])(popupEle);
            win.addEventListener('pointerdown', onPointerDown, true);
            win.addEventListener('mousedown', onTriggerClose, true);
            win.addEventListener('contextmenu', onTriggerClose, true);
            // shadow root
            var targetShadowRoot = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$shadow$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getShadowRoot"])(targetEle);
            if (targetShadowRoot) {
                targetShadowRoot.addEventListener('mousedown', onTriggerClose, true);
                targetShadowRoot.addEventListener('contextmenu', onTriggerClose, true);
            }
            // Warning if target and popup not in same root
            if ("TURBOPACK compile-time truthy", 1) {
                var _targetEle$getRootNod, _popupEle$getRootNode;
                var targetRoot = targetEle === null || targetEle === void 0 || (_targetEle$getRootNod = targetEle.getRootNode) === null || _targetEle$getRootNod === void 0 ? void 0 : _targetEle$getRootNod.call(targetEle);
                var popupRoot = (_popupEle$getRootNode = popupEle.getRootNode) === null || _popupEle$getRootNode === void 0 ? void 0 : _popupEle$getRootNode.call(popupEle);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$warning$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["warning"])(targetRoot === popupRoot, "trigger element and popup element should in same shadow root.");
            }
            return function() {
                win.removeEventListener('pointerdown', onPointerDown, true);
                win.removeEventListener('mousedown', onTriggerClose, true);
                win.removeEventListener('contextmenu', onTriggerClose, true);
                if (targetShadowRoot) {
                    targetShadowRoot.removeEventListener('mousedown', onTriggerClose, true);
                    targetShadowRoot.removeEventListener('contextmenu', onTriggerClose, true);
                }
            };
        }
    }, [
        clickToHide,
        targetEle,
        popupEle,
        mask,
        maskClosable
    ]);
    function onPopupPointerDown() {
        popupPointerDownRef.current = true;
    }
    return onPopupPointerDown;
}
}}),
"[project]/node_modules/@rc-component/trigger/es/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__),
    "generateTrigger": (()=>generateTrigger)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/slicedToArray.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$portal$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/portal/es/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$portal$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/portal/es/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$resize$2d$observer$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-resize-observer/es/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$resize$2d$observer$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/rc-resize-observer/es/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$findDOMNode$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/Dom/findDOMNode.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$shadow$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/Dom/shadow.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useEvent.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useId$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useId.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useLayoutEffect.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$isMobile$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/isMobile.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$Popup$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/trigger/es/Popup/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$TriggerWrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/trigger/es/TriggerWrapper.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$context$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/trigger/es/context.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$hooks$2f$useAction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/trigger/es/hooks/useAction.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$hooks$2f$useAlign$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/trigger/es/hooks/useAlign.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$hooks$2f$useWatch$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/trigger/es/hooks/useWatch.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$hooks$2f$useWinClick$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/trigger/es/hooks/useWinClick.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/trigger/es/util.js [app-ssr] (ecmascript)");
;
;
;
var _excluded = [
    "prefixCls",
    "children",
    "action",
    "showAction",
    "hideAction",
    "popupVisible",
    "defaultPopupVisible",
    "onPopupVisibleChange",
    "afterPopupVisibleChange",
    "mouseEnterDelay",
    "mouseLeaveDelay",
    "focusDelay",
    "blurDelay",
    "mask",
    "maskClosable",
    "getPopupContainer",
    "forceRender",
    "autoDestroy",
    "destroyPopupOnHide",
    "popup",
    "popupClassName",
    "popupStyle",
    "popupPlacement",
    "builtinPlacements",
    "popupAlign",
    "zIndex",
    "stretch",
    "getPopupClassNameFromAlign",
    "fresh",
    "alignPoint",
    "onPopupClick",
    "onPopupAlign",
    "arrow",
    "popupMotion",
    "maskMotion",
    "popupTransitionName",
    "popupAnimation",
    "maskTransitionName",
    "maskAnimation",
    "className",
    "getTriggerDOMNode"
];
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
function generateTrigger() {
    var PortalComponent = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$portal$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"];
    var Trigger = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(function(props, ref) {
        var _props$prefixCls = props.prefixCls, prefixCls = _props$prefixCls === void 0 ? 'rc-trigger-popup' : _props$prefixCls, children = props.children, _props$action = props.action, action = _props$action === void 0 ? 'hover' : _props$action, showAction = props.showAction, hideAction = props.hideAction, popupVisible = props.popupVisible, defaultPopupVisible = props.defaultPopupVisible, onPopupVisibleChange = props.onPopupVisibleChange, afterPopupVisibleChange = props.afterPopupVisibleChange, mouseEnterDelay = props.mouseEnterDelay, _props$mouseLeaveDela = props.mouseLeaveDelay, mouseLeaveDelay = _props$mouseLeaveDela === void 0 ? 0.1 : _props$mouseLeaveDela, focusDelay = props.focusDelay, blurDelay = props.blurDelay, mask = props.mask, _props$maskClosable = props.maskClosable, maskClosable = _props$maskClosable === void 0 ? true : _props$maskClosable, getPopupContainer = props.getPopupContainer, forceRender = props.forceRender, autoDestroy = props.autoDestroy, destroyPopupOnHide = props.destroyPopupOnHide, popup = props.popup, popupClassName = props.popupClassName, popupStyle = props.popupStyle, popupPlacement = props.popupPlacement, _props$builtinPlaceme = props.builtinPlacements, builtinPlacements = _props$builtinPlaceme === void 0 ? {} : _props$builtinPlaceme, popupAlign = props.popupAlign, zIndex = props.zIndex, stretch = props.stretch, getPopupClassNameFromAlign = props.getPopupClassNameFromAlign, fresh = props.fresh, alignPoint = props.alignPoint, onPopupClick = props.onPopupClick, onPopupAlign = props.onPopupAlign, arrow = props.arrow, popupMotion = props.popupMotion, maskMotion = props.maskMotion, popupTransitionName = props.popupTransitionName, popupAnimation = props.popupAnimation, maskTransitionName = props.maskTransitionName, maskAnimation = props.maskAnimation, className = props.className, getTriggerDOMNode = props.getTriggerDOMNode, restProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(props, _excluded);
        var mergedAutoDestroy = autoDestroy || destroyPopupOnHide || false;
        // =========================== Mobile ===========================
        var _React$useState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false), _React$useState2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_React$useState, 2), mobile = _React$useState2[0], setMobile = _React$useState2[1];
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function() {
            setMobile((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$isMobile$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])());
        }, []);
        // ========================== Context ===========================
        var subPopupElements = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])({});
        var parentContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$context$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
        var context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(function() {
            return {
                registerSubPopup: function registerSubPopup(id, subPopupEle) {
                    subPopupElements.current[id] = subPopupEle;
                    parentContext === null || parentContext === void 0 || parentContext.registerSubPopup(id, subPopupEle);
                }
            };
        }, [
            parentContext
        ]);
        // =========================== Popup ============================
        var id = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useId$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
        var _React$useState3 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null), _React$useState4 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_React$useState3, 2), popupEle = _React$useState4[0], setPopupEle = _React$useState4[1];
        // Used for forwardRef popup. Not use internal
        var externalPopupRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
        var setPopupRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function(node) {
            externalPopupRef.current = node;
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$findDOMNode$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDOM"])(node) && popupEle !== node) {
                setPopupEle(node);
            }
            parentContext === null || parentContext === void 0 || parentContext.registerSubPopup(id, node);
        });
        // =========================== Target ===========================
        // Use state to control here since `useRef` update not trigger render
        var _React$useState5 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null), _React$useState6 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_React$useState5, 2), targetEle = _React$useState6[0], setTargetEle = _React$useState6[1];
        // Used for forwardRef target. Not use internal
        var externalForwardRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
        var setTargetRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function(node) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$findDOMNode$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDOM"])(node) && targetEle !== node) {
                setTargetEle(node);
                externalForwardRef.current = node;
            }
        });
        // ========================== Children ==========================
        var child = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Children"].only(children);
        var originChildProps = (child === null || child === void 0 ? void 0 : child.props) || {};
        var cloneProps = {};
        var inPopupOrChild = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function(ele) {
            var _getShadowRoot, _getShadowRoot2;
            var childDOM = targetEle;
            return (childDOM === null || childDOM === void 0 ? void 0 : childDOM.contains(ele)) || ((_getShadowRoot = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$shadow$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getShadowRoot"])(childDOM)) === null || _getShadowRoot === void 0 ? void 0 : _getShadowRoot.host) === ele || ele === childDOM || (popupEle === null || popupEle === void 0 ? void 0 : popupEle.contains(ele)) || ((_getShadowRoot2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$shadow$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getShadowRoot"])(popupEle)) === null || _getShadowRoot2 === void 0 ? void 0 : _getShadowRoot2.host) === ele || ele === popupEle || Object.values(subPopupElements.current).some(function(subPopupEle) {
                return (subPopupEle === null || subPopupEle === void 0 ? void 0 : subPopupEle.contains(ele)) || ele === subPopupEle;
            });
        });
        // =========================== Motion ===========================
        var mergePopupMotion = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getMotion"])(prefixCls, popupMotion, popupAnimation, popupTransitionName);
        var mergeMaskMotion = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getMotion"])(prefixCls, maskMotion, maskAnimation, maskTransitionName);
        // ============================ Open ============================
        var _React$useState7 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(defaultPopupVisible || false), _React$useState8 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_React$useState7, 2), internalOpen = _React$useState8[0], setInternalOpen = _React$useState8[1];
        // Render still use props as first priority
        var mergedOpen = popupVisible !== null && popupVisible !== void 0 ? popupVisible : internalOpen;
        // We use effect sync here in case `popupVisible` back to `undefined`
        var setMergedOpen = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function(nextOpen) {
            if (popupVisible === undefined) {
                setInternalOpen(nextOpen);
            }
        });
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function() {
            setInternalOpen(popupVisible || false);
        }, [
            popupVisible
        ]);
        var openRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(mergedOpen);
        openRef.current = mergedOpen;
        var lastTriggerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])([]);
        lastTriggerRef.current = [];
        var internalTriggerOpen = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function(nextOpen) {
            var _lastTriggerRef$curre;
            setMergedOpen(nextOpen);
            // Enter or Pointer will both trigger open state change
            // We only need take one to avoid duplicated change event trigger
            // Use `lastTriggerRef` to record last open type
            if (((_lastTriggerRef$curre = lastTriggerRef.current[lastTriggerRef.current.length - 1]) !== null && _lastTriggerRef$curre !== void 0 ? _lastTriggerRef$curre : mergedOpen) !== nextOpen) {
                lastTriggerRef.current.push(nextOpen);
                onPopupVisibleChange === null || onPopupVisibleChange === void 0 || onPopupVisibleChange(nextOpen);
            }
        });
        // Trigger for delay
        var delayRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])();
        var clearDelay = function clearDelay() {
            clearTimeout(delayRef.current);
        };
        var triggerOpen = function triggerOpen(nextOpen) {
            var delay = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
            clearDelay();
            if (delay === 0) {
                internalTriggerOpen(nextOpen);
            } else {
                delayRef.current = setTimeout(function() {
                    internalTriggerOpen(nextOpen);
                }, delay * 1000);
            }
        };
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(function() {
            return clearDelay;
        }, []);
        // ========================== Motion ============================
        var _React$useState9 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false), _React$useState10 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_React$useState9, 2), inMotion = _React$useState10[0], setInMotion = _React$useState10[1];
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function(firstMount) {
            if (!firstMount || mergedOpen) {
                setInMotion(true);
            }
        }, [
            mergedOpen
        ]);
        var _React$useState11 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null), _React$useState12 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_React$useState11, 2), motionPrepareResolve = _React$useState12[0], setMotionPrepareResolve = _React$useState12[1];
        // =========================== Align ============================
        var _React$useState13 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null), _React$useState14 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_React$useState13, 2), mousePos = _React$useState14[0], setMousePos = _React$useState14[1];
        var setMousePosByEvent = function setMousePosByEvent(event) {
            setMousePos([
                event.clientX,
                event.clientY
            ]);
        };
        var _useAlign = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$hooks$2f$useAlign$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(mergedOpen, popupEle, alignPoint && mousePos !== null ? mousePos : targetEle, popupPlacement, builtinPlacements, popupAlign, onPopupAlign), _useAlign2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_useAlign, 11), ready = _useAlign2[0], offsetX = _useAlign2[1], offsetY = _useAlign2[2], offsetR = _useAlign2[3], offsetB = _useAlign2[4], arrowX = _useAlign2[5], arrowY = _useAlign2[6], scaleX = _useAlign2[7], scaleY = _useAlign2[8], alignInfo = _useAlign2[9], onAlign = _useAlign2[10];
        var _useAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$hooks$2f$useAction$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(mobile, action, showAction, hideAction), _useAction2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_useAction, 2), showActions = _useAction2[0], hideActions = _useAction2[1];
        var clickToShow = showActions.has('click');
        var clickToHide = hideActions.has('click') || hideActions.has('contextMenu');
        var triggerAlign = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function() {
            if (!inMotion) {
                onAlign();
            }
        });
        var onScroll = function onScroll() {
            if (openRef.current && alignPoint && clickToHide) {
                triggerOpen(false);
            }
        };
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$hooks$2f$useWatch$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(mergedOpen, targetEle, popupEle, triggerAlign, onScroll);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function() {
            triggerAlign();
        }, [
            mousePos,
            popupPlacement
        ]);
        // When no builtinPlacements and popupAlign changed
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function() {
            if (mergedOpen && !(builtinPlacements !== null && builtinPlacements !== void 0 && builtinPlacements[popupPlacement])) {
                triggerAlign();
            }
        }, [
            JSON.stringify(popupAlign)
        ]);
        var alignedClassName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(function() {
            var baseClassName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAlignPopupClassName"])(builtinPlacements, prefixCls, alignInfo, alignPoint);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(baseClassName, getPopupClassNameFromAlign === null || getPopupClassNameFromAlign === void 0 ? void 0 : getPopupClassNameFromAlign(alignInfo));
        }, [
            alignInfo,
            getPopupClassNameFromAlign,
            builtinPlacements,
            prefixCls,
            alignPoint
        ]);
        // ============================ Refs ============================
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, function() {
            return {
                nativeElement: externalForwardRef.current,
                popupElement: externalPopupRef.current,
                forceAlign: triggerAlign
            };
        });
        // ========================== Stretch ===========================
        var _React$useState15 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0), _React$useState16 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_React$useState15, 2), targetWidth = _React$useState16[0], setTargetWidth = _React$useState16[1];
        var _React$useState17 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0), _React$useState18 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_React$useState17, 2), targetHeight = _React$useState18[0], setTargetHeight = _React$useState18[1];
        var syncTargetSize = function syncTargetSize() {
            if (stretch && targetEle) {
                var rect = targetEle.getBoundingClientRect();
                setTargetWidth(rect.width);
                setTargetHeight(rect.height);
            }
        };
        var onTargetResize = function onTargetResize() {
            syncTargetSize();
            triggerAlign();
        };
        // ========================== Motion ============================
        var onVisibleChanged = function onVisibleChanged(visible) {
            setInMotion(false);
            onAlign();
            afterPopupVisibleChange === null || afterPopupVisibleChange === void 0 || afterPopupVisibleChange(visible);
        };
        // We will trigger align when motion is in prepare
        var onPrepare = function onPrepare() {
            return new Promise(function(resolve) {
                syncTargetSize();
                setMotionPrepareResolve(function() {
                    return resolve;
                });
            });
        };
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function() {
            if (motionPrepareResolve) {
                onAlign();
                motionPrepareResolve();
                setMotionPrepareResolve(null);
            }
        }, [
            motionPrepareResolve
        ]);
        // =========================== Action ===========================
        /**
     * Util wrapper for trigger action
     */ function wrapperAction(eventName, nextOpen, delay, preEvent) {
            cloneProps[eventName] = function(event) {
                var _originChildProps$eve;
                preEvent === null || preEvent === void 0 || preEvent(event);
                triggerOpen(nextOpen, delay);
                // Pass to origin
                for(var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){
                    args[_key - 1] = arguments[_key];
                }
                (_originChildProps$eve = originChildProps[eventName]) === null || _originChildProps$eve === void 0 || _originChildProps$eve.call.apply(_originChildProps$eve, [
                    originChildProps,
                    event
                ].concat(args));
            };
        }
        // ======================= Action: Click ========================
        if (clickToShow || clickToHide) {
            cloneProps.onClick = function(event) {
                var _originChildProps$onC;
                if (openRef.current && clickToHide) {
                    triggerOpen(false);
                } else if (!openRef.current && clickToShow) {
                    setMousePosByEvent(event);
                    triggerOpen(true);
                }
                // Pass to origin
                for(var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++){
                    args[_key2 - 1] = arguments[_key2];
                }
                (_originChildProps$onC = originChildProps.onClick) === null || _originChildProps$onC === void 0 || _originChildProps$onC.call.apply(_originChildProps$onC, [
                    originChildProps,
                    event
                ].concat(args));
            };
        }
        // Click to hide is special action since click popup element should not hide
        var onPopupPointerDown = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$hooks$2f$useWinClick$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(mergedOpen, clickToHide, targetEle, popupEle, mask, maskClosable, inPopupOrChild, triggerOpen);
        // ======================= Action: Hover ========================
        var hoverToShow = showActions.has('hover');
        var hoverToHide = hideActions.has('hover');
        var onPopupMouseEnter;
        var onPopupMouseLeave;
        if (hoverToShow) {
            // Compatible with old browser which not support pointer event
            wrapperAction('onMouseEnter', true, mouseEnterDelay, function(event) {
                setMousePosByEvent(event);
            });
            wrapperAction('onPointerEnter', true, mouseEnterDelay, function(event) {
                setMousePosByEvent(event);
            });
            onPopupMouseEnter = function onPopupMouseEnter(event) {
                // Only trigger re-open when popup is visible
                if ((mergedOpen || inMotion) && popupEle !== null && popupEle !== void 0 && popupEle.contains(event.target)) {
                    triggerOpen(true, mouseEnterDelay);
                }
            };
            // Align Point
            if (alignPoint) {
                cloneProps.onMouseMove = function(event) {
                    var _originChildProps$onM;
                    // setMousePosByEvent(event);
                    (_originChildProps$onM = originChildProps.onMouseMove) === null || _originChildProps$onM === void 0 || _originChildProps$onM.call(originChildProps, event);
                };
            }
        }
        if (hoverToHide) {
            wrapperAction('onMouseLeave', false, mouseLeaveDelay);
            wrapperAction('onPointerLeave', false, mouseLeaveDelay);
            onPopupMouseLeave = function onPopupMouseLeave() {
                triggerOpen(false, mouseLeaveDelay);
            };
        }
        // ======================= Action: Focus ========================
        if (showActions.has('focus')) {
            wrapperAction('onFocus', true, focusDelay);
        }
        if (hideActions.has('focus')) {
            wrapperAction('onBlur', false, blurDelay);
        }
        // ==================== Action: ContextMenu =====================
        if (showActions.has('contextMenu')) {
            cloneProps.onContextMenu = function(event) {
                var _originChildProps$onC2;
                if (openRef.current && hideActions.has('contextMenu')) {
                    triggerOpen(false);
                } else {
                    setMousePosByEvent(event);
                    triggerOpen(true);
                }
                event.preventDefault();
                // Pass to origin
                for(var _len3 = arguments.length, args = new Array(_len3 > 1 ? _len3 - 1 : 0), _key3 = 1; _key3 < _len3; _key3++){
                    args[_key3 - 1] = arguments[_key3];
                }
                (_originChildProps$onC2 = originChildProps.onContextMenu) === null || _originChildProps$onC2 === void 0 || _originChildProps$onC2.call.apply(_originChildProps$onC2, [
                    originChildProps,
                    event
                ].concat(args));
            };
        }
        // ========================= ClassName ==========================
        if (className) {
            cloneProps.className = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(originChildProps.className, className);
        }
        // =========================== Render ===========================
        var mergedChildrenProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({}, originChildProps), cloneProps);
        // Pass props into cloneProps for nest usage
        var passedProps = {};
        var passedEventList = [
            'onContextMenu',
            'onClick',
            'onMouseDown',
            'onTouchStart',
            'onMouseEnter',
            'onMouseLeave',
            'onFocus',
            'onBlur'
        ];
        passedEventList.forEach(function(eventName) {
            if (restProps[eventName]) {
                passedProps[eventName] = function() {
                    var _mergedChildrenProps$;
                    for(var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++){
                        args[_key4] = arguments[_key4];
                    }
                    (_mergedChildrenProps$ = mergedChildrenProps[eventName]) === null || _mergedChildrenProps$ === void 0 || _mergedChildrenProps$.call.apply(_mergedChildrenProps$, [
                        mergedChildrenProps
                    ].concat(args));
                    restProps[eventName].apply(restProps, args);
                };
            }
        });
        // Child Node
        var triggerNode = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cloneElement"])(child, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({}, mergedChildrenProps), passedProps));
        var arrowPos = {
            x: arrowX,
            y: arrowY
        };
        var innerArrow = arrow ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({}, arrow !== true ? arrow : {}) : null;
        // Render
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], null, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$resize$2d$observer$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"], {
            disabled: !mergedOpen,
            ref: setTargetRef,
            onResize: onTargetResize
        }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$TriggerWrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
            getTriggerDOMNode: getTriggerDOMNode
        }, triggerNode)), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$context$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Provider, {
            value: context
        }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$Popup$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
            portal: PortalComponent,
            ref: setPopupRef,
            prefixCls: prefixCls,
            popup: popup,
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(popupClassName, alignedClassName),
            style: popupStyle,
            target: targetEle,
            onMouseEnter: onPopupMouseEnter,
            onMouseLeave: onPopupMouseLeave,
            onPointerEnter: onPopupMouseEnter,
            zIndex: zIndex,
            open: mergedOpen,
            keepDom: inMotion,
            fresh: fresh,
            onClick: onPopupClick,
            onPointerDownCapture: onPopupPointerDown,
            mask: mask,
            motion: mergePopupMotion,
            maskMotion: mergeMaskMotion,
            onVisibleChanged: onVisibleChanged,
            onPrepare: onPrepare,
            forceRender: forceRender,
            autoDestroy: mergedAutoDestroy,
            getPopupContainer: getPopupContainer,
            align: alignInfo,
            arrow: innerArrow,
            arrowPos: arrowPos,
            ready: ready,
            offsetX: offsetX,
            offsetY: offsetY,
            offsetR: offsetR,
            offsetB: offsetB,
            onAlign: triggerAlign,
            stretch: stretch,
            targetWidth: targetWidth / scaleX,
            targetHeight: targetHeight / scaleY
        })));
    });
    if ("TURBOPACK compile-time truthy", 1) {
        Trigger.displayName = 'Trigger';
    }
    return Trigger;
}
const __TURBOPACK__default__export__ = generateTrigger(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$portal$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"]);
}}),
"[project]/node_modules/@rc-component/mini-decimal/es/supportUtil.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "supportBigInt": (()=>supportBigInt)
});
function supportBigInt() {
    return typeof BigInt === 'function';
}
}}),
"[project]/node_modules/@rc-component/mini-decimal/es/numberUtil.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getNumberPrecision": (()=>getNumberPrecision),
    "isE": (()=>isE),
    "isEmpty": (()=>isEmpty),
    "num2str": (()=>num2str),
    "trimNumber": (()=>trimNumber),
    "validateNumber": (()=>validateNumber)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$supportUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mini-decimal/es/supportUtil.js [app-ssr] (ecmascript)");
;
function isEmpty(value) {
    return !value && value !== 0 && !Number.isNaN(value) || !String(value).trim();
}
function trimNumber(numStr) {
    var str = numStr.trim();
    var negative = str.startsWith('-');
    if (negative) {
        str = str.slice(1);
    }
    str = str// Remove decimal 0. `1.000` => `1.`, `1.100` => `1.1`
    .replace(/(\.\d*[^0])0*$/, '$1')// Remove useless decimal. `1.` => `1`
    .replace(/\.0*$/, '')// Remove integer 0. `0001` => `1`, 000.1' => `.1`
    .replace(/^0+/, '');
    if (str.startsWith('.')) {
        str = "0".concat(str);
    }
    var trimStr = str || '0';
    var splitNumber = trimStr.split('.');
    var integerStr = splitNumber[0] || '0';
    var decimalStr = splitNumber[1] || '0';
    if (integerStr === '0' && decimalStr === '0') {
        negative = false;
    }
    var negativeStr = negative ? '-' : '';
    return {
        negative: negative,
        negativeStr: negativeStr,
        trimStr: trimStr,
        integerStr: integerStr,
        decimalStr: decimalStr,
        fullStr: "".concat(negativeStr).concat(trimStr)
    };
}
function isE(number) {
    var str = String(number);
    return !Number.isNaN(Number(str)) && str.includes('e');
}
function getNumberPrecision(number) {
    var numStr = String(number);
    if (isE(number)) {
        var precision = Number(numStr.slice(numStr.indexOf('e-') + 2));
        var decimalMatch = numStr.match(/\.(\d+)/);
        if (decimalMatch !== null && decimalMatch !== void 0 && decimalMatch[1]) {
            precision += decimalMatch[1].length;
        }
        return precision;
    }
    return numStr.includes('.') && validateNumber(numStr) ? numStr.length - numStr.indexOf('.') - 1 : 0;
}
function num2str(number) {
    var numStr = String(number);
    if (isE(number)) {
        if (number > Number.MAX_SAFE_INTEGER) {
            return String((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$supportUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supportBigInt"])() ? BigInt(number).toString() : Number.MAX_SAFE_INTEGER);
        }
        if (number < Number.MIN_SAFE_INTEGER) {
            return String((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$supportUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supportBigInt"])() ? BigInt(number).toString() : Number.MIN_SAFE_INTEGER);
        }
        numStr = number.toFixed(getNumberPrecision(numStr));
    }
    return trimNumber(numStr).fullStr;
}
function validateNumber(num) {
    if (typeof num === 'number') {
        return !Number.isNaN(num);
    }
    // Empty
    if (!num) {
        return false;
    }
    return(// Normal type: 11.28
    /^\s*-?\d+(\.\d+)?\s*$/.test(num) || // Pre-number: 1.
    /^\s*-?\d+\.\s*$/.test(num) || // Post-number: .1
    /^\s*-?\.\d+\s*$/.test(num));
}
}}),
"[project]/node_modules/@rc-component/mini-decimal/es/BigIntDecimal.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>BigIntDecimal)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$classCallCheck$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/classCallCheck.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$createClass$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/createClass.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/defineProperty.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$numberUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mini-decimal/es/numberUtil.js [app-ssr] (ecmascript)");
;
;
;
;
var BigIntDecimal = /*#__PURE__*/ function() {
    /** BigInt will convert `0009` to `9`. We need record the len of decimal */ function BigIntDecimal(value) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$classCallCheck$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, BigIntDecimal);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, "origin", '');
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, "negative", void 0);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, "integer", void 0);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, "decimal", void 0);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, "decimalLen", void 0);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, "empty", void 0);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, "nan", void 0);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$numberUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isEmpty"])(value)) {
            this.empty = true;
            return;
        }
        this.origin = String(value);
        // Act like Number convert
        if (value === '-' || Number.isNaN(value)) {
            this.nan = true;
            return;
        }
        var mergedValue = value;
        // We need convert back to Number since it require `toFixed` to handle this
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$numberUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isE"])(mergedValue)) {
            mergedValue = Number(mergedValue);
        }
        mergedValue = typeof mergedValue === 'string' ? mergedValue : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$numberUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["num2str"])(mergedValue);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$numberUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["validateNumber"])(mergedValue)) {
            var trimRet = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$numberUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["trimNumber"])(mergedValue);
            this.negative = trimRet.negative;
            var numbers = trimRet.trimStr.split('.');
            this.integer = BigInt(numbers[0]);
            var decimalStr = numbers[1] || '0';
            this.decimal = BigInt(decimalStr);
            this.decimalLen = decimalStr.length;
        } else {
            this.nan = true;
        }
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$createClass$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(BigIntDecimal, [
        {
            key: "getMark",
            value: function getMark() {
                return this.negative ? '-' : '';
            }
        },
        {
            key: "getIntegerStr",
            value: function getIntegerStr() {
                return this.integer.toString();
            }
        },
        {
            key: "getDecimalStr",
            value: function getDecimalStr() {
                return this.decimal.toString().padStart(this.decimalLen, '0');
            }
        },
        {
            key: "alignDecimal",
            value: function alignDecimal(decimalLength) {
                var str = "".concat(this.getMark()).concat(this.getIntegerStr()).concat(this.getDecimalStr().padEnd(decimalLength, '0'));
                return BigInt(str);
            }
        },
        {
            key: "negate",
            value: function negate() {
                var clone = new BigIntDecimal(this.toString());
                clone.negative = !clone.negative;
                return clone;
            }
        },
        {
            key: "cal",
            value: function cal(offset, calculator, calDecimalLen) {
                var maxDecimalLength = Math.max(this.getDecimalStr().length, offset.getDecimalStr().length);
                var myAlignedDecimal = this.alignDecimal(maxDecimalLength);
                var offsetAlignedDecimal = offset.alignDecimal(maxDecimalLength);
                var valueStr = calculator(myAlignedDecimal, offsetAlignedDecimal).toString();
                var nextDecimalLength = calDecimalLen(maxDecimalLength);
                // We need fill string length back to `maxDecimalLength` to avoid parser failed
                var _trimNumber = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$numberUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["trimNumber"])(valueStr), negativeStr = _trimNumber.negativeStr, trimStr = _trimNumber.trimStr;
                var hydrateValueStr = "".concat(negativeStr).concat(trimStr.padStart(nextDecimalLength + 1, '0'));
                return new BigIntDecimal("".concat(hydrateValueStr.slice(0, -nextDecimalLength), ".").concat(hydrateValueStr.slice(-nextDecimalLength)));
            }
        },
        {
            key: "add",
            value: function add(value) {
                if (this.isInvalidate()) {
                    return new BigIntDecimal(value);
                }
                var offset = new BigIntDecimal(value);
                if (offset.isInvalidate()) {
                    return this;
                }
                return this.cal(offset, function(num1, num2) {
                    return num1 + num2;
                }, function(len) {
                    return len;
                });
            }
        },
        {
            key: "multi",
            value: function multi(value) {
                var target = new BigIntDecimal(value);
                if (this.isInvalidate() || target.isInvalidate()) {
                    return new BigIntDecimal(NaN);
                }
                return this.cal(target, function(num1, num2) {
                    return num1 * num2;
                }, function(len) {
                    return len * 2;
                });
            }
        },
        {
            key: "isEmpty",
            value: function isEmpty() {
                return this.empty;
            }
        },
        {
            key: "isNaN",
            value: function isNaN() {
                return this.nan;
            }
        },
        {
            key: "isInvalidate",
            value: function isInvalidate() {
                return this.isEmpty() || this.isNaN();
            }
        },
        {
            key: "equals",
            value: function equals(target) {
                return this.toString() === (target === null || target === void 0 ? void 0 : target.toString());
            }
        },
        {
            key: "lessEquals",
            value: function lessEquals(target) {
                return this.add(target.negate().toString()).toNumber() <= 0;
            }
        },
        {
            key: "toNumber",
            value: function toNumber() {
                if (this.isNaN()) {
                    return NaN;
                }
                return Number(this.toString());
            }
        },
        {
            key: "toString",
            value: function toString() {
                var safe = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;
                if (!safe) {
                    return this.origin;
                }
                if (this.isInvalidate()) {
                    return '';
                }
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$numberUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["trimNumber"])("".concat(this.getMark()).concat(this.getIntegerStr(), ".").concat(this.getDecimalStr())).fullStr;
            }
        }
    ]);
    return BigIntDecimal;
}();
;
}}),
"[project]/node_modules/@rc-component/mini-decimal/es/NumberDecimal.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>NumberDecimal)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$classCallCheck$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/classCallCheck.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$createClass$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/createClass.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/defineProperty.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$numberUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mini-decimal/es/numberUtil.js [app-ssr] (ecmascript)");
;
;
;
;
/**
 * We can remove this when IE not support anymore
 */ var NumberDecimal = /*#__PURE__*/ function() {
    function NumberDecimal(value) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$classCallCheck$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, NumberDecimal);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, "origin", '');
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, "number", void 0);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, "empty", void 0);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$numberUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isEmpty"])(value)) {
            this.empty = true;
            return;
        }
        this.origin = String(value);
        this.number = Number(value);
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$createClass$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(NumberDecimal, [
        {
            key: "negate",
            value: function negate() {
                return new NumberDecimal(-this.toNumber());
            }
        },
        {
            key: "add",
            value: function add(value) {
                if (this.isInvalidate()) {
                    return new NumberDecimal(value);
                }
                var target = Number(value);
                if (Number.isNaN(target)) {
                    return this;
                }
                var number = this.number + target;
                // [Legacy] Back to safe integer
                if (number > Number.MAX_SAFE_INTEGER) {
                    return new NumberDecimal(Number.MAX_SAFE_INTEGER);
                }
                if (number < Number.MIN_SAFE_INTEGER) {
                    return new NumberDecimal(Number.MIN_SAFE_INTEGER);
                }
                var maxPrecision = Math.max((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$numberUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getNumberPrecision"])(this.number), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$numberUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getNumberPrecision"])(target));
                return new NumberDecimal(number.toFixed(maxPrecision));
            }
        },
        {
            key: "multi",
            value: function multi(value) {
                var target = Number(value);
                if (this.isInvalidate() || Number.isNaN(target)) {
                    return new NumberDecimal(NaN);
                }
                var number = this.number * target;
                // [Legacy] Back to safe integer
                if (number > Number.MAX_SAFE_INTEGER) {
                    return new NumberDecimal(Number.MAX_SAFE_INTEGER);
                }
                if (number < Number.MIN_SAFE_INTEGER) {
                    return new NumberDecimal(Number.MIN_SAFE_INTEGER);
                }
                var maxPrecision = Math.max((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$numberUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getNumberPrecision"])(this.number), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$numberUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getNumberPrecision"])(target));
                return new NumberDecimal(number.toFixed(maxPrecision));
            }
        },
        {
            key: "isEmpty",
            value: function isEmpty() {
                return this.empty;
            }
        },
        {
            key: "isNaN",
            value: function isNaN() {
                return Number.isNaN(this.number);
            }
        },
        {
            key: "isInvalidate",
            value: function isInvalidate() {
                return this.isEmpty() || this.isNaN();
            }
        },
        {
            key: "equals",
            value: function equals(target) {
                return this.toNumber() === (target === null || target === void 0 ? void 0 : target.toNumber());
            }
        },
        {
            key: "lessEquals",
            value: function lessEquals(target) {
                return this.add(target.negate().toString()).toNumber() <= 0;
            }
        },
        {
            key: "toNumber",
            value: function toNumber() {
                return this.number;
            }
        },
        {
            key: "toString",
            value: function toString() {
                var safe = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : true;
                if (!safe) {
                    return this.origin;
                }
                if (this.isInvalidate()) {
                    return '';
                }
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$numberUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["num2str"])(this.number);
            }
        }
    ]);
    return NumberDecimal;
}();
;
}}),
"[project]/node_modules/@rc-component/mini-decimal/es/MiniDecimal.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/* eslint-disable max-classes-per-file */ __turbopack_context__.s({
    "default": (()=>getMiniDecimal),
    "toFixed": (()=>toFixed)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$BigIntDecimal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mini-decimal/es/BigIntDecimal.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$NumberDecimal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mini-decimal/es/NumberDecimal.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$numberUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mini-decimal/es/numberUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$supportUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mini-decimal/es/supportUtil.js [app-ssr] (ecmascript)");
;
;
;
;
;
function getMiniDecimal(value) {
    // We use BigInt here.
    // Will fallback to Number if not support.
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$supportUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supportBigInt"])()) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$BigIntDecimal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](value);
    }
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$NumberDecimal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](value);
}
function toFixed(numStr, separatorStr, precision) {
    var cutOnly = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : false;
    if (numStr === '') {
        return '';
    }
    var _trimNumber = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$numberUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["trimNumber"])(numStr), negativeStr = _trimNumber.negativeStr, integerStr = _trimNumber.integerStr, decimalStr = _trimNumber.decimalStr;
    var precisionDecimalStr = "".concat(separatorStr).concat(decimalStr);
    var numberWithoutDecimal = "".concat(negativeStr).concat(integerStr);
    if (precision >= 0) {
        // We will get last + 1 number to check if need advanced number
        var advancedNum = Number(decimalStr[precision]);
        if (advancedNum >= 5 && !cutOnly) {
            var advancedDecimal = getMiniDecimal(numStr).add("".concat(negativeStr, "0.").concat('0'.repeat(precision)).concat(10 - advancedNum));
            return toFixed(advancedDecimal.toString(), separatorStr, precision, cutOnly);
        }
        if (precision === 0) {
            return numberWithoutDecimal;
        }
        return "".concat(numberWithoutDecimal).concat(separatorStr).concat(decimalStr.padEnd(precision, '0').slice(0, precision));
    }
    if (precisionDecimalStr === '.0') {
        return numberWithoutDecimal;
    }
    return "".concat(numberWithoutDecimal).concat(precisionDecimalStr);
}
}}),
"[project]/node_modules/@rc-component/mini-decimal/es/MiniDecimal.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$BigIntDecimal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mini-decimal/es/BigIntDecimal.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$NumberDecimal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mini-decimal/es/NumberDecimal.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$numberUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mini-decimal/es/numberUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$supportUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mini-decimal/es/supportUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$MiniDecimal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mini-decimal/es/MiniDecimal.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@rc-component/mini-decimal/es/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$MiniDecimal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mini-decimal/es/MiniDecimal.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$MiniDecimal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mini-decimal/es/MiniDecimal.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$numberUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mini-decimal/es/numberUtil.js [app-ssr] (ecmascript)");
;
;
;
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$MiniDecimal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"];
}}),
"[project]/node_modules/@rc-component/mini-decimal/es/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$MiniDecimal$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mini-decimal/es/MiniDecimal.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$numberUtil$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mini-decimal/es/numberUtil.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mini$2d$decimal$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mini-decimal/es/index.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@rc-component/qrcode/es/libs/qrcodegen.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Ecc": (()=>Ecc),
    "Mode": (()=>Mode),
    "QrCode": (()=>QrCode),
    "QrSegment": (()=>QrSegment)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$createForOfIteratorHelper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/createForOfIteratorHelper.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$classCallCheck$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/classCallCheck.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$createClass$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/createClass.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/defineProperty.js [app-ssr] (ecmascript)");
;
;
;
;
var _class, _class2;
// Copyright (c) Project Nayuki. (MIT License)
// https://www.nayuki.io/page/qr-code-generator-library
// Modification with code reorder and prettier
// --------------------------------------------
// Appends the given number of low-order bits of the given value
// to the given buffer. Requires 0 <= len <= 31 and 0 <= val < 2^len.
function appendBits(val, len, bb) {
    if (len < 0 || len > 31 || val >>> len != 0) throw new RangeError('Value out of range');
    for(var i = len - 1; i >= 0; i-- // Append bit by bit
    )bb.push(val >>> i & 1);
}
// Returns true iff the i'th bit of x is set to 1.
function getBit(x, i) {
    return (x >>> i & 1) != 0;
}
// Throws an exception if the given condition is false.
function assert(cond) {
    if (!cond) throw new Error('Assertion error');
}
var Mode = /*#__PURE__*/ function() {
    function Mode(modeBits, numBitsCharCount) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$classCallCheck$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, Mode);
        /*-- Constructor and fields --*/ // The mode indicator bits, which is a unumber4 value (range 0 to 15).
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, "modeBits", void 0);
        // Number of character count bits for three different version ranges.
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, "numBitsCharCount", void 0);
        this.modeBits = modeBits;
        this.numBitsCharCount = numBitsCharCount;
    }
    /*-- Method --*/ // (Package-private) Returns the bit width of the character count field for a segment in
    // this mode in a QR Code at the given version number. The result is in the range [0, 16].
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$createClass$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(Mode, [
        {
            key: "numCharCountBits",
            value: function numCharCountBits(ver) {
                return this.numBitsCharCount[Math.floor((ver + 7) / 17)];
            }
        }
    ]);
    return Mode;
}();
/*---- Public helper enumeration ----*/ /*
 * The error correction level in a QR Code symbol. Immutable.
 */ _class = Mode;
/*-- Constants --*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(Mode, "NUMERIC", new _class(0x1, [
    10,
    12,
    14
]));
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(Mode, "ALPHANUMERIC", new _class(0x2, [
    9,
    11,
    13
]));
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(Mode, "BYTE", new _class(0x4, [
    8,
    16,
    16
]));
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(Mode, "KANJI", new _class(0x8, [
    8,
    10,
    12
]));
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(Mode, "ECI", new _class(0x7, [
    0,
    0,
    0
]));
var Ecc = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$createClass$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function Ecc(ordinal, formatBits) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$classCallCheck$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, Ecc);
    // The QR Code can tolerate about 30% erroneous codewords
    /*-- Constructor and fields --*/ // In the range 0 to 3 (unsigned 2-bit numbereger).
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, "ordinal", void 0);
    // (Package-private) In the range 0 to 3 (unsigned 2-bit numbereger).
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, "formatBits", void 0);
    this.ordinal = ordinal;
    this.formatBits = formatBits;
});
/*
 * A segment of character/binary/control data in a QR Code symbol.
 * Instances of this class are immutable.
 * The mid-level way to create a segment is to take the payload data
 * and call a static factory function such as QrSegment.makeNumeric().
 * The low-level way to create a segment is to custom-make the bit buffer
 * and call the QrSegment() constructor with appropriate values.
 * This segment class imposes no length restrictions, but QR Codes have restrictions.
 * Even in the most favorable conditions, a QR Code can only hold 7089 characters of data.
 * Any segment longer than this is meaningless for the purpose of generating QR Codes.
 */ _class2 = Ecc;
/*-- Constants --*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(Ecc, "LOW", new _class2(0, 1));
// The QR Code can tolerate about  7% erroneous codewords
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(Ecc, "MEDIUM", new _class2(1, 0));
// The QR Code can tolerate about 15% erroneous codewords
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(Ecc, "QUARTILE", new _class2(2, 3));
// The QR Code can tolerate about 25% erroneous codewords
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(Ecc, "HIGH", new _class2(3, 2));
var QrSegment = /*#__PURE__*/ function() {
    // Creates a new QR Code segment with the given attributes and data.
    // The character count (numChars) must agree with the mode and the bit buffer length,
    // but the constranumber isn't checked. The given bit buffer is cloned and stored.
    function QrSegment(mode, numChars, bitData) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$classCallCheck$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, QrSegment);
        /*-- Constructor (low level) and fields --*/ // The mode indicator of this segment.
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, "mode", void 0);
        // The length of this segment's unencoded data. Measured in characters for
        // numeric/alphanumeric/kanji mode, bytes for byte mode, and 0 for ECI mode.
        // Always zero or positive. Not the same as the data's bit length.
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, "numChars", void 0);
        // The data bits of this segment. Accessed through getData().
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, "bitData", void 0);
        this.mode = mode;
        this.numChars = numChars;
        this.bitData = bitData;
        if (numChars < 0) throw new RangeError('Invalid argument');
        this.bitData = bitData.slice(); // Make defensive copy
    }
    /*-- Methods --*/ // Returns a new copy of the data bits of this segment.
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$createClass$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(QrSegment, [
        {
            key: "getData",
            value: function getData() {
                return this.bitData.slice(); // Make defensive copy
            }
        }
    ], [
        {
            key: "makeBytes",
            value: /*-- Static factory functions (mid level) --*/ // Returns a segment representing the given binary data encoded in
            // byte mode. All input byte arrays are acceptable. Any text string
            // can be converted to UTF-8 bytes and encoded as a byte mode segment.
            function makeBytes(data) {
                var bb = [];
                var _iterator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$createForOfIteratorHelper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(data), _step;
                try {
                    for(_iterator.s(); !(_step = _iterator.n()).done;){
                        var b = _step.value;
                        appendBits(b, 8, bb);
                    }
                } catch (err) {
                    _iterator.e(err);
                } finally{
                    _iterator.f();
                }
                return new QrSegment(Mode.BYTE, data.length, bb);
            }
        },
        {
            key: "makeNumeric",
            value: function makeNumeric(digits) {
                if (!QrSegment.isNumeric(digits)) throw new RangeError('String contains non-numeric characters');
                var bb = [];
                for(var i = 0; i < digits.length;){
                    // Consume up to 3 digits per iteration
                    var n = Math.min(digits.length - i, 3);
                    appendBits(parseInt(digits.substring(i, i + n), 10), n * 3 + 1, bb);
                    i += n;
                }
                return new QrSegment(Mode.NUMERIC, digits.length, bb);
            }
        },
        {
            key: "makeAlphanumeric",
            value: function makeAlphanumeric(text) {
                if (!QrSegment.isAlphanumeric(text)) throw new RangeError('String contains unencodable characters in alphanumeric mode');
                var bb = [];
                var i;
                for(i = 0; i + 2 <= text.length; i += 2){
                    // Process groups of 2
                    var temp = QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)) * 45;
                    temp += QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i + 1));
                    appendBits(temp, 11, bb);
                }
                if (i < text.length) // 1 character remaining
                appendBits(QrSegment.ALPHANUMERIC_CHARSET.indexOf(text.charAt(i)), 6, bb);
                return new QrSegment(Mode.ALPHANUMERIC, text.length, bb);
            }
        },
        {
            key: "makeSegments",
            value: function makeSegments(text) {
                // Select the most efficient segment encoding automatically
                if (text == '') return [];
                else if (QrSegment.isNumeric(text)) return [
                    QrSegment.makeNumeric(text)
                ];
                else if (QrSegment.isAlphanumeric(text)) return [
                    QrSegment.makeAlphanumeric(text)
                ];
                else return [
                    QrSegment.makeBytes(QrSegment.toUtf8ByteArray(text))
                ];
            }
        },
        {
            key: "makeEci",
            value: function makeEci(assignVal) {
                var bb = [];
                if (assignVal < 0) throw new RangeError('ECI assignment value out of range');
                else if (assignVal < 1 << 7) appendBits(assignVal, 8, bb);
                else if (assignVal < 1 << 14) {
                    appendBits(2, 2, bb);
                    appendBits(assignVal, 14, bb);
                } else if (assignVal < 1000000) {
                    appendBits(6, 3, bb);
                    appendBits(assignVal, 21, bb);
                } else throw new RangeError('ECI assignment value out of range');
                return new QrSegment(Mode.ECI, 0, bb);
            }
        },
        {
            key: "isNumeric",
            value: function isNumeric(text) {
                return QrSegment.NUMERIC_REGEX.test(text);
            }
        },
        {
            key: "isAlphanumeric",
            value: function isAlphanumeric(text) {
                return QrSegment.ALPHANUMERIC_REGEX.test(text);
            }
        },
        {
            key: "getTotalBits",
            value: function getTotalBits(segs, version) {
                var result = 0;
                var _iterator2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$createForOfIteratorHelper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(segs), _step2;
                try {
                    for(_iterator2.s(); !(_step2 = _iterator2.n()).done;){
                        var seg = _step2.value;
                        var ccbits = seg.mode.numCharCountBits(version);
                        if (seg.numChars >= 1 << ccbits) return Infinity; // The segment's length doesn't fit the field's bit width
                        result += 4 + ccbits + seg.bitData.length;
                    }
                } catch (err) {
                    _iterator2.e(err);
                } finally{
                    _iterator2.f();
                }
                return result;
            }
        },
        {
            key: "toUtf8ByteArray",
            value: function toUtf8ByteArray(input) {
                var str = encodeURI(input);
                var result = [];
                for(var i = 0; i < str.length; i++){
                    if (str.charAt(i) != '%') result.push(str.charCodeAt(i));
                    else {
                        result.push(parseInt(str.substring(i + 1, i + 3), 16));
                        i += 2;
                    }
                }
                return result;
            }
        }
    ]);
    return QrSegment;
}();
/*
 * A QR Code symbol, which is a type of two-dimension barcode.
 * Invented by Denso Wave and described in the ISO/IEC 18004 standard.
 * Instances of this class represent an immutable square grid of dark and light cells.
 * The class provides static factory functions to create a QR Code from text or binary data.
 * The class covers the QR Code Model 2 specification, supporting all versions (sizes)
 * from 1 to 40, all 4 error correction levels, and 4 character encoding modes.
 *
 * Ways to create a QR Code object:
 * - High level: Take the payload data and call QrCode.encodeText() or QrCode.encodeBinary().
 * - Mid level: Custom-make the list of segments and call QrCode.encodeSegments().
 * - Low level: Custom-make the array of data codeword bytes (including
 *   segment headers and final padding, excluding error correction codewords),
 *   supply the appropriate version number, and call the QrCode() constructor.
 * (Note that all ways require supplying the desired error correction level.)
 */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(QrSegment, "NUMERIC_REGEX", /^[0-9]*$/);
// Describes precisely all strings that are encodable in alphanumeric mode.
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(QrSegment, "ALPHANUMERIC_REGEX", /^[A-Z0-9 $%*+.\/:-]*$/);
// The set of all legal characters in alphanumeric mode,
// where each character value maps to the index in the string.
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(QrSegment, "ALPHANUMERIC_CHARSET", '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ $%*+-./:');
var QrCode = /*#__PURE__*/ function() {
    // Creates a new QR Code with the given version number,
    // error correction level, data codeword bytes, and mask number.
    // This is a low-level API that most users should not use directly.
    // A mid-level API is the encodeSegments() function.
    function QrCode(// The version number of this QR Code, which is between 1 and 40 (inclusive).
    // This determines the size of this barcode.
    version, // The error correction level used in this QR Code.
    errorCorrectionLevel, dataCodewords, oriMsk) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$classCallCheck$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, QrCode);
        /*-- Fields --*/ // The width and height of this QR Code, measured in modules, between
        // 21 and 177 (inclusive). This is equal to version * 4 + 17.
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, "size", void 0);
        // The index of the mask pattern used in this QR Code, which is between 0 and 7 (inclusive).
        // Even if a QR Code is created with automatic masking requested (mask = -1),
        // the resulting object still has a mask value between 0 and 7.
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, "mask", void 0);
        // The modules of this QR Code (false = light, true = dark).
        // Immutable after constructor finishes. Accessed through getModule().
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, "modules", []);
        // Indicates function modules that are not subjected to masking. Discarded when constructor finishes.
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, "isFunction", []);
        /*-- Constructor (low level) and fields --*/ // The version number of this QR Code, which is between 1 and 40 (inclusive).
        // This determines the size of this barcode.
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, "version", void 0);
        // The error correction level used in this QR Code.
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, "errorCorrectionLevel", void 0);
        var msk = oriMsk;
        this.version = version;
        this.errorCorrectionLevel = errorCorrectionLevel;
        // Check scalar arguments
        if (version < QrCode.MIN_VERSION || version > QrCode.MAX_VERSION) throw new RangeError('Version value out of range');
        if (msk < -1 || msk > 7) throw new RangeError('Mask value out of range');
        this.size = version * 4 + 17;
        // Initialize both grids to be size*size arrays of Boolean false
        var row = [];
        for(var i = 0; i < this.size; i++)row.push(false);
        for(var _i = 0; _i < this.size; _i++){
            this.modules.push(row.slice()); // Initially all light
            this.isFunction.push(row.slice());
        }
        // Compute ECC, draw modules
        this.drawFunctionPatterns();
        var allCodewords = this.addEccAndInterleave(dataCodewords);
        this.drawCodewords(allCodewords);
        // Do masking
        if (msk == -1) {
            // Automatically choose best mask
            var minPenalty = 1000000000;
            for(var _i2 = 0; _i2 < 8; _i2++){
                this.applyMask(_i2);
                this.drawFormatBits(_i2);
                var penalty = this.getPenaltyScore();
                if (penalty < minPenalty) {
                    msk = _i2;
                    minPenalty = penalty;
                }
                this.applyMask(_i2); // Undoes the mask due to XOR
            }
        }
        assert(0 <= msk && msk <= 7);
        this.mask = msk;
        this.applyMask(msk); // Apply the final choice of mask
        this.drawFormatBits(msk); // Overwrite old format bits
        this.isFunction = [];
    }
    /*-- Accessor methods --*/ // Returns the color of the module (pixel) at the given coordinates, which is false
    // for light or true for dark. The top left corner has the coordinates (x=0, y=0).
    // If the given coordinates are out of bounds, then false (light) is returned.
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$createClass$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(QrCode, [
        {
            key: "getModule",
            value: function getModule(x, y) {
                return 0 <= x && x < this.size && 0 <= y && y < this.size && this.modules[y][x];
            }
        },
        {
            key: "getModules",
            value: function getModules() {
                return this.modules;
            }
        },
        {
            key: "drawFunctionPatterns",
            value: function drawFunctionPatterns() {
                // Draw horizontal and vertical timing patterns
                for(var i = 0; i < this.size; i++){
                    this.setFunctionModule(6, i, i % 2 == 0);
                    this.setFunctionModule(i, 6, i % 2 == 0);
                }
                // Draw 3 finder patterns (all corners except bottom right; overwrites some timing modules)
                this.drawFinderPattern(3, 3);
                this.drawFinderPattern(this.size - 4, 3);
                this.drawFinderPattern(3, this.size - 4);
                // Draw numerous alignment patterns
                var alignPatPos = this.getAlignmentPatternPositions();
                var numAlign = alignPatPos.length;
                for(var _i3 = 0; _i3 < numAlign; _i3++){
                    for(var j = 0; j < numAlign; j++){
                        // Don't draw on the three finder corners
                        if (!(_i3 == 0 && j == 0 || _i3 == 0 && j == numAlign - 1 || _i3 == numAlign - 1 && j == 0)) this.drawAlignmentPattern(alignPatPos[_i3], alignPatPos[j]);
                    }
                }
                // Draw configuration data
                this.drawFormatBits(0); // Dummy mask value; overwritten later in the constructor
                this.drawVersion();
            }
        },
        {
            key: "drawFormatBits",
            value: function drawFormatBits(mask) {
                // Calculate error correction code and pack bits
                var data = this.errorCorrectionLevel.formatBits << 3 | mask; // errCorrLvl is unumber2, mask is unumber3
                var rem = data;
                for(var i = 0; i < 10; i++)rem = rem << 1 ^ (rem >>> 9) * 0x537;
                var bits = (data << 10 | rem) ^ 0x5412; // unumber15
                assert(bits >>> 15 == 0);
                // Draw first copy
                for(var _i4 = 0; _i4 <= 5; _i4++)this.setFunctionModule(8, _i4, getBit(bits, _i4));
                this.setFunctionModule(8, 7, getBit(bits, 6));
                this.setFunctionModule(8, 8, getBit(bits, 7));
                this.setFunctionModule(7, 8, getBit(bits, 8));
                for(var _i5 = 9; _i5 < 15; _i5++)this.setFunctionModule(14 - _i5, 8, getBit(bits, _i5));
                // Draw second copy
                for(var _i6 = 0; _i6 < 8; _i6++)this.setFunctionModule(this.size - 1 - _i6, 8, getBit(bits, _i6));
                for(var _i7 = 8; _i7 < 15; _i7++)this.setFunctionModule(8, this.size - 15 + _i7, getBit(bits, _i7));
                this.setFunctionModule(8, this.size - 8, true); // Always dark
            }
        },
        {
            key: "drawVersion",
            value: function drawVersion() {
                if (this.version < 7) return;
                // Calculate error correction code and pack bits
                var rem = this.version; // version is unumber6, in the range [7, 40]
                for(var i = 0; i < 12; i++)rem = rem << 1 ^ (rem >>> 11) * 0x1f25;
                var bits = this.version << 12 | rem; // unumber18
                assert(bits >>> 18 == 0);
                // Draw two copies
                for(var _i8 = 0; _i8 < 18; _i8++){
                    var color = getBit(bits, _i8);
                    var a = this.size - 11 + _i8 % 3;
                    var b = Math.floor(_i8 / 3);
                    this.setFunctionModule(a, b, color);
                    this.setFunctionModule(b, a, color);
                }
            }
        },
        {
            key: "drawFinderPattern",
            value: function drawFinderPattern(x, y) {
                for(var dy = -4; dy <= 4; dy++){
                    for(var dx = -4; dx <= 4; dx++){
                        var dist = Math.max(Math.abs(dx), Math.abs(dy)); // Chebyshev/infinity norm
                        var xx = x + dx;
                        var yy = y + dy;
                        if (0 <= xx && xx < this.size && 0 <= yy && yy < this.size) this.setFunctionModule(xx, yy, dist != 2 && dist != 4);
                    }
                }
            }
        },
        {
            key: "drawAlignmentPattern",
            value: function drawAlignmentPattern(x, y) {
                for(var dy = -2; dy <= 2; dy++){
                    for(var dx = -2; dx <= 2; dx++)this.setFunctionModule(x + dx, y + dy, Math.max(Math.abs(dx), Math.abs(dy)) != 1);
                }
            }
        },
        {
            key: "setFunctionModule",
            value: function setFunctionModule(x, y, isDark) {
                this.modules[y][x] = isDark;
                this.isFunction[y][x] = true;
            }
        },
        {
            key: "addEccAndInterleave",
            value: function addEccAndInterleave(data) {
                var ver = this.version;
                var ecl = this.errorCorrectionLevel;
                if (data.length != QrCode.getNumDataCodewords(ver, ecl)) throw new RangeError('Invalid argument');
                // Calculate parameter numbers
                var numBlocks = QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];
                var blockEccLen = QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver];
                var rawCodewords = Math.floor(QrCode.getNumRawDataModules(ver) / 8);
                var numShortBlocks = numBlocks - rawCodewords % numBlocks;
                var shortBlockLen = Math.floor(rawCodewords / numBlocks);
                // Split data numbero blocks and append ECC to each block
                var blocks = [];
                var rsDiv = QrCode.reedSolomonComputeDivisor(blockEccLen);
                for(var i = 0, k = 0; i < numBlocks; i++){
                    var dat = data.slice(k, k + shortBlockLen - blockEccLen + (i < numShortBlocks ? 0 : 1));
                    k += dat.length;
                    var ecc = QrCode.reedSolomonComputeRemainder(dat, rsDiv);
                    if (i < numShortBlocks) dat.push(0);
                    blocks.push(dat.concat(ecc));
                }
                // Interleave (not concatenate) the bytes from every block numbero a single sequence
                var result = [];
                var _loop = function _loop(_i9) {
                    blocks.forEach(function(block, j) {
                        // Skip the padding byte in short blocks
                        if (_i9 != shortBlockLen - blockEccLen || j >= numShortBlocks) result.push(block[_i9]);
                    });
                };
                for(var _i9 = 0; _i9 < blocks[0].length; _i9++){
                    _loop(_i9);
                }
                assert(result.length == rawCodewords);
                return result;
            }
        },
        {
            key: "drawCodewords",
            value: function drawCodewords(data) {
                if (data.length != Math.floor(QrCode.getNumRawDataModules(this.version) / 8)) throw new RangeError('Invalid argument');
                var i = 0; // Bit index numbero the data
                // Do the funny zigzag scan
                for(var right = this.size - 1; right >= 1; right -= 2){
                    // Index of right column in each column pair
                    if (right == 6) right = 5;
                    for(var vert = 0; vert < this.size; vert++){
                        // Vertical counter
                        for(var j = 0; j < 2; j++){
                            var x = right - j; // Actual x coordinate
                            var upward = (right + 1 & 2) == 0;
                            var y = upward ? this.size - 1 - vert : vert; // Actual y coordinate
                            if (!this.isFunction[y][x] && i < data.length * 8) {
                                this.modules[y][x] = getBit(data[i >>> 3], 7 - (i & 7));
                                i++;
                            }
                        // If this QR Code has any remainder bits (0 to 7), they were assigned as
                        // 0/false/light by the constructor and are left unchanged by this method
                        }
                    }
                }
                assert(i == data.length * 8);
            }
        },
        {
            key: "applyMask",
            value: function applyMask(mask) {
                if (mask < 0 || mask > 7) throw new RangeError('Mask value out of range');
                for(var y = 0; y < this.size; y++){
                    for(var x = 0; x < this.size; x++){
                        var invert = void 0;
                        switch(mask){
                            case 0:
                                invert = (x + y) % 2 == 0;
                                break;
                            case 1:
                                invert = y % 2 == 0;
                                break;
                            case 2:
                                invert = x % 3 == 0;
                                break;
                            case 3:
                                invert = (x + y) % 3 == 0;
                                break;
                            case 4:
                                invert = (Math.floor(x / 3) + Math.floor(y / 2)) % 2 == 0;
                                break;
                            case 5:
                                invert = x * y % 2 + x * y % 3 == 0;
                                break;
                            case 6:
                                invert = (x * y % 2 + x * y % 3) % 2 == 0;
                                break;
                            case 7:
                                invert = ((x + y) % 2 + x * y % 3) % 2 == 0;
                                break;
                            default:
                                throw new Error('Unreachable');
                        }
                        if (!this.isFunction[y][x] && invert) this.modules[y][x] = !this.modules[y][x];
                    }
                }
            }
        },
        {
            key: "getPenaltyScore",
            value: function getPenaltyScore() {
                var result = 0;
                // Adjacent modules in row having same color, and finder-like patterns
                for(var y = 0; y < this.size; y++){
                    var runColor = false;
                    var runX = 0;
                    var runHistory = [
                        0,
                        0,
                        0,
                        0,
                        0,
                        0,
                        0
                    ];
                    for(var x = 0; x < this.size; x++){
                        if (this.modules[y][x] == runColor) {
                            runX++;
                            if (runX == 5) result += QrCode.PENALTY_N1;
                            else if (runX > 5) result++;
                        } else {
                            this.finderPenaltyAddHistory(runX, runHistory);
                            if (!runColor) result += this.finderPenaltyCountPatterns(runHistory) * QrCode.PENALTY_N3;
                            runColor = this.modules[y][x];
                            runX = 1;
                        }
                    }
                    result += this.finderPenaltyTerminateAndCount(runColor, runX, runHistory) * QrCode.PENALTY_N3;
                }
                // Adjacent modules in column having same color, and finder-like patterns
                for(var _x = 0; _x < this.size; _x++){
                    var _runColor = false;
                    var runY = 0;
                    var _runHistory = [
                        0,
                        0,
                        0,
                        0,
                        0,
                        0,
                        0
                    ];
                    for(var _y = 0; _y < this.size; _y++){
                        if (this.modules[_y][_x] == _runColor) {
                            runY++;
                            if (runY == 5) result += QrCode.PENALTY_N1;
                            else if (runY > 5) result++;
                        } else {
                            this.finderPenaltyAddHistory(runY, _runHistory);
                            if (!_runColor) result += this.finderPenaltyCountPatterns(_runHistory) * QrCode.PENALTY_N3;
                            _runColor = this.modules[_y][_x];
                            runY = 1;
                        }
                    }
                    result += this.finderPenaltyTerminateAndCount(_runColor, runY, _runHistory) * QrCode.PENALTY_N3;
                }
                // 2*2 blocks of modules having same color
                for(var _y2 = 0; _y2 < this.size - 1; _y2++){
                    for(var _x2 = 0; _x2 < this.size - 1; _x2++){
                        var color = this.modules[_y2][_x2];
                        if (color == this.modules[_y2][_x2 + 1] && color == this.modules[_y2 + 1][_x2] && color == this.modules[_y2 + 1][_x2 + 1]) result += QrCode.PENALTY_N2;
                    }
                }
                // Balance of dark and light modules
                var dark = 0;
                var _iterator3 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$createForOfIteratorHelper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this.modules), _step3;
                try {
                    for(_iterator3.s(); !(_step3 = _iterator3.n()).done;){
                        var row = _step3.value;
                        dark = row.reduce(function(sum, color) {
                            return sum + (color ? 1 : 0);
                        }, dark);
                    }
                } catch (err) {
                    _iterator3.e(err);
                } finally{
                    _iterator3.f();
                }
                var total = this.size * this.size; // Note that size is odd, so dark/total != 1/2
                // Compute the smallest numbereger k >= 0 such that (45-5k)% <= dark/total <= (55+5k)%
                var k = Math.ceil(Math.abs(dark * 20 - total * 10) / total) - 1;
                assert(0 <= k && k <= 9);
                result += k * QrCode.PENALTY_N4;
                assert(0 <= result && result <= 2568888); // Non-tight upper bound based on default values of PENALTY_N1, ..., N4
                return result;
            }
        },
        {
            key: "getAlignmentPatternPositions",
            value: function getAlignmentPatternPositions() {
                if (this.version == 1) return [];
                else {
                    var numAlign = Math.floor(this.version / 7) + 2;
                    var step = this.version == 32 ? 26 : Math.ceil((this.version * 4 + 4) / (numAlign * 2 - 2)) * 2;
                    var result = [
                        6
                    ];
                    for(var pos = this.size - 7; result.length < numAlign; pos -= step)result.splice(1, 0, pos);
                    return result;
                }
            }
        },
        {
            key: "finderPenaltyCountPatterns",
            value: // Can only be called immediately after a light run is added, and
            // returns either 0, 1, or 2. A helper function for getPenaltyScore().
            function finderPenaltyCountPatterns(runHistory) {
                var n = runHistory[1];
                assert(n <= this.size * 3);
                var core = n > 0 && runHistory[2] == n && runHistory[3] == n * 3 && runHistory[4] == n && runHistory[5] == n;
                return (core && runHistory[0] >= n * 4 && runHistory[6] >= n ? 1 : 0) + (core && runHistory[6] >= n * 4 && runHistory[0] >= n ? 1 : 0);
            }
        },
        {
            key: "finderPenaltyTerminateAndCount",
            value: function finderPenaltyTerminateAndCount(currentRunColor, oriCurrentRunLength, runHistory) {
                var currentRunLength = oriCurrentRunLength;
                if (currentRunColor) {
                    // Terminate dark run
                    this.finderPenaltyAddHistory(currentRunLength, runHistory);
                    currentRunLength = 0;
                }
                currentRunLength += this.size; // Add light border to final run
                this.finderPenaltyAddHistory(currentRunLength, runHistory);
                return this.finderPenaltyCountPatterns(runHistory);
            }
        },
        {
            key: "finderPenaltyAddHistory",
            value: function finderPenaltyAddHistory(oriCurrentRunLength, runHistory) {
                var currentRunLength = oriCurrentRunLength;
                if (runHistory[0] == 0) currentRunLength += this.size; // Add light border to initial run
                runHistory.pop();
                runHistory.unshift(currentRunLength);
            }
        }
    ], [
        {
            key: "encodeText",
            value: /*-- Static factory functions (high level) --*/ // Returns a QR Code representing the given Unicode text string at the given error correction level.
            // As a conservative upper bound, this function is guaranteed to succeed for strings that have 738 or fewer
            // Unicode code ponumbers (not UTF-16 code units) if the low error correction level is used. The smallest possible
            // QR Code version is automatically chosen for the output. The ECC level of the result may be higher than the
            // ecl argument if it can be done without increasing the version.
            function encodeText(text, ecl) {
                var segs = QrSegment.makeSegments(text);
                return QrCode.encodeSegments(segs, ecl);
            }
        },
        {
            key: "encodeBinary",
            value: function encodeBinary(data, ecl) {
                var seg = QrSegment.makeBytes(data);
                return QrCode.encodeSegments([
                    seg
                ], ecl);
            }
        },
        {
            key: "encodeSegments",
            value: function encodeSegments(segs, oriEcl) {
                var minVersion = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 1;
                var maxVersion = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 40;
                var mask = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : -1;
                var boostEcl = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : true;
                if (!(QrCode.MIN_VERSION <= minVersion && minVersion <= maxVersion && maxVersion <= QrCode.MAX_VERSION) || mask < -1 || mask > 7) throw new RangeError('Invalid value');
                // Find the minimal version number to use
                var version;
                var dataUsedBits;
                for(version = minVersion;; version++){
                    var _dataCapacityBits = QrCode.getNumDataCodewords(version, oriEcl) * 8; // Number of data bits available
                    var usedBits = QrSegment.getTotalBits(segs, version);
                    if (usedBits <= _dataCapacityBits) {
                        dataUsedBits = usedBits;
                        break; // This version number is found to be suitable
                    }
                    if (version >= maxVersion) // All versions in the range could not fit the given data
                    throw new RangeError('Data too long');
                }
                var ecl = oriEcl;
                // Increase the error correction level while the data still fits in the current version number
                for(var _i10 = 0, _arr = [
                    Ecc.MEDIUM,
                    Ecc.QUARTILE,
                    Ecc.HIGH
                ]; _i10 < _arr.length; _i10++){
                    var newEcl = _arr[_i10];
                    // From low to high
                    if (boostEcl && dataUsedBits <= QrCode.getNumDataCodewords(version, newEcl) * 8) ecl = newEcl;
                }
                // Concatenate all segments to create the data bit string
                var bb = [];
                var _iterator4 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$createForOfIteratorHelper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(segs), _step4;
                try {
                    for(_iterator4.s(); !(_step4 = _iterator4.n()).done;){
                        var seg = _step4.value;
                        appendBits(seg.mode.modeBits, 4, bb);
                        appendBits(seg.numChars, seg.mode.numCharCountBits(version), bb);
                        var _iterator5 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$createForOfIteratorHelper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(seg.getData()), _step5;
                        try {
                            for(_iterator5.s(); !(_step5 = _iterator5.n()).done;){
                                var b = _step5.value;
                                bb.push(b);
                            }
                        } catch (err) {
                            _iterator5.e(err);
                        } finally{
                            _iterator5.f();
                        }
                    }
                } catch (err) {
                    _iterator4.e(err);
                } finally{
                    _iterator4.f();
                }
                assert(bb.length == dataUsedBits);
                // Add terminator and pad up to a byte if applicable
                var dataCapacityBits = QrCode.getNumDataCodewords(version, ecl) * 8;
                assert(bb.length <= dataCapacityBits);
                appendBits(0, Math.min(4, dataCapacityBits - bb.length), bb);
                appendBits(0, (8 - bb.length % 8) % 8, bb);
                assert(bb.length % 8 == 0);
                // Pad with alternating bytes until data capacity is reached
                for(var padByte = 0xec; bb.length < dataCapacityBits; padByte ^= 0xec ^ 0x11)appendBits(padByte, 8, bb);
                // Pack bits numbero bytes in big endian
                var dataCodewords = [];
                while(dataCodewords.length * 8 < bb.length)dataCodewords.push(0);
                bb.forEach(function(b, i) {
                    return dataCodewords[i >>> 3] |= b << 7 - (i & 7);
                });
                // Create the QR Code object
                return new QrCode(version, ecl, dataCodewords, mask);
            }
        },
        {
            key: "getNumRawDataModules",
            value: function getNumRawDataModules(ver) {
                if (ver < QrCode.MIN_VERSION || ver > QrCode.MAX_VERSION) throw new RangeError('Version number out of range');
                var result = (16 * ver + 128) * ver + 64;
                if (ver >= 2) {
                    var numAlign = Math.floor(ver / 7) + 2;
                    result -= (25 * numAlign - 10) * numAlign - 55;
                    if (ver >= 7) result -= 36;
                }
                assert(208 <= result && result <= 29648);
                return result;
            }
        },
        {
            key: "getNumDataCodewords",
            value: function getNumDataCodewords(ver, ecl) {
                return Math.floor(QrCode.getNumRawDataModules(ver) / 8) - QrCode.ECC_CODEWORDS_PER_BLOCK[ecl.ordinal][ver] * QrCode.NUM_ERROR_CORRECTION_BLOCKS[ecl.ordinal][ver];
            }
        },
        {
            key: "reedSolomonComputeDivisor",
            value: function reedSolomonComputeDivisor(degree) {
                if (degree < 1 || degree > 255) throw new RangeError('Degree out of range');
                // Polynomial coefficients are stored from highest to lowest power, excluding the leading term which is always 1.
                // For example the polynomial x^3 + 255x^2 + 8x + 93 is stored as the unumber8 array [255, 8, 93].
                var result = [];
                for(var i = 0; i < degree - 1; i++)result.push(0);
                result.push(1); // Start off with the monomial x^0
                // Compute the product polynomial (x - r^0) * (x - r^1) * (x - r^2) * ... * (x - r^{degree-1}),
                // and drop the highest monomial term which is always 1x^degree.
                // Note that r = 0x02, which is a generator element of this field GF(2^8/0x11D).
                var root = 1;
                for(var _i11 = 0; _i11 < degree; _i11++){
                    // Multiply the current product by (x - r^i)
                    for(var j = 0; j < result.length; j++){
                        result[j] = QrCode.reedSolomonMultiply(result[j], root);
                        if (j + 1 < result.length) result[j] ^= result[j + 1];
                    }
                    root = QrCode.reedSolomonMultiply(root, 0x02);
                }
                return result;
            }
        },
        {
            key: "reedSolomonComputeRemainder",
            value: function reedSolomonComputeRemainder(data, divisor) {
                var result = divisor.map(function() {
                    return 0;
                });
                var _iterator6 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$createForOfIteratorHelper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(data), _step6;
                try {
                    var _loop2 = function _loop2() {
                        var b = _step6.value;
                        // Polynomial division
                        var factor = b ^ result.shift();
                        result.push(0);
                        divisor.forEach(function(coef, i) {
                            return result[i] ^= QrCode.reedSolomonMultiply(coef, factor);
                        });
                    };
                    for(_iterator6.s(); !(_step6 = _iterator6.n()).done;){
                        _loop2();
                    }
                } catch (err) {
                    _iterator6.e(err);
                } finally{
                    _iterator6.f();
                }
                return result;
            }
        },
        {
            key: "reedSolomonMultiply",
            value: function reedSolomonMultiply(x, y) {
                if (x >>> 8 != 0 || y >>> 8 != 0) throw new RangeError('Byte out of range');
                // Russian peasant multiplication
                var z = 0;
                for(var i = 7; i >= 0; i--){
                    z = z << 1 ^ (z >>> 7) * 0x11d;
                    z ^= (y >>> i & 1) * x;
                }
                assert(z >>> 8 == 0);
                return z;
            }
        }
    ]);
    return QrCode;
}();
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(QrCode, "MIN_VERSION", 1);
// The maximum version number supported in the QR Code Model 2 standard.
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(QrCode, "MAX_VERSION", 40);
// For use in getPenaltyScore(), when evaluating which mask is best.
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(QrCode, "PENALTY_N1", 3);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(QrCode, "PENALTY_N2", 3);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(QrCode, "PENALTY_N3", 40);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(QrCode, "PENALTY_N4", 10);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(QrCode, "ECC_CODEWORDS_PER_BLOCK", [
    // Version: (note that index 0 is for padding, and is set to an illegal value)
    //0,  1,  2,  3,  4,  5,  6,  7,  8,  9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level
    [
        -1,
        7,
        10,
        15,
        20,
        26,
        18,
        20,
        24,
        30,
        18,
        20,
        24,
        26,
        30,
        22,
        24,
        28,
        30,
        28,
        28,
        28,
        28,
        30,
        30,
        26,
        28,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30
    ],
    // Low
    [
        -1,
        10,
        16,
        26,
        18,
        24,
        16,
        18,
        22,
        22,
        26,
        30,
        22,
        22,
        24,
        24,
        28,
        28,
        26,
        26,
        26,
        26,
        28,
        28,
        28,
        28,
        28,
        28,
        28,
        28,
        28,
        28,
        28,
        28,
        28,
        28,
        28,
        28,
        28,
        28,
        28
    ],
    // Medium
    [
        -1,
        13,
        22,
        18,
        26,
        18,
        24,
        18,
        22,
        20,
        24,
        28,
        26,
        24,
        20,
        30,
        24,
        28,
        28,
        26,
        30,
        28,
        30,
        30,
        30,
        30,
        28,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30
    ],
    // Quartile
    [
        -1,
        17,
        28,
        22,
        16,
        22,
        28,
        26,
        26,
        24,
        28,
        24,
        28,
        22,
        24,
        24,
        30,
        28,
        28,
        26,
        28,
        30,
        24,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30,
        30
    ] // High
]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$defineProperty$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(QrCode, "NUM_ERROR_CORRECTION_BLOCKS", [
    // Version: (note that index 0 is for padding, and is set to an illegal value)
    //0, 1, 2, 3, 4, 5, 6, 7, 8, 9,10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40    Error correction level
    [
        -1,
        1,
        1,
        1,
        1,
        1,
        2,
        2,
        2,
        2,
        4,
        4,
        4,
        4,
        4,
        6,
        6,
        6,
        6,
        7,
        8,
        8,
        9,
        9,
        10,
        12,
        12,
        12,
        13,
        14,
        15,
        16,
        17,
        18,
        19,
        19,
        20,
        21,
        22,
        24,
        25
    ],
    // Low
    [
        -1,
        1,
        1,
        1,
        2,
        2,
        4,
        4,
        4,
        5,
        5,
        5,
        8,
        9,
        9,
        10,
        10,
        11,
        13,
        14,
        16,
        17,
        17,
        18,
        20,
        21,
        23,
        25,
        26,
        28,
        29,
        31,
        33,
        35,
        37,
        38,
        40,
        43,
        45,
        47,
        49
    ],
    // Medium
    [
        -1,
        1,
        1,
        2,
        2,
        4,
        4,
        6,
        6,
        8,
        8,
        8,
        10,
        12,
        16,
        12,
        17,
        16,
        18,
        21,
        20,
        23,
        23,
        25,
        27,
        29,
        34,
        34,
        35,
        38,
        40,
        43,
        45,
        48,
        51,
        53,
        56,
        59,
        62,
        65,
        68
    ],
    // Quartile
    [
        -1,
        1,
        1,
        2,
        4,
        4,
        4,
        5,
        6,
        8,
        8,
        11,
        11,
        16,
        16,
        18,
        16,
        19,
        21,
        25,
        25,
        25,
        34,
        30,
        32,
        35,
        37,
        40,
        42,
        45,
        48,
        51,
        54,
        57,
        60,
        63,
        66,
        70,
        74,
        77,
        81
    ] // High
]);
}}),
"[project]/node_modules/@rc-component/qrcode/es/utils.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Part logic is from `qrcode.react`. (ISC License)
// https://github.com/zpao/qrcode.react
// ==========================================================
__turbopack_context__.s({
    "DEFAULT_BACKGROUND_COLOR": (()=>DEFAULT_BACKGROUND_COLOR),
    "DEFAULT_FRONT_COLOR": (()=>DEFAULT_FRONT_COLOR),
    "DEFAULT_IMG_SCALE": (()=>DEFAULT_IMG_SCALE),
    "DEFAULT_LEVEL": (()=>DEFAULT_LEVEL),
    "DEFAULT_MARGIN_SIZE": (()=>DEFAULT_MARGIN_SIZE),
    "DEFAULT_MINVERSION": (()=>DEFAULT_MINVERSION),
    "DEFAULT_NEED_MARGIN": (()=>DEFAULT_NEED_MARGIN),
    "DEFAULT_SIZE": (()=>DEFAULT_SIZE),
    "ERROR_LEVEL_MAP": (()=>ERROR_LEVEL_MAP),
    "SPEC_MARGIN_SIZE": (()=>SPEC_MARGIN_SIZE),
    "excavateModules": (()=>excavateModules),
    "generatePath": (()=>generatePath),
    "getImageSettings": (()=>getImageSettings),
    "getMarginSize": (()=>getMarginSize),
    "isSupportPath2d": (()=>isSupportPath2d)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$libs$2f$qrcodegen$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/qrcode/es/libs/qrcodegen.js [app-ssr] (ecmascript)");
;
var ERROR_LEVEL_MAP = {
    L: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$libs$2f$qrcodegen$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Ecc"].LOW,
    M: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$libs$2f$qrcodegen$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Ecc"].MEDIUM,
    Q: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$libs$2f$qrcodegen$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Ecc"].QUARTILE,
    H: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$libs$2f$qrcodegen$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Ecc"].HIGH
};
var DEFAULT_SIZE = 128;
var DEFAULT_LEVEL = 'L';
var DEFAULT_BACKGROUND_COLOR = '#FFFFFF';
var DEFAULT_FRONT_COLOR = '#000000';
var DEFAULT_NEED_MARGIN = false;
var DEFAULT_MINVERSION = 1;
var SPEC_MARGIN_SIZE = 4;
var DEFAULT_MARGIN_SIZE = 0;
var DEFAULT_IMG_SCALE = 0.1;
function generatePath(modules) {
    var margin = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;
    var ops = [];
    modules.forEach(function(row, y) {
        var start = null;
        row.forEach(function(cell, x) {
            if (!cell && start !== null) {
                ops.push("M".concat(start + margin, " ").concat(y + margin, "h").concat(x - start, "v1H").concat(start + margin, "z"));
                start = null;
                return;
            }
            if (x === row.length - 1) {
                if (!cell) {
                    return;
                }
                if (start === null) {
                    ops.push("M".concat(x + margin, ",").concat(y + margin, " h1v1H").concat(x + margin, "z"));
                } else {
                    ops.push("M".concat(start + margin, ",").concat(y + margin, " h").concat(x + 1 - start, "v1H").concat(start + margin, "z"));
                }
                return;
            }
            if (cell && start === null) {
                start = x;
            }
        });
    });
    return ops.join('');
}
function excavateModules(modules, excavation) {
    return modules.slice().map(function(row, y) {
        if (y < excavation.y || y >= excavation.y + excavation.h) {
            return row;
        }
        return row.map(function(cell, x) {
            if (x < excavation.x || x >= excavation.x + excavation.w) {
                return cell;
            }
            return false;
        });
    });
}
function getImageSettings(cells, size, margin, imageSettings) {
    if (imageSettings == null) {
        return null;
    }
    var numCells = cells.length + margin * 2;
    var defaultSize = Math.floor(size * DEFAULT_IMG_SCALE);
    var scale = numCells / size;
    var w = (imageSettings.width || defaultSize) * scale;
    var h = (imageSettings.height || defaultSize) * scale;
    var x = imageSettings.x == null ? cells.length / 2 - w / 2 : imageSettings.x * scale;
    var y = imageSettings.y == null ? cells.length / 2 - h / 2 : imageSettings.y * scale;
    var opacity = imageSettings.opacity == null ? 1 : imageSettings.opacity;
    var excavation = null;
    if (imageSettings.excavate) {
        var floorX = Math.floor(x);
        var floorY = Math.floor(y);
        var ceilW = Math.ceil(w + x - floorX);
        var ceilH = Math.ceil(h + y - floorY);
        excavation = {
            x: floorX,
            y: floorY,
            w: ceilW,
            h: ceilH
        };
    }
    var crossOrigin = imageSettings.crossOrigin;
    return {
        x: x,
        y: y,
        h: h,
        w: w,
        excavation: excavation,
        opacity: opacity,
        crossOrigin: crossOrigin
    };
}
function getMarginSize(needMargin, marginSize) {
    if (marginSize != null) {
        return Math.floor(marginSize);
    }
    return needMargin ? SPEC_MARGIN_SIZE : DEFAULT_MARGIN_SIZE;
}
var isSupportPath2d = function() {
    try {
        new Path2D().addPath(new Path2D());
    } catch (e) {
        return false;
    }
    return true;
}();
}}),
"[project]/node_modules/@rc-component/qrcode/es/hooks/useQRCode.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useQRCode": (()=>useQRCode)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$libs$2f$qrcodegen$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/qrcode/es/libs/qrcodegen.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/qrcode/es/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
;
;
function useQRCode(_ref) {
    var value = _ref.value, level = _ref.level, minVersion = _ref.minVersion, includeMargin = _ref.includeMargin, marginSize = _ref.marginSize, imageSettings = _ref.imageSettings, size = _ref.size;
    var qrcode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(function() {
        var segments = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$libs$2f$qrcodegen$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QrSegment"].makeSegments(value);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$libs$2f$qrcodegen$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["QrCode"].encodeSegments(segments, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ERROR_LEVEL_MAP"][level], minVersion);
    }, [
        value,
        level,
        minVersion
    ]);
    var _useMemo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(function() {
        var cs = qrcode.getModules();
        var mg = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getMarginSize"])(includeMargin, marginSize);
        var ncs = cs.length + mg * 2;
        var cis = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getImageSettings"])(cs, size, mg, imageSettings);
        return {
            cells: cs,
            margin: mg,
            numCells: ncs,
            calculatedImageSettings: cis
        };
    }, [
        qrcode,
        size,
        imageSettings,
        includeMargin,
        marginSize
    ]), cells = _useMemo.cells, margin = _useMemo.margin, numCells = _useMemo.numCells, calculatedImageSettings = _useMemo.calculatedImageSettings;
    return {
        qrcode: qrcode,
        margin: margin,
        cells: cells,
        numCells: numCells,
        calculatedImageSettings: calculatedImageSettings
    };
}
}}),
"[project]/node_modules/@rc-component/qrcode/es/QRCodeCanvas.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "QRCodeCanvas": (()=>QRCodeCanvas)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/slicedToArray.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$hooks$2f$useQRCode$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/qrcode/es/hooks/useQRCode.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/qrcode/es/utils.js [app-ssr] (ecmascript)");
;
;
;
;
var _excluded = [
    "value",
    "size",
    "level",
    "bgColor",
    "fgColor",
    "includeMargin",
    "minVersion",
    "marginSize",
    "style",
    "imageSettings"
];
;
;
;
var QRCodeCanvas = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].forwardRef(function QRCodeCanvas(props, forwardedRef) {
    var value = props.value, _props$size = props.size, size = _props$size === void 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_SIZE"] : _props$size, _props$level = props.level, level = _props$level === void 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_LEVEL"] : _props$level, _props$bgColor = props.bgColor, bgColor = _props$bgColor === void 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_BACKGROUND_COLOR"] : _props$bgColor, _props$fgColor = props.fgColor, fgColor = _props$fgColor === void 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_FRONT_COLOR"] : _props$fgColor, _props$includeMargin = props.includeMargin, includeMargin = _props$includeMargin === void 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_NEED_MARGIN"] : _props$includeMargin, _props$minVersion = props.minVersion, minVersion = _props$minVersion === void 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_MINVERSION"] : _props$minVersion, marginSize = props.marginSize, style = props.style, imageSettings = props.imageSettings, otherProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(props, _excluded);
    var imgSrc = imageSettings === null || imageSettings === void 0 ? void 0 : imageSettings.src;
    var _canvas = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    var _image = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    var setCanvasRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(function(node) {
        _canvas.current = node;
        if (typeof forwardedRef === 'function') {
            forwardedRef(node);
        } else if (forwardedRef) {
            forwardedRef.current = node;
        }
    }, [
        forwardedRef
    ]);
    var _useState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false), _useState2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_useState, 2), setIsImageLoaded = _useState2[1];
    var _useQRCode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$hooks$2f$useQRCode$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQRCode"])({
        value: value,
        level: level,
        minVersion: minVersion,
        includeMargin: includeMargin,
        marginSize: marginSize,
        imageSettings: imageSettings,
        size: size
    }), margin = _useQRCode.margin, cells = _useQRCode.cells, numCells = _useQRCode.numCells, calculatedImageSettings = _useQRCode.calculatedImageSettings;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(function() {
        if (_canvas.current != null) {
            var canvas = _canvas.current;
            var ctx = canvas.getContext('2d');
            if (!ctx) {
                return;
            }
            var cellsToDraw = cells;
            var image = _image.current;
            var haveImageToRender = calculatedImageSettings != null && image !== null && image.complete && image.naturalHeight !== 0 && image.naturalWidth !== 0;
            if (haveImageToRender) {
                if (calculatedImageSettings.excavation != null) {
                    cellsToDraw = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["excavateModules"])(cells, calculatedImageSettings.excavation);
                }
            }
            var pixelRatio = window.devicePixelRatio || 1;
            canvas.height = canvas.width = size * pixelRatio;
            var scale = size / numCells * pixelRatio;
            ctx.scale(scale, scale);
            ctx.fillStyle = bgColor;
            ctx.fillRect(0, 0, numCells, numCells);
            ctx.fillStyle = fgColor;
            if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isSupportPath2d"]) {
                ctx.fill(new Path2D((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["generatePath"])(cellsToDraw, margin)));
            } else {
                cells.forEach(function(row, rdx) {
                    row.forEach(function(cell, cdx) {
                        if (cell) {
                            ctx.fillRect(cdx + margin, rdx + margin, 1, 1);
                        }
                    });
                });
            }
            if (calculatedImageSettings) {
                ctx.globalAlpha = calculatedImageSettings.opacity;
            }
            if (haveImageToRender) {
                ctx.drawImage(image, calculatedImageSettings.x + margin, calculatedImageSettings.y + margin, calculatedImageSettings.w, calculatedImageSettings.h);
            }
        }
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(function() {
        setIsImageLoaded(false);
    }, [
        imgSrc
    ]);
    var canvasStyle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({
        height: size,
        width: size
    }, style);
    var img = null;
    if (imgSrc != null) {
        img = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("img", {
            src: imgSrc,
            key: imgSrc,
            style: {
                display: 'none'
            },
            onLoad: function onLoad() {
                setIsImageLoaded(true);
            },
            ref: _image,
            crossOrigin: calculatedImageSettings === null || calculatedImageSettings === void 0 ? void 0 : calculatedImageSettings.crossOrigin
        });
    }
    return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Fragment, null, /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("canvas", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({
        style: canvasStyle,
        height: size,
        width: size,
        ref: setCanvasRef,
        role: "img"
    }, otherProps)), img);
});
QRCodeCanvas.displayName = 'QRCodeCanvas';
;
}}),
"[project]/node_modules/@rc-component/qrcode/es/QRCodeSVG.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "QRCodeSVG": (()=>QRCodeSVG)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/qrcode/es/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$hooks$2f$useQRCode$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/qrcode/es/hooks/useQRCode.js [app-ssr] (ecmascript)");
;
;
var _excluded = [
    "value",
    "size",
    "level",
    "bgColor",
    "fgColor",
    "includeMargin",
    "minVersion",
    "title",
    "marginSize",
    "imageSettings"
];
;
;
;
var QRCodeSVG = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].forwardRef(function QRCodeSVG(props, forwardedRef) {
    var value = props.value, _props$size = props.size, size = _props$size === void 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_SIZE"] : _props$size, _props$level = props.level, level = _props$level === void 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_LEVEL"] : _props$level, _props$bgColor = props.bgColor, bgColor = _props$bgColor === void 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_BACKGROUND_COLOR"] : _props$bgColor, _props$fgColor = props.fgColor, fgColor = _props$fgColor === void 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_FRONT_COLOR"] : _props$fgColor, _props$includeMargin = props.includeMargin, includeMargin = _props$includeMargin === void 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_NEED_MARGIN"] : _props$includeMargin, _props$minVersion = props.minVersion, minVersion = _props$minVersion === void 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULT_MINVERSION"] : _props$minVersion, title = props.title, marginSize = props.marginSize, imageSettings = props.imageSettings, otherProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(props, _excluded);
    var _useQRCode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$hooks$2f$useQRCode$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useQRCode"])({
        value: value,
        level: level,
        minVersion: minVersion,
        includeMargin: includeMargin,
        marginSize: marginSize,
        imageSettings: imageSettings,
        size: size
    }), margin = _useQRCode.margin, cells = _useQRCode.cells, numCells = _useQRCode.numCells, calculatedImageSettings = _useQRCode.calculatedImageSettings;
    var cellsToDraw = cells;
    var image = null;
    if (imageSettings != null && calculatedImageSettings != null) {
        if (calculatedImageSettings.excavation != null) {
            cellsToDraw = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["excavateModules"])(cells, calculatedImageSettings.excavation);
        }
        image = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("image", {
            href: imageSettings.src,
            height: calculatedImageSettings.h,
            width: calculatedImageSettings.w,
            x: calculatedImageSettings.x + margin,
            y: calculatedImageSettings.y + margin,
            preserveAspectRatio: "none",
            opacity: calculatedImageSettings.opacity,
            crossOrigin: calculatedImageSettings.crossOrigin
        });
    }
    var fgPath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["generatePath"])(cellsToDraw, margin);
    return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("svg", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({
        height: size,
        width: size,
        viewBox: "0 0 ".concat(numCells, " ").concat(numCells),
        ref: forwardedRef,
        role: "img"
    }, otherProps), !!title && /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("title", null, title), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        fill: bgColor,
        d: "M0,0 h".concat(numCells, "v").concat(numCells, "H0z"),
        shapeRendering: "crispEdges"
    }), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("path", {
        fill: fgColor,
        d: fgPath,
        shapeRendering: "crispEdges"
    }), image);
});
QRCodeSVG.displayName = 'QRCodeSVG';
;
}}),
"[project]/node_modules/@rc-component/qrcode/es/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/qrcode/es/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$QRCodeCanvas$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/qrcode/es/QRCodeCanvas.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$QRCodeSVG$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/qrcode/es/QRCodeSVG.js [app-ssr] (ecmascript)");
;
;
;
}}),
"[project]/node_modules/@rc-component/qrcode/es/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/qrcode/es/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$QRCodeCanvas$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/qrcode/es/QRCodeCanvas.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$QRCodeSVG$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/qrcode/es/QRCodeSVG.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$qrcode$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/qrcode/es/index.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@rc-component/context/es/context.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createContext": (()=>createContext),
    "useContext": (()=>useContext)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/slicedToArray.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useEvent.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useLayoutEffect.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$isEqual$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/isEqual.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
function createContext(defaultValue) {
    var Context = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
    var Provider = function Provider(_ref) {
        var value = _ref.value, children = _ref.children;
        var valueRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(value);
        valueRef.current = value;
        var _React$useState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(function() {
            return {
                getValue: function getValue() {
                    return valueRef.current;
                },
                listeners: new Set()
            };
        }), _React$useState2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_React$useState, 1), context = _React$useState2[0];
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function() {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["unstable_batchedUpdates"])(function() {
                context.listeners.forEach(function(listener) {
                    listener(value);
                });
            });
        }, [
            value
        ]);
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(Context.Provider, {
            value: context
        }, children);
    };
    return {
        Context: Context,
        Provider: Provider,
        defaultValue: defaultValue
    };
}
function useContext(holder, selector) {
    var eventSelector = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(typeof selector === 'function' ? selector : function(ctx) {
        if (selector === undefined) {
            return ctx;
        }
        if (!Array.isArray(selector)) {
            return ctx[selector];
        }
        var obj = {};
        selector.forEach(function(key) {
            obj[key] = ctx[key];
        });
        return obj;
    });
    var context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(holder === null || holder === void 0 ? void 0 : holder.Context);
    var _ref2 = context || {}, listeners = _ref2.listeners, getValue = _ref2.getValue;
    var valueRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])();
    valueRef.current = eventSelector(context ? getValue() : holder === null || holder === void 0 ? void 0 : holder.defaultValue);
    var _React$useState3 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({}), _React$useState4 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_React$useState3, 2), forceUpdate = _React$useState4[1];
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function() {
        if (!context) {
            return;
        }
        function trigger(nextValue) {
            var nextSelectorValue = eventSelector(nextValue);
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$isEqual$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(valueRef.current, nextSelectorValue, true)) {
                forceUpdate({});
            }
        }
        listeners.add(trigger);
        return function() {
            listeners.delete(trigger);
        };
    }, [
        context
    ]);
    return valueRef.current;
}
}}),
"[project]/node_modules/@rc-component/context/es/Immutable.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>createImmutable)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/ref.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
;
;
function createImmutable() {
    var ImmutableContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(null);
    /**
   * Get render update mark by `makeImmutable` root.
   * Do not deps on the return value as render times
   * but only use for `useMemo` or `useCallback` deps.
   */ function useImmutableMark() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(ImmutableContext);
    }
    /**
  * Wrapped Component will be marked as Immutable.
  * When Component parent trigger render,
  * it will notice children component (use with `responseImmutable`) node that parent has updated.
  * @param Component Passed Component
  * @param triggerRender Customize trigger `responseImmutable` children re-render logic. Default will always trigger re-render when this component re-render.
  */ function makeImmutable(Component, shouldTriggerRender) {
        var refAble = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supportRef"])(Component);
        var ImmutableComponent = function ImmutableComponent(props, ref) {
            var refProps = refAble ? {
                ref: ref
            } : {};
            var renderTimesRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(0);
            var prevProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(props);
            // If parent has the context, we do not wrap it
            var mark = useImmutableMark();
            if (mark !== null) {
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(Component, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({}, props, refProps));
            }
            if (// Always trigger re-render if not provide `notTriggerRender`
            !shouldTriggerRender || shouldTriggerRender(prevProps.current, props)) {
                renderTimesRef.current += 1;
            }
            prevProps.current = props;
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(ImmutableContext.Provider, {
                value: renderTimesRef.current
            }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(Component, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({}, props, refProps)));
        };
        if ("TURBOPACK compile-time truthy", 1) {
            ImmutableComponent.displayName = "ImmutableRoot(".concat(Component.displayName || Component.name, ")");
        }
        return refAble ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(ImmutableComponent) : ImmutableComponent;
    }
    /**
   * Wrapped Component with `React.memo`.
   * But will rerender when parent with `makeImmutable` rerender.
   */ function responseImmutable(Component, propsAreEqual) {
        var refAble = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supportRef"])(Component);
        var ImmutableComponent = function ImmutableComponent(props, ref) {
            var refProps = refAble ? {
                ref: ref
            } : {};
            useImmutableMark();
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(Component, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({}, props, refProps));
        };
        if ("TURBOPACK compile-time truthy", 1) {
            ImmutableComponent.displayName = "ImmutableResponse(".concat(Component.displayName || Component.name, ")");
        }
        return refAble ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])(/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(ImmutableComponent), propsAreEqual) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["memo"])(ImmutableComponent, propsAreEqual);
    }
    return {
        makeImmutable: makeImmutable,
        responseImmutable: responseImmutable,
        useImmutableMark: useImmutableMark
    };
}
}}),
"[project]/node_modules/@rc-component/context/es/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "makeImmutable": (()=>makeImmutable),
    "responseImmutable": (()=>responseImmutable),
    "useImmutableMark": (()=>useImmutableMark)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/context.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$Immutable$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/Immutable.js [app-ssr] (ecmascript)");
;
;
// For legacy usage, we export it directly
var _createImmutable = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$Immutable$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(), makeImmutable = _createImmutable.makeImmutable, responseImmutable = _createImmutable.responseImmutable, useImmutableMark = _createImmutable.useImmutableMark;
;
}}),
"[project]/node_modules/@rc-component/context/es/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$context$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/context.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$Immutable$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/Immutable.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/index.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@rc-component/context/es/Immutable.js [app-ssr] (ecmascript) <export default as createImmutable>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "createImmutable": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$Immutable$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$context$2f$es$2f$Immutable$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/context/es/Immutable.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/@rc-component/tour/es/hooks/useClosable.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useClosable": (()=>useClosable)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/typeof.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
;
;
function isConfigObj(closable) {
    return closable !== null && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(closable) === 'object';
}
/**
 * Convert `closable` to ClosableConfig.
 * When `preset` is true, will auto fill ClosableConfig with default value.
 */ function getClosableConfig(closable, closeIcon, preset) {
    if (closable === false || closeIcon === false && (!isConfigObj(closable) || !closable.closeIcon)) {
        return null;
    }
    var mergedCloseIcon = typeof closeIcon !== 'boolean' ? closeIcon : undefined;
    if (isConfigObj(closable)) {
        var _closable$closeIcon;
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({}, closable), {}, {
            closeIcon: (_closable$closeIcon = closable.closeIcon) !== null && _closable$closeIcon !== void 0 ? _closable$closeIcon : mergedCloseIcon
        });
    }
    // When StepClosable no need auto fill, but RootClosable need this.
    return preset || closable || closeIcon ? {
        closeIcon: mergedCloseIcon
    } : 'empty';
}
function useClosable(stepClosable, stepCloseIcon, closable, closeIcon) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(function() {
        var stepClosableConfig = getClosableConfig(stepClosable, stepCloseIcon, false);
        var rootClosableConfig = getClosableConfig(closable, closeIcon, true);
        if (stepClosableConfig !== 'empty') {
            return stepClosableConfig;
        }
        return rootClosableConfig;
    }, [
        closable,
        closeIcon,
        stepClosable,
        stepCloseIcon
    ]);
}
}}),
"[project]/node_modules/@rc-component/tour/es/util.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getPlacement": (()=>getPlacement),
    "isInViewPort": (()=>isInViewPort)
});
function isInViewPort(element) {
    var viewWidth = window.innerWidth || document.documentElement.clientWidth;
    var viewHeight = window.innerHeight || document.documentElement.clientHeight;
    var _element$getBoundingC = element.getBoundingClientRect(), top = _element$getBoundingC.top, right = _element$getBoundingC.right, bottom = _element$getBoundingC.bottom, left = _element$getBoundingC.left;
    return top >= 0 && left >= 0 && right <= viewWidth && bottom <= viewHeight;
}
function getPlacement(targetElement, placement, stepPlacement) {
    var _ref;
    return (_ref = stepPlacement !== null && stepPlacement !== void 0 ? stepPlacement : placement) !== null && _ref !== void 0 ? _ref : targetElement === null ? 'center' : 'bottom';
}
}}),
"[project]/node_modules/@rc-component/tour/es/hooks/useTarget.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useTarget)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/slicedToArray.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useEvent.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useLayoutEffect.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$tour$2f$es$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/tour/es/util.js [app-ssr] (ecmascript)");
;
;
;
;
;
function isValidNumber(val) {
    return typeof val === 'number' && !Number.isNaN(val);
}
function useTarget(target, open, gap, scrollIntoViewOptions) {
    // ========================= Target =========================
    // We trade `undefined` as not get target by function yet.
    // `null` as empty target.
    var _useState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(undefined), _useState2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_useState, 2), targetElement = _useState2[0], setTargetElement = _useState2[1];
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function() {
        var nextElement = typeof target === 'function' ? target() : target;
        setTargetElement(nextElement || null);
    });
    // ========================= Align ==========================
    var _useState3 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null), _useState4 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_useState3, 2), posInfo = _useState4[0], setPosInfo = _useState4[1];
    var updatePos = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function() {
        if (targetElement) {
            // Exist target element. We should scroll and get target position
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$tour$2f$es$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isInViewPort"])(targetElement) && open) {
                targetElement.scrollIntoView(scrollIntoViewOptions);
            }
            var _targetElement$getBou = targetElement.getBoundingClientRect(), left = _targetElement$getBou.left, top = _targetElement$getBou.top, width = _targetElement$getBou.width, height = _targetElement$getBou.height;
            var nextPosInfo = {
                left: left,
                top: top,
                width: width,
                height: height,
                radius: 0
            };
            setPosInfo(function(origin) {
                if (JSON.stringify(origin) !== JSON.stringify(nextPosInfo)) {
                    return nextPosInfo;
                }
                return origin;
            });
        } else {
            // Not exist target which means we just show in center
            setPosInfo(null);
        }
    });
    var getGapOffset = function getGapOffset(index) {
        var _ref;
        return (_ref = Array.isArray(gap === null || gap === void 0 ? void 0 : gap.offset) ? gap === null || gap === void 0 ? void 0 : gap.offset[index] : gap === null || gap === void 0 ? void 0 : gap.offset) !== null && _ref !== void 0 ? _ref : 6;
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function() {
        updatePos();
        // update when window resize
        window.addEventListener('resize', updatePos);
        return function() {
            window.removeEventListener('resize', updatePos);
        };
    }, [
        targetElement,
        open,
        updatePos
    ]);
    // ======================== PosInfo =========================
    var mergedPosInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(function() {
        if (!posInfo) {
            return posInfo;
        }
        var gapOffsetX = getGapOffset(0);
        var gapOffsetY = getGapOffset(1);
        var gapRadius = isValidNumber(gap === null || gap === void 0 ? void 0 : gap.radius) ? gap === null || gap === void 0 ? void 0 : gap.radius : 2;
        return {
            left: posInfo.left - gapOffsetX,
            top: posInfo.top - gapOffsetY,
            width: posInfo.width + gapOffsetX * 2,
            height: posInfo.height + gapOffsetY * 2,
            radius: gapRadius
        };
    }, [
        posInfo,
        gap
    ]);
    return [
        mergedPosInfo,
        targetElement
    ];
}
}}),
"[project]/node_modules/@rc-component/tour/es/Mask.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/typeof.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$portal$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/portal/es/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$portal$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/portal/es/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useId$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useId.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
var COVER_PROPS = {
    fill: 'transparent',
    pointerEvents: 'auto'
};
var Mask = function Mask(props) {
    var prefixCls = props.prefixCls, rootClassName = props.rootClassName, pos = props.pos, showMask = props.showMask, _props$style = props.style, style = _props$style === void 0 ? {} : _props$style, _props$fill = props.fill, fill = _props$fill === void 0 ? "rgba(0,0,0,0.5)" : _props$fill, open = props.open, animated = props.animated, zIndex = props.zIndex, disabledInteraction = props.disabledInteraction;
    var id = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useId$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])();
    var maskId = "".concat(prefixCls, "-mask-").concat(id);
    var mergedAnimated = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(animated) === 'object' ? animated === null || animated === void 0 ? void 0 : animated.placeholder : animated;
    var isSafari = typeof navigator !== 'undefined' && /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
    var maskRectSize = isSafari ? {
        width: '100%',
        height: '100%'
    } : {
        width: '100vw',
        height: '100vh'
    };
    return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$portal$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"], {
        open: open,
        autoLock: true
    }, /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("".concat(prefixCls, "-mask"), rootClassName),
        style: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({
            position: 'fixed',
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
            zIndex: zIndex,
            pointerEvents: pos && !disabledInteraction ? 'none' : 'auto'
        }, style)
    }, showMask ? /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("svg", {
        style: {
            width: '100%',
            height: '100%'
        }
    }, /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("defs", null, /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("mask", {
        id: maskId
    }, /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("rect", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({
        x: "0",
        y: "0"
    }, maskRectSize, {
        fill: "white"
    })), pos && /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("rect", {
        x: pos.left,
        y: pos.top,
        rx: pos.radius,
        width: pos.width,
        height: pos.height,
        fill: "black",
        className: mergedAnimated ? "".concat(prefixCls, "-placeholder-animated") : ''
    }))), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("rect", {
        x: "0",
        y: "0",
        width: "100%",
        height: "100%",
        fill: fill,
        mask: "url(#".concat(maskId, ")")
    }), pos && /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Fragment, null, /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("rect", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({}, COVER_PROPS, {
        x: "0",
        y: "0",
        width: "100%",
        height: pos.top
    })), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("rect", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({}, COVER_PROPS, {
        x: "0",
        y: "0",
        width: pos.left,
        height: "100%"
    })), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("rect", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({}, COVER_PROPS, {
        x: "0",
        y: pos.top + pos.height,
        width: "100%",
        height: "calc(100vh - ".concat(pos.top + pos.height, "px)")
    })), /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("rect", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({}, COVER_PROPS, {
        x: pos.left + pos.width,
        y: "0",
        width: "calc(100vw - ".concat(pos.left + pos.width, "px)"),
        height: "100%"
    })))) : null));
};
const __TURBOPACK__default__export__ = Mask;
}}),
"[project]/node_modules/@rc-component/tour/es/placements.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getPlacements": (()=>getPlacements),
    "placements": (()=>placements)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-ssr] (ecmascript)");
;
var autoAdjustOverflow = {
    adjustX: 1,
    adjustY: 1
};
var targetOffset = [
    0,
    0
];
var basePlacements = {
    left: {
        points: [
            'cr',
            'cl'
        ],
        offset: [
            -8,
            0
        ]
    },
    right: {
        points: [
            'cl',
            'cr'
        ],
        offset: [
            8,
            0
        ]
    },
    top: {
        points: [
            'bc',
            'tc'
        ],
        offset: [
            0,
            -8
        ]
    },
    bottom: {
        points: [
            'tc',
            'bc'
        ],
        offset: [
            0,
            8
        ]
    },
    topLeft: {
        points: [
            'bl',
            'tl'
        ],
        offset: [
            0,
            -8
        ]
    },
    leftTop: {
        points: [
            'tr',
            'tl'
        ],
        offset: [
            -8,
            0
        ]
    },
    topRight: {
        points: [
            'br',
            'tr'
        ],
        offset: [
            0,
            -8
        ]
    },
    rightTop: {
        points: [
            'tl',
            'tr'
        ],
        offset: [
            8,
            0
        ]
    },
    bottomRight: {
        points: [
            'tr',
            'br'
        ],
        offset: [
            0,
            8
        ]
    },
    rightBottom: {
        points: [
            'bl',
            'br'
        ],
        offset: [
            8,
            0
        ]
    },
    bottomLeft: {
        points: [
            'tl',
            'bl'
        ],
        offset: [
            0,
            8
        ]
    },
    leftBottom: {
        points: [
            'br',
            'bl'
        ],
        offset: [
            -8,
            0
        ]
    }
};
function getPlacements() {
    var arrowPointAtCenter = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;
    var placements = {};
    Object.keys(basePlacements).forEach(function(key) {
        placements[key] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({}, basePlacements[key]), {}, {
            autoArrow: arrowPointAtCenter,
            targetOffset: targetOffset
        });
    });
    return placements;
}
var placements = getPlacements();
}}),
"[project]/node_modules/@rc-component/tour/es/TourStep/DefaultPanel.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>DefaultPanel)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$pickAttrs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/pickAttrs.js [app-ssr] (ecmascript)");
;
;
;
;
;
function DefaultPanel(props) {
    var _closable$closeIcon;
    var prefixCls = props.prefixCls, current = props.current, total = props.total, title = props.title, description = props.description, onClose = props.onClose, onPrev = props.onPrev, onNext = props.onNext, onFinish = props.onFinish, className = props.className, closable = props.closable;
    var ariaProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$pickAttrs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(closable || {}, true);
    var closeIcon = (_closable$closeIcon = closable === null || closable === void 0 ? void 0 : closable.closeIcon) !== null && _closable$closeIcon !== void 0 ? _closable$closeIcon : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("span", {
        className: "".concat(prefixCls, "-close-x")
    }, "\xD7");
    var mergedClosable = !!closable;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("".concat(prefixCls, "-content"), className)
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: "".concat(prefixCls, "-inner")
    }, mergedClosable && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("button", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({
        type: "button",
        onClick: onClose,
        "aria-label": "Close"
    }, ariaProps, {
        className: "".concat(prefixCls, "-close")
    }), closeIcon), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: "".concat(prefixCls, "-header")
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: "".concat(prefixCls, "-title")
    }, title)), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: "".concat(prefixCls, "-description")
    }, description), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: "".concat(prefixCls, "-footer")
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: "".concat(prefixCls, "-sliders")
    }, total > 1 ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$toConsumableArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(Array.from({
        length: total
    }).keys()).map(function(item, index) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("span", {
            key: item,
            className: index === current ? 'active' : ''
        });
    }) : null), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: "".concat(prefixCls, "-buttons")
    }, current !== 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("button", {
        className: "".concat(prefixCls, "-prev-btn"),
        onClick: onPrev
    }, "Prev") : null, current === total - 1 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("button", {
        className: "".concat(prefixCls, "-finish-btn"),
        onClick: onFinish
    }, "Finish") : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("button", {
        className: "".concat(prefixCls, "-next-btn"),
        onClick: onNext
    }, "Next")))));
}
}}),
"[project]/node_modules/@rc-component/tour/es/TourStep/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$tour$2f$es$2f$TourStep$2f$DefaultPanel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/tour/es/TourStep/DefaultPanel.js [app-ssr] (ecmascript)");
;
;
var TourStep = function TourStep(props) {
    var current = props.current, renderPanel = props.renderPanel;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], null, typeof renderPanel === 'function' ? renderPanel(props, current) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$tour$2f$es$2f$TourStep$2f$DefaultPanel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], props));
};
const __TURBOPACK__default__export__ = TourStep;
}}),
"[project]/node_modules/@rc-component/tour/es/Tour.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectSpread2.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/typeof.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/slicedToArray.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$portal$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/portal/es/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$portal$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/portal/es/index.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/trigger/es/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/classnames/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useLayoutEffect.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useMergedState$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useMergedState.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$tour$2f$es$2f$hooks$2f$useClosable$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/tour/es/hooks/useClosable.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$tour$2f$es$2f$hooks$2f$useTarget$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/tour/es/hooks/useTarget.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$tour$2f$es$2f$Mask$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/tour/es/Mask.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$tour$2f$es$2f$placements$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/tour/es/placements.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$tour$2f$es$2f$TourStep$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/tour/es/TourStep/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$tour$2f$es$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/tour/es/util.js [app-ssr] (ecmascript)");
;
;
;
;
;
var _excluded = [
    "prefixCls",
    "steps",
    "defaultCurrent",
    "current",
    "onChange",
    "onClose",
    "onFinish",
    "open",
    "mask",
    "arrow",
    "rootClassName",
    "placement",
    "renderPanel",
    "gap",
    "animated",
    "scrollIntoViewOptions",
    "zIndex",
    "closeIcon",
    "closable",
    "builtinPlacements",
    "disabledInteraction"
];
;
;
;
;
;
;
;
;
;
;
;
;
;
var CENTER_PLACEHOLDER = {
    left: '50%',
    top: '50%',
    width: 1,
    height: 1
};
var defaultScrollIntoViewOptions = {
    block: 'center',
    inline: 'center'
};
var Tour = function Tour(props) {
    var _props$prefixCls = props.prefixCls, prefixCls = _props$prefixCls === void 0 ? 'rc-tour' : _props$prefixCls, _props$steps = props.steps, steps = _props$steps === void 0 ? [] : _props$steps, defaultCurrent = props.defaultCurrent, current = props.current, onChange = props.onChange, onClose = props.onClose, _onFinish = props.onFinish, open = props.open, _props$mask = props.mask, mask = _props$mask === void 0 ? true : _props$mask, _props$arrow = props.arrow, arrow = _props$arrow === void 0 ? true : _props$arrow, rootClassName = props.rootClassName, placement = props.placement, renderPanel = props.renderPanel, gap = props.gap, animated = props.animated, _props$scrollIntoView = props.scrollIntoViewOptions, scrollIntoViewOptions = _props$scrollIntoView === void 0 ? defaultScrollIntoViewOptions : _props$scrollIntoView, _props$zIndex = props.zIndex, zIndex = _props$zIndex === void 0 ? 1001 : _props$zIndex, closeIcon = props.closeIcon, closable = props.closable, builtinPlacements = props.builtinPlacements, disabledInteraction = props.disabledInteraction, restProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutProperties$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(props, _excluded);
    var triggerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])();
    var _useMergedState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useMergedState$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(0, {
        value: current,
        defaultValue: defaultCurrent
    }), _useMergedState2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_useMergedState, 2), mergedCurrent = _useMergedState2[0], setMergedCurrent = _useMergedState2[1];
    var _useMergedState3 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useMergedState$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(undefined, {
        value: open,
        postState: function postState(origin) {
            return mergedCurrent < 0 || mergedCurrent >= steps.length ? false : origin !== null && origin !== void 0 ? origin : true;
        }
    }), _useMergedState4 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_useMergedState3, 2), mergedOpen = _useMergedState4[0], setMergedOpen = _useMergedState4[1];
    // Record if already rended in the DOM to avoid `findDOMNode` issue
    var _React$useState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(mergedOpen), _React$useState2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_React$useState, 2), hasOpened = _React$useState2[0], setHasOpened = _React$useState2[1];
    var openRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(mergedOpen);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function() {
        if (mergedOpen) {
            if (!openRef.current) {
                setMergedCurrent(0);
            }
            setHasOpened(true);
        }
        openRef.current = mergedOpen;
    }, [
        mergedOpen
    ]);
    var _ref = steps[mergedCurrent] || {}, target = _ref.target, stepPlacement = _ref.placement, stepStyle = _ref.style, stepArrow = _ref.arrow, stepClassName = _ref.className, stepMask = _ref.mask, _ref$scrollIntoViewOp = _ref.scrollIntoViewOptions, stepScrollIntoViewOptions = _ref$scrollIntoViewOp === void 0 ? defaultScrollIntoViewOptions : _ref$scrollIntoViewOp, stepCloseIcon = _ref.closeIcon, stepClosable = _ref.closable;
    var mergedClosable = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$tour$2f$es$2f$hooks$2f$useClosable$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useClosable"])(stepClosable, stepCloseIcon, closable, closeIcon);
    var mergedMask = mergedOpen && (stepMask !== null && stepMask !== void 0 ? stepMask : mask);
    var mergedScrollIntoViewOptions = stepScrollIntoViewOptions !== null && stepScrollIntoViewOptions !== void 0 ? stepScrollIntoViewOptions : scrollIntoViewOptions;
    var _useTarget = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$tour$2f$es$2f$hooks$2f$useTarget$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(target, open, gap, mergedScrollIntoViewOptions), _useTarget2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_useTarget, 2), posInfo = _useTarget2[0], targetElement = _useTarget2[1];
    var mergedPlacement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$tour$2f$es$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getPlacement"])(targetElement, placement, stepPlacement);
    // ========================= arrow =========================
    var mergedArrow = targetElement ? typeof stepArrow === 'undefined' ? arrow : stepArrow : false;
    var arrowPointAtCenter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$typeof$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(mergedArrow) === 'object' ? mergedArrow.pointAtCenter : false;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function() {
        var _triggerRef$current;
        (_triggerRef$current = triggerRef.current) === null || _triggerRef$current === void 0 ? void 0 : _triggerRef$current.forceAlign();
    }, [
        arrowPointAtCenter,
        mergedCurrent
    ]);
    // ========================= Change =========================
    var onInternalChange = function onInternalChange(nextCurrent) {
        setMergedCurrent(nextCurrent);
        onChange === null || onChange === void 0 ? void 0 : onChange(nextCurrent);
    };
    var mergedBuiltinPlacements = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(function() {
        if (builtinPlacements) {
            return typeof builtinPlacements === 'function' ? builtinPlacements({
                arrowPointAtCenter: arrowPointAtCenter
            }) : builtinPlacements;
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$tour$2f$es$2f$placements$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getPlacements"])(arrowPointAtCenter);
    }, [
        builtinPlacements,
        arrowPointAtCenter
    ]);
    // ========================= Render =========================
    // Skip if not init yet
    if (targetElement === undefined || !hasOpened) {
        return null;
    }
    var handleClose = function handleClose() {
        setMergedOpen(false);
        onClose === null || onClose === void 0 ? void 0 : onClose(mergedCurrent);
    };
    var getPopupElement = function getPopupElement() {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$tour$2f$es$2f$TourStep$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({
            arrow: mergedArrow,
            key: "content",
            prefixCls: prefixCls,
            total: steps.length,
            renderPanel: renderPanel,
            onPrev: function onPrev() {
                onInternalChange(mergedCurrent - 1);
            },
            onNext: function onNext() {
                onInternalChange(mergedCurrent + 1);
            },
            onClose: handleClose,
            current: mergedCurrent,
            onFinish: function onFinish() {
                handleClose();
                _onFinish === null || _onFinish === void 0 ? void 0 : _onFinish();
            }
        }, steps[mergedCurrent], {
            closable: mergedClosable
        }));
    };
    var mergedShowMask = typeof mergedMask === 'boolean' ? mergedMask : !!mergedMask;
    var mergedMaskStyle = typeof mergedMask === 'boolean' ? undefined : mergedMask;
    // when targetElement is not exist, use body as triggerDOMNode
    var getTriggerDOMNode = function getTriggerDOMNode(node) {
        return node || targetElement || document.body;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], null, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$tour$2f$es$2f$Mask$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
        zIndex: zIndex,
        prefixCls: prefixCls,
        pos: posInfo,
        showMask: mergedShowMask,
        style: mergedMaskStyle === null || mergedMaskStyle === void 0 ? void 0 : mergedMaskStyle.style,
        fill: mergedMaskStyle === null || mergedMaskStyle === void 0 ? void 0 : mergedMaskStyle.color,
        open: mergedOpen,
        animated: animated,
        rootClassName: rootClassName,
        disabledInteraction: disabledInteraction
    }), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$trigger$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({}, restProps, {
        builtinPlacements: mergedBuiltinPlacements,
        ref: triggerRef,
        popupStyle: stepStyle,
        popupPlacement: mergedPlacement,
        popupVisible: mergedOpen,
        popupClassName: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(rootClassName, stepClassName),
        prefixCls: prefixCls,
        popup: getPopupElement,
        forceRender: false,
        destroyPopupOnHide: true,
        zIndex: zIndex,
        getTriggerDOMNode: getTriggerDOMNode,
        arrow: !!mergedArrow
    }), /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$portal$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"], {
        open: mergedOpen,
        autoLock: true
    }, /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createElement"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$classnames$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(rootClassName, "".concat(prefixCls, "-target-placeholder")),
        style: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectSpread2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({}, posInfo || CENTER_PLACEHOLDER), {}, {
            position: 'fixed',
            pointerEvents: 'none'
        })
    }))));
};
const __TURBOPACK__default__export__ = Tour;
}}),
"[project]/node_modules/@rc-component/tour/es/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$tour$2f$es$2f$Tour$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/tour/es/Tour.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$tour$2f$es$2f$Tour$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
}}),
"[project]/node_modules/@rc-component/mutate-observer/es/wrapper.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$classCallCheck$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/classCallCheck.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$createClass$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/createClass.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$inherits$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/inherits.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$createSuper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/createSuper.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
;
;
;
;
var DomWrapper = /*#__PURE__*/ function(_React$Component) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$inherits$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(DomWrapper, _React$Component);
    var _super = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$createSuper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(DomWrapper);
    function DomWrapper() {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$classCallCheck$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, DomWrapper);
        return _super.apply(this, arguments);
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$createClass$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(DomWrapper, [
        {
            key: "render",
            value: function render() {
                return this.props.children;
            }
        }
    ]);
    return DomWrapper;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].Component);
const __TURBOPACK__default__export__ = DomWrapper;
}}),
"[project]/node_modules/@rc-component/mutate-observer/es/useMutateObserver.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>useMutateObserver)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$canUseDom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/Dom/canUseDom.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
;
;
var defaultOptions = {
    subtree: true,
    childList: true,
    attributeFilter: [
        'style',
        'class'
    ]
};
function useMutateObserver(nodeOrList, callback) {
    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : defaultOptions;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(function() {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$canUseDom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])() || !nodeOrList) {
            return;
        }
        var instance;
        var nodeList = Array.isArray(nodeOrList) ? nodeOrList : [
            nodeOrList
        ];
        if ('MutationObserver' in window) {
            instance = new MutationObserver(callback);
            nodeList.forEach(function(element) {
                instance.observe(element, options);
            });
        }
        return function() {
            var _instance, _instance2;
            (_instance = instance) === null || _instance === void 0 ? void 0 : _instance.takeRecords();
            (_instance2 = instance) === null || _instance2 === void 0 ? void 0 : _instance2.disconnect();
        };
    }, [
        options,
        nodeOrList
    ]);
}
}}),
"[project]/node_modules/@rc-component/mutate-observer/es/MutateObserver.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/slicedToArray.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useLayoutEffect.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/ref.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$findDOMNode$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/Dom/findDOMNode.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/rc-util/es/hooks/useEvent.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mutate$2d$observer$2f$es$2f$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mutate-observer/es/wrapper.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mutate$2d$observer$2f$es$2f$useMutateObserver$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mutate-observer/es/useMutateObserver.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
var MutateObserver = function MutateObserver(props) {
    var children = props.children, options = props.options, _props$onMutate = props.onMutate, onMutate = _props$onMutate === void 0 ? function() {} : _props$onMutate;
    var callback = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useEvent$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(onMutate);
    var wrapperRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useRef(null);
    var elementRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useRef(null);
    var canRef = /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isValidElement(children) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["supportRef"])(children);
    var mergedRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$ref$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useComposeRef"])(elementRef, canRef ? children.ref : null);
    var _React$useState = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useState(null), _React$useState2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$slicedToArray$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_React$useState, 2), target = _React$useState2[0], setTarget = _React$useState2[1];
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mutate$2d$observer$2f$es$2f$useMutateObserver$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(target, callback, options);
    // =========================== Effect ===========================
    // Bind target
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$hooks$2f$useLayoutEffect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function() {
        setTarget((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$findDOMNode$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(elementRef.current) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$rc$2d$util$2f$es$2f$Dom$2f$findDOMNode$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(wrapperRef.current));
    });
    // =========================== Render ===========================
    if (!children) {
        if ("TURBOPACK compile-time truthy", 1) {
            console.error('MutationObserver need children props');
        }
        return null;
    }
    return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mutate$2d$observer$2f$es$2f$wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
        ref: wrapperRef
    }, canRef ? /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].cloneElement(children, {
        ref: mergedRef
    }) : children);
};
const __TURBOPACK__default__export__ = MutateObserver;
}}),
"[project]/node_modules/@rc-component/mutate-observer/es/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mutate$2d$observer$2f$es$2f$MutateObserver$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mutate-observer/es/MutateObserver.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mutate$2d$observer$2f$es$2f$useMutateObserver$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mutate-observer/es/useMutateObserver.js [app-ssr] (ecmascript)");
;
;
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mutate$2d$observer$2f$es$2f$MutateObserver$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
}}),
"[project]/node_modules/@rc-component/mutate-observer/es/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mutate$2d$observer$2f$es$2f$MutateObserver$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mutate-observer/es/MutateObserver.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mutate$2d$observer$2f$es$2f$useMutateObserver$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mutate-observer/es/useMutateObserver.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mutate$2d$observer$2f$es$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mutate-observer/es/index.js [app-ssr] (ecmascript) <locals>");
}}),
"[project]/node_modules/@rc-component/mutate-observer/es/useMutateObserver.js [app-ssr] (ecmascript) <export default as useMutateObserver>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "useMutateObserver": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mutate$2d$observer$2f$es$2f$useMutateObserver$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$rc$2d$component$2f$mutate$2d$observer$2f$es$2f$useMutateObserver$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@rc-component/mutate-observer/es/useMutateObserver.js [app-ssr] (ecmascript)");
}}),

};

//# sourceMappingURL=node_modules_%40rc-component_c2732a02._.js.map