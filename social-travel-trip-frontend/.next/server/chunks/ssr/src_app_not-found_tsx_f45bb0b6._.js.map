{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/app/not-found.tsx"], "sourcesContent": ["'use client';\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\n\nexport default function NotFound() {\n  const router = useRouter();\n\n  useEffect(() => {\n    // console.error('Page not found');\n  }, []);\n\n  return (\n    <div className=\"flex flex-col items-center justify-center mt-20\">\n      <div className=\"bg-white shadow-md rounded-lg p-8 w-full text-center\">\n        <h1 className=\"text-8xl font-bold text-gray-500\">404</h1>\n        <h2 className=\"text-2xl font-semibold mt-4\">Trang không tồn tại</h2>\n        <p className=\"mt-2 text-gray-600\">Xin lỗi, trang bạn đang tìm kiếm không tồn tại.</p>\n        <button\n          onClick={() => router.push('/')}\n          className=\"bg-slate-600 hover:bg-slate-700 text-white font-semibold py-1 px-3 rounded mt-4\"\n        >\n          Quay lại trang chủ\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAFA;;;;AAIe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;IACR,mCAAmC;IACrC,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAmC;;;;;;8BACjD,8OAAC;oBAAG,WAAU;8BAA8B;;;;;;8BAC5C,8OAAC;oBAAE,WAAU;8BAAqB;;;;;;8BAClC,8OAAC;oBACC,SAAS,IAAM,OAAO,IAAI,CAAC;oBAC3B,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}]}