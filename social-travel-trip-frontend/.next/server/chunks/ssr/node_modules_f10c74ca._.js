module.exports = {

"[project]/node_modules/@radix-ui/react-tabs/dist/index.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Content": (()=>Content),
    "List": (()=>List),
    "Root": (()=>Root2),
    "Tabs": (()=>Tabs),
    "TabsContent": (()=>TabsContent),
    "TabsList": (()=>TabsList),
    "TabsTrigger": (()=>TabsTrigger),
    "Trigger": (()=>Trigger),
    "createTabsScope": (()=>createTabsScope)
});
// src/tabs.tsx
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/primitive/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$context$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-context/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$roving$2d$focus$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-roving-focus/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$presence$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-presence/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-primitive/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$direction$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-direction/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$use$2d$controllable$2d$state$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$id$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-id/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
;
;
;
;
var TABS_NAME = "Tabs";
var [createTabsContext, createTabsScope] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$context$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContextScope"])(TABS_NAME, [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$roving$2d$focus$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createRovingFocusGroupScope"]
]);
var useRovingFocusGroupScope = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$roving$2d$focus$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createRovingFocusGroupScope"])();
var [TabsProvider, useTabsContext] = createTabsContext(TABS_NAME);
var Tabs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])((props, forwardedRef)=>{
    const { __scopeTabs, value: valueProp, onValueChange, defaultValue, orientation = "horizontal", dir, activationMode = "automatic", ...tabsProps } = props;
    const direction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$direction$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useDirection"])(dir);
    const [value, setValue] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$use$2d$controllable$2d$state$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useControllableState"])({
        prop: valueProp,
        onChange: onValueChange,
        defaultProp: defaultValue ?? "",
        caller: TABS_NAME
    });
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])(TabsProvider, {
        scope: __scopeTabs,
        baseId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$id$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useId"])(),
        value,
        onValueChange: setValue,
        orientation,
        dir: direction,
        activationMode,
        children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Primitive"].div, {
            dir: direction,
            "data-orientation": orientation,
            ...tabsProps,
            ref: forwardedRef
        })
    });
});
Tabs.displayName = TABS_NAME;
var TAB_LIST_NAME = "TabsList";
var TabsList = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])((props, forwardedRef)=>{
    const { __scopeTabs, loop = true, ...listProps } = props;
    const context = useTabsContext(TAB_LIST_NAME, __scopeTabs);
    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$roving$2d$focus$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"], {
        asChild: true,
        ...rovingFocusGroupScope,
        orientation: context.orientation,
        dir: context.dir,
        loop,
        children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Primitive"].div, {
            role: "tablist",
            "aria-orientation": context.orientation,
            ...listProps,
            ref: forwardedRef
        })
    });
});
TabsList.displayName = TAB_LIST_NAME;
var TRIGGER_NAME = "TabsTrigger";
var TabsTrigger = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])((props, forwardedRef)=>{
    const { __scopeTabs, value, disabled = false, ...triggerProps } = props;
    const context = useTabsContext(TRIGGER_NAME, __scopeTabs);
    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);
    const triggerId = makeTriggerId(context.baseId, value);
    const contentId = makeContentId(context.baseId, value);
    const isSelected = value === context.value;
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$roving$2d$focus$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Item"], {
        asChild: true,
        ...rovingFocusGroupScope,
        focusable: !disabled,
        active: isSelected,
        children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Primitive"].button, {
            type: "button",
            role: "tab",
            "aria-selected": isSelected,
            "aria-controls": contentId,
            "data-state": isSelected ? "active" : "inactive",
            "data-disabled": disabled ? "" : void 0,
            disabled,
            id: triggerId,
            ...triggerProps,
            ref: forwardedRef,
            onMouseDown: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["composeEventHandlers"])(props.onMouseDown, (event)=>{
                if (!disabled && event.button === 0 && event.ctrlKey === false) {
                    context.onValueChange(value);
                } else {
                    event.preventDefault();
                }
            }),
            onKeyDown: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["composeEventHandlers"])(props.onKeyDown, (event)=>{
                if ([
                    " ",
                    "Enter"
                ].includes(event.key)) context.onValueChange(value);
            }),
            onFocus: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["composeEventHandlers"])(props.onFocus, ()=>{
                const isAutomaticActivation = context.activationMode !== "manual";
                if (!isSelected && !disabled && isAutomaticActivation) {
                    context.onValueChange(value);
                }
            })
        })
    });
});
TabsTrigger.displayName = TRIGGER_NAME;
var CONTENT_NAME = "TabsContent";
var TabsContent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])((props, forwardedRef)=>{
    const { __scopeTabs, value, forceMount, children, ...contentProps } = props;
    const context = useTabsContext(CONTENT_NAME, __scopeTabs);
    const triggerId = makeTriggerId(context.baseId, value);
    const contentId = makeContentId(context.baseId, value);
    const isSelected = value === context.value;
    const isMountAnimationPreventedRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(isSelected);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const rAF = requestAnimationFrame(()=>isMountAnimationPreventedRef.current = false);
        return ()=>cancelAnimationFrame(rAF);
    }, []);
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$presence$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Presence"], {
        present: forceMount || isSelected,
        children: ({ present })=>/* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Primitive"].div, {
                "data-state": isSelected ? "active" : "inactive",
                "data-orientation": context.orientation,
                role: "tabpanel",
                "aria-labelledby": triggerId,
                hidden: !present,
                id: contentId,
                tabIndex: 0,
                ...contentProps,
                ref: forwardedRef,
                style: {
                    ...props.style,
                    animationDuration: isMountAnimationPreventedRef.current ? "0s" : void 0
                },
                children: present && children
            })
    });
});
TabsContent.displayName = CONTENT_NAME;
function makeTriggerId(baseId, value) {
    return `${baseId}-trigger-${value}`;
}
function makeContentId(baseId, value) {
    return `${baseId}-content-${value}`;
}
var Root2 = Tabs;
var List = TabsList;
var Trigger = TabsTrigger;
var Content = TabsContent;
;
 //# sourceMappingURL=index.mjs.map
}}),
"[project]/node_modules/@radix-ui/react-use-is-hydrated/dist/index.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// src/use-is-hydrated.tsx
__turbopack_context__.s({
    "useIsHydrated": (()=>useIsHydrated)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$sync$2d$external$2d$store$2f$shim$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/use-sync-external-store/shim/index.js [app-ssr] (ecmascript)");
;
function useIsHydrated() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$use$2d$sync$2d$external$2d$store$2f$shim$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useSyncExternalStore"])(subscribe, ()=>true, ()=>false);
}
function subscribe() {
    return ()=>{};
}
;
 //# sourceMappingURL=index.mjs.map
}}),
"[project]/node_modules/@radix-ui/react-avatar/dist/index.mjs [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Avatar": (()=>Avatar),
    "AvatarFallback": (()=>AvatarFallback),
    "AvatarImage": (()=>AvatarImage),
    "Fallback": (()=>Fallback),
    "Image": (()=>Image),
    "Root": (()=>Root),
    "createAvatarScope": (()=>createAvatarScope)
});
// src/avatar.tsx
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$context$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-context/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$use$2d$callback$2d$ref$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$use$2d$layout$2d$effect$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-primitive/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$use$2d$is$2d$hydrated$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-use-is-hydrated/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
;
;
var AVATAR_NAME = "Avatar";
var [createAvatarContext, createAvatarScope] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$context$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContextScope"])(AVATAR_NAME);
var [AvatarProvider, useAvatarContext] = createAvatarContext(AVATAR_NAME);
var Avatar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])((props, forwardedRef)=>{
    const { __scopeAvatar, ...avatarProps } = props;
    const [imageLoadingStatus, setImageLoadingStatus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])("idle");
    return /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])(AvatarProvider, {
        scope: __scopeAvatar,
        imageLoadingStatus,
        onImageLoadingStatusChange: setImageLoadingStatus,
        children: /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Primitive"].span, {
            ...avatarProps,
            ref: forwardedRef
        })
    });
});
Avatar.displayName = AVATAR_NAME;
var IMAGE_NAME = "AvatarImage";
var AvatarImage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])((props, forwardedRef)=>{
    const { __scopeAvatar, src, onLoadingStatusChange = ()=>{}, ...imageProps } = props;
    const context = useAvatarContext(IMAGE_NAME, __scopeAvatar);
    const imageLoadingStatus = useImageLoadingStatus(src, imageProps);
    const handleLoadingStatusChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$use$2d$callback$2d$ref$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallbackRef"])((status)=>{
        onLoadingStatusChange(status);
        context.onImageLoadingStatusChange(status);
    });
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$use$2d$layout$2d$effect$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useLayoutEffect"])(()=>{
        if (imageLoadingStatus !== "idle") {
            handleLoadingStatusChange(imageLoadingStatus);
        }
    }, [
        imageLoadingStatus,
        handleLoadingStatusChange
    ]);
    return imageLoadingStatus === "loaded" ? /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Primitive"].img, {
        ...imageProps,
        ref: forwardedRef,
        src
    }) : null;
});
AvatarImage.displayName = IMAGE_NAME;
var FALLBACK_NAME = "AvatarFallback";
var AvatarFallback = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])((props, forwardedRef)=>{
    const { __scopeAvatar, delayMs, ...fallbackProps } = props;
    const context = useAvatarContext(FALLBACK_NAME, __scopeAvatar);
    const [canRender, setCanRender] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(delayMs === void 0);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (delayMs !== void 0) {
            const timerId = window.setTimeout(()=>setCanRender(true), delayMs);
            return ()=>window.clearTimeout(timerId);
        }
    }, [
        delayMs
    ]);
    return canRender && context.imageLoadingStatus !== "loaded" ? /* @__PURE__ */ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsx"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$primitive$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Primitive"].span, {
        ...fallbackProps,
        ref: forwardedRef
    }) : null;
});
AvatarFallback.displayName = FALLBACK_NAME;
function resolveLoadingStatus(image, src) {
    if (!image) {
        return "idle";
    }
    if (!src) {
        return "error";
    }
    if (image.src !== src) {
        image.src = src;
    }
    return image.complete && image.naturalWidth > 0 ? "loaded" : "loading";
}
function useImageLoadingStatus(src, { referrerPolicy, crossOrigin }) {
    const isHydrated = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$use$2d$is$2d$hydrated$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useIsHydrated"])();
    const imageRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const image = (()=>{
        if (!isHydrated) return null;
        if (!imageRef.current) {
            imageRef.current = new window.Image();
        }
        return imageRef.current;
    })();
    const [loadingStatus, setLoadingStatus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(()=>resolveLoadingStatus(image, src));
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$use$2d$layout$2d$effect$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useLayoutEffect"])(()=>{
        setLoadingStatus(resolveLoadingStatus(image, src));
    }, [
        image,
        src
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$use$2d$layout$2d$effect$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useLayoutEffect"])(()=>{
        const updateStatus = (status)=>()=>{
                setLoadingStatus(status);
            };
        if (!image) return;
        const handleLoad = updateStatus("loaded");
        const handleError = updateStatus("error");
        image.addEventListener("load", handleLoad);
        image.addEventListener("error", handleError);
        if (referrerPolicy) {
            image.referrerPolicy = referrerPolicy;
        }
        if (typeof crossOrigin === "string") {
            image.crossOrigin = crossOrigin;
        }
        return ()=>{
            image.removeEventListener("load", handleLoad);
            image.removeEventListener("error", handleError);
        };
    }, [
        image,
        crossOrigin,
        referrerPolicy
    ]);
    return loadingStatus;
}
var Root = Avatar;
var Image = AvatarImage;
var Fallback = AvatarFallback;
;
 //# sourceMappingURL=index.mjs.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/map-pin.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.507.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>MapPin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",
            key: "1r0f0z"
        }
    ],
    [
        "circle",
        {
            cx: "12",
            cy: "10",
            r: "3",
            key: "ilqhr7"
        }
    ]
];
const MapPin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("map-pin", __iconNode);
;
 //# sourceMappingURL=map-pin.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/map-pin.js [app-ssr] (ecmascript) <export default as MapPin>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "MapPin": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/map-pin.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.507.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Users)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",
            key: "1yyitq"
        }
    ],
    [
        "path",
        {
            d: "M16 3.128a4 4 0 0 1 0 7.744",
            key: "16gr8j"
        }
    ],
    [
        "path",
        {
            d: "M22 21v-2a4 4 0 0 0-3-3.87",
            key: "kshegd"
        }
    ],
    [
        "circle",
        {
            cx: "9",
            cy: "7",
            r: "4",
            key: "nufk8"
        }
    ]
];
const Users = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("users", __iconNode);
;
 //# sourceMappingURL=users.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-ssr] (ecmascript) <export default as Users>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Users": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$users$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/users.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/star.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.507.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Star)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",
            key: "r04s7s"
        }
    ]
];
const Star = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("star", __iconNode);
;
 //# sourceMappingURL=star.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/star.js [app-ssr] (ecmascript) <export default as Star>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Star": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$star$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$star$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/star.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/share-2.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.507.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Share2)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)");
;
const __iconNode = [
    [
        "circle",
        {
            cx: "18",
            cy: "5",
            r: "3",
            key: "gq8acd"
        }
    ],
    [
        "circle",
        {
            cx: "6",
            cy: "12",
            r: "3",
            key: "w7nqdw"
        }
    ],
    [
        "circle",
        {
            cx: "18",
            cy: "19",
            r: "3",
            key: "1xt0gg"
        }
    ],
    [
        "line",
        {
            x1: "8.59",
            x2: "15.42",
            y1: "13.51",
            y2: "17.49",
            key: "47mynk"
        }
    ],
    [
        "line",
        {
            x1: "15.41",
            x2: "8.59",
            y1: "6.51",
            y2: "10.49",
            key: "1n3mei"
        }
    ]
];
const Share2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("share-2", __iconNode);
;
 //# sourceMappingURL=share-2.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/share-2.js [app-ssr] (ecmascript) <export default as Share2>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Share2": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$share$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$share$2d$2$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/share-2.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/bookmark.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.507.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Bookmark)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z",
            key: "1fy3hk"
        }
    ]
];
const Bookmark = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("bookmark", __iconNode);
;
 //# sourceMappingURL=bookmark.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/bookmark.js [app-ssr] (ecmascript) <export default as Bookmark>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Bookmark": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bookmark$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$bookmark$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/bookmark.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/camera.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.507.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Camera)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",
            key: "1tc9qg"
        }
    ],
    [
        "circle",
        {
            cx: "12",
            cy: "13",
            r: "3",
            key: "1vg3eu"
        }
    ]
];
const Camera = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("camera", __iconNode);
;
 //# sourceMappingURL=camera.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/camera.js [app-ssr] (ecmascript) <export default as Camera>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Camera": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$camera$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$camera$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/camera.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/maximize.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.507.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Maximize)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M8 3H5a2 2 0 0 0-2 2v3",
            key: "1dcmit"
        }
    ],
    [
        "path",
        {
            d: "M21 8V5a2 2 0 0 0-2-2h-3",
            key: "1e4gt3"
        }
    ],
    [
        "path",
        {
            d: "M3 16v3a2 2 0 0 0 2 2h3",
            key: "wsl5sc"
        }
    ],
    [
        "path",
        {
            d: "M16 21h3a2 2 0 0 0 2-2v-3",
            key: "18trek"
        }
    ]
];
const Maximize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("maximize", __iconNode);
;
 //# sourceMappingURL=maximize.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/maximize.js [app-ssr] (ecmascript) <export default as Maximize>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Maximize": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$maximize$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$maximize$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/maximize.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/minimize.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.507.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>Minimize)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M8 3v3a2 2 0 0 1-2 2H3",
            key: "hohbtr"
        }
    ],
    [
        "path",
        {
            d: "M21 8h-3a2 2 0 0 1-2-2V3",
            key: "5jw1f3"
        }
    ],
    [
        "path",
        {
            d: "M3 16h3a2 2 0 0 1 2 2v3",
            key: "198tvr"
        }
    ],
    [
        "path",
        {
            d: "M16 21v-3a2 2 0 0 1 2-2h3",
            key: "ph8mxp"
        }
    ]
];
const Minimize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("minimize", __iconNode);
;
 //# sourceMappingURL=minimize.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/minimize.js [app-ssr] (ecmascript) <export default as Minimize>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Minimize": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$minimize$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$minimize$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/minimize.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/rotate-ccw.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.507.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>RotateCcw)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",
            key: "1357e3"
        }
    ],
    [
        "path",
        {
            d: "M3 3v5h5",
            key: "1xhq8a"
        }
    ]
];
const RotateCcw = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("rotate-ccw", __iconNode);
;
 //# sourceMappingURL=rotate-ccw.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/rotate-ccw.js [app-ssr] (ecmascript) <export default as RotateCcw>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "RotateCcw": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rotate$2d$ccw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rotate$2d$ccw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/rotate-ccw.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/chevron-left.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * @license lucide-react v0.507.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */ __turbopack_context__.s({
    "__iconNode": (()=>__iconNode),
    "default": (()=>ChevronLeft)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js [app-ssr] (ecmascript)");
;
const __iconNode = [
    [
        "path",
        {
            d: "m15 18-6-6 6-6",
            key: "1wnfg3"
        }
    ]
];
const ChevronLeft = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$createLucideIcon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("chevron-left", __iconNode);
;
 //# sourceMappingURL=chevron-left.js.map
}}),
"[project]/node_modules/lucide-react/dist/esm/icons/chevron-left.js [app-ssr] (ecmascript) <export default as ChevronLeft>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ChevronLeft": (()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-left.js [app-ssr] (ecmascript)");
}}),
"[project]/node_modules/react-photo-sphere-viewer/dist/_virtual/_rollupPluginBabelHelpers.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "arrayLikeToArray": (()=>_arrayLikeToArray),
    "arrayWithHoles": (()=>_arrayWithHoles),
    "defineProperty": (()=>_defineProperty),
    "iterableToArrayLimit": (()=>_iterableToArrayLimit),
    "nonIterableRest": (()=>_nonIterableRest),
    "objectSpread2": (()=>_objectSpread2),
    "slicedToArray": (()=>_slicedToArray),
    "toPrimitive": (()=>_toPrimitive),
    "toPropertyKey": (()=>_toPropertyKey),
    "typeof": (()=>_typeof),
    "unsupportedIterableToArray": (()=>_unsupportedIterableToArray)
});
function _arrayLikeToArray(r, a) {
    (null == a || a > r.length) && (a = r.length);
    for(var e = 0, n = Array(a); e < a; e++)n[e] = r[e];
    return n;
}
function _arrayWithHoles(r) {
    if (Array.isArray(r)) return r;
}
function _defineProperty(e, r, t) {
    return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, {
        value: t,
        enumerable: true,
        configurable: true,
        writable: true
    }) : e[r] = t, e;
}
function _iterableToArrayLimit(r, l) {
    var t = null == r ? null : "undefined" != typeof Symbol && r[Symbol.iterator] || r["@@iterator"];
    if (null != t) {
        var e, n, i, u, a = [], f = true, o = false;
        try {
            if (i = (t = t.call(r)).next, 0 === l) {
                if (Object(t) !== t) return;
                f = !1;
            } else for(; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);
        } catch (r) {
            o = true, n = r;
        } finally{
            try {
                if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return;
            } finally{
                if (o) throw n;
            }
        }
        return a;
    }
}
function _nonIterableRest() {
    throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.");
}
function ownKeys(e, r) {
    var t = Object.keys(e);
    if (Object.getOwnPropertySymbols) {
        var o = Object.getOwnPropertySymbols(e);
        r && (o = o.filter(function(r) {
            return Object.getOwnPropertyDescriptor(e, r).enumerable;
        })), t.push.apply(t, o);
    }
    return t;
}
function _objectSpread2(e) {
    for(var r = 1; r < arguments.length; r++){
        var t = null != arguments[r] ? arguments[r] : {};
        r % 2 ? ownKeys(Object(t), true).forEach(function(r) {
            _defineProperty(e, r, t[r]);
        }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function(r) {
            Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));
        });
    }
    return e;
}
function _slicedToArray(r, e) {
    return _arrayWithHoles(r) || _iterableToArrayLimit(r, e) || _unsupportedIterableToArray(r, e) || _nonIterableRest();
}
function _toPrimitive(t, r) {
    if ("object" != typeof t || !t) return t;
    var e = t[Symbol.toPrimitive];
    if (void 0 !== e) {
        var i = e.call(t, r);
        if ("object" != typeof i) return i;
        throw new TypeError("@@toPrimitive must return a primitive value.");
    }
    return ("string" === r ? String : Number)(t);
}
function _toPropertyKey(t) {
    var i = _toPrimitive(t, "string");
    return "symbol" == typeof i ? i : i + "";
}
function _typeof(o) {
    "@babel/helpers - typeof";
    return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function(o) {
        return typeof o;
    } : function(o) {
        return o && "function" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? "symbol" : typeof o;
    }, _typeof(o);
}
function _unsupportedIterableToArray(r, a) {
    if (r) {
        if ("string" == typeof r) return _arrayLikeToArray(r, a);
        var t = ({}).toString.call(r).slice(8, -1);
        return "Object" === t && r.constructor && (t = r.constructor.name), "Map" === t || "Set" === t ? Array.from(r) : "Arguments" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0;
    }
}
;
 //# sourceMappingURL=_rollupPluginBabelHelpers.js.map
}}),
"[project]/node_modules/react-photo-sphere-viewer/dist/_virtual/_commonjsHelpers.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "getDefaultExportFromCjs": (()=>getDefaultExportFromCjs)
});
function getDefaultExportFromCjs(x) {
    return x && x.__esModule && Object.prototype.hasOwnProperty.call(x, 'default') ? x['default'] : x;
}
;
 //# sourceMappingURL=_commonjsHelpers.js.map
}}),
"[project]/node_modules/react-photo-sphere-viewer/dist/_virtual/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "__module": (()=>eventemitter3)
});
var eventemitter3 = {
    exports: {}
};
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/react-photo-sphere-viewer/dist/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "ReactPhotoSphereViewer": (()=>ReactPhotoSphereViewer)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$sphere$2d$viewer$2f$dist$2f$_virtual$2f$_rollupPluginBabelHelpers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-photo-sphere-viewer/dist/_virtual/_rollupPluginBabelHelpers.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@photo-sphere-viewer/core/index.module.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$sphere$2d$viewer$2f$dist$2f$node_modules$2f$eventemitter3$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-photo-sphere-viewer/dist/node_modules/eventemitter3/index.js [app-ssr] (ecmascript)");
;
;
;
;
;
var eventEmitter = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$sphere$2d$viewer$2f$dist$2f$node_modules$2f$eventemitter3$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]();
var Emitter = {
    on: function on(event, fn) {
        return eventEmitter.on(event, fn);
    },
    once: function once(event, fn) {
        return eventEmitter.once(event, fn);
    },
    off: function off(event, fn) {
        return eventEmitter.off(event, fn);
    },
    emit: function emit(event, payload) {
        return eventEmitter.emit(event, payload);
    }
};
Object.freeze(Emitter);
var omittedProps = [
    "src",
    "height",
    "width",
    "hideNavbarButton",
    "containerClass",
    "littlePlanet",
    "onPositionChange",
    "onZoomChange",
    "onClick",
    "onDblclick",
    "onReady"
];
/**
 * Props interface for the Viewer component.
 * 
 * @interface
 * @property {string} src - The source of the image to be viewed.
 * @property {boolean | string | Array<string | NavbarCustomButton>} [navbar] - Configuration for the navbar. Can be a boolean, string, or an array of strings or NavbarCustomButton.
 * @property {string} height - The height of the viewer.
 * @property {string} [width] - The width of the viewer.
 * @property {string} [containerClass] - The CSS class for the viewer container.
 * @property {boolean} [littlePlanet] - Enable or disable the little planet effect.
 * @property {boolean | number} [fishEye] - Enable or disable the fisheye effect, or set the fisheye level.
 * @property {boolean} [hideNavbarButton] - Show/hide the button that hides the navbar.
 * @property {Object} [lang] - Language configuration for the viewer. Each property is a string that represents the text for a specific action.
 * @property {Function} [onPositionChange] - Event handler for when the position changes. Receives the latitude, longitude, and the Viewer instance.
 * @property {Function} [onZoomChange] - Event handler for when the zoom level changes. Receives the ZoomUpdatedEvent and the Viewer instance.
 * @property {Function} [onClick] - Event handler for when the viewer is clicked. Receives the ClickEvent and the Viewer instance.
 * @property {Function} [onDblclick] - Event handler for when the viewer is double clicked. Receives the ClickEvent and the Viewer instance.
 * @property {Function} [onReady] - Event handler for when the viewer is ready. Receives the Viewer instance.
 */ var defaultNavbar = [
    "zoom",
    "fullscreen"
];
function adaptOptions(options) {
    var adaptedOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$sphere$2d$viewer$2f$dist$2f$_virtual$2f$_rollupPluginBabelHelpers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["objectSpread2"])({}, options);
    for(var key in adaptedOptions){
        if (omittedProps.includes(key)) {
            delete adaptedOptions[key];
        }
    }
    return adaptedOptions;
}
function map(_in, inMin, inMax, outMin, outMax) {
    return (_in - inMin) * (outMax - outMin) / (inMax - inMin) + outMin;
}
function filterNavbar(navbar) {
    if (navbar == null) return defaultNavbar;
    if (!Array.isArray(navbar)) {
        if (typeof navbar === "string") {
            return navbar === "" ? false : [
                navbar
            ];
        }
        return navbar ? defaultNavbar : false;
    }
    return navbar;
}
function useDomElement() {
    var _useState = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(), _useState2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$sphere$2d$viewer$2f$dist$2f$_virtual$2f$_rollupPluginBabelHelpers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["slicedToArray"])(_useState, 2), element = _useState2[0], setElement = _useState2[1];
    var ref = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(function(r) {
        if (r && r !== element) {
            setElement(r);
        }
    }, [
        element
    ]);
    return [
        element,
        ref
    ];
}
/**
 * Interface for the Viewer API.
 * 
 * @interface
 * @property {Function} animate - Starts an animation. Receives an object of AnimateOptions.
 * @property {Function} destroy - Destroys the viewer.
 * @property {Function} createTooltip - Creates a tooltip. Receives a TooltipConfig object.
 * @property {Function} needsContinuousUpdate - Enables or disables continuous updates. Receives a boolean.
 * @property {Function} observeObjects - Starts observing objects. Receives a string key.
 * @property {Function} unobserveObjects - Stops observing objects. Receives a string key.
 * @property {Function} setCursor - Sets the cursor. Receives a string.
 * @property {Function} stopAnimation - Stops the current animation. Returns a Promise.
 * @property {Function} rotate - Rotates the viewer. Receives an ExtendedPosition object.
 * @property {Function} setOption - Sets a single option. Receives an option key and a value.
 * @property {Function} setOptions - Sets multiple options. Receives an object of options.
 * @property {Function} getCurrentNavbar - Returns the current navbar.
 * @property {Function} zoom - Sets the zoom level. Receives a number.
 * @property {Function} zoomIn - Increases the zoom level. Receives a number.
 * @property {Function} zoomOut - Decreases the zoom level. Receives a number.
 * @property {Function} resize - Resizes the viewer. Receives a CssSize object.
 * @property {Function} enterFullscreen - Enters fullscreen mode.
 * @property {Function} exitFullscreen - Exits fullscreen mode.
 * @property {Function} toggleFullscreen - Toggles fullscreen mode.
 * @property {Function} isFullscreenEnabled - Returns whether fullscreen is enabled.
 * @property {Function} getPlugin - Returns a plugin. Receives a plugin ID or a PluginConstructor.
 * @property {Function} getPosition - Returns the current position.
 * @property {Function} getZoomLevel - Returns the current zoom level.
 * @property {Function} getSize - Returns the current size.
 * @property {Function} needsUpdate - Updates the viewer.
 * @property {Function} autoSize - Sets the size to auto.
 * @property {Function} setPanorama - Sets the panorama. Receives a path and an optional PanoramaOptions object. Returns a Promise.
 * @property {Function} showError - Shows an error message. Receives a string.
 * @property {Function} hideError - Hides the error message.
 * @property {Function} startKeyboardControl - Starts keyboard control.
 * @property {Function} stopKeyboardControl - Stops keyboard control.
 */ var ReactPhotoSphereViewer = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(function(props, ref) {
    var _useDomElement = useDomElement(), _useDomElement2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$sphere$2d$viewer$2f$dist$2f$_virtual$2f$_rollupPluginBabelHelpers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["slicedToArray"])(_useDomElement, 2), sphereElement = _useDomElement2[0], setRef = _useDomElement2[1];
    var options = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(function() {
        return props;
    }, [
        // recreate options when individual props change
        props.panorama,
        props.src,
        props.size,
        props.canvasBackground,
        props.navbar,
        props.height,
        props.width,
        props.containerClass,
        props.hideNavbarButton || true,
        props.littlePlanet,
        props.fishEye,
        props.lang,
        props.onPositionChange,
        props.onZoomChange,
        props.onClick,
        props.onDblclick,
        props.onReady,
        props.moveSpeed,
        props.zoomSpeed,
        props.moveInertia,
        props.mousewheel,
        props.mousemove,
        props.mousewheelCtrlKey,
        props.touchmoveTwoFingers,
        props.panoData,
        props.requestHeaders,
        props.withCredentials,
        props.keyboard,
        props.keyboardActions,
        props.plugins,
        props.adapter,
        props.sphereCorrection,
        props.minFov,
        props.maxFov,
        props.defaultZoomLvl,
        props.defaultYaw,
        props.defaultPitch,
        props.caption,
        props.description,
        props.downloadUrl,
        props.downloadName,
        props.loadingImg,
        props.loadingTxt,
        props.rendererParameters,
        props.defaultTransition
    ]);
    var spherePlayerInstance = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    var LITTLEPLANET_MAX_ZOOM = 130;
    var _useState3 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(-90), _useState4 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$sphere$2d$viewer$2f$dist$2f$_virtual$2f$_rollupPluginBabelHelpers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["slicedToArray"])(_useState3, 1), LITTLEPLANET_DEF_LAT = _useState4[0];
    var _useState5 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(2), _useState6 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$sphere$2d$viewer$2f$dist$2f$_virtual$2f$_rollupPluginBabelHelpers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["slicedToArray"])(_useState5, 1), LITTLEPLANET_FISHEYE = _useState6[0];
    var _useState7 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(0), _useState8 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$sphere$2d$viewer$2f$dist$2f$_virtual$2f$_rollupPluginBabelHelpers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["slicedToArray"])(_useState7, 1), LITTLEPLANET_DEF_ZOOM = _useState8[0];
    var _useState9 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(defaultNavbar), _useState10 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$sphere$2d$viewer$2f$dist$2f$_virtual$2f$_rollupPluginBabelHelpers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["slicedToArray"])(_useState9, 2), currentNavbar = _useState10[0], setCurrentNavbar = _useState10[1];
    var littlePlanetEnabledRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(function() {
        function handleResize() {
            var aspectRatio = window.innerWidth / window.innerHeight;
            //console.log(aspectRatio)
            LITTLEPLANET_MAX_ZOOM = Math.floor(map(aspectRatio, 0.5, 1.8, 140, 115));
        }
        // Add event listener
        window.addEventListener("resize", handleResize);
        handleResize();
        return function() {
            return window.removeEventListener("resize", handleResize);
        };
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(function() {
        if (sphereElement && !spherePlayerInstance.current) {
            var _options$minFov, _options$maxFov, _options$defaultZoomL, _options$defaultYaw, _options$defaultPitch, _options$moveInertia, _options$mousewheel, _options$mousemove, _options$plugins;
            var _c = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Viewer"]((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$sphere$2d$viewer$2f$dist$2f$_virtual$2f$_rollupPluginBabelHelpers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["objectSpread2"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$sphere$2d$viewer$2f$dist$2f$_virtual$2f$_rollupPluginBabelHelpers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["objectSpread2"])({}, adaptOptions(options)), {}, {
                container: sphereElement,
                panorama: options.panorama || options.src,
                size: {
                    height: options.height,
                    width: options.width || "100px"
                },
                fisheye: options.littlePlanet ? LITTLEPLANET_FISHEYE : options.fisheye || false,
                minFov: (_options$minFov = options.minFov) !== null && _options$minFov !== void 0 ? _options$minFov : 30,
                maxFov: options.littlePlanet ? LITTLEPLANET_MAX_ZOOM : (_options$maxFov = options.maxFov) !== null && _options$maxFov !== void 0 ? _options$maxFov : 90,
                defaultZoomLvl: options.littlePlanet ? LITTLEPLANET_DEF_ZOOM : (_options$defaultZoomL = options.defaultZoomLvl) !== null && _options$defaultZoomL !== void 0 ? _options$defaultZoomL : 50,
                defaultYaw: (_options$defaultYaw = options.defaultYaw) !== null && _options$defaultYaw !== void 0 ? _options$defaultYaw : 0,
                defaultPitch: options.littlePlanet ? LITTLEPLANET_DEF_LAT : (_options$defaultPitch = options.defaultPitch) !== null && _options$defaultPitch !== void 0 ? _options$defaultPitch : 0,
                sphereCorrection: options.sphereCorrection || {
                    pan: 0,
                    tilt: 0,
                    roll: 0
                },
                moveSpeed: options.moveSpeed || 1,
                zoomSpeed: options.zoomSpeed || 1,
                // when it undefined, = true, then use input value.
                // The input value maybe false, value || true => false => true
                moveInertia: (_options$moveInertia = options.moveInertia) !== null && _options$moveInertia !== void 0 ? _options$moveInertia : true,
                mousewheel: options.littlePlanet ? false : (_options$mousewheel = options.mousewheel) !== null && _options$mousewheel !== void 0 ? _options$mousewheel : true,
                mousemove: (_options$mousemove = options.mousemove) !== null && _options$mousemove !== void 0 ? _options$mousemove : true,
                mousewheelCtrlKey: options.mousewheelCtrlKey || false,
                touchmoveTwoFingers: options.touchmoveTwoFingers || false,
                panoData: options.panoData || undefined,
                requestHeaders: options.requestHeaders || undefined,
                withCredentials: options.withCredentials || false,
                navbar: filterNavbar(options.navbar),
                lang: options.lang || {},
                keyboard: options.keyboard || "fullscreen",
                plugins: (_options$plugins = options.plugins) !== null && _options$plugins !== void 0 ? _options$plugins : []
            }));
            _c.addEventListener("ready", function() {
                if (options.onReady) {
                    options.onReady(_c);
                }
            }, {
                once: true
            });
            _c.addEventListener("click", function(data) {
                if (options.onClick) {
                    options.onClick(data, _c);
                }
                if (options.littlePlanet && littlePlanetEnabledRef.current) {
                    littlePlanetEnabledRef.current = false;
                    // fly inside the sphere
                    _c.animate({
                        yaw: 0,
                        pitch: LITTLEPLANET_DEF_LAT,
                        zoom: 75,
                        speed: "3rpm"
                    }).then(function() {
                        // watch on the sky
                        _c.animate({
                            yaw: 0,
                            pitch: 0,
                            zoom: 90,
                            speed: "10rpm"
                        }).then(function() {
                            var _options$mousewheel2;
                            // Disable Little Planet.
                            _c.setOption("maxFov", options.maxFov || 70);
                            _c.setOption("mousewheel", (_options$mousewheel2 = options.mousewheel) !== null && _options$mousewheel2 !== void 0 ? _options$mousewheel2 : true);
                        });
                    });
                }
            });
            _c.addEventListener("dblclick", function(data) {
                if (options.onDblclick) {
                    options.onDblclick(data, _c);
                }
            });
            _c.addEventListener("zoom-updated", function(zoom) {
                if (options.onZoomChange) {
                    options.onZoomChange(zoom, _c);
                }
            });
            _c.addEventListener("position-updated", function(position) {
                if (options.onPositionChange) {
                    options.onPositionChange(position.position.pitch, position.position.yaw, _c);
                }
            });
            var _currentNavbar = filterNavbar(options.navbar);
            if (options.littlePlanet) {
                var _props$lang, _props$lang2;
                var littlePlanetIcon = "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24px\" height=\"24px\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 20C16.4183 20 20 16.4183 20 12C20 11.8805 19.9974 11.7615 19.9922 11.6433C20.2479 11.4141 20.4882 11.1864 20.7118 10.9611C21.0037 10.6672 21.002 10.1923 20.708 9.90049C20.4336 9.628 20.0014 9.61143 19.7077 9.84972C19.4023 8.75248 18.8688 7.75024 18.1616 6.89725C18.4607 6.84611 18.7436 6.8084 19.0087 6.784C19.4212 6.74604 19.7247 6.38089 19.6868 5.96842C19.6488 5.55595 19.2837 5.25235 18.8712 5.29032C18.4474 5.32932 17.9972 5.39638 17.5262 5.48921C17.3267 5.52851 17.1614 5.64353 17.0543 5.79852C15.6765 4.67424 13.917 4 12 4C7.58172 4 4 7.58172 4 12C4 12.2776 4.01414 12.552 4.04175 12.8223C3.78987 12.7532 3.50899 12.8177 3.31137 13.0159C2.97651 13.3517 2.67596 13.6846 2.415 14.0113C2.15647 14.3349 2.20924 14.8069 2.53287 15.0654C2.8565 15.3239 3.32843 15.2711 3.58696 14.9475C3.78866 14.695 4.02466 14.4302 4.2938 14.1557C4.60754 15.2796 5.16056 16.3037 5.8945 17.1697C5.66824 17.3368 5.54578 17.6248 5.60398 17.919C5.68437 18.3253 6.07894 18.5896 6.48528 18.5092C6.7024 18.4662 6.92455 18.4177 7.15125 18.3637C8.49656 19.3903 10.1771 20 12 20ZM7.15125 18.3637C6.69042 18.012 6.26891 17.6114 5.8945 17.1697C5.98073 17.106 6.08204 17.0599 6.19417 17.0377C7.19089 16.8405 8.33112 16.5084 9.55581 16.0486C9.94359 15.903 10.376 16.0994 10.5216 16.4872C10.6671 16.8749 10.4708 17.3073 10.083 17.4529C9.05325 17.8395 8.0653 18.1459 7.15125 18.3637ZM19.7077 9.84972C19.6869 9.86663 19.6667 9.88483 19.6474 9.90431C18.9609 10.5957 18.0797 11.3337 17.0388 12.0753C16.7014 12.3157 16.6228 12.784 16.8631 13.1213C17.1035 13.4587 17.5718 13.5373 17.9091 13.297C18.6809 12.7471 19.3806 12.1912 19.9922 11.6433C19.965 11.0246 19.8676 10.4241 19.7077 9.84972ZM20.9366 5.37924C20.5336 5.28378 20.1294 5.53313 20.034 5.93619C19.9385 6.33925 20.1879 6.74339 20.5909 6.83886C20.985 6.93219 21.1368 7.07125 21.1932 7.16142C21.2565 7.26269 21.3262 7.52732 21.0363 8.10938C20.8516 8.48014 21.0025 8.93042 21.3732 9.1151C21.744 9.29979 22.1943 9.14894 22.379 8.77818C22.7566 8.02003 22.9422 7.12886 22.4648 6.36582C22.1206 5.81574 21.5416 5.52252 20.9366 5.37924ZM2.81481 16.2501C2.94057 15.8555 2.72259 15.4336 2.32793 15.3078C1.93327 15.1821 1.51138 15.4 1.38562 15.7947C1.19392 16.3963 1.17354 17.0573 1.53488 17.6349C1.98775 18.3587 2.84153 18.6413 3.68907 18.7224C4.1014 18.7619 4.46765 18.4596 4.50712 18.0473C4.54658 17.635 4.24432 17.2687 3.83199 17.2293C3.13763 17.1628 2.88355 16.9624 2.80651 16.8393C2.75679 16.7598 2.70479 16.5954 2.81481 16.2501ZM15.7504 14.704C16.106 14.4915 16.2218 14.0309 16.0093 13.6754C15.7967 13.3199 15.3362 13.204 14.9807 13.4166C14.4991 13.7045 13.9974 13.9881 13.4781 14.2648C12.9445 14.5491 12.4132 14.8149 11.8883 15.0615C11.5134 15.2376 11.3522 15.6843 11.5283 16.0592C11.7044 16.4341 12.1511 16.5953 12.526 16.4192C13.0739 16.1618 13.6277 15.8847 14.1834 15.5887C14.7242 15.3005 15.2474 15.0048 15.7504 14.704Z\" fill=\"rgba(255,255,255,.7)\"/>\n                </svg>";
                var resetLittlePlanetButton = {
                    id: "resetLittlePlanetButton",
                    content: ((_props$lang = props.lang) === null || _props$lang === void 0 ? void 0 : _props$lang.littlePlanetIcon) || littlePlanetIcon,
                    title: ((_props$lang2 = props.lang) === null || _props$lang2 === void 0 ? void 0 : _props$lang2.littlePlanetButton) || "Reset Little Planet",
                    className: "resetLittlePlanetButton",
                    onClick: function onClick() {
                        littlePlanetEnabledRef.current = true;
                        _c.setOption("maxFov", LITTLEPLANET_MAX_ZOOM);
                        //_c.setOption("fisheye", LITTLEPLANET_FISHEYE) // @ts-ignore ts(2345)
                        _c.setOption("mousewheel", false);
                        _c.animate({
                            yaw: 0,
                            pitch: LITTLEPLANET_DEF_LAT,
                            zoom: LITTLEPLANET_DEF_ZOOM,
                            speed: "10rpm"
                        });
                    }
                };
                if (_currentNavbar !== false && !_currentNavbar.find(function(item) {
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$sphere$2d$viewer$2f$dist$2f$_virtual$2f$_rollupPluginBabelHelpers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["typeof"])(item) === "object" && (item === null || item === void 0 ? void 0 : item.id) === "resetLittlePlanetButton";
                })) {
                    _currentNavbar.splice(1, 0, resetLittlePlanetButton);
                }
            }
            if (options.hideNavbarButton) {
                // add toggle navbar visibility button
                var hideNavbarButton = {
                    id: "hideNavbarButton",
                    content: "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24px\" height=\"24px\" viewBox=\"0 0 24 24\" fill=\"none\">\n                            <g clip-path=\"url(#clip0_429_11083)\">\n                            <path d=\"M7 7.00006L17 17.0001M7 17.0001L17 7.00006\" stroke=\"rgba(255,255,255,.7)\" stroke-width=\"2.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                            </g>\n                            <defs>\n                            <clipPath id=\"clip0_429_11083\">\n                            <rect width=\"24\" height=\"24\" fill=\"white\"/>\n                            </clipPath>\n                            </defs>\n                            </svg>",
                    title: "Hide Navbar",
                    className: "hideNavbarButton",
                    onClick: function onClick() {
                        _c.navbar.hide();
                        // add a show navbar button that is always hidden until mouseover
                        var btn = document.createElement("a");
                        btn.className = "showNavbarButton";
                        // add svg icon
                        btn.innerHTML = "<svg version=\"1.1\" id=\"Layer_1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\" viewBox=\"0 0 26 26\" style=\"enable-background:new 0 0 26 26;\" xml:space=\"preserve\" class=\"icon icon-back-to-top\">\n                                        <g>\n                                        <path d=\"M13.8,1.3L21.6,9c0.1,0.1,0.1,0.3,0.2,0.4c0.1,0.1,0.1,0.3,0.1,0.4s0,0.3-0.1,0.4c-0.1,0.1-0.1,0.3-0.3,0.4\n                                            c-0.1,0.1-0.2,0.2-0.4,0.3c-0.2,0.1-0.3,0.1-0.4,0.1c-0.1,0-0.3,0-0.4-0.1c-0.2-0.1-0.3-0.2-0.4-0.3L14.2,5l0,19.1\n                                            c0,0.2-0.1,0.3-0.1,0.5c0,0.1-0.1,0.3-0.3,0.4c-0.1,0.1-0.2,0.2-0.4,0.3c-0.1,0.1-0.3,0.1-0.5,0.1c-0.1,0-0.3,0-0.4-0.1\n                                            c-0.1-0.1-0.3-0.1-0.4-0.3c-0.1-0.1-0.2-0.2-0.3-0.4c-0.1-0.1-0.1-0.3-0.1-0.5l0-19.1l-5.7,5.7C6,10.8,5.8,10.9,5.7,11\n                                            c-0.1,0.1-0.3,0.1-0.4,0.1c-0.2,0-0.3,0-0.4-0.1c-0.1-0.1-0.3-0.2-0.4-0.3c-0.1-0.1-0.1-0.2-0.2-0.4C4.1,10.2,4,10.1,4.1,9.9\n                                            c0-0.1,0-0.3,0.1-0.4c0-0.1,0.1-0.3,0.3-0.4l7.7-7.8c0.1,0,0.2-0.1,0.2-0.1c0,0,0.1-0.1,0.2-0.1c0.1,0,0.2,0,0.2-0.1\n                                            c0.1,0,0.1,0,0.2,0c0,0,0.1,0,0.2,0c0.1,0,0.2,0,0.2,0.1c0.1,0,0.1,0.1,0.2,0.1C13.7,1.2,13.8,1.2,13.8,1.3z\"></path>\n                                        </g>\n                                        </svg>";
                        btn.title = "Show Navbar";
                        btn.onclick = function(e) {
                            e.preventDefault();
                            _c.navbar.show();
                            btn.remove();
                        };
                        // add the button to the viewer container
                        document.body.appendChild(btn);
                    }
                };
                if (_currentNavbar !== false && !_currentNavbar.find(function(item) {
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$sphere$2d$viewer$2f$dist$2f$_virtual$2f$_rollupPluginBabelHelpers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["typeof"])(item) === "object" && (item === null || item === void 0 ? void 0 : item.id) === "hideNavbarButton";
                })) {
                    _currentNavbar.push(hideNavbarButton);
                }
            }
            if (_currentNavbar !== false) {
                _c.setOption("navbar", _currentNavbar);
                setCurrentNavbar(_currentNavbar);
            } else {
                _c.navbar.hide();
            }
            /* @ts-ignore next line */ Emitter.on("animate", function(options) {
                _c.animate(options);
            }).on("stop-animation", function() {
                _c.stopAnimation();
            }).on("destroy", function() {
                _c.destroy();
            }).on("rotate", function(options) {
                _c.rotate(options);
            }).on("setOption", function(pair) {
                var option = pair.option, value = pair.value;
                _c.setOption(option, value);
            }).on("setOptions", function(options) {
                _c.setOptions(options);
            }).on("zoom", function(zoom) {
                _c.zoom(zoom);
            }).on("zoomIn", function(step) {
                _c.zoomIn(step);
            }).on("zoomOut", function(step) {
                _c.zoomOut(step);
            }).on("resize", function(size) {
                _c.resize(size);
            }).on("enterFullscreen", function() {
                _c.enterFullscreen();
            }).on("exitFullscreen", function() {
                _c.exitFullscreen();
            }).on("toggleFullscreen", function() {
                _c.toggleFullscreen();
            }).on("needsContinuousUpdate", function(enabled) {
                _c.needsContinuousUpdate(enabled);
            }).on("observeObjects", function(userDataKey) {
                _c.observeObjects(userDataKey);
            }).on("unobserveObjects", function(userDataKey) {
                _c.unobserveObjects(userDataKey);
            }).on("setCursor", function(cursor) {
                _c.setCursor(cursor);
            }).on("setPanorama", function(payload) {
                _c.setPanorama(payload.path, payload.options);
            }).on("showError", function(message) {
                _c.showError(message);
            }).on("hideError", function() {
                _c.hideError();
            }).on("startKeyboardControl", function() {
                _c.startKeyboardControl();
            }).on("stopKeyboardControl", function() {
                _c.stopKeyboardControl();
            });
            spherePlayerInstance.current = _c;
        }
    }, [
        sphereElement,
        options
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(function() {
        var viewer = spherePlayerInstance.current;
        if (viewer && viewer.container && viewer.container.parentNode) {
            if (viewer && viewer.container && viewer.container.parentNode) {
                var _viewer$renderer, _viewer$renderer$rend, _viewer$renderer2, _viewer$renderer2$ren;
                (_viewer$renderer = viewer.renderer) === null || _viewer$renderer === void 0 ? void 0 : (_viewer$renderer$rend = _viewer$renderer.renderer) === null || _viewer$renderer$rend === void 0 ? void 0 : _viewer$renderer$rend.dispose();
                (_viewer$renderer2 = viewer.renderer) === null || _viewer$renderer2 === void 0 ? void 0 : (_viewer$renderer2$ren = _viewer$renderer2.renderer) === null || _viewer$renderer2$ren === void 0 ? void 0 : _viewer$renderer2$ren.forceContextLoss();
                viewer.destroy();
            }
        }
    }, [
        spherePlayerInstance
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(function() {
        var _options$panorama;
        var panorama = (_options$panorama = options.panorama) !== null && _options$panorama !== void 0 ? _options$panorama : options.src;
        if (spherePlayerInstance.current && panorama) {
            spherePlayerInstance.current.setPanorama(panorama, {});
        }
    }, [
        options.src,
        options.panorama
    ]);
    var _imperativeHandle = function _imperativeHandle() {
        return {
            animate: function animate(options) {
                Emitter.emit("animate", options);
            },
            destroy: function destroy() {
                Emitter.emit("destroy", {});
            },
            createTooltip: function createTooltip(config) {
                var _spherePlayerInstance;
                return (_spherePlayerInstance = spherePlayerInstance.current) === null || _spherePlayerInstance === void 0 ? void 0 : _spherePlayerInstance.createTooltip(config);
            },
            needsContinuousUpdate: function needsContinuousUpdate(enabled) {
                Emitter.emit("needsContinuousUpdate", enabled);
            },
            observeObjects: function observeObjects(userDataKey) {
                Emitter.emit("observeObjects", userDataKey);
            },
            unobserveObjects: function unobserveObjects(userDataKey) {
                Emitter.emit("unobserveObjects", userDataKey);
            },
            setCursor: function setCursor(cursor) {
                Emitter.emit("setCursor", cursor);
            },
            stopAnimation: function stopAnimation() {
                Emitter.emit("stop-animation", {});
            },
            rotate: function rotate(position) {
                Emitter.emit("rotate", position);
            },
            setOption: function setOption(option, value) {
                Emitter.emit("setOption", {
                    option: option,
                    value: value
                });
            },
            setOptions: function setOptions(options) {
                var _spherePlayerInstance2;
                return (_spherePlayerInstance2 = spherePlayerInstance.current) === null || _spherePlayerInstance2 === void 0 ? void 0 : _spherePlayerInstance2.setOptions(options);
            },
            getCurrentNavbar: function getCurrentNavbar() {
                return currentNavbar;
            },
            zoom: function zoom(value) {
                Emitter.emit("zoom", value);
            },
            zoomIn: function zoomIn(step) {
                Emitter.emit("zoomIn", {
                    step: step
                });
            },
            zoomOut: function zoomOut(step) {
                Emitter.emit("zoomOut", {
                    step: step
                });
            },
            resize: function resize(size) {
                var _spherePlayerInstance3;
                return (_spherePlayerInstance3 = spherePlayerInstance.current) === null || _spherePlayerInstance3 === void 0 ? void 0 : _spherePlayerInstance3.resize(size);
            },
            enterFullscreen: function enterFullscreen() {
                var _spherePlayerInstance4;
                return (_spherePlayerInstance4 = spherePlayerInstance.current) === null || _spherePlayerInstance4 === void 0 ? void 0 : _spherePlayerInstance4.enterFullscreen();
            },
            exitFullscreen: function exitFullscreen() {
                var _spherePlayerInstance5;
                return (_spherePlayerInstance5 = spherePlayerInstance.current) === null || _spherePlayerInstance5 === void 0 ? void 0 : _spherePlayerInstance5.exitFullscreen();
            },
            toggleFullscreen: function toggleFullscreen() {
                var _spherePlayerInstance6;
                return (_spherePlayerInstance6 = spherePlayerInstance.current) === null || _spherePlayerInstance6 === void 0 ? void 0 : _spherePlayerInstance6.toggleFullscreen();
            },
            isFullscreenEnabled: function isFullscreenEnabled() {
                var _spherePlayerInstance7;
                return (_spherePlayerInstance7 = spherePlayerInstance.current) === null || _spherePlayerInstance7 === void 0 ? void 0 : _spherePlayerInstance7.isFullscreenEnabled();
            },
            getPlugin: function getPlugin(pluginId) {
                var _spherePlayerInstance8;
                return (_spherePlayerInstance8 = spherePlayerInstance.current) === null || _spherePlayerInstance8 === void 0 ? void 0 : _spherePlayerInstance8.getPlugin(pluginId);
            },
            getPosition: function getPosition() {
                var _spherePlayerInstance9;
                return (_spherePlayerInstance9 = spherePlayerInstance.current) === null || _spherePlayerInstance9 === void 0 ? void 0 : _spherePlayerInstance9.getPosition();
            },
            getZoomLevel: function getZoomLevel() {
                var _spherePlayerInstance10;
                return (_spherePlayerInstance10 = spherePlayerInstance.current) === null || _spherePlayerInstance10 === void 0 ? void 0 : _spherePlayerInstance10.getZoomLevel();
            },
            getSize: function getSize() {
                var _spherePlayerInstance11;
                return (_spherePlayerInstance11 = spherePlayerInstance.current) === null || _spherePlayerInstance11 === void 0 ? void 0 : _spherePlayerInstance11.getSize();
            },
            needsUpdate: function needsUpdate() {
                var _spherePlayerInstance12;
                return (_spherePlayerInstance12 = spherePlayerInstance.current) === null || _spherePlayerInstance12 === void 0 ? void 0 : _spherePlayerInstance12.needsUpdate();
            },
            autoSize: function autoSize() {
                var _spherePlayerInstance13;
                return (_spherePlayerInstance13 = spherePlayerInstance.current) === null || _spherePlayerInstance13 === void 0 ? void 0 : _spherePlayerInstance13.autoSize();
            },
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            setPanorama: function setPanorama(path, options) {
                var _spherePlayerInstance14;
                return (_spherePlayerInstance14 = spherePlayerInstance.current) === null || _spherePlayerInstance14 === void 0 ? void 0 : _spherePlayerInstance14.setPanorama(path, options);
            },
            showError: function showError(message) {
                var _spherePlayerInstance15;
                return (_spherePlayerInstance15 = spherePlayerInstance.current) === null || _spherePlayerInstance15 === void 0 ? void 0 : _spherePlayerInstance15.showError(message);
            },
            hideError: function hideError() {
                var _spherePlayerInstance16;
                return (_spherePlayerInstance16 = spherePlayerInstance.current) === null || _spherePlayerInstance16 === void 0 ? void 0 : _spherePlayerInstance16.hideError();
            },
            startKeyboardControl: function startKeyboardControl() {
                var _spherePlayerInstance17;
                return (_spherePlayerInstance17 = spherePlayerInstance.current) === null || _spherePlayerInstance17 === void 0 ? void 0 : _spherePlayerInstance17.startKeyboardControl();
            },
            stopKeyboardControl: function stopKeyboardControl() {
                var _spherePlayerInstance18;
                return (_spherePlayerInstance18 = spherePlayerInstance.current) === null || _spherePlayerInstance18 === void 0 ? void 0 : _spherePlayerInstance18.stopKeyboardControl();
            }
        };
    };
    // Methods
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, _imperativeHandle, [
        spherePlayerInstance.current,
        sphereElement,
        options,
        ref
    ]);
    return /*#__PURE__*/ __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div", {
        className: options.containerClass || "view-container",
        ref: setRef
    });
});
ReactPhotoSphereViewer.displayName = "ReactPhotoSphereViewer";
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/react-photo-sphere-viewer/dist/node_modules/eventemitter3/index.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>EventEmitter)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$sphere$2d$viewer$2f$dist$2f$_virtual$2f$_commonjsHelpers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-photo-sphere-viewer/dist/_virtual/_commonjsHelpers.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$sphere$2d$viewer$2f$dist$2f$_virtual$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-photo-sphere-viewer/dist/_virtual/index.js [app-ssr] (ecmascript)");
;
;
(function(module) {
    var has = Object.prototype.hasOwnProperty, prefix = '~';
    /**
	 * Constructor to create a storage for our `EE` objects.
	 * An `Events` instance is a plain object whose properties are event names.
	 *
	 * @constructor
	 * @private
	 */ function Events() {}
    //
    // We try to not inherit from `Object.prototype`. In some engines creating an
    // instance in this way is faster than calling `Object.create(null)` directly.
    // If `Object.create(null)` is not supported we prefix the event names with a
    // character to make sure that the built-in object properties are not
    // overridden or used as an attack vector.
    //
    if (Object.create) {
        Events.prototype = Object.create(null);
        //
        // This hack is needed because the `__proto__` property is still inherited in
        // some old browsers like Android 4, iPhone 5.1, Opera 11 and Safari 5.
        //
        if (!new Events().__proto__) prefix = false;
    }
    /**
	 * Representation of a single event listener.
	 *
	 * @param {Function} fn The listener function.
	 * @param {*} context The context to invoke the listener with.
	 * @param {Boolean} [once=false] Specify if the listener is a one-time listener.
	 * @constructor
	 * @private
	 */ function EE(fn, context, once) {
        this.fn = fn;
        this.context = context;
        this.once = once || false;
    }
    /**
	 * Add a listener for a given event.
	 *
	 * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.
	 * @param {(String|Symbol)} event The event name.
	 * @param {Function} fn The listener function.
	 * @param {*} context The context to invoke the listener with.
	 * @param {Boolean} once Specify if the listener is a one-time listener.
	 * @returns {EventEmitter}
	 * @private
	 */ function addListener(emitter, event, fn, context, once) {
        if (typeof fn !== 'function') {
            throw new TypeError('The listener must be a function');
        }
        var listener = new EE(fn, context || emitter, once), evt = prefix ? prefix + event : event;
        if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;
        else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);
        else emitter._events[evt] = [
            emitter._events[evt],
            listener
        ];
        return emitter;
    }
    /**
	 * Clear event by name.
	 *
	 * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.
	 * @param {(String|Symbol)} evt The Event name.
	 * @private
	 */ function clearEvent(emitter, evt) {
        if (--emitter._eventsCount === 0) emitter._events = new Events();
        else delete emitter._events[evt];
    }
    /**
	 * Minimal `EventEmitter` interface that is molded against the Node.js
	 * `EventEmitter` interface.
	 *
	 * @constructor
	 * @public
	 */ function EventEmitter() {
        this._events = new Events();
        this._eventsCount = 0;
    }
    /**
	 * Return an array listing the events for which the emitter has registered
	 * listeners.
	 *
	 * @returns {Array}
	 * @public
	 */ EventEmitter.prototype.eventNames = function eventNames() {
        var names = [], events, name;
        if (this._eventsCount === 0) return names;
        for(name in events = this._events){
            if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);
        }
        if (Object.getOwnPropertySymbols) {
            return names.concat(Object.getOwnPropertySymbols(events));
        }
        return names;
    };
    /**
	 * Return the listeners registered for a given event.
	 *
	 * @param {(String|Symbol)} event The event name.
	 * @returns {Array} The registered listeners.
	 * @public
	 */ EventEmitter.prototype.listeners = function listeners(event) {
        var evt = prefix ? prefix + event : event, handlers = this._events[evt];
        if (!handlers) return [];
        if (handlers.fn) return [
            handlers.fn
        ];
        for(var i = 0, l = handlers.length, ee = new Array(l); i < l; i++){
            ee[i] = handlers[i].fn;
        }
        return ee;
    };
    /**
	 * Return the number of listeners listening to a given event.
	 *
	 * @param {(String|Symbol)} event The event name.
	 * @returns {Number} The number of listeners.
	 * @public
	 */ EventEmitter.prototype.listenerCount = function listenerCount(event) {
        var evt = prefix ? prefix + event : event, listeners = this._events[evt];
        if (!listeners) return 0;
        if (listeners.fn) return 1;
        return listeners.length;
    };
    /**
	 * Calls each of the listeners registered for a given event.
	 *
	 * @param {(String|Symbol)} event The event name.
	 * @returns {Boolean} `true` if the event had listeners, else `false`.
	 * @public
	 */ EventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {
        var evt = prefix ? prefix + event : event;
        if (!this._events[evt]) return false;
        var listeners = this._events[evt], len = arguments.length, args, i;
        if (listeners.fn) {
            if (listeners.once) this.removeListener(event, listeners.fn, undefined, true);
            switch(len){
                case 1:
                    return listeners.fn.call(listeners.context), true;
                case 2:
                    return listeners.fn.call(listeners.context, a1), true;
                case 3:
                    return listeners.fn.call(listeners.context, a1, a2), true;
                case 4:
                    return listeners.fn.call(listeners.context, a1, a2, a3), true;
                case 5:
                    return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;
                case 6:
                    return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;
            }
            for(i = 1, args = new Array(len - 1); i < len; i++){
                args[i - 1] = arguments[i];
            }
            listeners.fn.apply(listeners.context, args);
        } else {
            var length = listeners.length, j;
            for(i = 0; i < length; i++){
                if (listeners[i].once) this.removeListener(event, listeners[i].fn, undefined, true);
                switch(len){
                    case 1:
                        listeners[i].fn.call(listeners[i].context);
                        break;
                    case 2:
                        listeners[i].fn.call(listeners[i].context, a1);
                        break;
                    case 3:
                        listeners[i].fn.call(listeners[i].context, a1, a2);
                        break;
                    case 4:
                        listeners[i].fn.call(listeners[i].context, a1, a2, a3);
                        break;
                    default:
                        if (!args) for(j = 1, args = new Array(len - 1); j < len; j++){
                            args[j - 1] = arguments[j];
                        }
                        listeners[i].fn.apply(listeners[i].context, args);
                }
            }
        }
        return true;
    };
    /**
	 * Add a listener for a given event.
	 *
	 * @param {(String|Symbol)} event The event name.
	 * @param {Function} fn The listener function.
	 * @param {*} [context=this] The context to invoke the listener with.
	 * @returns {EventEmitter} `this`.
	 * @public
	 */ EventEmitter.prototype.on = function on(event, fn, context) {
        return addListener(this, event, fn, context, false);
    };
    /**
	 * Add a one-time listener for a given event.
	 *
	 * @param {(String|Symbol)} event The event name.
	 * @param {Function} fn The listener function.
	 * @param {*} [context=this] The context to invoke the listener with.
	 * @returns {EventEmitter} `this`.
	 * @public
	 */ EventEmitter.prototype.once = function once(event, fn, context) {
        return addListener(this, event, fn, context, true);
    };
    /**
	 * Remove the listeners of a given event.
	 *
	 * @param {(String|Symbol)} event The event name.
	 * @param {Function} fn Only remove the listeners that match this function.
	 * @param {*} context Only remove the listeners that have this context.
	 * @param {Boolean} once Only remove one-time listeners.
	 * @returns {EventEmitter} `this`.
	 * @public
	 */ EventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {
        var evt = prefix ? prefix + event : event;
        if (!this._events[evt]) return this;
        if (!fn) {
            clearEvent(this, evt);
            return this;
        }
        var listeners = this._events[evt];
        if (listeners.fn) {
            if (listeners.fn === fn && (!once || listeners.once) && (!context || listeners.context === context)) {
                clearEvent(this, evt);
            }
        } else {
            for(var i = 0, events = [], length = listeners.length; i < length; i++){
                if (listeners[i].fn !== fn || once && !listeners[i].once || context && listeners[i].context !== context) {
                    events.push(listeners[i]);
                }
            }
            //
            // Reset the array, or remove it completely if we have no more listeners.
            //
            if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;
            else clearEvent(this, evt);
        }
        return this;
    };
    /**
	 * Remove all listeners, or those of the specified event.
	 *
	 * @param {(String|Symbol)} [event] The event name.
	 * @returns {EventEmitter} `this`.
	 * @public
	 */ EventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {
        var evt;
        if (event) {
            evt = prefix ? prefix + event : event;
            if (this._events[evt]) clearEvent(this, evt);
        } else {
            this._events = new Events();
            this._eventsCount = 0;
        }
        return this;
    };
    //
    // Alias methods names because people roll like that.
    //
    EventEmitter.prototype.off = EventEmitter.prototype.removeListener;
    EventEmitter.prototype.addListener = EventEmitter.prototype.on;
    //
    // Expose the prefix.
    //
    EventEmitter.prefixed = prefix;
    //
    // Allow `EventEmitter` to be imported as module namespace.
    //
    EventEmitter.EventEmitter = EventEmitter;
    //
    // Expose the module.
    //
    {
        module.exports = EventEmitter;
    }
})(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$sphere$2d$viewer$2f$dist$2f$_virtual$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__module"]);
var eventemitter3Exports = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$sphere$2d$viewer$2f$dist$2f$_virtual$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__module"].exports;
var EventEmitter = /*@__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$sphere$2d$viewer$2f$dist$2f$_virtual$2f$_commonjsHelpers$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDefaultExportFromCjs"])(eventemitter3Exports);
;
 //# sourceMappingURL=index.js.map
}}),
"[project]/node_modules/@photo-sphere-viewer/markers-plugin/index.module.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*!
 * Photo Sphere Viewer / Markers Plugin 5.13.2
 * @copyright 2015-2025 Damien "Mistic" Sorel
 * @licence MIT (https://opensource.org/licenses/MIT)
 */ __turbopack_context__.s({
    "MarkersPlugin": (()=>MarkersPlugin),
    "events": (()=>events_exports)
});
// src/index.ts
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@photo-sphere-viewer/core/index.module.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/three/build/three.module.js [app-ssr] (ecmascript)");
var __defProp = Object.defineProperty;
var __export = (target, all)=>{
    for(var name in all)__defProp(target, name, {
        get: all[name],
        enumerable: true
    });
};
;
// src/events.ts
var events_exports = {};
__export(events_exports, {
    EnterMarkerEvent: ()=>EnterMarkerEvent,
    GotoMarkerDoneEvent: ()=>GotoMarkerDoneEvent,
    HideMarkersEvent: ()=>HideMarkersEvent,
    LeaveMarkerEvent: ()=>LeaveMarkerEvent,
    MarkerVisibilityEvent: ()=>MarkerVisibilityEvent,
    MarkersPluginEvent: ()=>MarkersPluginEvent,
    RenderMarkersListEvent: ()=>RenderMarkersListEvent,
    SelectMarkerEvent: ()=>SelectMarkerEvent,
    SelectMarkerListEvent: ()=>SelectMarkerListEvent,
    SetMarkersEvent: ()=>SetMarkersEvent,
    ShowMarkersEvent: ()=>ShowMarkersEvent,
    UnselectMarkerEvent: ()=>UnselectMarkerEvent
});
;
var MarkersPluginEvent = class extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TypedEvent"] {
};
var _MarkerVisibilityEvent = class _MarkerVisibilityEvent extends MarkersPluginEvent {
    /** @internal */ constructor(marker, visible){
        super(_MarkerVisibilityEvent.type);
        this.marker = marker;
        this.visible = visible;
    }
};
_MarkerVisibilityEvent.type = "marker-visibility";
var MarkerVisibilityEvent = _MarkerVisibilityEvent;
var _GotoMarkerDoneEvent = class _GotoMarkerDoneEvent extends MarkersPluginEvent {
    /** @internal */ constructor(marker){
        super(_GotoMarkerDoneEvent.type);
        this.marker = marker;
    }
};
_GotoMarkerDoneEvent.type = "goto-marker-done";
var GotoMarkerDoneEvent = _GotoMarkerDoneEvent;
var _LeaveMarkerEvent = class _LeaveMarkerEvent extends MarkersPluginEvent {
    /** @internal */ constructor(marker){
        super(_LeaveMarkerEvent.type);
        this.marker = marker;
    }
};
_LeaveMarkerEvent.type = "leave-marker";
var LeaveMarkerEvent = _LeaveMarkerEvent;
var _EnterMarkerEvent = class _EnterMarkerEvent extends MarkersPluginEvent {
    /** @internal */ constructor(marker){
        super(_EnterMarkerEvent.type);
        this.marker = marker;
    }
};
_EnterMarkerEvent.type = "enter-marker";
var EnterMarkerEvent = _EnterMarkerEvent;
var _SelectMarkerEvent = class _SelectMarkerEvent extends MarkersPluginEvent {
    /** @internal */ constructor(marker, doubleClick, rightClick){
        super(_SelectMarkerEvent.type);
        this.marker = marker;
        this.doubleClick = doubleClick;
        this.rightClick = rightClick;
    }
};
_SelectMarkerEvent.type = "select-marker";
var SelectMarkerEvent = _SelectMarkerEvent;
var _SelectMarkerListEvent = class _SelectMarkerListEvent extends MarkersPluginEvent {
    /** @internal */ constructor(marker){
        super(_SelectMarkerListEvent.type);
        this.marker = marker;
    }
};
_SelectMarkerListEvent.type = "select-marker-list";
var SelectMarkerListEvent = _SelectMarkerListEvent;
var _UnselectMarkerEvent = class _UnselectMarkerEvent extends MarkersPluginEvent {
    /** @internal */ constructor(marker){
        super(_UnselectMarkerEvent.type);
        this.marker = marker;
    }
};
_UnselectMarkerEvent.type = "unselect-marker";
var UnselectMarkerEvent = _UnselectMarkerEvent;
var _HideMarkersEvent = class _HideMarkersEvent extends MarkersPluginEvent {
    /** @internal */ constructor(){
        super(_HideMarkersEvent.type);
    }
};
_HideMarkersEvent.type = "hide-markers";
var HideMarkersEvent = _HideMarkersEvent;
var _SetMarkersEvent = class _SetMarkersEvent extends MarkersPluginEvent {
    /** @internal */ constructor(markers){
        super(_SetMarkersEvent.type);
        this.markers = markers;
    }
};
_SetMarkersEvent.type = "set-markers";
var SetMarkersEvent = _SetMarkersEvent;
var _ShowMarkersEvent = class _ShowMarkersEvent extends MarkersPluginEvent {
    /** @internal */ constructor(){
        super(_ShowMarkersEvent.type);
    }
};
_ShowMarkersEvent.type = "show-markers";
var ShowMarkersEvent = _ShowMarkersEvent;
var _RenderMarkersListEvent = class _RenderMarkersListEvent extends MarkersPluginEvent {
    /** @internal */ constructor(markers){
        super(_RenderMarkersListEvent.type);
        this.markers = markers;
    }
};
_RenderMarkersListEvent.type = "render-markers-list";
var RenderMarkersListEvent = _RenderMarkersListEvent;
;
// src/icons/pin.svg
var pin_default = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="10 9 81 81"><path fill="currentColor" d="M50.5 90S22.9 51.9 22.9 36.6 35.2 9 50.5 9s27.6 12.4 27.6 27.6S50.5 90 50.5 90zm0-66.3c-6.1 0-11 4.9-11 11s4.9 11 11 11 11-4.9 11-11-4.9-11-11-11z"/><!--Created by Rohith M S from the Noun Project--></svg>\n';
// src/MarkersButton.ts
var MarkersButton = class extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AbstractButton"] {
    constructor(navbar){
        super(navbar, {
            className: "psv-markers-button",
            icon: pin_default,
            hoverScale: true,
            collapsable: true,
            tabbable: true
        });
        this.plugin = this.viewer.getPlugin("markers");
        if (this.plugin) {
            this.plugin.addEventListener(ShowMarkersEvent.type, this);
            this.plugin.addEventListener(HideMarkersEvent.type, this);
            this.toggleActive(true);
        }
    }
    destroy() {
        if (this.plugin) {
            this.plugin.removeEventListener(ShowMarkersEvent.type, this);
            this.plugin.removeEventListener(HideMarkersEvent.type, this);
        }
        super.destroy();
    }
    isSupported() {
        return !!this.plugin;
    }
    handleEvent(e) {
        if (e instanceof ShowMarkersEvent) {
            this.toggleActive(true);
        } else if (e instanceof HideMarkersEvent) {
            this.toggleActive(false);
        }
    }
    onClick() {
        this.plugin.toggleAllMarkers();
    }
};
MarkersButton.id = "markers";
;
;
// src/icons/pin-list.svg
var pin_list_default = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="9 9 81 81"><path fill="currentColor" d="M37.5 90S9.9 51.9 9.9 36.6 22.2 9 37.5 9s27.6 12.4 27.6 27.6S37.5 90 37.5 90zm0-66.3c-6.1 0-11 4.9-11 11s4.9 11 11 11 11-4.9 11-11-4.9-11-11-11zM86.7 55H70c-1.8 0-3.3-1.5-3.3-3.3s1.5-3.3 3.3-3.3h16.7c1.8 0 3.3 1.5 3.3 3.3S88.5 55 86.7 55zm0-25h-15a3.3 3.3 0 0 1-3.3-3.3c0-1.8 1.5-3.3 3.3-3.3h15c1.8 0 3.3 1.5 3.3 3.3 0 1.8-1.5 3.3-3.3 3.3zM56.5 73h30c1.8 0 3.3 1.5 3.3 3.3 0 1.8-1.5 3.3-3.3 3.3h-30a3.3 3.3 0 0 1-3.3-3.3 3.2 3.2 0 0 1 3.3-3.3z"/><!--Created by Rohith M S from the Noun Project--></svg>\n';
// src/constants.ts
var SVG_NS = "http://www.w3.org/2000/svg";
var MARKER_DATA = "psvMarker";
var MARKER_DATA_KEY = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].dasherize(MARKER_DATA);
var ID_PANEL_MARKER = "marker";
var ID_PANEL_MARKERS_LIST = "markersList";
var DEFAULT_HOVER_SCALE = {
    amount: 2,
    duration: 100,
    easing: "linear"
};
var MARKERS_LIST_TEMPLATE = (markers, title)=>`
<div class="psv-panel-menu psv-panel-menu--stripped">
    <h1 class="psv-panel-menu-title">${pin_list_default} ${title}</h1>
    <ul class="psv-panel-menu-list">
    ${markers.map((marker)=>`
        <li data-${MARKER_DATA_KEY}="${marker.id}" class="psv-panel-menu-item" tabindex="0">
          ${marker.type === "image" ? `<span class="psv-panel-menu-item-icon"><img src="${marker.definition}"/></span>` : ""}
          <span class="psv-panel-menu-item-label">${marker.getListContent()}</span>
        </li>
    `).join("")}
    </ul>
</div>
`;
// src/MarkersListButton.ts
var MarkersListButton = class extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AbstractButton"] {
    constructor(navbar){
        super(navbar, {
            className: " psv-markers-list-button",
            icon: pin_list_default,
            hoverScale: true,
            collapsable: true,
            tabbable: true
        });
        this.plugin = this.viewer.getPlugin("markers");
        if (this.plugin) {
            this.viewer.addEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ShowPanelEvent.type, this);
            this.viewer.addEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].HidePanelEvent.type, this);
        }
    }
    destroy() {
        this.viewer.removeEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ShowPanelEvent.type, this);
        this.viewer.removeEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].HidePanelEvent.type, this);
        super.destroy();
    }
    isSupported() {
        return !!this.plugin;
    }
    handleEvent(e) {
        if (e instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ShowPanelEvent) {
            this.toggleActive(e.panelId === ID_PANEL_MARKERS_LIST);
        } else if (e instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].HidePanelEvent) {
            this.toggleActive(false);
        }
    }
    onClick() {
        this.plugin.toggleMarkersList();
    }
};
MarkersListButton.id = "markersList";
;
;
;
;
var _position = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"]();
var _quaternion = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Quaternion"]();
var _scale = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"]();
var CSS3DObject = class extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Object3D"] {
    /**
   * Constructs a new CSS3D object.
   *
   * @param {DOMElement} [element] - The DOM element.
   */ constructor(element = document.createElement("div")){
        super();
        this.isCSS3DObject = true;
        this.element = element;
        this.element.style.position = "absolute";
        this.element.style.pointerEvents = "auto";
        this.element.style.userSelect = "none";
        this.element.setAttribute("draggable", false);
        this.addEventListener("removed", function() {
            this.traverse(function(object) {
                if (object.element instanceof object.element.ownerDocument.defaultView.Element && object.element.parentNode !== null) {
                    object.element.remove();
                }
            });
        });
    }
    copy(source, recursive) {
        super.copy(source, recursive);
        this.element = source.element.cloneNode(true);
        return this;
    }
};
var _matrix = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Matrix4"]();
var _matrix2 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Matrix4"]();
var CSS3DRenderer = class {
    /**
   * Constructs a new CSS3D renderer.
   *
   * @param {CSS3DRenderer~Parameters} [parameters] - The parameters.
   */ constructor(parameters = {}){
        const _this = this;
        let _width, _height;
        let _widthHalf, _heightHalf;
        const cache = {
            camera: {
                style: ""
            },
            objects: /* @__PURE__ */ new WeakMap()
        };
        const domElement = parameters.element !== void 0 ? parameters.element : document.createElement("div");
        domElement.style.overflow = "hidden";
        this.domElement = domElement;
        const viewElement = document.createElement("div");
        viewElement.style.transformOrigin = "0 0";
        viewElement.style.pointerEvents = "none";
        domElement.appendChild(viewElement);
        const cameraElement = document.createElement("div");
        cameraElement.style.transformStyle = "preserve-3d";
        viewElement.appendChild(cameraElement);
        this.getSize = function() {
            return {
                width: _width,
                height: _height
            };
        };
        this.render = function(scene, camera) {
            const fov = camera.projectionMatrix.elements[5] * _heightHalf;
            if (camera.view && camera.view.enabled) {
                viewElement.style.transform = `translate( ${-camera.view.offsetX * (_width / camera.view.width)}px, ${-camera.view.offsetY * (_height / camera.view.height)}px )`;
                viewElement.style.transform += `scale( ${camera.view.fullWidth / camera.view.width}, ${camera.view.fullHeight / camera.view.height} )`;
            } else {
                viewElement.style.transform = "";
            }
            if (scene.matrixWorldAutoUpdate === true) scene.updateMatrixWorld();
            if (camera.parent === null && camera.matrixWorldAutoUpdate === true) camera.updateMatrixWorld();
            let tx, ty;
            if (camera.isOrthographicCamera) {
                tx = -(camera.right + camera.left) / 2;
                ty = (camera.top + camera.bottom) / 2;
            }
            const scaleByViewOffset = camera.view && camera.view.enabled ? camera.view.height / camera.view.fullHeight : 1;
            const cameraCSSMatrix = camera.isOrthographicCamera ? `scale( ${scaleByViewOffset} )scale(` + fov + ")translate(" + epsilon(tx) + "px," + epsilon(ty) + "px)" + getCameraCSSMatrix(camera.matrixWorldInverse) : `scale( ${scaleByViewOffset} )translateZ(` + fov + "px)" + getCameraCSSMatrix(camera.matrixWorldInverse);
            const perspective = camera.isPerspectiveCamera ? "perspective(" + fov + "px) " : "";
            const style = perspective + cameraCSSMatrix + "translate(" + _widthHalf + "px," + _heightHalf + "px)";
            if (cache.camera.style !== style) {
                cameraElement.style.transform = style;
                cache.camera.style = style;
            }
            renderObject(scene, scene, camera, cameraCSSMatrix);
        };
        this.setSize = function(width, height) {
            _width = width;
            _height = height;
            _widthHalf = _width / 2;
            _heightHalf = _height / 2;
            domElement.style.width = width + "px";
            domElement.style.height = height + "px";
            viewElement.style.width = width + "px";
            viewElement.style.height = height + "px";
            cameraElement.style.width = width + "px";
            cameraElement.style.height = height + "px";
        };
        function epsilon(value) {
            return Math.abs(value) < 1e-10 ? 0 : value;
        }
        function getCameraCSSMatrix(matrix) {
            const elements = matrix.elements;
            return "matrix3d(" + epsilon(elements[0]) + "," + epsilon(-elements[1]) + "," + epsilon(elements[2]) + "," + epsilon(elements[3]) + "," + epsilon(elements[4]) + "," + epsilon(-elements[5]) + "," + epsilon(elements[6]) + "," + epsilon(elements[7]) + "," + epsilon(elements[8]) + "," + epsilon(-elements[9]) + "," + epsilon(elements[10]) + "," + epsilon(elements[11]) + "," + epsilon(elements[12]) + "," + epsilon(-elements[13]) + "," + epsilon(elements[14]) + "," + epsilon(elements[15]) + ")";
        }
        function getObjectCSSMatrix(matrix) {
            const elements = matrix.elements;
            const matrix3d = "matrix3d(" + epsilon(elements[0]) + "," + epsilon(elements[1]) + "," + epsilon(elements[2]) + "," + epsilon(elements[3]) + "," + epsilon(-elements[4]) + "," + epsilon(-elements[5]) + "," + epsilon(-elements[6]) + "," + epsilon(-elements[7]) + "," + epsilon(elements[8]) + "," + epsilon(elements[9]) + "," + epsilon(elements[10]) + "," + epsilon(elements[11]) + "," + epsilon(elements[12]) + "," + epsilon(elements[13]) + "," + epsilon(elements[14]) + "," + epsilon(elements[15]) + ")";
            return "translate(-50%,-50%)" + matrix3d;
        }
        function hideObject(object) {
            if (object.isCSS3DObject) object.element.style.display = "none";
            for(let i = 0, l = object.children.length; i < l; i++){
                hideObject(object.children[i]);
            }
        }
        function renderObject(object, scene, camera, cameraCSSMatrix) {
            if (object.visible === false) {
                hideObject(object);
                return;
            }
            if (object.isCSS3DObject) {
                const visible = object.layers.test(camera.layers) === true;
                const element = object.element;
                element.style.display = visible === true ? "" : "none";
                if (visible === true) {
                    object.onBeforeRender(_this, scene, camera);
                    let style;
                    if (object.isCSS3DSprite) {
                        _matrix.copy(camera.matrixWorldInverse);
                        _matrix.transpose();
                        if (object.rotation2D !== 0) _matrix.multiply(_matrix2.makeRotationZ(object.rotation2D));
                        object.matrixWorld.decompose(_position, _quaternion, _scale);
                        _matrix.setPosition(_position);
                        _matrix.scale(_scale);
                        _matrix.elements[3] = 0;
                        _matrix.elements[7] = 0;
                        _matrix.elements[11] = 0;
                        _matrix.elements[15] = 1;
                        style = getObjectCSSMatrix(_matrix);
                    } else {
                        style = getObjectCSSMatrix(object.matrixWorld);
                    }
                    const cachedObject = cache.objects.get(object);
                    if (cachedObject === void 0 || cachedObject.style !== style) {
                        element.style.transform = style;
                        const objectData = {
                            style
                        };
                        cache.objects.set(object, objectData);
                    }
                    if (element.parentNode !== cameraElement) {
                        cameraElement.appendChild(element);
                    }
                    object.onAfterRender(_this, scene, camera);
                }
            }
            for(let i = 0, l = object.children.length; i < l; i++){
                renderObject(object.children[i], scene, camera, cameraCSSMatrix);
            }
        }
    }
};
// src/CSS3DContainer.ts
var CSS3DContainer = class {
    constructor(viewer){
        this.viewer = viewer;
        this.element = document.createElement("div");
        this.element.className = "psv-markers-css3d-container";
        this.renderer = new CSS3DRenderer({
            element: this.element
        });
        this.scene = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Scene"]();
        this.intersectionObserver = new IntersectionObserver((entries)=>{
            entries.forEach((entry)=>{
                const marker = entry.target[MARKER_DATA];
                if (marker.config.visible) {
                    marker.viewportIntersection = entry.isIntersecting;
                }
            });
        }, {
            root: this.element
        });
        viewer.addEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ReadyEvent.type, this, {
            once: true
        });
        viewer.addEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].SizeUpdatedEvent.type, this);
        viewer.addEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].RenderEvent.type, this);
    }
    handleEvent(e) {
        switch(e.type){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ReadyEvent.type:
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].SizeUpdatedEvent.type:
                this.updateSize();
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].RenderEvent.type:
                this.render();
                break;
        }
    }
    destroy() {
        this.viewer.removeEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ReadyEvent.type, this);
        this.viewer.removeEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].SizeUpdatedEvent.type, this);
        this.viewer.removeEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].RenderEvent.type, this);
        this.intersectionObserver.disconnect();
    }
    updateSize() {
        const size = this.viewer.getSize();
        this.renderer.setSize(size.width, size.height);
    }
    render() {
        this.renderer.render(this.scene, this.viewer.renderer.camera);
    }
    addObject(marker) {
        this.scene.add(marker.threeElement);
        this.intersectionObserver.observe(marker.domElement);
    }
    removeObject(marker) {
        this.scene.remove(marker.threeElement);
        this.intersectionObserver.unobserve(marker.domElement);
    }
};
;
var MarkerType = /* @__PURE__ */ ((MarkerType3)=>{
    MarkerType3["image"] = "image";
    MarkerType3["html"] = "html";
    MarkerType3["element"] = "element";
    MarkerType3["imageLayer"] = "imageLayer";
    MarkerType3["videoLayer"] = "videoLayer";
    MarkerType3["elementLayer"] = "elementLayer";
    MarkerType3["polygon"] = "polygon";
    MarkerType3["polygonPixels"] = "polygonPixels";
    MarkerType3["polyline"] = "polyline";
    MarkerType3["polylinePixels"] = "polylinePixels";
    MarkerType3["square"] = "square";
    MarkerType3["rect"] = "rect";
    MarkerType3["circle"] = "circle";
    MarkerType3["ellipse"] = "ellipse";
    MarkerType3["path"] = "path";
    return MarkerType3;
})(MarkerType || {});
function getMarkerType(config, allowNone = false) {
    const found = [];
    Object.keys(MarkerType).forEach((type)=>{
        if (config[type]) {
            found.push(type);
        }
    });
    if (found.length === 0 && !allowNone) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PSVError"](`missing marker content, either ${Object.keys(MarkerType).join(", ")}`);
    } else if (found.length > 1) {
        throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PSVError"](`multiple marker content, either ${Object.keys(MarkerType).join(", ")}`);
    }
    return found[0];
}
;
;
;
;
var Marker = class {
    constructor(viewer, plugin, config){
        this.viewer = viewer;
        this.plugin = plugin;
        /** @internal */ this.state = {
            anchor: null,
            visible: false,
            staticTooltip: false,
            position: null,
            position2D: null,
            positions3D: null,
            size: null
        };
        if (!config.id) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PSVError"]("missing marker id");
        }
        this.type = getMarkerType(config);
        this.createElement();
        this.update(config);
    }
    get id() {
        return this.config.id;
    }
    get data() {
        return this.config.data;
    }
    get domElement() {
        return null;
    }
    get threeElement() {
        return null;
    }
    get video() {
        return null;
    }
    /**
   * @internal
   */ destroy() {
        delete this.viewer;
        delete this.plugin;
        delete this.element;
        this.hideTooltip();
    }
    /**
   * Checks if it is a 3D marker (imageLayer, videoLayer)
   */ is3d() {
        return false;
    }
    /**
   * Checks if it is a normal marker (image, html, element)
   */ isNormal() {
        return false;
    }
    /**
   * Checks if it is a polygon/polyline marker
   */ isPoly() {
        return false;
    }
    /**
   * Checks if it is an SVG marker
   */ isSvg() {
        return false;
    }
    /**
   * Checks if it is an CSS3D marker
   */ isCss3d() {
        return false;
    }
    /**
   * Updates the marker with new properties
   * @throws {@link PSVError} if the configuration is invalid
   * @internal
   */ update(config) {
        const newType = getMarkerType(config, true);
        if (newType !== void 0 && newType !== this.type) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PSVError"](`cannot change marker ${config.id} type`);
        }
        this.config = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].deepmerge(this.config, config);
        if (typeof this.config.tooltip === "string") {
            this.config.tooltip = {
                content: this.config.tooltip
            };
        }
        if (this.config.tooltip && !this.config.tooltip.trigger) {
            this.config.tooltip.trigger = "hover";
        }
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].isNil(this.config.visible)) {
            this.config.visible = true;
        }
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].isNil(this.config.zIndex)) {
            this.config.zIndex = 1;
        }
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].isNil(this.config.opacity)) {
            this.config.opacity = 1;
        }
        if (this.config.rotation) {
            const rot = this.config.rotation;
            if (typeof rot === "object") {
                this.config.rotation = {
                    yaw: rot.yaw ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].parseAngle(rot.yaw, true, false) : 0,
                    pitch: rot.pitch ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].parseAngle(rot.pitch, true, false) : 0,
                    roll: rot.roll ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].parseAngle(rot.roll, true, false) : 0
                };
            } else {
                this.config.rotation = {
                    yaw: 0,
                    pitch: 0,
                    roll: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].parseAngle(rot, true, false)
                };
            }
        } else {
            this.config.rotation = {
                yaw: 0,
                pitch: 0,
                roll: 0
            };
        }
        this.state.anchor = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].parsePoint(this.config.anchor);
    }
    /**
   * Returns the markers list content for the marker, it can be either :
   * - the `listContent`
   * - the `tooltip`
   * - the `html`
   * - the `id`
   * @internal
   */ getListContent() {
        if (this.config.listContent) {
            return this.config.listContent;
        } else if (this.config.tooltip?.content) {
            return this.config.tooltip.content;
        } else if (this.config.html) {
            return this.config.html;
        } else {
            return this.id;
        }
    }
    /**
   * Display the tooltip of this marker
   * @internal
   */ showTooltip(clientX, clientY, forceUpdate = false) {
        if (this.state.visible && this.config.tooltip?.content && this.state.position2D) {
            const config = {
                ...this.config.tooltip,
                style: {
                    // prevents conflicts with tooltip tracking
                    pointerEvents: this.state.staticTooltip ? "auto" : "none"
                },
                data: this,
                top: 0,
                left: 0
            };
            if (this.isPoly() || this.is3d() || this.isCss3d()) {
                if (clientX || clientY) {
                    const viewerPos = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].getPosition(this.viewer.container);
                    config.top = clientY - viewerPos.y + 10;
                    config.left = clientX - viewerPos.x;
                    config.box = {
                        // separate the tooltip from the cursor
                        width: 20,
                        height: 20
                    };
                } else {
                    config.top = this.state.position2D.y;
                    config.left = this.state.position2D.x;
                }
            } else {
                const position = this.viewer.dataHelper.vector3ToViewerCoords(this.state.positions3D[0]);
                let width = this.state.size.width;
                let height = this.state.size.height;
                if (this.config.hoverScale && !this.state.staticTooltip) {
                    width *= this.config.hoverScale.amount;
                    height *= this.config.hoverScale.amount;
                }
                config.top = position.y - height * this.state.anchor.y + height / 2;
                config.left = position.x - width * this.state.anchor.x + width / 2;
                config.box = {
                    width,
                    height
                };
            }
            if (this.tooltip) {
                if (forceUpdate) {
                    this.tooltip.update(this.config.tooltip.content, config);
                } else {
                    this.tooltip.move(config);
                }
            } else {
                this.tooltip = this.viewer.createTooltip(config);
            }
        }
    }
    /**
   * Hides the tooltip of this marker
   * @internal
   */ hideTooltip() {
        if (this.tooltip) {
            this.tooltip.hide();
            this.tooltip = null;
        }
    }
};
// src/markers/AbstractDomMarker.ts
var AbstractDomMarker = class extends Marker {
    get domElement() {
        return this.element;
    }
    constructor(viewer, plugin, config){
        super(viewer, plugin, config);
    }
    afterCreateElement() {
        this.element[MARKER_DATA] = this;
    }
    destroy() {
        delete this.element[MARKER_DATA];
        super.destroy();
    }
    update(config) {
        super.update(config);
        const element = this.domElement;
        element.id = `psv-marker-${this.config.id}`;
        element.setAttribute("class", "psv-marker");
        if (this.state.visible) {
            element.classList.add("psv-marker--visible");
        }
        if (this.config.tooltip) {
            element.classList.add("psv-marker--has-tooltip");
        }
        if (this.config.content) {
            element.classList.add("psv-marker--has-content");
        }
        if (this.config.className) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].addClasses(element, this.config.className);
        }
        element.style.opacity = `${this.config.opacity}`;
        element.style.zIndex = `${30 + this.config.zIndex}`;
        if (this.config.style) {
            Object.assign(element.style, this.config.style);
        }
    }
};
// src/markers/AbstractStandardMarker.ts
var AbstractStandardMarker = class extends AbstractDomMarker {
    constructor(viewer, plugin, config){
        super(viewer, plugin, config);
    }
    afterCreateElement() {
        super.afterCreateElement();
        this.domElement.addEventListener("transitionend", ()=>{
            this.domElement.style.transition = "";
        });
    }
    render({ viewerPosition, zoomLevel, hoveringMarker }) {
        this.__updateSize();
        const position = this.viewer.dataHelper.vector3ToViewerCoords(this.state.positions3D[0]);
        position.x -= this.state.size.width * this.state.anchor.x;
        position.y -= this.state.size.height * this.state.anchor.y;
        const isVisible = this.state.positions3D[0].dot(this.viewer.state.direction) > 0 && position.x + this.state.size.width >= 0 && position.x - this.state.size.width <= this.viewer.state.size.width && position.y + this.state.size.height >= 0 && position.y - this.state.size.height <= this.viewer.state.size.height;
        if (isVisible) {
            this.domElement.style.translate = `${position.x}px ${position.y}px 0px`;
            this.applyScale({
                zoomLevel,
                viewerPosition,
                mouseover: this === hoveringMarker
            });
            return position;
        } else {
            return null;
        }
    }
    update(config) {
        super.update(config);
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].isExtendedPosition(this.config.position)) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PSVError"](`missing marker ${this.id} position`);
        }
        try {
            this.state.position = this.viewer.dataHelper.cleanPosition(this.config.position);
        } catch (e) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PSVError"](`invalid marker ${this.id} position`, e);
        }
        this.state.positions3D = [
            this.viewer.dataHelper.sphericalCoordsToVector3(this.state.position)
        ];
        const element = this.domElement;
        element.classList.add("psv-marker--normal");
        if (this.config.scale && Array.isArray(this.config.scale)) {
            this.config.scale = {
                zoom: this.config.scale
            };
        }
        if (typeof this.config.hoverScale === "boolean") {
            this.config.hoverScale = this.config.hoverScale ? this.plugin.config.defaultHoverScale || DEFAULT_HOVER_SCALE : null;
        } else if (typeof this.config.hoverScale === "number") {
            this.config.hoverScale = {
                amount: this.config.hoverScale
            };
        } else if (!this.config.hoverScale) {
            this.config.hoverScale = this.plugin.config.defaultHoverScale;
        }
        if (this.config.hoverScale) {
            this.config.hoverScale = {
                ...this.plugin.config.defaultHoverScale,
                ...this.config.hoverScale
            };
        }
        element.style.rotate = this.config.rotation.roll !== 0 ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MathUtils"].radToDeg(this.config.rotation.roll) + "deg" : null;
        element.style.transformOrigin = `${this.state.anchor.x * 100}% ${this.state.anchor.y * 100}%`;
    }
    /**
   * Computes the real size of a marker
   * @description This is done by removing all it's transformations (if any) and making it visible
   * before querying its bounding rect
   */ __updateSize() {
        if (!this.needsUpdateSize) {
            return;
        }
        const element = this.domElement;
        const makeTransparent = !this.state.visible || !this.state.size;
        if (makeTransparent) {
            element.classList.add("psv-marker--transparent");
        }
        if (this.isSvg()) {
            const rect = element.firstElementChild.getBoundingClientRect();
            this.state.size = {
                width: rect.width,
                height: rect.height
            };
        } else {
            this.state.size = {
                width: element.offsetWidth,
                height: element.offsetHeight
            };
        }
        if (makeTransparent) {
            element.classList.remove("psv-marker--transparent");
        }
        if (this.isSvg()) {
            element.style.width = this.state.size.width + "px";
            element.style.height = this.state.size.height + "px";
        }
        if (this.type !== "element" /* element */ ) {
            this.needsUpdateSize = false;
        }
    }
    /**
   * Computes and applies the scale to the marker
   */ applyScale({ zoomLevel, viewerPosition, mouseover }) {
        if (mouseover !== null && this.config.hoverScale) {
            this.domElement.style.transition = `scale ${this.config.hoverScale.duration}ms ${this.config.hoverScale.easing}`;
        }
        let scale = 1;
        if (typeof this.config.scale === "function") {
            scale = this.config.scale(zoomLevel, viewerPosition);
        } else if (this.config.scale) {
            if (Array.isArray(this.config.scale.zoom)) {
                const [min, max] = this.config.scale.zoom;
                scale *= min + (max - min) * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CONSTANTS"].EASINGS.inQuad(zoomLevel / 100);
            }
            if (Array.isArray(this.config.scale.yaw)) {
                const [min, max] = this.config.scale.yaw;
                const halfFov = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MathUtils"].degToRad(this.viewer.state.hFov) / 2;
                const arc = Math.abs(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].getShortestArc(this.state.position.yaw, viewerPosition.yaw));
                scale *= max + (min - max) * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CONSTANTS"].EASINGS.outQuad(Math.max(0, (halfFov - arc) / halfFov));
            }
        }
        if (mouseover && this.config.hoverScale) {
            scale *= this.config.hoverScale.amount;
        }
        this.domElement.style.scale = `${scale}`;
    }
};
;
;
;
// ../shared/shaders/chromaKey.fragment.glsl
var chromaKey_fragment_default = "// https://www.8thwall.com/playground/chromakey-threejs\n\nuniform sampler2D map;\nuniform float alpha;\nuniform bool keying;\nuniform vec3 color;\nuniform float similarity;\nuniform float smoothness;\nuniform float spill;\n\nvarying vec2 vUv;\n\nvec2 RGBtoUV(vec3 rgb) {\n    return vec2(\n        rgb.r * -0.169 + rgb.g * -0.331 + rgb.b *  0.5    + 0.5,\n        rgb.r *  0.5   + rgb.g * -0.419 + rgb.b * -0.081  + 0.5\n    );\n}\n\nvoid main(void) {\n    gl_FragColor = texture2D(map, vUv);\n\n    if (keying) {\n        float chromaDist = distance(RGBtoUV(gl_FragColor.rgb), RGBtoUV(color));\n\n        float baseMask = chromaDist - similarity;\n        float fullMask = pow(clamp(baseMask / smoothness, 0., 1.), 1.5);\n        gl_FragColor.a *= fullMask * alpha;\n\n        float spillVal = pow(clamp(baseMask / spill, 0., 1.), 1.5);\n        float desat = clamp(gl_FragColor.r * 0.2126 + gl_FragColor.g * 0.7152 + gl_FragColor.b * 0.0722, 0., 1.);\n        gl_FragColor.rgb = mix(vec3(desat, desat, desat), gl_FragColor.rgb, spillVal);\n    } else {\n        gl_FragColor.a *= alpha;\n    }\n}\n";
// ../shared/shaders/chromaKey.vertex.glsl
var chromaKey_vertex_default = "varying vec2 vUv;\nuniform vec2 repeat;\nuniform vec2 offset;\n\nvoid main() {\n    vUv = uv * repeat + offset;\n    gl_Position = projectionMatrix *  modelViewMatrix * vec4( position, 1.0 );\n}\n";
// ../shared/ChromaKeyMaterial.ts
var ChromaKeyMaterial = class extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ShaderMaterial"] {
    get map() {
        return this.uniforms.map.value;
    }
    set map(map) {
        this.uniforms.map.value = map;
    }
    set alpha(alpha) {
        this.uniforms.alpha.value = alpha;
    }
    get offset() {
        return this.uniforms.offset.value;
    }
    get repeat() {
        return this.uniforms.repeat.value;
    }
    set chromaKey(chromaKey) {
        this.uniforms.keying.value = chromaKey?.enabled === true;
        if (chromaKey?.enabled) {
            if (typeof chromaKey.color === "object" && "r" in chromaKey.color) {
                this.uniforms.color.value.set(chromaKey.color.r / 255, chromaKey.color.g / 255, chromaKey.color.b / 255);
            } else {
                this.uniforms.color.value.set(chromaKey.color ?? 65280);
            }
            this.uniforms.similarity.value = chromaKey.similarity ?? 0.2;
            this.uniforms.smoothness.value = chromaKey.smoothness ?? 0.2;
        }
    }
    constructor(params){
        super({
            transparent: true,
            depthTest: false,
            depthWrite: false,
            uniforms: {
                map: {
                    value: params?.map
                },
                repeat: {
                    value: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector2"](1, 1)
                },
                offset: {
                    value: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector2"](0, 0)
                },
                alpha: {
                    value: params?.alpha ?? 1
                },
                keying: {
                    value: false
                },
                color: {
                    value: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Color"](65280)
                },
                similarity: {
                    value: 0.2
                },
                smoothness: {
                    value: 0.2
                },
                spill: {
                    value: 0.1
                }
            },
            vertexShader: chromaKey_vertex_default,
            fragmentShader: chromaKey_fragment_default
        });
        this.chromaKey = params?.chromaKey;
    }
};
// ../shared/video-utils.ts
function createVideo({ src, withCredentials, muted, autoplay }) {
    const video = document.createElement("video");
    video.crossOrigin = withCredentials ? "use-credentials" : "anonymous";
    video.loop = true;
    video.playsInline = true;
    video.autoplay = autoplay;
    video.muted = muted;
    video.preload = "metadata";
    if (src instanceof MediaStream) {
        video.srcObject = src;
    } else {
        video.src = src;
    }
    return video;
}
;
;
function greatArcIntermediaryPoint(p1, p2, f) {
    const [λ1, φ1] = p1;
    const [λ2, φ2] = p2;
    const r = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].greatArcDistance(p1, p2);
    const a = Math.sin((1 - f) * r) / Math.sin(r);
    const b = Math.sin(f * r) / Math.sin(r);
    const x = a * Math.cos(φ1) * Math.cos(λ1) + b * Math.cos(φ2) * Math.cos(λ2);
    const y = a * Math.cos(φ1) * Math.sin(λ1) + b * Math.cos(φ2) * Math.sin(λ2);
    const z = a * Math.sin(φ1) + b * Math.sin(φ2);
    return [
        Math.atan2(y, x),
        Math.atan2(z, Math.sqrt(x * x + y * y))
    ];
}
function getPolygonCoherentPoints(points) {
    const workPoints = [
        points[0]
    ];
    let k = 0;
    for(let i = 1; i < points.length; i++){
        const d = points[i - 1][0] - points[i][0];
        if (d > Math.PI) {
            k += 1;
        } else if (d < -Math.PI) {
            k -= 1;
        }
        workPoints.push([
            points[i][0] + k * 2 * Math.PI,
            points[i][1]
        ]);
    }
    return workPoints;
}
function getPolygonCenter(polygon) {
    const points = getPolygonCoherentPoints(polygon);
    const sum = points.reduce((intermediary, point)=>[
            intermediary[0] + point[0],
            intermediary[1] + point[1]
        ]);
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].parseAngle(sum[0] / polygon.length),
        sum[1] / polygon.length
    ];
}
function getPolylineCenter(polyline) {
    const points = getPolygonCoherentPoints(polyline);
    let length = 0;
    const lengths = [];
    for(let i = 0; i < points.length - 1; i++){
        const l = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].greatArcDistance(points[i], points[i + 1]) * __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CONSTANTS"].SPHERE_RADIUS;
        lengths.push(l);
        length += l;
    }
    let consumed = 0;
    for(let j = 0; j < points.length - 1; j++){
        if (consumed + lengths[j] > length / 2) {
            const r = (length / 2 - consumed) / lengths[j];
            return greatArcIntermediaryPoint(points[j], points[j + 1], r);
        }
        consumed += lengths[j];
    }
    return points[Math.round(points.length / 2)];
}
var C = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"]();
var N = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"]();
var V = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"]();
var X = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"]();
var Y = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"]();
var A = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"]();
function getGreatCircleIntersection(P1, P2, direction) {
    C.copy(direction).normalize();
    N.crossVectors(P1, P2).normalize();
    V.crossVectors(N, P1).normalize();
    X.copy(P1).multiplyScalar(-C.dot(V));
    Y.copy(V).multiplyScalar(C.dot(P1));
    const H = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"]().addVectors(X, Y).normalize();
    A.crossVectors(H, C);
    return H.applyAxisAngle(A, 0.01).multiplyScalar(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CONSTANTS"].SPHERE_RADIUS);
}
// src/markers/Marker3D.ts
var Marker3D = class extends Marker {
    get threeElement() {
        return this.element;
    }
    get threeMesh() {
        return this.threeElement.children[0];
    }
    get video() {
        if (this.type === "videoLayer" /* videoLayer */ ) {
            return this.threeMesh.material.map.image;
        } else {
            return null;
        }
    }
    constructor(viewer, plugin, config){
        super(viewer, plugin, config);
    }
    is3d() {
        return true;
    }
    createElement() {
        const material = new ChromaKeyMaterial({
            alpha: 0
        });
        const geometry = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PlaneGeometry"](1, 1);
        const mesh = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Mesh"](geometry, material);
        mesh.userData = {
            [MARKER_DATA]: this
        };
        Object.defineProperty(mesh, "visible", {
            enumerable: true,
            get: function() {
                return this.userData[MARKER_DATA].config.visible;
            },
            set: function(visible) {
                this.userData[MARKER_DATA].config.visible = visible;
            }
        });
        this.element = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Group"]().add(mesh);
        if (this.type === "videoLayer" /* videoLayer */ ) {
            this.viewer.needsContinuousUpdate(true);
        }
    }
    destroy() {
        delete this.threeMesh.userData[MARKER_DATA];
        if (this.type === "videoLayer" /* videoLayer */ ) {
            this.video.pause();
            this.viewer.needsContinuousUpdate(false);
        }
        super.destroy();
    }
    render() {
        if (this.viewer.renderer.isObjectVisible(this.threeMesh)) {
            return this.viewer.dataHelper.sphericalCoordsToViewerCoords(this.state.position);
        } else {
            return null;
        }
    }
    update(config) {
        super.update(config);
        const mesh = this.threeMesh;
        const group = mesh.parent;
        const material = mesh.material;
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].isExtendedPosition(this.config.position)) {
            try {
                this.state.position = this.viewer.dataHelper.cleanPosition(this.config.position);
            } catch (e) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PSVError"](`invalid marker ${this.id} position`, e);
            }
            if (!this.config.size) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PSVError"](`missing marker ${this.id} size`);
            }
            this.state.size = this.config.size;
            mesh.scale.set(this.config.size.width / 100, this.config.size.height / 100, 1);
            mesh.position.set(mesh.scale.x * (0.5 - this.state.anchor.x), mesh.scale.y * (this.state.anchor.y - 0.5), 0);
            mesh.rotation.set(0, 0, 0);
            this.viewer.dataHelper.sphericalCoordsToVector3(this.state.position, group.position);
            group.lookAt(0, group.position.y, 0);
            mesh.rotateY(-this.config.rotation.yaw);
            mesh.rotateX(-this.config.rotation.pitch);
            mesh.rotateZ(-this.config.rotation.roll);
            const p = mesh.geometry.getAttribute("position");
            this.state.positions3D = [
                0,
                1,
                3,
                2
            ].map((i)=>{
                const v3 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Vector3"]();
                v3.fromBufferAttribute(p, i);
                return mesh.localToWorld(v3);
            });
        } else {
            if (this.config.position?.length !== 4) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PSVError"](`missing marker ${this.id} position`);
            }
            let positions;
            try {
                positions = this.config.position.map((p2)=>this.viewer.dataHelper.cleanPosition(p2));
            } catch (e) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PSVError"](`invalid marker ${this.id} position`, e);
            }
            const positions3D = positions.map((p2)=>this.viewer.dataHelper.sphericalCoordsToVector3(p2));
            const centroid = getPolygonCenter(positions.map(({ yaw, pitch })=>[
                    yaw,
                    pitch
                ]));
            this.state.position = {
                yaw: centroid[0],
                pitch: centroid[1]
            };
            this.state.positions3D = positions3D;
            const p = mesh.geometry.getAttribute("position");
            [
                positions3D[0],
                positions3D[1],
                positions3D[3],
                // not a mistake!
                positions3D[2]
            ].forEach((v, i)=>{
                p.setX(i, v.x);
                p.setY(i, v.y);
                p.setZ(i, v.z);
            });
            p.needsUpdate = true;
            this.__setTextureWrap(material);
        }
        switch(this.type){
            case "videoLayer" /* videoLayer */ :
                if (this.definition !== this.config.videoLayer) {
                    material.map?.dispose();
                    const video = createVideo({
                        src: this.config.videoLayer,
                        withCredentials: this.viewer.config.withCredentials,
                        muted: true,
                        autoplay: this.config.autoplay ?? true
                    });
                    const texture = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VideoTexture"](video);
                    material.map = texture;
                    material.alpha = 0;
                    video.addEventListener("loadedmetadata", ()=>{
                        if (!this.viewer) {
                            return;
                        }
                        material.alpha = this.config.opacity;
                        if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].isExtendedPosition(this.config.position)) {
                            mesh.material.userData[MARKER_DATA] = {
                                width: video.videoWidth,
                                height: video.videoHeight
                            };
                            this.__setTextureWrap(material);
                        }
                    }, {
                        once: true
                    });
                    if (video.autoplay) {
                        video.play();
                    }
                    this.definition = this.config.videoLayer;
                } else {
                    material.alpha = this.config.opacity;
                }
                break;
            case "imageLayer" /* imageLayer */ :
                if (this.definition !== this.config.imageLayer) {
                    material.map?.dispose();
                    const texture = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$three$2f$build$2f$three$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Texture"]();
                    material.map = texture;
                    material.alpha = 0;
                    this.viewer.textureLoader.loadImage(this.config.imageLayer).then((image)=>{
                        if (!this.viewer) {
                            return;
                        }
                        if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].isExtendedPosition(this.config.position)) {
                            mesh.material.userData[MARKER_DATA] = {
                                width: image.width,
                                height: image.height
                            };
                            this.__setTextureWrap(material);
                        }
                        texture.image = image;
                        texture.anisotropy = 4;
                        texture.needsUpdate = true;
                        material.alpha = this.config.opacity;
                        this.viewer.needsUpdate();
                    });
                    this.definition = this.config.imageLayer;
                } else {
                    material.alpha = this.config.opacity;
                }
                break;
        }
        material.chromaKey = this.config.chromaKey;
        mesh.renderOrder = 1e3 + this.config.zIndex;
        mesh.geometry.boundingBox = null;
    }
    /**
   * For layers positionned by corners, applies offset to the texture in order to keep its proportions
   */ __setTextureWrap(material) {
        const imageSize = material.userData[MARKER_DATA];
        if (!imageSize || !imageSize.height || !imageSize.width) {
            material.repeat.set(1, 1);
            material.offset.set(0, 0);
            return;
        }
        const positions = this.config.position.map((p)=>{
            return this.viewer.dataHelper.cleanPosition(p);
        });
        const w1 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].greatArcDistance([
            positions[0].yaw,
            positions[0].pitch
        ], [
            positions[1].yaw,
            positions[1].pitch
        ]);
        const w2 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].greatArcDistance([
            positions[3].yaw,
            positions[3].pitch
        ], [
            positions[2].yaw,
            positions[2].pitch
        ]);
        const h1 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].greatArcDistance([
            positions[1].yaw,
            positions[1].pitch
        ], [
            positions[2].yaw,
            positions[2].pitch
        ]);
        const h2 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].greatArcDistance([
            positions[0].yaw,
            positions[0].pitch
        ], [
            positions[3].yaw,
            positions[3].pitch
        ]);
        const layerRatio = (w1 + w2) / (h1 + h2);
        const imageRatio = imageSize.width / imageSize.height;
        let hMargin = 0;
        let vMargin = 0;
        if (layerRatio < imageRatio) {
            hMargin = imageRatio - layerRatio;
        } else {
            vMargin = 1 / imageRatio - 1 / layerRatio;
        }
        material.repeat.set(1 - hMargin, 1 - vMargin);
        material.offset.set(hMargin / 2, vMargin / 2);
    }
};
;
var MarkerCSS3D = class extends AbstractDomMarker {
    constructor(viewer, plugin, config){
        super(viewer, plugin, config);
        /**
     * @internal
     */ this.viewportIntersection = false;
    }
    get threeElement() {
        return this.object;
    }
    isCss3d() {
        return true;
    }
    createElement() {
        this.element = document.createElement("div");
        this.object = new CSS3DObject(this.element);
        this.object.userData = {
            [MARKER_DATA]: this
        };
        Object.defineProperty(this.object, "visible", {
            enumerable: true,
            get: function() {
                return this.userData[MARKER_DATA].config.visible;
            },
            set: function(visible) {
                this.userData[MARKER_DATA].config.visible = visible;
            }
        });
        this.afterCreateElement();
    }
    destroy() {
        delete this.object.userData[MARKER_DATA];
        delete this.object;
        super.destroy();
    }
    render({ viewerPosition, zoomLevel }) {
        const element = this.domElement;
        this.state.size = {
            width: element.offsetWidth,
            height: element.offsetHeight
        };
        const isVisible = this.state.positions3D[0].dot(this.viewer.state.direction) > 0 && this.viewportIntersection;
        if (isVisible) {
            const position = this.viewer.dataHelper.sphericalCoordsToViewerCoords(this.state.position);
            this.config.elementLayer.updateMarker?.({
                marker: this,
                position,
                viewerPosition,
                zoomLevel,
                viewerSize: this.viewer.state.size
            });
            return position;
        } else {
            return null;
        }
    }
    update(config) {
        super.update(config);
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].isExtendedPosition(this.config.position)) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PSVError"](`missing marker ${this.id} position`);
        }
        try {
            this.state.position = this.viewer.dataHelper.cleanPosition(this.config.position);
        } catch (e) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PSVError"](`invalid marker ${this.id} position`, e);
        }
        this.state.positions3D = [
            this.viewer.dataHelper.sphericalCoordsToVector3(this.state.position)
        ];
        const object = this.threeElement;
        const element = this.domElement;
        element.classList.add("psv-marker--css3d");
        element.childNodes.forEach((n)=>n.remove());
        element.appendChild(this.config.elementLayer);
        this.config.elementLayer.style.display = "block";
        object.position.copy(this.state.positions3D[0]).multiplyScalar(100);
        object.lookAt(0, this.state.positions3D[0].y * 100, 0);
        object.rotateY(-this.config.rotation.yaw);
        object.rotateX(-this.config.rotation.pitch);
        object.rotateZ(-this.config.rotation.roll);
    }
};
;
var MarkerNormal = class extends AbstractStandardMarker {
    constructor(viewer, plugin, config){
        super(viewer, plugin, config);
    }
    isNormal() {
        return true;
    }
    createElement() {
        this.element = document.createElement("div");
        this.afterCreateElement();
    }
    render(params) {
        const position = super.render(params);
        if (position && this.type === "element" /* element */ ) {
            this.config.element.updateMarker?.({
                marker: this,
                position,
                viewerPosition: params.viewerPosition,
                zoomLevel: params.zoomLevel,
                viewerSize: this.viewer.state.size
            });
        }
        return position;
    }
    update(config) {
        super.update(config);
        const element = this.domElement;
        if (this.config.image && !this.config.size) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PSVError"](`missing marker ${this.id} size`);
        }
        if (this.config.size) {
            this.needsUpdateSize = false;
            this.state.size = this.config.size;
            element.style.width = this.config.size.width + "px";
            element.style.height = this.config.size.height + "px";
        } else {
            this.needsUpdateSize = true;
        }
        switch(this.type){
            case "image" /* image */ :
                this.definition = this.config.image;
                element.style.backgroundImage = `url("${this.config.image}")`;
                break;
            case "html" /* html */ :
                this.definition = this.config.html;
                element.innerHTML = this.config.html;
                break;
            case "element" /* element */ :
                if (this.definition !== this.config.element) {
                    this.definition = this.config.element;
                    element.childNodes.forEach((n)=>n.remove());
                    element.appendChild(this.config.element);
                    this.config.element.style.display = "block";
                }
                break;
        }
    }
};
;
var MarkerPolygon = class extends AbstractDomMarker {
    constructor(viewer, plugin, config){
        super(viewer, plugin, config);
    }
    createElement() {
        this.element = document.createElementNS(SVG_NS, "path");
        this.element[MARKER_DATA] = this;
    }
    isPoly() {
        return true;
    }
    /**
   * Checks if it is a polygon/polyline using pixel coordinates
   */ get isPixels() {
        return this.type === "polygonPixels" /* polygonPixels */  || this.type === "polylinePixels" /* polylinePixels */ ;
    }
    /**
   * Checks if it is a polygon marker
   */ get isPolygon() {
        return this.type === "polygon" /* polygon */  || this.type === "polygonPixels" /* polygonPixels */ ;
    }
    /**
   * Checks if it is a polyline marker
   */ get isPolyline() {
        return this.type === "polyline" /* polyline */  || this.type === "polylinePixels" /* polylinePixels */ ;
    }
    get coords() {
        return this.definition;
    }
    render() {
        const positions = this.__getAllPolyPositions();
        const isVisible = positions[0].length > (this.isPolygon ? 2 : 1);
        if (isVisible) {
            const position = this.viewer.dataHelper.sphericalCoordsToViewerCoords(this.state.position);
            const points = positions.filter((innerPos)=>innerPos.length > 0).map((innerPos)=>{
                let innerPoints = "M";
                innerPoints += innerPos.map((pos)=>`${pos.x - position.x},${pos.y - position.y}`).join("L");
                if (this.isPolygon) {
                    innerPoints += "Z";
                }
                return innerPoints;
            }).join(" ");
            this.domElement.setAttributeNS(null, "d", points);
            this.domElement.setAttributeNS(null, "transform", `translate(${position.x} ${position.y})`);
            return position;
        } else {
            return null;
        }
    }
    update(config) {
        super.update(config);
        const element = this.domElement;
        element.classList.add("psv-marker--poly");
        if (this.config.svgStyle) {
            Object.entries(this.config.svgStyle).forEach(([prop, value])=>{
                element.setAttributeNS(null, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].dasherize(prop), value);
            });
            if (this.isPolyline && !this.config.svgStyle.fill) {
                element.setAttributeNS(null, "fill", "none");
            }
        } else if (this.isPolygon) {
            element.setAttributeNS(null, "fill", "rgba(0,0,0,0.5)");
        } else if (this.isPolyline) {
            element.setAttributeNS(null, "fill", "none");
            element.setAttributeNS(null, "stroke", "rgb(0,0,0)");
        }
        try {
            let actualPoly = this.config[this.type];
            if (!Array.isArray(actualPoly[0]) && typeof actualPoly[0] !== "object") {
                for(let i = 0; i < actualPoly.length; i++){
                    actualPoly.splice(i, 2, [
                        actualPoly[i],
                        actualPoly[i + 1]
                    ]);
                }
            }
            if (!Array.isArray(actualPoly[0][0]) && typeof actualPoly[0][0] !== "object") {
                actualPoly = [
                    actualPoly
                ];
            }
            if (this.isPolyline && actualPoly.length > 1) {
                throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PSVError"](`polylines cannot have holes`);
            }
            if (this.isPixels) {
                this.definition = actualPoly.map((coords)=>{
                    return coords.map((coord)=>{
                        let sphericalCoord;
                        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].isExtendedPosition(coord)) {
                            sphericalCoord = this.viewer.dataHelper.cleanPosition(coord);
                        } else {
                            sphericalCoord = this.viewer.dataHelper.textureCoordsToSphericalCoords({
                                textureX: coord[0],
                                textureY: coord[1]
                            });
                        }
                        return [
                            sphericalCoord.yaw,
                            sphericalCoord.pitch
                        ];
                    });
                });
            } else {
                this.definition = actualPoly.map((coords)=>{
                    return coords.map((coord)=>{
                        let sphericalCoord;
                        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].isExtendedPosition(coord)) {
                            sphericalCoord = this.viewer.dataHelper.cleanPosition(coord);
                        } else {
                            sphericalCoord = this.viewer.dataHelper.cleanPosition({
                                yaw: coord[0],
                                pitch: coord[1]
                            });
                        }
                        return [
                            sphericalCoord.yaw,
                            sphericalCoord.pitch
                        ];
                    });
                });
            }
        } catch (e) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PSVError"](`invalid marker ${this.id} position`, e);
        }
        const centroid = this.isPolygon ? getPolygonCenter(this.coords[0]) : getPolylineCenter(this.coords[0]);
        this.state.position = {
            yaw: centroid[0],
            pitch: centroid[1]
        };
        this.positions3D = this.coords.map((coords)=>{
            return coords.map((coord)=>{
                return this.viewer.dataHelper.sphericalCoordsToVector3({
                    yaw: coord[0],
                    pitch: coord[1]
                });
            });
        });
        this.state.positions3D = this.positions3D[0];
    }
    __getAllPolyPositions() {
        return this.positions3D.map((positions)=>{
            return this.__getPolyPositions(positions);
        });
    }
    /**
   * Computes viewer coordinates of each point of a polygon/polyline<br>
   * It handles points behind the camera by creating intermediary points suitable for the projector
   */ __getPolyPositions(positions) {
        const nbVectors = positions.length;
        const positions3D = positions.map((vector)=>{
            return {
                vector,
                visible: vector.dot(this.viewer.state.direction) > 0
            };
        });
        const toBeComputed = [];
        positions3D.forEach((pos, i)=>{
            if (!pos.visible) {
                const neighbours = [
                    i === 0 ? positions3D[nbVectors - 1] : positions3D[i - 1],
                    i === nbVectors - 1 ? positions3D[0] : positions3D[i + 1]
                ];
                neighbours.forEach((neighbour)=>{
                    if (neighbour.visible) {
                        toBeComputed.push({
                            visible: neighbour.vector,
                            invisible: pos.vector,
                            index: i
                        });
                    }
                });
            }
        });
        toBeComputed.reverse().forEach((pair)=>{
            positions3D.splice(pair.index, 0, {
                vector: getGreatCircleIntersection(pair.visible, pair.invisible, this.viewer.state.direction),
                visible: true
            });
        });
        return positions3D.filter((pos)=>pos.visible).map((pos)=>this.viewer.dataHelper.vector3ToViewerCoords(pos.vector));
    }
};
;
var MarkerSvg = class extends AbstractStandardMarker {
    get svgElement() {
        return this.domElement.firstElementChild;
    }
    constructor(viewer, plugin, config){
        super(viewer, plugin, config);
    }
    isSvg() {
        return true;
    }
    createElement() {
        const svgType = this.type === "square" /* square */  ? "rect" : this.type;
        const elt = document.createElementNS(SVG_NS, svgType);
        this.element = document.createElementNS(SVG_NS, "svg");
        this.element.appendChild(elt);
        this.afterCreateElement();
    }
    update(config) {
        super.update(config);
        const svgElement = this.svgElement;
        this.needsUpdateSize = true;
        switch(this.type){
            case "square" /* square */ :
                this.definition = {
                    x: 0,
                    y: 0,
                    width: this.config.square,
                    height: this.config.square
                };
                break;
            case "rect" /* rect */ :
                if (Array.isArray(this.config.rect)) {
                    this.definition = {
                        x: 0,
                        y: 0,
                        width: this.config.rect[0],
                        height: this.config.rect[1]
                    };
                } else {
                    this.definition = {
                        x: 0,
                        y: 0,
                        width: this.config.rect.width,
                        height: this.config.rect.height
                    };
                }
                break;
            case "circle" /* circle */ :
                this.definition = {
                    cx: this.config.circle,
                    cy: this.config.circle,
                    r: this.config.circle
                };
                break;
            case "ellipse" /* ellipse */ :
                if (Array.isArray(this.config.ellipse)) {
                    this.definition = {
                        cx: this.config.ellipse[0],
                        cy: this.config.ellipse[1],
                        rx: this.config.ellipse[0],
                        ry: this.config.ellipse[1]
                    };
                } else {
                    this.definition = {
                        cx: this.config.ellipse.rx,
                        cy: this.config.ellipse.ry,
                        rx: this.config.ellipse.rx,
                        ry: this.config.ellipse.ry
                    };
                }
                break;
            case "path" /* path */ :
                this.definition = {
                    d: this.config.path
                };
                break;
        }
        Object.entries(this.definition).forEach(([prop, value])=>{
            svgElement.setAttributeNS(null, prop, value);
        });
        if (this.config.svgStyle) {
            Object.entries(this.config.svgStyle).forEach(([prop, value])=>{
                svgElement.setAttributeNS(null, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].dasherize(prop), value);
            });
        } else {
            svgElement.setAttributeNS(null, "fill", "rgba(0,0,0,0.5)");
        }
    }
};
// src/MarkersPlugin.ts
var getConfig = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].getConfigParser({
    clickEventOnMarker: false,
    gotoMarkerSpeed: "8rpm",
    markers: null,
    defaultHoverScale: null
}, {
    defaultHoverScale (defaultHoverScale) {
        if (!defaultHoverScale) {
            return null;
        }
        if (defaultHoverScale === true) {
            defaultHoverScale = DEFAULT_HOVER_SCALE;
        }
        if (typeof defaultHoverScale === "number") {
            defaultHoverScale = {
                amount: defaultHoverScale
            };
        }
        return {
            ...DEFAULT_HOVER_SCALE,
            ...defaultHoverScale
        };
    }
});
function getMarkerCtor(config) {
    const type = getMarkerType(config, false);
    switch(type){
        case "image":
        case "html":
        case "element":
            return MarkerNormal;
        case "imageLayer":
        case "videoLayer":
            return Marker3D;
        case "elementLayer":
            return MarkerCSS3D;
        case "polygon":
        case "polyline":
        case "polygonPixels":
        case "polylinePixels":
            return MarkerPolygon;
        case "square":
        case "rect":
        case "circle":
        case "ellipse":
        case "path":
            return MarkerSvg;
        default:
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PSVError"]("invalid marker type");
    }
}
var MarkersPlugin = class extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AbstractConfigurablePlugin"] {
    constructor(viewer, config){
        super(viewer, config);
        this.markers = {};
        this.state = {
            allVisible: true,
            showAllTooltips: false,
            currentMarker: null,
            hoveringMarker: null,
            // require a 2nd render (only the scene) when 3d markers visibility changes
            needsReRender: false,
            // use when updating a polygon marker in order to keep the current position
            lastClientX: null,
            lastClientY: null
        };
        this.container = document.createElement("div");
        this.container.className = "psv-markers";
        this.viewer.container.appendChild(this.container);
        this.container.addEventListener("contextmenu", (e)=>e.preventDefault());
        this.svgContainer = document.createElementNS(SVG_NS, "svg");
        this.svgContainer.setAttribute("class", "psv-markers-svg-container");
        this.container.appendChild(this.svgContainer);
        this.css3DContainer = new CSS3DContainer(viewer);
        this.container.appendChild(this.css3DContainer.element);
        this.container.addEventListener("mouseenter", this, true);
        this.container.addEventListener("mouseleave", this, true);
        this.container.addEventListener("mousemove", this, true);
    }
    /**
   * @internal
   */ init() {
        super.init();
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].checkStylesheet(this.viewer.container, "markers-plugin");
        this.viewer.addEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ClickEvent.type, this);
        this.viewer.addEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].DoubleClickEvent.type, this);
        this.viewer.addEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].RenderEvent.type, this);
        this.viewer.addEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ConfigChangedEvent.type, this);
        this.viewer.addEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ObjectEnterEvent.type, this);
        this.viewer.addEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ObjectHoverEvent.type, this);
        this.viewer.addEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ObjectLeaveEvent.type, this);
        this.viewer.addEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ReadyEvent.type, this, {
            once: true
        });
    }
    /**
   * @internal
   */ destroy() {
        this.clearMarkers(false);
        this.viewer.unobserveObjects(MARKER_DATA);
        this.viewer.removeEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ClickEvent.type, this);
        this.viewer.removeEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].DoubleClickEvent.type, this);
        this.viewer.removeEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].RenderEvent.type, this);
        this.viewer.removeEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ObjectEnterEvent.type, this);
        this.viewer.removeEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ObjectHoverEvent.type, this);
        this.viewer.removeEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ObjectLeaveEvent.type, this);
        this.viewer.removeEventListener(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ReadyEvent.type, this);
        this.css3DContainer.destroy();
        this.viewer.container.removeChild(this.container);
        super.destroy();
    }
    /**
   * @internal
   */ handleEvent(e) {
        switch(e.type){
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ReadyEvent.type:
                if (this.config.markers) {
                    this.setMarkers(this.config.markers);
                    delete this.config.markers;
                }
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].RenderEvent.type:
                this.renderMarkers();
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ClickEvent.type:
                this.__onClick(e, false);
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].DoubleClickEvent.type:
                this.__onClick(e, true);
                break;
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ObjectEnterEvent.type:
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ObjectLeaveEvent.type:
            case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ObjectHoverEvent.type:
                if (e.userDataKey === MARKER_DATA) {
                    const event = e.originalEvent;
                    const marker = e.object.userData[MARKER_DATA];
                    switch(e.type){
                        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ObjectEnterEvent.type:
                            if (marker.config.style?.cursor) {
                                this.viewer.setCursor(marker.config.style.cursor);
                            } else if (marker.config.tooltip || marker.config.content) {
                                this.viewer.setCursor("pointer");
                            }
                            this.__onEnterMarker(event, marker);
                            break;
                        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ObjectLeaveEvent.type:
                            this.viewer.setCursor(null);
                            this.__onLeaveMarker(marker);
                            break;
                        case __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["events"].ObjectHoverEvent.type:
                            this.__onHoverMarker(event, marker);
                            break;
                    }
                }
                break;
            case "mouseenter":
                {
                    const marker = this.__getTargetMarker(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].getEventTarget(e));
                    this.__onEnterMarker(e, marker);
                    break;
                }
            case "mouseleave":
                {
                    const marker = this.__getTargetMarker(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].getEventTarget(e));
                    this.__onLeaveMarker(marker);
                    break;
                }
            case "mousemove":
                {
                    const marker = this.__getTargetMarker(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].getEventTarget(e), true);
                    this.__onHoverMarker(e, marker);
                    break;
                }
        }
    }
    /**
   * Toggles all markers
   */ toggleAllMarkers() {
        if (this.state.allVisible) {
            this.hideAllMarkers();
        } else {
            this.showAllMarkers();
        }
    }
    /**
   * Shows all markers
   */ showAllMarkers() {
        this.state.allVisible = true;
        Object.values(this.markers).forEach((marker)=>{
            marker.config.visible = true;
        });
        this.renderMarkers();
        this.dispatchEvent(new ShowMarkersEvent());
    }
    /**
   * Hides all markers
   */ hideAllMarkers() {
        this.state.allVisible = false;
        Object.values(this.markers).forEach((marker)=>{
            marker.config.visible = false;
        });
        this.renderMarkers();
        this.dispatchEvent(new HideMarkersEvent());
    }
    /**
   * Toggles the visibility of all tooltips
   */ toggleAllTooltips() {
        if (this.state.showAllTooltips) {
            this.hideAllTooltips();
        } else {
            this.showAllTooltips();
        }
    }
    /**
   *  Displays all tooltips
   */ showAllTooltips() {
        this.state.showAllTooltips = true;
        Object.values(this.markers).forEach((marker)=>{
            marker.state.staticTooltip = true;
            marker.showTooltip();
        });
    }
    /**
   * Hides all tooltips
   */ hideAllTooltips() {
        this.state.showAllTooltips = false;
        Object.values(this.markers).forEach((marker)=>{
            marker.state.staticTooltip = false;
            marker.hideTooltip();
        });
    }
    /**
   * Returns the total number of markers
   */ getNbMarkers() {
        return Object.keys(this.markers).length;
    }
    /**
   * Returns all the markers
   */ getMarkers() {
        return Object.values(this.markers);
    }
    /**
   * Adds a new marker to viewer
   * @throws {@link PSVError} when the marker's id is missing or already exists
   */ addMarker(config, render = true) {
        if (this.markers[config.id]) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PSVError"](`marker "${config.id}" already exists`);
        }
        const marker = new (getMarkerCtor(config))(this.viewer, this, config);
        if (marker.isPoly()) {
            this.svgContainer.appendChild(marker.domElement);
        } else if (marker.isCss3d()) {
            this.css3DContainer.addObject(marker);
        } else if (marker.is3d()) {
            this.viewer.renderer.addObject(marker.threeElement);
        } else {
            this.container.appendChild(marker.domElement);
        }
        this.markers[marker.id] = marker;
        if (this.state.showAllTooltips) {
            marker.state.staticTooltip = true;
        }
        if (render) {
            this.__afterChangeMarkers();
        }
    }
    /**
   * Returns the internal marker object for a marker id
   * @throws {@link PSVError} when the marker cannot be found
   */ getMarker(markerId) {
        const id = typeof markerId === "object" ? markerId.id : markerId;
        if (!this.markers[id]) {
            throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PSVError"](`cannot find marker "${id}"`);
        }
        return this.markers[id];
    }
    /**
   * Returns the last marker selected by the user
   */ getCurrentMarker() {
        return this.state.currentMarker;
    }
    /**
   * Updates the existing marker with the same id
   * Every property can be changed but you can't change its type (Eg: `image` to `html`)
   */ updateMarker(config, render = true) {
        const marker = this.getMarker(config.id);
        marker.update(config);
        if (render) {
            this.__afterChangeMarkers();
            if (marker === this.state.hoveringMarker && marker.config.tooltip?.trigger === "hover" || marker.state.staticTooltip) {
                marker.showTooltip(this.state.lastClientX, this.state.lastClientY, true);
            }
        }
    }
    /**
   * Removes a marker from the viewer
   */ removeMarker(markerId, render = true) {
        const marker = this.getMarker(markerId);
        if (marker.isPoly()) {
            this.svgContainer.removeChild(marker.domElement);
        } else if (marker.isCss3d()) {
            this.css3DContainer.removeObject(marker);
        } else if (marker.is3d()) {
            this.viewer.renderer.removeObject(marker.threeElement);
        } else {
            this.container.removeChild(marker.domElement);
        }
        if (this.state.hoveringMarker === marker) {
            this.state.hoveringMarker = null;
        }
        if (this.state.currentMarker === marker) {
            this.state.currentMarker = null;
        }
        marker.destroy();
        delete this.markers[marker.id];
        if (render) {
            this.__afterChangeMarkers();
        }
    }
    /**
   * Removes multiple markers
   */ removeMarkers(markerIds, render = true) {
        markerIds.forEach((markerId)=>this.removeMarker(markerId, false));
        if (render) {
            this.__afterChangeMarkers();
        }
    }
    /**
   * Replaces all markers
   */ setMarkers(markers, render = true) {
        this.clearMarkers(false);
        markers?.forEach((marker)=>{
            this.addMarker(marker, false);
        });
        if (render) {
            this.__afterChangeMarkers();
        }
    }
    /**
   * Removes all markers
   */ clearMarkers(render = true) {
        Object.keys(this.markers).forEach((markerId)=>{
            this.removeMarker(markerId, false);
        });
        if (render) {
            this.__afterChangeMarkers();
        }
    }
    /**
   * Rotate the view to face the marker
   */ gotoMarker(markerId, speed = this.config.gotoMarkerSpeed) {
        const marker = this.getMarker(markerId);
        if (!speed) {
            this.viewer.rotate(marker.state.position);
            if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].isNil(marker.config.zoomLvl)) {
                this.viewer.zoom(marker.config.zoomLvl);
            }
            this.dispatchEvent(new GotoMarkerDoneEvent(marker));
            return Promise.resolve();
        } else {
            return this.viewer.animate({
                ...marker.state.position,
                zoom: marker.config.zoomLvl,
                speed
            }).then(()=>{
                this.dispatchEvent(new GotoMarkerDoneEvent(marker));
            });
        }
    }
    /**
   * Hides a marker
   */ hideMarker(markerId) {
        this.toggleMarker(markerId, false);
    }
    /**
   * Shows a marker
   */ showMarker(markerId) {
        this.toggleMarker(markerId, true);
    }
    /**
   * Forces the display of the tooltip of a marker
   */ showMarkerTooltip(markerId) {
        const marker = this.getMarker(markerId);
        marker.state.staticTooltip = true;
        marker.showTooltip();
    }
    /**
   * Hides the tooltip of a marker
   */ hideMarkerTooltip(markerId) {
        const marker = this.getMarker(markerId);
        marker.state.staticTooltip = false;
        marker.hideTooltip();
    }
    /**
   * Toggles a marker visibility
   */ toggleMarker(markerId, visible) {
        const marker = this.getMarker(markerId);
        marker.config.visible = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].isNil(visible) ? !marker.config.visible : visible;
        this.renderMarkers();
    }
    /**
   * Opens the panel with the content of the marker
   */ showMarkerPanel(markerId) {
        const marker = this.getMarker(markerId);
        if (marker.config.content) {
            this.viewer.panel.show({
                id: ID_PANEL_MARKER,
                content: marker.config.content
            });
        } else {
            this.hideMarkerPanel();
        }
    }
    /**
   * Closes the panel if currently showing the content of a marker
   */ hideMarkerPanel() {
        this.viewer.panel.hide(ID_PANEL_MARKER);
    }
    /**
   * Toggles the visibility of the list of markers
   */ toggleMarkersList() {
        if (this.viewer.panel.isVisible(ID_PANEL_MARKERS_LIST)) {
            this.hideMarkersList();
        } else {
            this.showMarkersList();
        }
    }
    /**
   * Opens side panel with the list of markers
   */ showMarkersList() {
        let markers = [];
        Object.values(this.markers).forEach((marker)=>{
            if (marker.config.visible && !marker.config.hideList) {
                markers.push(marker);
            }
        });
        const e = new RenderMarkersListEvent(markers);
        this.dispatchEvent(e);
        markers = e.markers;
        this.viewer.panel.show({
            id: ID_PANEL_MARKERS_LIST,
            content: MARKERS_LIST_TEMPLATE(markers, this.viewer.config.lang[MarkersButton.id]),
            noMargin: true,
            clickHandler: (target)=>{
                const li = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].getClosest(target, ".psv-panel-menu-item");
                const markerId = li ? li.dataset[MARKER_DATA] : void 0;
                if (markerId) {
                    const marker = this.getMarker(markerId);
                    this.dispatchEvent(new SelectMarkerListEvent(marker));
                    this.gotoMarker(marker.id);
                    this.hideMarkersList();
                }
            }
        });
    }
    /**
   * Closes side panel if it contains the list of markers
   */ hideMarkersList() {
        this.viewer.panel.hide(ID_PANEL_MARKERS_LIST);
    }
    /**
   * Updates the visibility and the position of all markers
   */ renderMarkers() {
        if (this.state.needsReRender) {
            this.state.needsReRender = false;
            return;
        }
        const zoomLevel = this.viewer.getZoomLevel();
        const viewerPosition = this.viewer.getPosition();
        const hoveringMarker = this.state.hoveringMarker;
        Object.values(this.markers).forEach((marker)=>{
            let isVisible = marker.config.visible;
            let visibilityChanged = false;
            let position = null;
            if (isVisible) {
                position = marker.render({
                    viewerPosition,
                    zoomLevel,
                    hoveringMarker
                });
                isVisible = !!position;
            }
            visibilityChanged = marker.state.visible !== isVisible;
            marker.state.visible = isVisible;
            marker.state.position2D = position;
            if (marker.domElement) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].toggleClass(marker.domElement, "psv-marker--visible", isVisible);
            }
            if (!isVisible) {
                marker.hideTooltip();
            } else if (marker.state.staticTooltip) {
                marker.showTooltip();
            } else if (marker !== this.state.hoveringMarker) {
                marker.hideTooltip();
            }
            if (visibilityChanged) {
                this.dispatchEvent(new MarkerVisibilityEvent(marker, isVisible));
                if (marker.is3d() || marker.isCss3d()) {
                    this.state.needsReRender = true;
                }
            }
        });
        if (this.state.needsReRender) {
            this.viewer.needsUpdate();
        }
    }
    __getTargetMarker(target, closest = false) {
        if (target instanceof Node) {
            const target2 = closest ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["utils"].getClosest(target, ".psv-marker") : target;
            return target2 ? target2[MARKER_DATA] : void 0;
        } else if (Array.isArray(target)) {
            return target.map((o)=>o.userData[MARKER_DATA]).filter((m)=>!!m).sort((a, b)=>b.config.zIndex - a.config.zIndex)[0];
        } else {
            return null;
        }
    }
    /**
   * Handles mouse enter events, show the tooltip for non polygon markers
   */ __onEnterMarker(e, marker) {
        if (marker) {
            this.state.hoveringMarker = marker;
            this.state.lastClientX = e.clientX;
            this.state.lastClientY = e.clientY;
            this.dispatchEvent(new EnterMarkerEvent(marker));
            if (marker instanceof AbstractStandardMarker) {
                marker.applyScale({
                    zoomLevel: this.viewer.getZoomLevel(),
                    viewerPosition: this.viewer.getPosition(),
                    mouseover: true
                });
            }
            if (!marker.state.staticTooltip && marker.config.tooltip?.trigger === "hover") {
                marker.showTooltip(e.clientX, e.clientY);
            }
        }
    }
    /**
   * Handles mouse leave events, hide the tooltip
   */ __onLeaveMarker(marker) {
        if (marker) {
            this.dispatchEvent(new LeaveMarkerEvent(marker));
            if (marker instanceof AbstractStandardMarker) {
                marker.applyScale({
                    zoomLevel: this.viewer.getZoomLevel(),
                    viewerPosition: this.viewer.getPosition(),
                    mouseover: false
                });
            }
            this.state.hoveringMarker = null;
            if (!marker.state.staticTooltip && marker.config.tooltip?.trigger === "hover") {
                marker.hideTooltip();
            } else if (marker.state.staticTooltip) {
                marker.showTooltip();
            }
        }
    }
    /**
   * Handles mouse move events, refresh the tooltip for polygon markers
   */ __onHoverMarker(e, marker) {
        if (marker) {
            this.state.lastClientX = e.clientX;
            this.state.lastClientY = e.clientY;
            if (marker.isPoly() || marker.is3d() || marker.isCss3d()) {
                if (marker.config.tooltip?.trigger === "hover") {
                    marker.showTooltip(e.clientX, e.clientY);
                }
            }
        }
    }
    /**
   * Handles mouse click events, select the marker and open the panel if necessary
   */ __onClick(e, dblclick) {
        const threeMarker = this.__getTargetMarker(e.data.objects);
        const stdMarker = this.__getTargetMarker(e.data.target, true);
        const marker = stdMarker || threeMarker;
        if (this.state.currentMarker && this.state.currentMarker !== marker) {
            this.dispatchEvent(new UnselectMarkerEvent(this.state.currentMarker));
            this.viewer.panel.hide(ID_PANEL_MARKER);
            if (!this.state.showAllTooltips && this.state.currentMarker.config.tooltip?.trigger === "click") {
                this.hideMarkerTooltip(this.state.currentMarker.id);
            }
            this.state.currentMarker = null;
        }
        if (marker) {
            this.state.currentMarker = marker;
            this.dispatchEvent(new SelectMarkerEvent(marker, dblclick, e.data.rightclick));
            if (this.config.clickEventOnMarker) {
                e.data.marker = marker;
            } else {
                e.stopImmediatePropagation();
            }
            if (this.markers[marker.id] && !e.data.rightclick) {
                if (marker.config.tooltip?.trigger === "click") {
                    if (marker.tooltip) {
                        this.hideMarkerTooltip(marker.id);
                    } else {
                        this.showMarkerTooltip(marker.id);
                    }
                } else {
                    this.showMarkerPanel(marker.id);
                }
            }
        }
    }
    __afterChangeMarkers() {
        this.__refreshUi();
        this.__checkObjectsObserver();
        this.viewer.needsUpdate();
        this.dispatchEvent(new SetMarkersEvent(this.getMarkers()));
    }
    /**
   * Updates the visiblity of the panel and the buttons
   */ __refreshUi() {
        const nbMarkers = Object.values(this.markers).filter((m)=>!m.config.hideList).length;
        if (nbMarkers === 0) {
            this.viewer.panel.hide(ID_PANEL_MARKER);
            this.viewer.panel.hide(ID_PANEL_MARKERS_LIST);
        } else {
            if (this.viewer.panel.isVisible(ID_PANEL_MARKERS_LIST)) {
                this.showMarkersList();
            } else if (this.viewer.panel.isVisible(ID_PANEL_MARKER)) {
                this.state.currentMarker ? this.showMarkerPanel(this.state.currentMarker.id) : this.viewer.panel.hide();
            }
        }
        this.viewer.navbar.getButton(MarkersButton.id, false)?.toggle(nbMarkers > 0);
        this.viewer.navbar.getButton(MarkersListButton.id, false)?.toggle(nbMarkers > 0);
    }
    /**
   * Adds or remove the objects observer if there are 3D markers
   */ __checkObjectsObserver() {
        const has3d = Object.values(this.markers).some((marker)=>marker.is3d());
        if (has3d) {
            this.viewer.observeObjects(MARKER_DATA);
        } else {
            this.viewer.unobserveObjects(MARKER_DATA);
        }
    }
};
MarkersPlugin.id = "markers";
MarkersPlugin.VERSION = "5.13.2";
MarkersPlugin.configParser = getConfig;
MarkersPlugin.readonlyOptions = [
    "markers"
];
// src/index.ts
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULTS"].lang[MarkersButton.id] = "Markers";
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DEFAULTS"].lang[MarkersListButton.id] = "Markers list";
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["registerButton"])(MarkersButton, "caption:left");
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$core$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["registerButton"])(MarkersListButton, "caption:left");
;
 //# sourceMappingURL=index.module.js.map
}}),

};

//# sourceMappingURL=node_modules_f10c74ca._.js.map