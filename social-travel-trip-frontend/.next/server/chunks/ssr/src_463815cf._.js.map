{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/components/ui/page-header.tsx"], "sourcesContent": ["interface PageHeaderProps {\n  title: string;\n  description?: string;\n}\n\nexport function PageHeader({ title, description }: PageHeaderProps) {\n  return (\n    <div className=\"space-y-1\">\n      <h1 className=\"text-2xl md:text-3xl font-bold text-purple-800 dark:text-purple-400\">{title}</h1>\n      {description && (\n        <p className=\"text-muted-foreground\">{description}</p>\n      )}\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;;AAKO,SAAS,WAAW,EAAE,KAAK,EAAE,WAAW,EAAmB;IAChE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAuE;;;;;;YACpF,6BACC,8OAAC;gBAAE,WAAU;0BAAyB;;;;;;;;;;;;AAI9C", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/view360/view360.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useState, useEffect } from 'react';\nimport { ReactPhotoSphereViewer } from 'react-photo-sphere-viewer';\nimport { MarkersPlugin } from '@photo-sphere-viewer/markers-plugin';\n// Bỏ import CSS vì nó không tồn tại trong package\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Maximize, Minimize, RotateCcw, ChevronLeft, ChevronRight, MapPin, ArrowRight } from 'lucide-react';\n\nexport interface PanoramaScene {\n  id: string;\n  name: string;\n  image: string;\n  description?: string;\n  position?: {\n    lat?: number;\n    lng?: number;\n  };\n  // Các điểm liên kết đến các scene khác\n  hotspots?: {\n    id: string; // ID của scene đích\n    name: string;\n    position: { yaw: string; pitch: string }; // Vị trí của hotspot trên ảnh 360\n    tooltip?: string;\n  }[];\n}\n\ninterface View360Props {\n  scenes: PanoramaScene[];\n  initialSceneId?: string;\n  className?: string;\n  height?: string;\n  width?: string;\n  fullscreenButton?: boolean;\n  resetButton?: boolean;\n  showSceneSelector?: boolean;\n}\n\nexport const View360: React.FC<View360Props> = ({\n  scenes,\n  initialSceneId,\n  className = '',\n  height = '400px',\n  width = '100%',\n  fullscreenButton = true,\n  resetButton = true,\n  showSceneSelector = true,\n}) => {\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [currentSceneIndex, setCurrentSceneIndex] = useState(\n    initialSceneId\n      ? scenes.findIndex(scene => scene.id === initialSceneId)\n      : 0\n  );\n  const viewerRef = useRef<any>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  // Xử lý khi component unmount\n  useEffect(() => {\n    return () => {\n      if (viewerRef.current) {\n        try {\n          viewerRef.current.destroy();\n        } catch (error) {\n          console.error('Error destroying viewer:', error);\n        }\n      }\n    };\n  }, []);\n\n  // Xử lý khi nhấn nút fullscreen\n  const toggleFullscreen = () => {\n    if (!containerRef.current) return;\n\n    if (!isFullscreen) {\n      if (containerRef.current.requestFullscreen) {\n        containerRef.current.requestFullscreen();\n      }\n      setIsFullscreen(true);\n    } else {\n      if (document.exitFullscreen) {\n        document.exitFullscreen();\n      }\n      setIsFullscreen(false);\n    }\n  };\n\n  // Xử lý khi nhấn nút reset\n  const handleReset = () => {\n    if (viewerRef.current) {\n      viewerRef.current.animate({\n        yaw: 0,\n        pitch: 0,\n        zoom: 50,\n        speed: '10rpm',\n      });\n    }\n  };\n\n  // Xử lý sự kiện fullscreen change\n  useEffect(() => {\n    const handleFullscreenChange = () => {\n      setIsFullscreen(!!document.fullscreenElement);\n    };\n\n    document.addEventListener('fullscreenchange', handleFullscreenChange);\n    return () => {\n      document.removeEventListener('fullscreenchange', handleFullscreenChange);\n    };\n  }, []);\n\n  // Chuyển đến scene trước đó\n  const goToPreviousScene = () => {\n    if (currentSceneIndex > 0) {\n      setCurrentSceneIndex(currentSceneIndex - 1);\n    } else {\n      setCurrentSceneIndex(scenes.length - 1);\n    }\n  };\n\n  // Chuyển đến scene tiếp theo\n  const goToNextScene = () => {\n    if (currentSceneIndex < scenes.length - 1) {\n      setCurrentSceneIndex(currentSceneIndex + 1);\n    } else {\n      setCurrentSceneIndex(0);\n    }\n  };\n\n  // Chuyển đến scene cụ thể theo index\n  const goToScene = (index: number) => {\n    if (index >= 0 && index < scenes.length) {\n      setCurrentSceneIndex(index);\n    }\n  };\n\n  // Chuyển đến scene cụ thể theo ID\n  const goToSceneById = (sceneId: string) => {\n    const sceneIndex = scenes.findIndex(scene => scene.id === sceneId);\n    if (sceneIndex !== -1) {\n      setCurrentSceneIndex(sceneIndex);\n    }\n  };\n\n  // Xử lý khi nhấp vào hotspot\n  const handleHotspotClick = (hotspotId: string) => {\n    // Chuyển đến scene khác trong cùng địa điểm\n    goToSceneById(hotspotId);\n  };\n\n  // Lấy scene hiện tại\n  const currentScene = scenes[currentSceneIndex];\n\n  return (\n    <Card className={`overflow-hidden border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs ${className}`}>\n      <CardContent className=\"p-0\">\n        <div ref={containerRef} className=\"relative\">\n          <div style={{ height, width }}>\n            <ReactPhotoSphereViewer\n              key={currentScene.id} // Quan trọng: key thay đổi khi scene thay đổi để re-render component\n              ref={viewerRef}\n              src={currentScene.image}\n              height={isFullscreen ? '100vh' : height}\n              width={width}\n              littlePlanet={false}\n              container={containerRef.current || undefined}\n              plugins={[\n                [MarkersPlugin, {\n                  markers: currentScene.hotspots?.map(hotspot => ({\n                    id: hotspot.id,\n                    position: hotspot.position,\n                    tooltip: {\n                      content: hotspot.tooltip || hotspot.name,\n                      position: 'bottom'\n                    },\n                    html: `\n                      <div style=\"\n                        display: flex;\n                        align-items: center;\n                        background-color: rgba(255, 255, 255, 0.8);\n                        padding: 5px 10px;\n                        border-radius: 20px;\n                        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\n                        cursor: pointer;\n                        transition: all 0.2s ease;\n                      \">\n                        <div style=\"\n                          margin-right: 5px;\n                          color: rgb(147, 51, 234);\n                        \">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                            <path d=\"M5 12h14\"></path>\n                            <path d=\"m12 5 7 7-7 7\"></path>\n                          </svg>\n                        </div>\n                        <div style=\"\n                          font-weight: 500;\n                          font-size: 14px;\n                        \">${hotspot.name}</div>\n                      </div>\n                    `,\n                    data: { sceneId: hotspot.id }\n                  })) || []\n                }]\n              ]}\n              onReady={(instance) => {\n                // Xử lý sự kiện click vào marker\n                if (currentScene.hotspots && currentScene.hotspots.length > 0) {\n                  const markersPlugin = instance.getPlugin(MarkersPlugin);\n                  if (markersPlugin) {\n                    markersPlugin.addEventListener('select-marker', (e: any) => {\n                      if (e.marker && e.marker.data && e.marker.data.sceneId) {\n                        handleHotspotClick(e.marker.data.sceneId);\n                      }\n                    });\n                  }\n                }\n              }}\n            />\n          </div>\n\n\n\n          {/* Scene Info */}\n          <div className=\"absolute top-4 left-4 right-4 flex justify-between items-center\">\n            <div className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm px-3 py-1.5 rounded-md flex items-center\">\n              <MapPin className=\"h-4 w-4 mr-2 text-purple-600\" />\n              <span className=\"font-medium text-sm\">{currentScene.name}</span>\n            </div>\n\n            {currentScene.description && (\n              <div className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm px-3 py-1.5 rounded-md\">\n                <span className=\"text-sm\">{currentScene.description}</span>\n              </div>\n            )}\n          </div>\n\n          {/* Scene Navigation */}\n          {scenes.length > 1 && (\n            <div className=\"absolute top-1/2 left-4 right-4 -translate-y-1/2 flex justify-between\">\n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\"\n                onClick={goToPreviousScene}\n                title=\"Cảnh trước đó\"\n              >\n                <ChevronLeft className=\"h-4 w-4\" />\n              </Button>\n\n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\"\n                onClick={goToNextScene}\n                title=\"Cảnh tiếp theo\"\n              >\n                <ChevronRight className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          )}\n\n          {/* Controls */}\n          <div className=\"absolute bottom-4 right-4 flex gap-2\">\n            {resetButton && (\n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\"\n                onClick={handleReset}\n                title=\"Đặt lại góc nhìn\"\n              >\n                <RotateCcw className=\"h-4 w-4\" />\n              </Button>\n            )}\n\n            {fullscreenButton && (\n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\"\n                onClick={toggleFullscreen}\n                title={isFullscreen ? \"Thoát toàn màn hình\" : \"Toàn màn hình\"}\n              >\n                {isFullscreen ? (\n                  <Minimize className=\"h-4 w-4\" />\n                ) : (\n                  <Maximize className=\"h-4 w-4\" />\n                )}\n              </Button>\n            )}\n          </div>\n\n          {/* Scene Selector */}\n          {showSceneSelector && scenes.length > 1 && (\n            <div className=\"absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2\">\n              {scenes.map((scene, index) => (\n                <Button\n                  key={scene.id}\n                  variant={index === currentSceneIndex ? \"default\" : \"outline\"}\n                  size=\"sm\"\n                  className={`bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm ${\n                    index === currentSceneIndex ? \"bg-purple-600 text-white\" : \"\"\n                  }`}\n                  onClick={() => goToScene(index)}\n                >\n                  {scene.name}\n                </Button>\n              ))}\n            </div>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default View360;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA,kDAAkD;AAClD;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;AAuCO,MAAM,UAAkC,CAAC,EAC9C,MAAM,EACN,cAAc,EACd,YAAY,EAAE,EACd,SAAS,OAAO,EAChB,QAAQ,MAAM,EACd,mBAAmB,IAAI,EACvB,cAAc,IAAI,EAClB,oBAAoB,IAAI,EACzB;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACvD,iBACI,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,kBACvC;IAEN,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAC9B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,UAAU,OAAO,EAAE;gBACrB,IAAI;oBACF,UAAU,OAAO,CAAC,OAAO;gBAC3B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC5C;YACF;QACF;IACF,GAAG,EAAE;IAEL,gCAAgC;IAChC,MAAM,mBAAmB;QACvB,IAAI,CAAC,aAAa,OAAO,EAAE;QAE3B,IAAI,CAAC,cAAc;YACjB,IAAI,aAAa,OAAO,CAAC,iBAAiB,EAAE;gBAC1C,aAAa,OAAO,CAAC,iBAAiB;YACxC;YACA,gBAAgB;QAClB,OAAO;YACL,IAAI,SAAS,cAAc,EAAE;gBAC3B,SAAS,cAAc;YACzB;YACA,gBAAgB;QAClB;IACF;IAEA,2BAA2B;IAC3B,MAAM,cAAc;QAClB,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,OAAO,CAAC;gBACxB,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,OAAO;YACT;QACF;IACF;IAEA,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,yBAAyB;YAC7B,gBAAgB,CAAC,CAAC,SAAS,iBAAiB;QAC9C;QAEA,SAAS,gBAAgB,CAAC,oBAAoB;QAC9C,OAAO;YACL,SAAS,mBAAmB,CAAC,oBAAoB;QACnD;IACF,GAAG,EAAE;IAEL,4BAA4B;IAC5B,MAAM,oBAAoB;QACxB,IAAI,oBAAoB,GAAG;YACzB,qBAAqB,oBAAoB;QAC3C,OAAO;YACL,qBAAqB,OAAO,MAAM,GAAG;QACvC;IACF;IAEA,6BAA6B;IAC7B,MAAM,gBAAgB;QACpB,IAAI,oBAAoB,OAAO,MAAM,GAAG,GAAG;YACzC,qBAAqB,oBAAoB;QAC3C,OAAO;YACL,qBAAqB;QACvB;IACF;IAEA,qCAAqC;IACrC,MAAM,YAAY,CAAC;QACjB,IAAI,SAAS,KAAK,QAAQ,OAAO,MAAM,EAAE;YACvC,qBAAqB;QACvB;IACF;IAEA,kCAAkC;IAClC,MAAM,gBAAgB,CAAC;QACrB,MAAM,aAAa,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QAC1D,IAAI,eAAe,CAAC,GAAG;YACrB,qBAAqB;QACvB;IACF;IAEA,6BAA6B;IAC7B,MAAM,qBAAqB,CAAC;QAC1B,4CAA4C;QAC5C,cAAc;IAChB;IAEA,qBAAqB;IACrB,MAAM,eAAe,MAAM,CAAC,kBAAkB;IAE9C,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAC,0GAA0G,EAAE,WAAW;kBACvI,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAI,KAAK;gBAAc,WAAU;;kCAChC,8OAAC;wBAAI,OAAO;4BAAE;4BAAQ;wBAAM;kCAC1B,cAAA,8OAAC,mKAAA,CAAA,yBAAsB;4BAErB,KAAK;4BACL,KAAK,aAAa,KAAK;4BACvB,QAAQ,eAAe,UAAU;4BACjC,OAAO;4BACP,cAAc;4BACd,WAAW,aAAa,OAAO,IAAI;4BACnC,SAAS;gCACP;oCAAC,mLAAA,CAAA,gBAAa;oCAAE;wCACd,SAAS,aAAa,QAAQ,EAAE,IAAI,CAAA,UAAW,CAAC;gDAC9C,IAAI,QAAQ,EAAE;gDACd,UAAU,QAAQ,QAAQ;gDAC1B,SAAS;oDACP,SAAS,QAAQ,OAAO,IAAI,QAAQ,IAAI;oDACxC,UAAU;gDACZ;gDACA,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;;0BAuBD,EAAE,QAAQ,IAAI,CAAC;;oBAErB,CAAC;gDACD,MAAM;oDAAE,SAAS,QAAQ,EAAE;gDAAC;4CAC9B,CAAC,MAAM,EAAE;oCACX;iCAAE;6BACH;4BACD,SAAS,CAAC;gCACR,iCAAiC;gCACjC,IAAI,aAAa,QAAQ,IAAI,aAAa,QAAQ,CAAC,MAAM,GAAG,GAAG;oCAC7D,MAAM,gBAAgB,SAAS,SAAS,CAAC,mLAAA,CAAA,gBAAa;oCACtD,IAAI,eAAe;wCACjB,cAAc,gBAAgB,CAAC,iBAAiB,CAAC;4CAC/C,IAAI,EAAE,MAAM,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;gDACtD,mBAAmB,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO;4CAC1C;wCACF;oCACF;gCACF;4BACF;2BA1DK,aAAa,EAAE;;;;;;;;;;kCAiExB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAAuB,aAAa,IAAI;;;;;;;;;;;;4BAGzD,aAAa,WAAW,kBACvB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAW,aAAa,WAAW;;;;;;;;;;;;;;;;;oBAMxD,OAAO,MAAM,GAAG,mBACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,OAAM;0CAEN,cAAA,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAGzB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,OAAM;0CAEN,cAAA,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAM9B,8OAAC;wBAAI,WAAU;;4BACZ,6BACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,OAAM;0CAEN,cAAA,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;4BAIxB,kCACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,OAAO,eAAe,wBAAwB;0CAE7C,6BACC,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;yDAEpB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAO3B,qBAAqB,OAAO,MAAM,GAAG,mBACpC,8OAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,kIAAA,CAAA,SAAM;gCAEL,SAAS,UAAU,oBAAoB,YAAY;gCACnD,MAAK;gCACL,WAAW,CAAC,iDAAiD,EAC3D,UAAU,oBAAoB,6BAA6B,IAC3D;gCACF,SAAS,IAAM,UAAU;0CAExB,MAAM,IAAI;+BARN,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AAiB/B;uCAEe", "debugId": null}}, {"offset": {"line": 513, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/view360/google-maps-view.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useState, useEffect } from 'react';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Maximize, Minimize, MapPin, RotateCcw } from 'lucide-react';\n\ninterface GoogleMapsViewProps {\n  mapUrl: string;\n  className?: string;\n  height?: string;\n  width?: string;\n  fullscreenButton?: boolean;\n  reloadButton?: boolean;\n  title?: string;\n  showInfoCard?: boolean;\n}\n\n/**\n * GoogleMapsView - Component để nhúng Google Maps Street View 360 độ (đã đơn giản hóa)\n *\n * @param mapUrl URL của Google Maps Street View (dạng https://maps.app.goo.gl/XXX hoặc https://goo.gl/maps/XXX)\n * @param className CSS class tùy chỉnh\n * @param height Chiều cao của component (mặc định: 400px)\n * @param width Chiều rộng của component (mặc định: 100%)\n * @param fullscreenButton Hiển thị nút toàn màn hình (mặc định: true)\n * @param reloadButton Hiển thị nút tải lại (mặc định: true)\n * @param title Tiêu đề hiển thị phía trên Street View\n * @param showInfoCard Hiển thị thẻ thông tin địa điểm (mặc định: true)\n */\nexport const GoogleMapsView: React.FC<GoogleMapsViewProps> = ({\n  mapUrl,\n  className = '',\n  height = '400px',\n  width = '100%',\n  fullscreenButton = true,\n  reloadButton = true,\n  title,\n  showInfoCard = true,\n}) => {\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const containerRef = useRef<HTMLDivElement>(null);\n  const iframeRef = useRef<HTMLIFrameElement>(null);\n\n  // Xử lý URL Google Maps để lấy embed URL và ẩn UI\n  const getEmbedUrl = (url: string): string => {\n    // Kiểm tra nếu URL đã là embed URL\n    if (url.includes('maps.google.com/maps/embed') || url.includes('www.google.com/maps/embed')) {\n      // Thêm tham số để ẩn UI\n      if (!url.includes('!3m2!1sen!2s!4v')) {\n        // Thêm tham số để ẩn UI\n        const modifiedUrl = url.replace(/!4v\\d+!/, '!4v1!3m2!1sen!2s!4v!');\n        // Thêm tham số để ẩn nút zoom và cho phép truy cập từ localhost\n        return modifiedUrl + '&disableDefaultUI=1&scrollwheel=0&zoomControl=0&origin=localhost:4200';\n      }\n      // Thêm tham số để ẩn nút zoom và cho phép truy cập từ localhost\n      return url + '&disableDefaultUI=1&scrollwheel=0&zoomControl=0&origin=localhost:4200';\n    }\n\n    // Xử lý URL dạng https://maps.app.goo.gl/XXX hoặc https://goo.gl/maps/XXX\n    let embedUrl = '';\n\n    try {\n      // Nếu URL chứa tham số pb (đây là URL embed đã được định dạng)\n      if (url.includes('pb=')) {\n        // Thêm tham số để ẩn UI\n        if (!url.includes('!3m2!1sen!2s!4v')) {\n          const modifiedUrl = url.replace(/!4v\\d+!/, '!4v1!3m2!1sen!2s!4v!');\n          // Thêm tham số để ẩn nút zoom và cho phép truy cập từ localhost\n          return modifiedUrl + '&disableDefaultUI=1&scrollwheel=0&zoomControl=0&origin=localhost:4200';\n        }\n        // Thêm tham số để ẩn nút zoom và cho phép truy cập từ localhost\n        return url + '&disableDefaultUI=1&scrollwheel=0&zoomControl=0&origin=localhost:4200';\n      }\n\n      // Nếu là URL rút gọn, thử trích xuất tham số\n      if (url.includes('maps.app.goo.gl') || url.includes('goo.gl/maps')) {\n        // Đối với URL rút gọn, chúng ta sẽ sử dụng mode=streetview và thêm tham số để ẩn UI\n        embedUrl = `https://www.google.com/maps/embed?pb=!4v1!1m3!1m2!1s${encodeURIComponent(url)}!2s!3m2!1sen!2s!4v!5e0!3m2!1svi!2s!4v1&disableDefaultUI=1&scrollwheel=0&zoomControl=0&origin=localhost:4200`;\n      }\n      // Nếu là URL đầy đủ với tham số\n      else if (url.includes('google.com/maps')) {\n        // Trích xuất tham số từ URL\n        const urlObj = new URL(url);\n        const params = new URLSearchParams(urlObj.search);\n\n        // Nếu có tham số ll (latitude,longitude)\n        if (params.has('ll')) {\n          const ll = params.get('ll');\n          embedUrl = `https://www.google.com/maps/embed?pb=!4v1!1m3!1m2!1s!2s${ll}!3m2!1sen!2s!4v!5e0!3m2!1svi!2s!4v1&disableDefaultUI=1&scrollwheel=0&zoomControl=0&origin=localhost:4200`;\n        }\n        // Nếu có tham số q (query/address)\n        else if (params.has('q')) {\n          const q = params.get('q');\n          embedUrl = `https://www.google.com/maps/embed?pb=!4v1!1m3!1m2!1s!2s${q}!3m2!1sen!2s!4v!5e0!3m2!1svi!2s!4v1&disableDefaultUI=1&scrollwheel=0&zoomControl=0&origin=localhost:4200`;\n        }\n        // Nếu không có tham số phù hợp, sử dụng URL gốc\n        else {\n          embedUrl = `https://www.google.com/maps/embed/v1/streetview?key=YOUR_API_KEY&location=${encodeURIComponent(url)}&showui=false&disableDefaultUI=1&scrollwheel=0&zoomControl=0&origin=localhost:4200`;\n        }\n      }\n      // Nếu không phải URL Google Maps hợp lệ\n      else {\n        console.error('URL không hợp lệ:', url);\n        embedUrl = url; // Sử dụng URL gốc\n      }\n    } catch (error) {\n      console.error('Lỗi khi xử lý URL:', error);\n      embedUrl = url; // Sử dụng URL gốc nếu có lỗi\n    }\n\n    return embedUrl;\n  };\n\n  // Xử lý khi nhấn nút fullscreen\n  const toggleFullscreen = () => {\n    if (!containerRef.current) return;\n\n    if (!isFullscreen) {\n      if (containerRef.current.requestFullscreen) {\n        containerRef.current.requestFullscreen();\n      }\n      setIsFullscreen(true);\n    } else {\n      if (document.exitFullscreen) {\n        document.exitFullscreen();\n      }\n      setIsFullscreen(false);\n    }\n  };\n\n  // Xử lý khi nhấn nút reload\n  const handleReload = () => {\n    if (iframeRef.current) {\n      // Lưu lại URL hiện tại\n      const currentSrc = iframeRef.current.src;\n      // Đặt src thành empty string để buộc iframe reload\n      iframeRef.current.src = '';\n      // Đặt lại src sau một khoảng thời gian ngắn\n      setTimeout(() => {\n        if (iframeRef.current) {\n          iframeRef.current.src = currentSrc;\n        }\n      }, 100);\n    }\n  };\n\n\n\n  // Theo dõi sự kiện fullscreenchange\n  useEffect(() => {\n    const handleFullscreenChange = () => {\n      setIsFullscreen(!!document.fullscreenElement);\n    };\n\n    document.addEventListener('fullscreenchange', handleFullscreenChange);\n    return () => {\n      document.removeEventListener('fullscreenchange', handleFullscreenChange);\n    };\n  }, []);\n\n  // Không cố gắng truy cập vào contentDocument của iframe để tránh lỗi CORS\n  useEffect(() => {\n    if (iframeRef.current) {\n      try {\n        // Chỉ đặt sự kiện onload để biết khi iframe đã tải xong\n        iframeRef.current.onload = () => {\n          console.log('Iframe đã tải xong');\n          // Không cố gắng truy cập vào contentDocument hoặc contentWindow\n        };\n      } catch (error) {\n        console.error('Lỗi khi xử lý iframe:', error);\n      }\n    }\n  }, [iframeRef.current]);\n\n  const embedUrl = getEmbedUrl(mapUrl);\n\n  return (\n    <Card className={`overflow-hidden border-teal-100 dark:border-teal-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs ${className}`}>\n      <CardContent className=\"p-0\">\n        <div ref={containerRef} className=\"relative\">\n          <div style={{ height, width }}>\n            <div className=\"google-maps-iframe-container\">\n              <iframe\n                ref={iframeRef}\n                src={embedUrl}\n                width=\"100%\"\n                height=\"100%\"\n                style={{ border: 0 }}\n                allowFullScreen\n                loading=\"lazy\"\n                referrerPolicy=\"no-referrer-when-downgrade\"\n                className=\"google-maps-iframe\"\n                allow=\"fullscreen\"\n                sandbox=\"allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox allow-presentation allow-forms\"\n              ></iframe>\n            </div>\n          </div>\n\n          {/* Location Info - Simplified */}\n          {title && showInfoCard && (\n            <div className=\"absolute top-4 left-4 z-10\">\n              <div className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm px-3 py-1.5 rounded-md flex items-center\">\n                <MapPin className=\"h-4 w-4 mr-2 text-teal-600 dark:text-teal-400\" />\n                <span className=\"font-medium text-sm\">{title}</span>\n              </div>\n            </div>\n          )}\n\n          {/* Controls - Reload and Fullscreen */}\n          <div className=\"absolute bottom-4 right-4 flex gap-2\">\n            {reloadButton && (\n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-blue-200 dark:border-blue-800 hover:bg-blue-50 dark:hover:bg-blue-900/30\"\n                onClick={handleReload}\n                title=\"Tải lại ảnh\"\n              >\n                <RotateCcw className=\"h-4 w-4 text-blue-600 dark:text-blue-400\" />\n              </Button>\n            )}\n\n            {fullscreenButton && (\n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-blue-200 dark:border-blue-800 hover:bg-blue-50 dark:hover:bg-blue-900/30\"\n                onClick={toggleFullscreen}\n                title={isFullscreen ? \"Thoát toàn màn hình\" : \"Toàn màn hình\"}\n              >\n                {isFullscreen ? (\n                  <Minimize className=\"h-4 w-4 text-blue-600 dark:text-blue-400\" />\n                ) : (\n                  <Maximize className=\"h-4 w-4 text-blue-600 dark:text-blue-400\" />\n                )}\n              </Button>\n            )}\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default GoogleMapsView;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AA8BO,MAAM,iBAAgD,CAAC,EAC5D,MAAM,EACN,YAAY,EAAE,EACd,SAAS,OAAO,EAChB,QAAQ,MAAM,EACd,mBAAmB,IAAI,EACvB,eAAe,IAAI,EACnB,KAAK,EACL,eAAe,IAAI,EACpB;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAE5C,kDAAkD;IAClD,MAAM,cAAc,CAAC;QACnB,mCAAmC;QACnC,IAAI,IAAI,QAAQ,CAAC,iCAAiC,IAAI,QAAQ,CAAC,8BAA8B;YAC3F,wBAAwB;YACxB,IAAI,CAAC,IAAI,QAAQ,CAAC,oBAAoB;gBACpC,wBAAwB;gBACxB,MAAM,cAAc,IAAI,OAAO,CAAC,WAAW;gBAC3C,gEAAgE;gBAChE,OAAO,cAAc;YACvB;YACA,gEAAgE;YAChE,OAAO,MAAM;QACf;QAEA,0EAA0E;QAC1E,IAAI,WAAW;QAEf,IAAI;YACF,+DAA+D;YAC/D,IAAI,IAAI,QAAQ,CAAC,QAAQ;gBACvB,wBAAwB;gBACxB,IAAI,CAAC,IAAI,QAAQ,CAAC,oBAAoB;oBACpC,MAAM,cAAc,IAAI,OAAO,CAAC,WAAW;oBAC3C,gEAAgE;oBAChE,OAAO,cAAc;gBACvB;gBACA,gEAAgE;gBAChE,OAAO,MAAM;YACf;YAEA,6CAA6C;YAC7C,IAAI,IAAI,QAAQ,CAAC,sBAAsB,IAAI,QAAQ,CAAC,gBAAgB;gBAClE,oFAAoF;gBACpF,WAAW,CAAC,oDAAoD,EAAE,mBAAmB,KAAK,2GAA2G,CAAC;YACxM,OAEK,IAAI,IAAI,QAAQ,CAAC,oBAAoB;gBACxC,4BAA4B;gBAC5B,MAAM,SAAS,IAAI,IAAI;gBACvB,MAAM,SAAS,IAAI,gBAAgB,OAAO,MAAM;gBAEhD,yCAAyC;gBACzC,IAAI,OAAO,GAAG,CAAC,OAAO;oBACpB,MAAM,KAAK,OAAO,GAAG,CAAC;oBACtB,WAAW,CAAC,uDAAuD,EAAE,GAAG,wGAAwG,CAAC;gBACnL,OAEK,IAAI,OAAO,GAAG,CAAC,MAAM;oBACxB,MAAM,IAAI,OAAO,GAAG,CAAC;oBACrB,WAAW,CAAC,uDAAuD,EAAE,EAAE,wGAAwG,CAAC;gBAClL,OAEK;oBACH,WAAW,CAAC,0EAA0E,EAAE,mBAAmB,KAAK,kFAAkF,CAAC;gBACrM;YACF,OAEK;gBACH,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,WAAW,KAAK,kBAAkB;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,WAAW,KAAK,6BAA6B;QAC/C;QAEA,OAAO;IACT;IAEA,gCAAgC;IAChC,MAAM,mBAAmB;QACvB,IAAI,CAAC,aAAa,OAAO,EAAE;QAE3B,IAAI,CAAC,cAAc;YACjB,IAAI,aAAa,OAAO,CAAC,iBAAiB,EAAE;gBAC1C,aAAa,OAAO,CAAC,iBAAiB;YACxC;YACA,gBAAgB;QAClB,OAAO;YACL,IAAI,SAAS,cAAc,EAAE;gBAC3B,SAAS,cAAc;YACzB;YACA,gBAAgB;QAClB;IACF;IAEA,4BAA4B;IAC5B,MAAM,eAAe;QACnB,IAAI,UAAU,OAAO,EAAE;YACrB,uBAAuB;YACvB,MAAM,aAAa,UAAU,OAAO,CAAC,GAAG;YACxC,mDAAmD;YACnD,UAAU,OAAO,CAAC,GAAG,GAAG;YACxB,4CAA4C;YAC5C,WAAW;gBACT,IAAI,UAAU,OAAO,EAAE;oBACrB,UAAU,OAAO,CAAC,GAAG,GAAG;gBAC1B;YACF,GAAG;QACL;IACF;IAIA,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,yBAAyB;YAC7B,gBAAgB,CAAC,CAAC,SAAS,iBAAiB;QAC9C;QAEA,SAAS,gBAAgB,CAAC,oBAAoB;QAC9C,OAAO;YACL,SAAS,mBAAmB,CAAC,oBAAoB;QACnD;IACF,GAAG,EAAE;IAEL,0EAA0E;IAC1E,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,OAAO,EAAE;YACrB,IAAI;gBACF,wDAAwD;gBACxD,UAAU,OAAO,CAAC,MAAM,GAAG;oBACzB,QAAQ,GAAG,CAAC;gBACZ,gEAAgE;gBAClE;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;YACzC;QACF;IACF,GAAG;QAAC,UAAU,OAAO;KAAC;IAEtB,MAAM,WAAW,YAAY;IAE7B,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAC,sGAAsG,EAAE,WAAW;kBACnI,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAI,KAAK;gBAAc,WAAU;;kCAChC,8OAAC;wBAAI,OAAO;4BAAE;4BAAQ;wBAAM;kCAC1B,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,KAAK;gCACL,KAAK;gCACL,OAAM;gCACN,QAAO;gCACP,OAAO;oCAAE,QAAQ;gCAAE;gCACnB,eAAe;gCACf,SAAQ;gCACR,gBAAe;gCACf,WAAU;gCACV,OAAM;gCACN,SAAQ;;;;;;;;;;;;;;;;oBAMb,SAAS,8BACR,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0MAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAK,WAAU;8CAAuB;;;;;;;;;;;;;;;;;kCAM7C,8OAAC;wBAAI,WAAU;;4BACZ,8BACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,OAAM;0CAEN,cAAA,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;4BAIxB,kCACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,OAAO,eAAe,wBAAwB;0CAE7C,6BACC,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;yDAEpB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStC;uCAEe", "debugId": null}}, {"offset": {"line": 798, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/view360/universal-view360.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { View360, PanoramaScene } from './view360';\nimport { GoogleMapsView } from './google-maps-view';\n\n// Định nghĩa các loại nguồn dữ liệu 360 độ\nexport type View360Source =\n  | { type: 'panorama'; scenes: PanoramaScene[]; initialSceneId?: string }\n  | { type: 'google-maps'; url: string; title?: string };\n\ninterface UniversalView360Props {\n  source: View360Source;\n  className?: string;\n  height?: string;\n  width?: string;\n  fullscreenButton?: boolean;\n  reloadButton?: boolean;\n  showSceneSelector?: boolean;\n  showInfoCard?: boolean;\n}\n\n/**\n * UniversalView360 - Component tổng hợp để hiển thị cả ảnh panorama 360 độ và Google Maps Street View (đã đơn giản hóa)\n *\n * @param source Nguồn dữ liệu 360 độ (panorama hoặc google-maps)\n * @param className CSS class tùy chỉnh\n * @param height Chiều cao của component (mặc định: 400px)\n * @param width Chiều rộng của component (mặc định: 100%)\n * @param fullscreenButton Hiển thị nút toàn màn hình (mặc định: true)\n * @param reloadButton Hiển thị nút tải lại (mặc định: true)\n * @param showSceneSelector Hiển thị bộ chọn cảnh (chỉ áp dụng cho panorama, mặc định: true)\n * @param showInfoCard Hiển thị thẻ thông tin địa điểm (chỉ áp dụng cho google-maps, mặc định: true)\n */\nexport const UniversalView360: React.FC<UniversalView360Props> = ({\n  source,\n  className = '',\n  height = '400px',\n  width = '100%',\n  fullscreenButton = true,\n  reloadButton = true,\n  showSceneSelector = true,\n  showInfoCard = true,\n}) => {\n  // Hiển thị component tương ứng dựa trên loại nguồn dữ liệu\n  if (source.type === 'panorama') {\n    return (\n      <View360\n        scenes={source.scenes}\n        initialSceneId={source.initialSceneId}\n        className={className}\n        height={height}\n        width={width}\n        fullscreenButton={fullscreenButton}\n        resetButton={true}\n        showSceneSelector={showSceneSelector}\n      />\n    );\n  } else if (source.type === 'google-maps') {\n    return (\n      <GoogleMapsView\n        mapUrl={source.url}\n        className={className}\n        height={height}\n        width={width}\n        fullscreenButton={fullscreenButton}\n        reloadButton={reloadButton}\n        title={source.title}\n        showInfoCard={showInfoCard}\n      />\n    );\n  }\n\n  // Trường hợp không có nguồn dữ liệu hợp lệ\n  return (\n    <div className={`flex items-center justify-center ${className}`} style={{ height, width }}>\n      <p className=\"text-muted-foreground\">Không có dữ liệu 360° hợp lệ</p>\n    </div>\n  );\n};\n\nexport default UniversalView360;\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAkCO,MAAM,mBAAoD,CAAC,EAChE,MAAM,EACN,YAAY,EAAE,EACd,SAAS,OAAO,EAChB,QAAQ,MAAM,EACd,mBAAmB,IAAI,EACvB,eAAe,IAAI,EACnB,oBAAoB,IAAI,EACxB,eAAe,IAAI,EACpB;IACC,2DAA2D;IAC3D,IAAI,OAAO,IAAI,KAAK,YAAY;QAC9B,qBACE,8OAAC,sIAAA,CAAA,UAAO;YACN,QAAQ,OAAO,MAAM;YACrB,gBAAgB,OAAO,cAAc;YACrC,WAAW;YACX,QAAQ;YACR,OAAO;YACP,kBAAkB;YAClB,aAAa;YACb,mBAAmB;;;;;;IAGzB,OAAO,IAAI,OAAO,IAAI,KAAK,eAAe;QACxC,qBACE,8OAAC,qJAAA,CAAA,iBAAc;YACb,QAAQ,OAAO,GAAG;YAClB,WAAW;YACX,QAAQ;YACR,OAAO;YACP,kBAAkB;YAClB,cAAc;YACd,OAAO,OAAO,KAAK;YACnB,cAAc;;;;;;IAGpB;IAEA,2CAA2C;IAC3C,qBACE,8OAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;QAAE,OAAO;YAAE;YAAQ;QAAM;kBACtF,cAAA,8OAAC;YAAE,WAAU;sBAAwB;;;;;;;;;;;AAG3C;uCAEe", "debugId": null}}, {"offset": {"line": 870, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/components/ui/radix-ui/tabs.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as TabsPrimitive from '@radix-ui/react-tabs';\n\nimport { cn } from '@/lib/utils';\n\nconst Tabs = TabsPrimitive.Root;\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      'inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground',\n      className\n    )}\n    {...props}\n  />\n));\nTabsList.displayName = TabsPrimitive.List.displayName;\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-xs',\n      className\n    )}\n    {...props}\n  />\n));\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      'mt-2 ring-offset-background focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',\n      className\n    )}\n    {...props}\n  />\n));\nTabsContent.displayName = TabsPrimitive.Content.displayName;\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent };\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 923, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/view360/view360-locations.ts"], "sourcesContent": ["/**\n * Predefined locations for the 360-degree view feature\n */\n\nexport interface View360Location {\n  id: string;\n  name: string;\n  city: string;\n  region: string;\n  description: string;\n  coordinates: {\n    lat: number;\n    lng: number;\n  };\n  googleMapsUrl: string;\n}\n\nexport const VIEW_360_LOCATIONS: View360Location[] = [\n  // Miền Nam\n  {\n    id: 'bai-sao',\n    name: 'Bãi Sao',\n    city: 'Kiên Giang',\n    region: 'Miền Nam',\n    description: '',\n    coordinates: {\n      lat: 10.05725757562915,\n      lng: 104.0363948436442\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747505297576!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQzRyZV9SN2dF!2m2!1d10.0552462121511!2d104.0366325129505!3f41.32156850984014!4f-22.57191954269578!5f0.7820865974627469'\n  },\n  {\n    id: 'hon-thom',\n    name: '<PERSON>òn Thơm',\n    city: 'Phú Quốc',\n    region: 'Miền Nam',\n    description: '',\n    coordinates: {\n      lat: 9.954605838430725,\n      lng: 104.0178143976055\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747332749752!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQzR3NERDSUE.!2m2!1d9.954605838430725!2d104.0178143976055!3f352.99579097798187!4f-11.542141392533921!5f0.7820865974627469'\n  },\n  {\n    id: 'vinpearl',\n    name: 'Vinpearl Resort',\n    city: 'Phú Quốc',\n    region: 'Miền Nam',\n    description: '',\n    coordinates: {\n      lat: 10.33683427532572,\n      lng: 103.8555491298273\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747332930528!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQzRyZV9zUlE.!2m2!1d10.33683427532572!2d103.8555491298273!3f9.87975441837457!4f-61.96086477266688!5f0.7820865974627469'\n  },\n  {\n    id: 'landmark',\n    name: 'Landmark 81',\n    city: 'Hồ Chí Minh',\n    region: 'Miền Nam',\n    description: '',\n    coordinates: {\n      lat: 10.79354656053439,\n      lng: 106.7240047363216\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747500249620!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJRGtqWXFtSmc.!2m2!1d10.79354656053439!2d106.7240047363216!3f65.9868573871558!4f-4.652932567714487!5f0.7820865974627469'\n  },\n  {\n    id: 'rach-vem',\n    name: 'Rạch Vẹm',\n    city: 'Phú Quốc',\n    region: 'Miền Nam',\n    description: '',\n    coordinates: {\n      lat: 10.37304277793628,\n      lng: 103.9377705339461\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747333532742!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQzRqZGo5S0E.!2m2!1d10.37304277793628!2d103.9377705339461!3f216.24777645854576!4f-0.38721998348161435!5f0.7820865974627469'\n  },\n  {\n    id: 'pho-di-bo-nguyen-hue',\n    name: 'Phố đi bộ Nguyễn Huệ',\n    city: 'Hồ Chí Minh',\n    region: 'Miền Nam',\n    description: '',\n    coordinates: {\n      lat: 10.37304277793628,\n      lng: 103.9377705339461\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747500618497!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQzQ0OHlWendF!2m2!1d10.77261925857123!2d106.7052078134514!3f98.16935090250226!4f12.161081380563019!5f0.7820865974627469'\n  },\n  {\n    id: 'ben-ninh-kieu',\n    name: 'Bến Ninh Kiều',\n    city: 'Cần Thơ',\n    region: 'Miền Nam',\n    description: '',\n    coordinates: {\n      lat: 10.0333,\n      lng: 105.7833\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747333532742!6m8!1m7!1sCAoSLEFGMVFpcE5XVnRXVXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXQ!2m2!1d10.0333!2d105.7833!3f0!4f0!5f0.7820865974627469'\n  },\n  {\n    id: 'cho-noi-cai-rang',\n    name: 'Chợ nổi Cái Răng',\n    city: 'Cần Thơ',\n    region: 'Miền Nam',\n    description: '',\n    coordinates: {\n      lat: 10.0361,\n      lng: 105.7908\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747333532742!6m8!1m7!1sCAoSLEFGMVFpcE5XVnRXVXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXQ!2m2!1d10.0361!2d105.7908!3f0!4f0!5f0.7820865974627469'\n  },\n  {\n    id: 'mui-ne',\n    name: 'Mũi Né',\n    city: 'Phan Thiết',\n    region: 'Miền Nam',\n    description: '',\n    coordinates: {\n      lat: 10.92277134063956,\n      lng: 108.2827949618694\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747413870859!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJRHFxOG5zRGc.!2m2!1d10.92277134063956!2d108.2827949618694!3f250.67714063698654!4f-21.37455813653939!5f0.7820865974627469'\n  },\n  {\n    id: 'nui-den',\n    name: 'Núi Đèn',\n    city: 'Hà Tiên',\n    region: 'Miền Nam',\n    description: '',\n    coordinates: {\n      lat: 9.954605838430725,\n      lng: 104.0178143976055\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747499560225!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJRHFyZWkyY3c.!2m2!1d10.37289226596782!2d104.4451584259442!3f211.81944677726216!4f0!5f0.7820865974627469'\n  },\n  {\n    id: 'rung-tram-tra-su',\n    name: 'Rừng tràm Trà Sư',\n    city: 'An Giang',\n    region: 'Miền Nam',\n    description: '',\n    coordinates: {\n      lat: 10.5833,\n      lng: 105.0833\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747495047834!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJRHEtZXZDZmc.!2m2!1d10.58140358825379!2d105.0589104983493!3f22.155458669411246!4f4.898122194557175!5f0.4000000000000002'\n  },\n  {\n    id: 'nui-sam',\n    name: 'Núi Sam và Miếu Bà Chúa Xứ',\n    city: 'An Giang',\n    region: 'Miền Nam',\n    description: '',\n    coordinates: {\n      lat: 10.6751202698838,\n      lng: 105.0782791127975\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747495235097!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQ09zNFQ3X2dF!2m2!1d10.68210573732136!2d105.0802910017875!3f261.63629762709695!4f10.182778710084548!5f0.4000000000000002'\n  },\n  {\n    id: 'bai-sau',\n    name: 'Bãi Sau Vũng Tàu',\n    city: 'Vũng Tàu',\n    region: 'Miền Nam',\n    description: '',\n    coordinates: {\n      lat: 10.34907186654009,\n      lng: 107.0971676648209\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747414304414!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJRDJzSlRnZWc.!2m2!1d10.34907186654009!2d107.0971676648209!3f38.71530157927323!4f-82.21191574849168!5f0.7820865974627469'\n  },\n  {\n    id: 'nha-cong-tu-bac-lieu',\n    name: 'Nhà Công tử Bạc Liêu',\n    city: 'Bạc Liêu',\n    region: 'Miền Nam',\n    description: '',\n    coordinates: {\n      lat: 9.2833,\n      lng: 105.7167\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747494902258!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQ3BoZGJZZFE.!2m2!1d9.28408452732009!2d105.7238055313035!3f7.423296444444819!4f-26.258200588604304!5f0.4000000000000002'\n  },\n  {\n    id: 'quan-dao-nam-du',\n    name: 'Quần đảo Nam Du',\n    city: 'Kiên Giang',\n    region: 'Miền Nam',\n    description: '',\n    coordinates: {\n      lat: 9.66389502040506,\n      lng: 104.3496417202332\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747414488523!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQ1VuTlMzQ2c.!2m2!1d9.66389502040506!2d104.3496417202332!3f99.81168081790992!4f0.7850056876728928!5f0.4000000000000002'\n  },\n\n\n  // Miền Trung\n  {\n    id: 'pho-co-hoi-an',\n    name: 'Phố cổ Hội An',\n    city: 'Hội An',\n    region: 'Miền Trung',\n    description: '',\n    coordinates: {\n      lat: 15.87498002820639,\n      lng: 108.3359990034829\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747414738724!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQzR5ckRWRnc.!2m2!1d15.87498002820639!2d108.3359990034829!3f176.96713275830936!4f2.585110776640718!5f0.7820865974627469'\n  },\n  {\n    id: 'bien-my-khe',\n    name: 'Biển Mỹ Khê',\n    city: 'Đà Nẵng',\n    region: 'Miền Trung',\n    description: '',\n    coordinates: {\n      lat: 16.03155926286188,\n      lng: 108.2569290591596\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747414942488!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQ2s4dVdmY1E.!2m2!1d16.03155926286188!2d108.2569290591596!3f70.15133672476054!4f16.882785257899485!5f0.7820865974627469'\n  },\n  {\n    id: 'cau-rong',\n    name: 'Cầu Rồng',\n    city: 'Đà Nẵng',\n    region: 'Miền Trung',\n    description: '',\n    coordinates: {\n      lat: 16.06128313959818,\n      lng: 108.2296736742121\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747416784145!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJRGFvLXFHV0E.!2m2!1d16.06128313959818!2d108.2296736742121!3f250.46428959931467!4f10.691060139093182!5f0.7820865974627469'\n  },\n  {\n    id: 'co-do-hue',\n    name: 'Cố đô Huế',\n    city: 'Huế',\n    region: 'Miền Trung',\n    description: '',\n    coordinates: {\n      lat: 16.46576501032935,\n      lng: 107.5876617319682\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747416638814!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJRFd2cERiaVFF!2m2!1d16.46576501032935!2d107.5876617319682!3f194.21115801890443!4f-13.360151105557819!5f0.7820865974627469'\n  },\n  {\n    id: 'phong-nha-ke-bang',\n    name: 'Phong Nha - Kẻ Bàng',\n    city: 'Quảng Bình',\n    region: 'Miền Trung',\n    description: '',\n    coordinates: {\n      lat: 17.47767903045252,\n      lng: 106.1340395116056\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747416553678!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQ0RwT2FoUGc.!2m2!1d17.47767903045252!2d106.1340395116056!3f160.51280089450208!4f-5.969980438522754!5f0.7820865974627469'\n  },\n  {\n    id: 'ba-na-hills',\n    name: 'Bà Nà Hills',\n    city: 'Đà Nẵng',\n    region: 'Miền Trung',\n    description: '',\n    coordinates: {\n      lat: 15.99480629711804,\n      lng: 107.996643130635\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747415798333!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQ0U2c0dOSUE.!2m2!1d15.99480629711804!2d107.996643130635!3f19.657977674331732!4f-2.1407677760772827!5f0.4000000000000002'\n  },\n  {\n    id: 'lang-co-beach',\n    name: 'Biển Lăng Cô',\n    city: 'Huế',\n    region: 'Miền Trung',\n    description: '',\n    coordinates: {\n      lat: 16.27289566516503,\n      lng: 108.0599606277014\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747416038681!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQ2NuZFQ3RlE.!2m2!1d16.27289566516503!2d108.0599606277014!3f303.7113294016193!4f0.9003761491211009!5f0.7820865974627469'\n  },\n  {\n    id: 'thien-mu-pagoda',\n    name: 'Chùa Thiên Mụ',\n    city: 'Huế',\n    region: 'Miền Trung',\n    description: '',\n    coordinates: {\n      lat: 16.45225832914808,\n      lng: 107.5450653153509\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747416075819!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQ2MtdnpqWnc.!2m2!1d16.45225832914808!2d107.5450653153509!3f49.94292753934999!4f-5.757623044544431!5f0.7820865974627469'\n  },\n  {\n    id: 'son-tra-peninsula',\n    name: 'Bán đảo Sơn Trà',\n    city: 'Đà Nẵng',\n    region: 'Miền Trung',\n    description: '',\n    coordinates: {\n      lat: 16.08574793591492,\n      lng: 108.2259978348675\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747416201895!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQ090b3FubkFF!2m2!1d16.08574793591492!2d108.2259978348675!3f211.3137785310005!4f-30.892596490263706!5f0.7820865974627469'\n  },\n  {\n    id: 'ly-son-island',\n    name: 'Đảo Lý Sơn',\n    city: 'Quảng Ngãi',\n    region: 'Miền Trung',\n    description: '',\n    coordinates: {\n      lat: 15.37973169526243,\n      lng: 109.0956007385369\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747416264668!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJRHF2ZDJiNWdF!2m2!1d15.37973169526243!2d109.0956007385369!3f328.7627233443913!4f0.7540181699922215!5f0.7820865974627469'\n  },\n  {\n    id: 'marble-mountains',\n    name: 'Ngũ Hành Sơn',\n    city: 'Đà Nẵng',\n    region: 'Miền Trung',\n    description: '',\n    coordinates: {\n      lat: 16.0406273796354,\n      lng: 108.2502678178907\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747495746304!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQ1dqS2Z1RkE.!2m2!1d16.00357997322952!2d108.2643904557801!3f73.7676291487919!4f16.219845205115803!5f0.4000000000000002'\n  },\n  {\n    id: 'tam-giang-lagoon',\n    name: 'Đầm phá Tam Giang',\n    city: 'Huế',\n    region: 'Miền Trung',\n    description: '',\n    coordinates: {\n      lat: 16.61206061804068,\n      lng: 107.5533934158846\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747416423335!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQ2xxWkx3bUFF!2m2!1d16.61206061804068!2d107.5533934158846!3f181.57897343718923!4f-33.32387855244893!5f0.7820865974627469'\n  },\n  {\n    id: 'my-son-sanctuary',\n    name: 'Thánh địa Mỹ Sơn',\n    city: 'Quảng Nam',\n    region: 'Miền Trung',\n    description: '',\n    coordinates: {\n      lat: 15.76300706641276,\n      lng: 108.123989442643\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747416506358!6m8!1m7!1sCAoSHENJQUJJaEFEeWRkbXFSS0JfbWV1OU13QUNKaGs.!2m2!1d15.76300706641276!2d108.123989442643!3f344.50527686499316!4f-14.758897808970943!5f0.7820865974627469'\n  },\n\n  // Miền Bắc\n  {\n    id: 'vinh-ha-long',\n    name: 'Vịnh Hạ Long',\n    city: 'Hạ Long',\n    region: 'Miền Bắc',\n    description: '',\n    coordinates: {\n      lat: 20.9100512289355,\n      lng: 107.1839024224061\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747415018081!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJRGZqT2VkdlFF!2m2!1d20.9100512289355!2d107.1839024224061!3f87.5373364913488!4f6.563715102588802!5f0.4000000000000002'\n  },\n  {\n    id: 'ho-hoan-kiem',\n    name: 'Hồ Hoàn Kiếm',\n    city: 'Hà Nội',\n    region: 'Miền Bắc',\n    description: '',\n    coordinates: {\n      lat: 21.0286301307459,\n      lng: 105.8524792676769\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747415060722!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJRDRfOWFlWFE.!2m2!1d21.0286301307459!2d105.8524792676769!3f83.13261344682465!4f-21.383569861287327!5f0.7820865974627469'\n  },\n  {\n    id: 'pho-co-ha-noi',\n    name: 'Phố cổ Hà Nội',\n    city: 'Hà Nội',\n    region: 'Miền Bắc',\n    description: '',\n    coordinates: {\n      lat: 21.03491298285746,\n      lng: 105.8501825722273\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747417954095!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQ0I4cG15OUFF!2m2!1d21.03491298285746!2d105.8501825722273!3f261.0068602876837!4f19.8481637385228!5f0.7820865974627469'\n  },\n  {\n    id: 'sapa',\n    name: 'Sa Pa',\n    city: 'Lào Cai',\n    region: 'Miền Bắc',\n    description: '',\n    coordinates: {\n      lat: 22.3351392005417,\n      lng: 103.8414929631456\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747418046410!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQzJsNkN0bHdF!2m2!1d22.3351392005417!2d103.8414929631456!3f66.51452436282926!4f-21.456194225199894!5f0.7820865974627469'\n  },\n  {\n    id: 'moc-chau',\n    name: 'Mộc Châu',\n    city: 'Sơn La',\n    region: 'Miền Bắc',\n    description: '',\n    coordinates: {\n      lat: 20.872343214208,\n      lng: 104.5861787075125\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747418108902!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJREIxYzdubUFF!2m2!1d20.872343214208!2d104.5861787075125!3f143.9887647994639!4f-4.121293306540068!5f0.7820865974627469'\n  },\n  {\n    id: 'dong-van',\n    name: 'Cao nguyên đá Đồng Văn',\n    city: 'Hà Giang',\n    region: 'Miền Bắc',\n    description: '',\n    coordinates: {\n      lat: 23.26028780776931,\n      lng: 105.2575134451518\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747418153307!6m8!1m7!1sCAoSLEFGMVFpcE9qSVlCckNaUFFQNzljQ2o0Rkw0OTBlMmtSaEJ4bTlsVmtMUTl3!2m2!1d23.26028780776931!2d105.2575134451518!3f113.12560539499034!4f9.127558760790421!5f0.4000000000000002'\n  },\n  {\n    id: 'tam-coc-bich-dong',\n    name: 'Tam Cốc - Bích Động',\n    city: 'Ninh Bình',\n    region: 'Miền Bắc',\n    description: '',\n    coordinates: {\n      lat: 20.21836211713047,\n      lng: 105.9163782869523\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747421765637!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQ2NncTZRTVE.!2m2!1d20.21836211713047!2d105.9163782869523!3f278.4057461623623!4f8.630222287469934!5f0.4000000000000002'\n  },\n  {\n    id: 'trang-an',\n    name: 'Tràng An',\n    city: 'Ninh Bình',\n    region: 'Miền Bắc',\n    description: '',\n    coordinates: {\n      lat: 20.26097517872346,\n      lng: 105.9488758025064\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747418243175!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJRGk0dVdBTEE.!2m2!1d20.26097517872346!2d105.9488758025064!3f245.81152627846677!4f2.946906027989897!5f0.4000000000000002'\n  },\n  {\n    id: 'ba-be',\n    name: 'Hồ Ba Bể',\n    city: 'Bắc Kạn',\n    region: 'Miền Bắc',\n    description: '',\n    coordinates: {\n      lat: 22.40503503635209,\n      lng: 105.618247999901\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747418301894!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJRHFqZEdqRnc.!2m2!1d22.40503503635209!2d105.618247999901!3f161.81056016349112!4f15.740176686016312!5f0.4000000000000002'\n  },\n  {\n    id: 'ban-gioc',\n    name: 'Thác Bản Giốc',\n    city: 'Cao Bằng',\n    region: 'Miền Bắc',\n    description: '',\n    coordinates: {\n      lat: 22.85571692846102,\n      lng: 106.723523327683\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747418347303!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJREVzSmJCLVFF!2m2!1d22.85571692846102!2d106.723523327683!3f265.75594752276453!4f-2.06832889283983!5f0.4000000000000002'\n  },\n  {\n    id: 'cat-ba',\n    name: 'Đảo Cát Bà',\n    city: 'Hải Phòng',\n    region: 'Miền Bắc',\n    description: '',\n    coordinates: {\n      lat: 20.7176737657048,\n      lng: 107.0475840797433\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747418414959!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJRDR3LVRQNVFF!2m2!1d20.7176737657048!2d107.0475840797433!3f292.59219783413215!4f8.61224911887335!5f0.4000000000000002'\n  },\n  {\n    id: 'chua-huong',\n    name: 'Chùa Hương',\n    city: 'Hà Nội',\n    region: 'Miền Bắc',\n    description: '',\n    coordinates: {\n      lat: 20.61814211249567,\n      lng: 105.7465708887379\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747418476805!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQ0VoZnJscEFF!2m2!1d20.61814211249567!2d105.7465708887379!3f147.52896567946613!4f1.6896686787253543!5f0.4000000000000002'\n  },\n  {\n    id: 'chua-bai-dinh',\n    name: 'Chùa Bái Đính',\n    city: 'Ninh Bình',\n    region: 'Miền Bắc',\n    description: '',\n    coordinates: {\n      lat: 20.27640809408571,\n      lng: 105.8647083883197\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747418525319!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQzR2OVRsSEE.!2m2!1d20.27640809408571!2d105.8647083883197!3f13.312925768905245!4f-5.935714180298291!5f0.4000000000000002'\n  },\n  {\n    id: 'lang-bac-ho',\n    name: 'Lăng Chủ tịch Hồ Chí Minh',\n    city: 'Hà Nội',\n    region: 'Miền Bắc',\n    description: '',\n    coordinates: {\n      lat: 21.03577328129474,\n      lng: 105.8348450571805\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747418643649!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJRDR4TWpBSEE.!2m2!1d21.03577328129474!2d105.8348450571805!3f340.95211465312445!4f5.437508876767197!5f0.4000000000000002'\n  },\n  {\n    id: 'mai-chau',\n    name: 'Mai Châu',\n    city: 'Hòa Bình',\n    region: 'Miền Bắc',\n    description: '',\n    coordinates: {\n      lat: 20.65792083742968,\n      lng: 105.0651627041354\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747418672165!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQ01zTXF2bmdF!2m2!1d20.65792083742968!2d105.0651627041354!3f11.58085652629353!4f-10.144319783809792!5f0.4000000000000002'\n  },\n  {\n    id: 'vuon-quoc-gia-ba-vi',\n    name: 'Vườn quốc gia Ba Vì',\n    city: 'Hà Nội',\n    region: 'Miền Bắc',\n    description: '',\n    coordinates: {\n      lat: 21.0868,\n      lng: 105.3725\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747502185809!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQ3BpSlQ0QWc.!2m2!1d21.09601891537984!2d105.4021723888768!3f10.588603722511014!4f10.5295985479449!5f0.4000000000000002'\n  },\n  {\n    id: 'den-hung',\n    name: 'Đền Hùng',\n    city: 'Phú Thọ',\n    region: 'Miền Bắc',\n    description: '',\n    coordinates: {\n      lat: 21.3869,\n      lng: 105.3683\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747501724744!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQzA1dDMxd1FF!2m2!1d21.36869176390695!2d105.3245776794423!3f148.0523867968432!4f-0.4643377189296558!5f0.4000000000000002'\n  },\n  {\n    id: 'yen-tu',\n    name: 'Yên Tử',\n    city: 'Quảng Ninh',\n    region: 'Miền Bắc',\n    description: '',\n    coordinates: {\n      lat: 21.0243,\n      lng: 106.7258\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747501808479!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQzZsY3ZqdmdF!2m2!1d21.10999434178431!2d106.7303056171739!3f206.85988696603425!4f-7.526339644999354!5f0.4000000000000002'\n  },\n  {\n    id: 'thung-lung-bac-son',\n    name: 'Thung lũng Bắc Sơn',\n    city: 'Lạng Sơn',\n    region: 'Miền Bắc',\n    description: '',\n    coordinates: {\n      lat: 21.9167,\n      lng: 106.3333\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747501888874!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQ0UwdWpfc1FF!2m2!1d21.82327158626864!2d106.3645267382869!3f9.221122842440405!4f11.198685276810096!5f0.4000000000000002'\n  },\n  {\n    id: 'dao-co-to',\n    name: 'Đảo Cô Tô',\n    city: 'Quảng Ninh',\n    region: 'Miền Bắc',\n    description: '',\n    coordinates: {\n      lat: 20.9833,\n      lng: 107.7667\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747502015504!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQ014cktXb3dF!2m2!1d21.0091022647372!2d107.751909999064!3f132.33947247101014!4f-41.172825724925644!5f0.4000000000000002'\n  }\n];\n\n/**\n * Filter locations by search query\n * @param query Search query\n * @returns Filtered locations\n */\nexport function searchLocations(query: string): View360Location[] {\n  if (!query || query.trim() === '') {\n    return VIEW_360_LOCATIONS;\n  }\n\n  const normalizedQuery = query.toLowerCase().trim();\n\n  return VIEW_360_LOCATIONS.filter(location =>\n    location.name.toLowerCase().includes(normalizedQuery) ||\n    location.city.toLowerCase().includes(normalizedQuery) ||\n    location.region.toLowerCase().includes(normalizedQuery) ||\n    location.description.toLowerCase().includes(normalizedQuery)\n  );\n}\n\n/**\n * Get locations by region\n * @param region Region name\n * @returns Locations in the specified region\n */\nexport function getLocationsByRegion(region: string): View360Location[] {\n  if (!region || region === 'all') {\n    return VIEW_360_LOCATIONS;\n  }\n\n  return VIEW_360_LOCATIONS.filter(location =>\n    location.region.toLowerCase() === region.toLowerCase()\n  );\n}\n\n/**\n * Get locations by city\n * @param city City name\n * @returns Locations in the specified city\n */\nexport function getLocationsByCity(city: string): View360Location[] {\n  if (!city || city === 'all') {\n    return VIEW_360_LOCATIONS;\n  }\n\n  return VIEW_360_LOCATIONS.filter(location =>\n    location.city.toLowerCase() === city.toLowerCase()\n  );\n}\n\n/**\n * Get all unique regions\n * @returns Array of unique region names\n */\nexport function getAllRegions(): string[] {\n  const regions = VIEW_360_LOCATIONS.map(location => location.region);\n  return [...new Set(regions)].sort();\n}\n\n/**\n * Get all unique cities\n * @returns Array of unique city names\n */\nexport function getAllCities(): string[] {\n  const cities = VIEW_360_LOCATIONS.map(location => location.city);\n  return [...new Set(cities)].sort();\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;AAeM,MAAM,qBAAwC;IACnD,WAAW;IACX;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IAGA,aAAa;IACb;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IAEA,WAAW;IACX;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;CACD;AAOM,SAAS,gBAAgB,KAAa;IAC3C,IAAI,CAAC,SAAS,MAAM,IAAI,OAAO,IAAI;QACjC,OAAO;IACT;IAEA,MAAM,kBAAkB,MAAM,WAAW,GAAG,IAAI;IAEhD,OAAO,mBAAmB,MAAM,CAAC,CAAA,WAC/B,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,oBACrC,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,oBACrC,SAAS,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,oBACvC,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC;AAEhD;AAOO,SAAS,qBAAqB,MAAc;IACjD,IAAI,CAAC,UAAU,WAAW,OAAO;QAC/B,OAAO;IACT;IAEA,OAAO,mBAAmB,MAAM,CAAC,CAAA,WAC/B,SAAS,MAAM,CAAC,WAAW,OAAO,OAAO,WAAW;AAExD;AAOO,SAAS,mBAAmB,IAAY;IAC7C,IAAI,CAAC,QAAQ,SAAS,OAAO;QAC3B,OAAO;IACT;IAEA,OAAO,mBAAmB,MAAM,CAAC,CAAA,WAC/B,SAAS,IAAI,CAAC,WAAW,OAAO,KAAK,WAAW;AAEpD;AAMO,SAAS;IACd,MAAM,UAAU,mBAAmB,GAAG,CAAC,CAAA,WAAY,SAAS,MAAM;IAClE,OAAO;WAAI,IAAI,IAAI;KAAS,CAAC,IAAI;AACnC;AAMO,SAAS;IACd,MAAM,SAAS,mBAAmB,GAAG,CAAC,CAAA,WAAY,SAAS,IAAI;IAC/D,OAAO;WAAI,IAAI,IAAI;KAAQ,CAAC,IAAI;AAClC", "debugId": null}}, {"offset": {"line": 1551, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/view360/view360-search.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Search } from 'lucide-react';\nimport { View360Location, VIEW_360_LOCATIONS } from './view360-locations';\n\ninterface View360SearchProps {\n  onSelectLocation: (location: View360Location) => void;\n  className?: string;\n}\n\nconst regions = [\n  { label: 'Tất cả', value: 'all' },\n  { label: 'Miền Bắc', value: 'north' },\n  { label: 'Miền Nam', value: 'south' },\n  { label: 'Miền Trung', value: 'central' },\n];\n\nexport function View360Search({ onSelectLocation, className = '' }: View360SearchProps) {\n  const [searchTerm, setSearchTerm] = useState('');\n  const [selectedRegion, setSelectedRegion] = useState('all');\n  const [filteredLocations, setFilteredLocations] = useState<View360Location[]>(VIEW_360_LOCATIONS);\n\n  // Filter locations based on search term and selected region\n  useEffect(() => {\n    let results = VIEW_360_LOCATIONS;\n    \n    // Filter by region\n    if (selectedRegion !== 'all') {\n      results = results.filter(location => location.region === selectedRegion);\n    }\n    \n    // Filter by search term\n    if (searchTerm) {\n      const term = searchTerm.toLowerCase();\n      results = results.filter(\n        location => \n          location.name.toLowerCase().includes(term) || \n          location.city.toLowerCase().includes(term)\n      );\n    }\n    \n    setFilteredLocations(results);\n  }, [searchTerm, selectedRegion]);\n\n  const handleRegionFilter = (region: string) => {\n    setSelectedRegion(region);\n  };\n\n  return (\n    <div className={className}>\n      {/* Search input */}\n      <div className=\"relative mb-4\">\n        <div className=\"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none\">\n          <Search className=\"h-5 w-5 text-gray-400\" />\n        </div>\n        <input\n          type=\"text\"\n          className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg bg-white focus:ring-2 focus:ring-teal-500 focus:border-teal-500\"\n          placeholder=\"Tìm kiếm địa điểm...\"\n          value={searchTerm}\n          onChange={(e) => setSearchTerm(e.target.value)}\n        />\n      </div>\n      \n      {/* Region filters - tất cả các nút đều có màu trắng */}\n      <div className=\"flex flex-wrap gap-2 mb-4\">\n        {regions.map(region => (\n          <button\n            key={region.value}\n            onClick={() => handleRegionFilter(region.value)}\n            className={`px-4 py-1.5 rounded-full text-sm font-medium transition-colors\n              ${selectedRegion === region.value \n                ? 'bg-white text-teal-600 border border-teal-300' \n                : 'bg-white text-gray-600 border border-gray-200 hover:border-teal-200 hover:text-teal-600'\n              }`}\n          >\n            {region.label}\n          </button>\n        ))}\n      </div>\n      \n      {/* Location results */}\n      <div className=\"space-y-2 max-h-60 overflow-y-auto pr-1\">\n        {filteredLocations.length > 0 ? (\n          filteredLocations.map(location => (\n            <button\n              key={location.id}\n              className=\"w-full text-left px-3 py-2 rounded-lg hover:bg-teal-50 transition-colors flex items-center\"\n              onClick={() => onSelectLocation(location)}\n            >\n              <div className=\"flex-1\">\n                <div className=\"font-medium text-teal-700\">{location.name}</div>\n                <div className=\"text-sm text-gray-500\">{location.city}</div>\n              </div>\n            </button>\n          ))\n        ) : (\n          <div className=\"text-center py-4 text-gray-500\">\n            Không tìm thấy địa điểm nào\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWA,MAAM,UAAU;IACd;QAAE,OAAO;QAAU,OAAO;IAAM;IAChC;QAAE,OAAO;QAAY,OAAO;IAAQ;IACpC;QAAE,OAAO;QAAY,OAAO;IAAQ;IACpC;QAAE,OAAO;QAAc,OAAO;IAAU;CACzC;AAEM,SAAS,cAAc,EAAE,gBAAgB,EAAE,YAAY,EAAE,EAAsB;IACpF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,kJAAA,CAAA,qBAAkB;IAEhG,4DAA4D;IAC5D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,kJAAA,CAAA,qBAAkB;QAEhC,mBAAmB;QACnB,IAAI,mBAAmB,OAAO;YAC5B,UAAU,QAAQ,MAAM,CAAC,CAAA,WAAY,SAAS,MAAM,KAAK;QAC3D;QAEA,wBAAwB;QACxB,IAAI,YAAY;YACd,MAAM,OAAO,WAAW,WAAW;YACnC,UAAU,QAAQ,MAAM,CACtB,CAAA,WACE,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,SACrC,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;QAE3C;QAEA,qBAAqB;IACvB,GAAG;QAAC;QAAY;KAAe;IAE/B,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;IACpB;IAEA,qBACE,8OAAC;QAAI,WAAW;;0BAEd,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,8OAAC;wBACC,MAAK;wBACL,WAAU;wBACV,aAAY;wBACZ,OAAO;wBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;0BAKjD,8OAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAA,uBACX,8OAAC;wBAEC,SAAS,IAAM,mBAAmB,OAAO,KAAK;wBAC9C,WAAW,CAAC;cACV,EAAE,mBAAmB,OAAO,KAAK,GAC7B,kDACA,2FACF;kCAEH,OAAO,KAAK;uBARR,OAAO,KAAK;;;;;;;;;;0BAcvB,8OAAC;gBAAI,WAAU;0BACZ,kBAAkB,MAAM,GAAG,IAC1B,kBAAkB,GAAG,CAAC,CAAA,yBACpB,8OAAC;wBAEC,WAAU;wBACV,SAAS,IAAM,iBAAiB;kCAEhC,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA6B,SAAS,IAAI;;;;;;8CACzD,8OAAC;oCAAI,WAAU;8CAAyB,SAAS,IAAI;;;;;;;;;;;;uBANlD,SAAS,EAAE;;;;8CAWpB,8OAAC;oBAAI,WAAU;8BAAiC;;;;;;;;;;;;;;;;;AAO1D", "debugId": null}}, {"offset": {"line": 1719, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/view360/view360-location-tabs.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/radix-ui/tabs';\nimport { MapPin } from 'lucide-react';\n\nimport { GoogleMapsView } from './google-maps-view';\nimport { View360Search } from './view360-search';\nimport { View360Location, VIEW_360_LOCATIONS } from './view360-locations';\n\ninterface View360LocationTabsProps {\n  defaultLocation?: string;\n  className?: string;\n  showInfoCard?: boolean;\n}\n\nexport function View360LocationTabs({ defaultLocation = 'bai-sao', className = '', showInfoCard = true }: View360LocationTabsProps) {\n  const [selectedLocation, setSelectedLocation] = useState<View360Location | null>(null);\n  const [activeTab, setActiveTab] = useState<string>(defaultLocation);\n\n  // Initialize with default location\n  useEffect(() => {\n    const defaultLoc = VIEW_360_LOCATIONS.find(loc => loc.id === defaultLocation);\n    if (defaultLoc) {\n      setSelectedLocation(defaultLoc);\n    }\n  }, [defaultLocation]);\n\n  // Handle location selection from search\n  const handleSelectLocation = (location: View360Location) => {\n    setSelectedLocation(location);\n    setActiveTab(location.id);\n  };\n\n  return (\n    <div className={`${className} space-y-6`}>\n      {/* Search component với viền rõ ràng hơn */}\n      <div className=\"bg-white rounded-xl shadow-md border border-teal-200 dark:border-teal-800\">\n        <View360Search\n          onSelectLocation={handleSelectLocation}\n          className=\"p-4\"\n        />\n      </div>\n\n      {/* Location details panel - thiết kế đẹp hơn */}\n      {selectedLocation && (\n        <div className=\"bg-white rounded-xl shadow-md border border-teal-200 dark:border-teal-800 p-4\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"bg-teal-100 dark:bg-teal-900 p-2 rounded-full\">\n              <MapPin className=\"h-5 w-5 text-teal-600 dark:text-teal-400\" />\n            </div>\n            <div>\n              <h3 className=\"text-lg font-semibold text-teal-800 dark:text-teal-300\">{selectedLocation.name}</h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n                {selectedLocation.city}, {selectedLocation.region}\n              </p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Location content với viền rõ ràng hơn */}\n      <div className=\"bg-white rounded-xl shadow-md border border-teal-200 dark:border-teal-800 overflow-hidden\">\n        <Tabs value={activeTab} onValueChange={setActiveTab}>\n          <TabsList className=\"hidden\">\n            {VIEW_360_LOCATIONS.map(location => (\n              <TabsTrigger key={location.id} value={location.id}>\n                {location.name}\n              </TabsTrigger>\n            ))}\n          </TabsList>\n\n          {VIEW_360_LOCATIONS.map(location => (\n            <TabsContent key={location.id} value={location.id} className=\"p-0\">\n              <GoogleMapsView\n                mapUrl={location.googleMapsUrl}\n                height=\"600px\"\n                title={location.name}\n                showInfoCard={showInfoCard}\n                reloadButton={true}\n                className=\"rounded-none border-0\"\n              />\n            </TabsContent>\n          ))}\n        </Tabs>\n      </div>\n    </div>\n  );\n}\n\nexport default View360LocationTabs;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AACA;AACA;AARA;;;;;;;;AAgBO,SAAS,oBAAoB,EAAE,kBAAkB,SAAS,EAAE,YAAY,EAAE,EAAE,eAAe,IAAI,EAA4B;IAChI,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACjF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEnD,mCAAmC;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,kJAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC7D,IAAI,YAAY;YACd,oBAAoB;QACtB;IACF,GAAG;QAAC;KAAgB;IAEpB,wCAAwC;IACxC,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;QACpB,aAAa,SAAS,EAAE;IAC1B;IAEA,qBACE,8OAAC;QAAI,WAAW,GAAG,UAAU,UAAU,CAAC;;0BAEtC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,gJAAA,CAAA,gBAAa;oBACZ,kBAAkB;oBAClB,WAAU;;;;;;;;;;;YAKb,kCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;;;;;;sCAEpB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA0D,iBAAiB,IAAI;;;;;;8CAC7F,8OAAC;oCAAE,WAAU;;wCACV,iBAAiB,IAAI;wCAAC;wCAAG,iBAAiB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;0BAQ3D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,+IAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;;sCACrC,8OAAC,+IAAA,CAAA,WAAQ;4BAAC,WAAU;sCACjB,kJAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAA,yBACtB,8OAAC,+IAAA,CAAA,cAAW;oCAAmB,OAAO,SAAS,EAAE;8CAC9C,SAAS,IAAI;mCADE,SAAS,EAAE;;;;;;;;;;wBAMhC,kJAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAA,yBACtB,8OAAC,+IAAA,CAAA,cAAW;gCAAmB,OAAO,SAAS,EAAE;gCAAE,WAAU;0CAC3D,cAAA,8OAAC,qJAAA,CAAA,iBAAc;oCACb,QAAQ,SAAS,aAAa;oCAC9B,QAAO;oCACP,OAAO,SAAS,IAAI;oCACpB,cAAc;oCACd,cAAc;oCACd,WAAU;;;;;;+BAPI,SAAS,EAAE;;;;;;;;;;;;;;;;;;;;;;AAezC;uCAEe", "debugId": null}}, {"offset": {"line": 1897, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/view360/index.ts"], "sourcesContent": ["export * from './view360';\nexport * from './google-maps-view';\nexport * from './universal-view360';\nexport * from './view360-location-tabs';\nexport * from './view360-search';\nexport * from './view360-locations';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1930, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/app/%28social-travel-trip%29/view360/page.tsx"], "sourcesContent": ["'use client';\n\nimport { PageHeader } from '@/components/ui/page-header';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { View360LocationTabs } from '@/features/view360';\n\nexport default function View360Page() {\n  return (\n    <div className=\"container mx-auto py-6 space-y-6\">\n      <PageHeader\n        heading=\"View 360°\"\n        description=\"Khám phá các địa điểm du lịch với góc nhìn 360 độ\"\n      />\n\n      <Card className=\"border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs\">\n        <CardContent className=\"p-6\">\n          <View360LocationTabs showInfoCard={false} />\n        </CardContent>\n      </Card>\n\n      <Card className=\"border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs\">\n        <CardContent className=\"p-6\">\n          <h2 className=\"text-xl font-semibold mb-4\">Hướng dẫn sử dụng</h2>\n          <div className=\"prose dark:prose-invert max-w-none\">\n            <h3>C<PERSON>ch sử dụng View 360°</h3>\n            <p>\n              Tính năng View 360° cho phép bạn khám phá các địa điểm du lịch với góc nhìn 360 độ từ Google Maps Street View.\n            </p>\n\n            <h4>Tìm kiếm địa điểm</h4>\n            <p>\n              Sử dụng thanh tìm kiếm phía trên để tìm kiếm các địa điểm có sẵn trong hệ thống. Bạn có thể tìm kiếm theo:\n            </p>\n            <ul>\n              <li>Tên địa điểm (ví dụ: \"Bãi Sao\", \"Hòn Thơm\")</li>\n              <li>Thành phố (ví dụ: \"Phú Quốc\", \"Đà Lạt\")</li>\n              <li>Vùng miền (ví dụ: \"Miền Nam\", \"Miền Bắc\")</li>\n              <li>Mô tả (ví dụ: \"biển\", \"cáp treo\")</li>\n            </ul>\n            <p>\n              Kết quả tìm kiếm sẽ hiển thị ngay bên dưới thanh tìm kiếm. Nhấp vào kết quả để xem hình ảnh 360° của địa điểm đó.\n            </p>\n\n            <h4>Lọc theo vùng miền</h4>\n            <p>\n              Bạn có thể lọc các địa điểm theo vùng miền bằng cách nhấp vào các nhãn vùng miền phía dưới thanh tìm kiếm. Hệ thống sẽ hiển thị tất cả các địa điểm thuộc vùng miền đó.\n            </p>\n\n            <h4>Điều hướng trong hình ảnh 360°</h4>\n            <p>\n              Khi xem hình ảnh 360°, bạn có thể:\n            </p>\n            <ul>\n              <li>Kéo chuột để xoay góc nhìn</li>\n              <li>Sử dụng bánh xe chuột để phóng to/thu nhỏ</li>\n              <li>Nhấp vào các điều khiển trên màn hình để di chuyển</li>\n              <li>Nhấp vào nút toàn màn hình để xem ở chế độ toàn màn hình</li>\n            </ul>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0IAAA,CAAA,aAAU;gBACT,SAAQ;gBACR,aAAY;;;;;;0BAGd,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC,0JAAA,CAAA,sBAAmB;wBAAC,cAAc;;;;;;;;;;;;;;;;0BAIvC,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAE;;;;;;8CAIH,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAE;;;;;;8CAGH,8OAAC;;sDACC,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;8CAEN,8OAAC;8CAAE;;;;;;8CAIH,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAE;;;;;;8CAIH,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAE;;;;;;8CAGH,8OAAC;;sDACC,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB", "debugId": null}}]}