{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/components/ui/page-header.tsx"], "sourcesContent": ["interface PageHeaderProps {\n  title: string;\n  description?: string;\n}\n\nexport function PageHeader({ title, description }: PageHeaderProps) {\n  return (\n    <div className=\"space-y-1\">\n      <h1 className=\"text-2xl md:text-3xl font-bold text-purple-800 dark:text-purple-400\">{title}</h1>\n      {description && (\n        <p className=\"text-muted-foreground\">{description}</p>\n      )}\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;;AAKO,SAAS,WAAW,EAAE,KAAK,EAAE,WAAW,EAAmB;IAChE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAuE;;;;;;YACpF,6BACC,8OAAC;gBAAE,WAAU;0BAAyB;;;;;;;;;;;;AAI9C", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/features/view360/view360.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useState, useEffect } from 'react';\nimport { ReactPhotoSphereViewer } from 'react-photo-sphere-viewer';\nimport { MarkersPlugin } from '@photo-sphere-viewer/markers-plugin';\n// Bỏ import CSS vì nó không tồn tại trong package\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Maximize, Minimize, RotateCcw, ChevronLeft, ChevronRight, MapPin, ArrowRight } from 'lucide-react';\n\nexport interface PanoramaScene {\n  id: string;\n  name: string;\n  image: string;\n  description?: string;\n  position?: {\n    lat?: number;\n    lng?: number;\n  };\n  // Các điểm liên kết đến các scene khác\n  hotspots?: {\n    id: string; // ID của scene đích\n    name: string;\n    position: { yaw: string; pitch: string }; // Vị trí của hotspot trên ảnh 360\n    tooltip?: string;\n  }[];\n}\n\ninterface View360Props {\n  scenes: PanoramaScene[];\n  initialSceneId?: string;\n  className?: string;\n  height?: string;\n  width?: string;\n  fullscreenButton?: boolean;\n  resetButton?: boolean;\n  showSceneSelector?: boolean;\n}\n\nexport const View360: React.FC<View360Props> = ({\n  scenes,\n  initialSceneId,\n  className = '',\n  height = '400px',\n  width = '100%',\n  fullscreenButton = true,\n  resetButton = true,\n  showSceneSelector = true,\n}) => {\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [currentSceneIndex, setCurrentSceneIndex] = useState(\n    initialSceneId\n      ? scenes.findIndex(scene => scene.id === initialSceneId)\n      : 0\n  );\n  const viewerRef = useRef<any>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  // Xử lý khi component unmount\n  useEffect(() => {\n    return () => {\n      if (viewerRef.current) {\n        try {\n          viewerRef.current.destroy();\n        } catch (error) {\n          console.error('Error destroying viewer:', error);\n        }\n      }\n    };\n  }, []);\n\n  // Xử lý khi nhấn nút fullscreen\n  const toggleFullscreen = () => {\n    if (!containerRef.current) return;\n\n    if (!isFullscreen) {\n      if (containerRef.current.requestFullscreen) {\n        containerRef.current.requestFullscreen();\n      }\n      setIsFullscreen(true);\n    } else {\n      if (document.exitFullscreen) {\n        document.exitFullscreen();\n      }\n      setIsFullscreen(false);\n    }\n  };\n\n  // Xử lý khi nhấn nút reset\n  const handleReset = () => {\n    if (viewerRef.current) {\n      viewerRef.current.animate({\n        yaw: 0,\n        pitch: 0,\n        zoom: 50,\n        speed: '10rpm',\n      });\n    }\n  };\n\n  // Xử lý sự kiện fullscreen change\n  useEffect(() => {\n    const handleFullscreenChange = () => {\n      setIsFullscreen(!!document.fullscreenElement);\n    };\n\n    document.addEventListener('fullscreenchange', handleFullscreenChange);\n    return () => {\n      document.removeEventListener('fullscreenchange', handleFullscreenChange);\n    };\n  }, []);\n\n  // Chuyển đến scene trước đó\n  const goToPreviousScene = () => {\n    if (currentSceneIndex > 0) {\n      setCurrentSceneIndex(currentSceneIndex - 1);\n    } else {\n      setCurrentSceneIndex(scenes.length - 1);\n    }\n  };\n\n  // Chuyển đến scene tiếp theo\n  const goToNextScene = () => {\n    if (currentSceneIndex < scenes.length - 1) {\n      setCurrentSceneIndex(currentSceneIndex + 1);\n    } else {\n      setCurrentSceneIndex(0);\n    }\n  };\n\n  // Chuyển đến scene cụ thể theo index\n  const goToScene = (index: number) => {\n    if (index >= 0 && index < scenes.length) {\n      setCurrentSceneIndex(index);\n    }\n  };\n\n  // Chuyển đến scene cụ thể theo ID\n  const goToSceneById = (sceneId: string) => {\n    const sceneIndex = scenes.findIndex(scene => scene.id === sceneId);\n    if (sceneIndex !== -1) {\n      setCurrentSceneIndex(sceneIndex);\n    }\n  };\n\n  // Xử lý khi nhấp vào hotspot\n  const handleHotspotClick = (hotspotId: string) => {\n    // Chuyển đến scene khác trong cùng địa điểm\n    goToSceneById(hotspotId);\n  };\n\n  // Lấy scene hiện tại\n  const currentScene = scenes[currentSceneIndex];\n\n  return (\n    <Card className={`overflow-hidden border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs ${className}`}>\n      <CardContent className=\"p-0\">\n        <div ref={containerRef} className=\"relative\">\n          <div style={{ height, width }}>\n            <ReactPhotoSphereViewer\n              key={currentScene.id} // Quan trọng: key thay đổi khi scene thay đổi để re-render component\n              ref={viewerRef}\n              src={currentScene.image}\n              height={isFullscreen ? '100vh' : height}\n              width={width}\n              littlePlanet={false}\n              container={containerRef.current || undefined}\n              plugins={[\n                [MarkersPlugin, {\n                  markers: currentScene.hotspots?.map(hotspot => ({\n                    id: hotspot.id,\n                    position: hotspot.position,\n                    tooltip: {\n                      content: hotspot.tooltip || hotspot.name,\n                      position: 'bottom'\n                    },\n                    html: `\n                      <div style=\"\n                        display: flex;\n                        align-items: center;\n                        background-color: rgba(255, 255, 255, 0.8);\n                        padding: 5px 10px;\n                        border-radius: 20px;\n                        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\n                        cursor: pointer;\n                        transition: all 0.2s ease;\n                      \">\n                        <div style=\"\n                          margin-right: 5px;\n                          color: rgb(147, 51, 234);\n                        \">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                            <path d=\"M5 12h14\"></path>\n                            <path d=\"m12 5 7 7-7 7\"></path>\n                          </svg>\n                        </div>\n                        <div style=\"\n                          font-weight: 500;\n                          font-size: 14px;\n                        \">${hotspot.name}</div>\n                      </div>\n                    `,\n                    data: { sceneId: hotspot.id }\n                  })) || []\n                }]\n              ]}\n              onReady={(instance) => {\n                // Xử lý sự kiện click vào marker\n                if (currentScene.hotspots && currentScene.hotspots.length > 0) {\n                  const markersPlugin = instance.getPlugin(MarkersPlugin);\n                  if (markersPlugin) {\n                    markersPlugin.addEventListener('select-marker', (e: any) => {\n                      if (e.marker && e.marker.data && e.marker.data.sceneId) {\n                        handleHotspotClick(e.marker.data.sceneId);\n                      }\n                    });\n                  }\n                }\n              }}\n            />\n          </div>\n\n\n\n          {/* Scene Info */}\n          <div className=\"absolute top-4 left-4 right-4 flex justify-between items-center\">\n            <div className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm px-3 py-1.5 rounded-md flex items-center\">\n              <MapPin className=\"h-4 w-4 mr-2 text-purple-600\" />\n              <span className=\"font-medium text-sm\">{currentScene.name}</span>\n            </div>\n\n            {currentScene.description && (\n              <div className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm px-3 py-1.5 rounded-md\">\n                <span className=\"text-sm\">{currentScene.description}</span>\n              </div>\n            )}\n          </div>\n\n          {/* Scene Navigation */}\n          {scenes.length > 1 && (\n            <div className=\"absolute top-1/2 left-4 right-4 -translate-y-1/2 flex justify-between\">\n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\"\n                onClick={goToPreviousScene}\n                title=\"Cảnh trước đó\"\n              >\n                <ChevronLeft className=\"h-4 w-4\" />\n              </Button>\n\n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\"\n                onClick={goToNextScene}\n                title=\"Cảnh tiếp theo\"\n              >\n                <ChevronRight className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          )}\n\n          {/* Controls */}\n          <div className=\"absolute bottom-4 right-4 flex gap-2\">\n            {resetButton && (\n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\"\n                onClick={handleReset}\n                title=\"Đặt lại góc nhìn\"\n              >\n                <RotateCcw className=\"h-4 w-4\" />\n              </Button>\n            )}\n\n            {fullscreenButton && (\n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\"\n                onClick={toggleFullscreen}\n                title={isFullscreen ? \"Thoát toàn màn hình\" : \"Toàn màn hình\"}\n              >\n                {isFullscreen ? (\n                  <Minimize className=\"h-4 w-4\" />\n                ) : (\n                  <Maximize className=\"h-4 w-4\" />\n                )}\n              </Button>\n            )}\n          </div>\n\n          {/* Scene Selector */}\n          {showSceneSelector && scenes.length > 1 && (\n            <div className=\"absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2\">\n              {scenes.map((scene, index) => (\n                <Button\n                  key={scene.id}\n                  variant={index === currentSceneIndex ? \"default\" : \"outline\"}\n                  size=\"sm\"\n                  className={`bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm ${\n                    index === currentSceneIndex ? \"bg-purple-600 text-white\" : \"\"\n                  }`}\n                  onClick={() => goToScene(index)}\n                >\n                  {scene.name}\n                </Button>\n              ))}\n            </div>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default View360;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA,kDAAkD;AAClD;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;AAuCO,MAAM,UAAkC,CAAC,EAC9C,MAAM,EACN,cAAc,EACd,YAAY,EAAE,EACd,SAAS,OAAO,EAChB,QAAQ,MAAM,EACd,mBAAmB,IAAI,EACvB,cAAc,IAAI,EAClB,oBAAoB,IAAI,EACzB;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACvD,iBACI,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,kBACvC;IAEN,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAC9B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,UAAU,OAAO,EAAE;gBACrB,IAAI;oBACF,UAAU,OAAO,CAAC,OAAO;gBAC3B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC5C;YACF;QACF;IACF,GAAG,EAAE;IAEL,gCAAgC;IAChC,MAAM,mBAAmB;QACvB,IAAI,CAAC,aAAa,OAAO,EAAE;QAE3B,IAAI,CAAC,cAAc;YACjB,IAAI,aAAa,OAAO,CAAC,iBAAiB,EAAE;gBAC1C,aAAa,OAAO,CAAC,iBAAiB;YACxC;YACA,gBAAgB;QAClB,OAAO;YACL,IAAI,SAAS,cAAc,EAAE;gBAC3B,SAAS,cAAc;YACzB;YACA,gBAAgB;QAClB;IACF;IAEA,2BAA2B;IAC3B,MAAM,cAAc;QAClB,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,OAAO,CAAC;gBACxB,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,OAAO;YACT;QACF;IACF;IAEA,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,yBAAyB;YAC7B,gBAAgB,CAAC,CAAC,SAAS,iBAAiB;QAC9C;QAEA,SAAS,gBAAgB,CAAC,oBAAoB;QAC9C,OAAO;YACL,SAAS,mBAAmB,CAAC,oBAAoB;QACnD;IACF,GAAG,EAAE;IAEL,4BAA4B;IAC5B,MAAM,oBAAoB;QACxB,IAAI,oBAAoB,GAAG;YACzB,qBAAqB,oBAAoB;QAC3C,OAAO;YACL,qBAAqB,OAAO,MAAM,GAAG;QACvC;IACF;IAEA,6BAA6B;IAC7B,MAAM,gBAAgB;QACpB,IAAI,oBAAoB,OAAO,MAAM,GAAG,GAAG;YACzC,qBAAqB,oBAAoB;QAC3C,OAAO;YACL,qBAAqB;QACvB;IACF;IAEA,qCAAqC;IACrC,MAAM,YAAY,CAAC;QACjB,IAAI,SAAS,KAAK,QAAQ,OAAO,MAAM,EAAE;YACvC,qBAAqB;QACvB;IACF;IAEA,kCAAkC;IAClC,MAAM,gBAAgB,CAAC;QACrB,MAAM,aAAa,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QAC1D,IAAI,eAAe,CAAC,GAAG;YACrB,qBAAqB;QACvB;IACF;IAEA,6BAA6B;IAC7B,MAAM,qBAAqB,CAAC;QAC1B,4CAA4C;QAC5C,cAAc;IAChB;IAEA,qBAAqB;IACrB,MAAM,eAAe,MAAM,CAAC,kBAAkB;IAE9C,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAC,0GAA0G,EAAE,WAAW;kBACvI,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAI,KAAK;gBAAc,WAAU;;kCAChC,8OAAC;wBAAI,OAAO;4BAAE;4BAAQ;wBAAM;kCAC1B,cAAA,8OAAC,mKAAA,CAAA,yBAAsB;4BAErB,KAAK;4BACL,KAAK,aAAa,KAAK;4BACvB,QAAQ,eAAe,UAAU;4BACjC,OAAO;4BACP,cAAc;4BACd,WAAW,aAAa,OAAO,IAAI;4BACnC,SAAS;gCACP;oCAAC,mLAAA,CAAA,gBAAa;oCAAE;wCACd,SAAS,aAAa,QAAQ,EAAE,IAAI,CAAA,UAAW,CAAC;gDAC9C,IAAI,QAAQ,EAAE;gDACd,UAAU,QAAQ,QAAQ;gDAC1B,SAAS;oDACP,SAAS,QAAQ,OAAO,IAAI,QAAQ,IAAI;oDACxC,UAAU;gDACZ;gDACA,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;;0BAuBD,EAAE,QAAQ,IAAI,CAAC;;oBAErB,CAAC;gDACD,MAAM;oDAAE,SAAS,QAAQ,EAAE;gDAAC;4CAC9B,CAAC,MAAM,EAAE;oCACX;iCAAE;6BACH;4BACD,SAAS,CAAC;gCACR,iCAAiC;gCACjC,IAAI,aAAa,QAAQ,IAAI,aAAa,QAAQ,CAAC,MAAM,GAAG,GAAG;oCAC7D,MAAM,gBAAgB,SAAS,SAAS,CAAC,mLAAA,CAAA,gBAAa;oCACtD,IAAI,eAAe;wCACjB,cAAc,gBAAgB,CAAC,iBAAiB,CAAC;4CAC/C,IAAI,EAAE,MAAM,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;gDACtD,mBAAmB,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO;4CAC1C;wCACF;oCACF;gCACF;4BACF;2BA1DK,aAAa,EAAE;;;;;;;;;;kCAiExB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAAuB,aAAa,IAAI;;;;;;;;;;;;4BAGzD,aAAa,WAAW,kBACvB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAW,aAAa,WAAW;;;;;;;;;;;;;;;;;oBAMxD,OAAO,MAAM,GAAG,mBACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,OAAM;0CAEN,cAAA,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAGzB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,OAAM;0CAEN,cAAA,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAM9B,8OAAC;wBAAI,WAAU;;4BACZ,6BACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,OAAM;0CAEN,cAAA,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;4BAIxB,kCACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,OAAO,eAAe,wBAAwB;0CAE7C,6BACC,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;yDAEpB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAO3B,qBAAqB,OAAO,MAAM,GAAG,mBACpC,8OAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,kIAAA,CAAA,SAAM;gCAEL,SAAS,UAAU,oBAAoB,YAAY;gCACnD,MAAK;gCACL,WAAW,CAAC,iDAAiD,EAC3D,UAAU,oBAAoB,6BAA6B,IAC3D;gCACF,SAAS,IAAM,UAAU;0CAExB,MAAM,IAAI;+BARN,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AAiB/B;uCAEe", "debugId": null}}, {"offset": {"line": 513, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/features/view360/google-maps-view.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useState, useEffect } from 'react';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Maximize, Minimize, RotateCcw, MapPin } from 'lucide-react';\n\ninterface GoogleMapsViewProps {\n  mapUrl: string;\n  className?: string;\n  height?: string;\n  width?: string;\n  fullscreenButton?: boolean;\n  resetButton?: boolean;\n  title?: string;\n  description?: string;\n}\n\n/**\n * GoogleMapsView - Component để nhúng Google Maps Street View 360 độ\n * \n * @param mapUrl URL của Google Maps Street View (dạng https://maps.app.goo.gl/XXX hoặc https://goo.gl/maps/XXX)\n * @param className CSS class tùy chỉnh\n * @param height Chiều cao của component (mặc định: 400px)\n * @param width Chiều rộng của component (mặc định: 100%)\n * @param fullscreenButton Hiển thị nút toàn màn hình (mặc định: true)\n * @param resetButton Hiển thị nút reset (mặc định: true)\n * @param title Tiêu đề hiển thị phía trên Street View\n * @param description Mô tả hiển thị phía trên Street View\n */\nexport const GoogleMapsView: React.FC<GoogleMapsViewProps> = ({\n  mapUrl,\n  className = '',\n  height = '400px',\n  width = '100%',\n  fullscreenButton = true,\n  resetButton = true,\n  title,\n  description,\n}) => {\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const containerRef = useRef<HTMLDivElement>(null);\n  const iframeRef = useRef<HTMLIFrameElement>(null);\n\n  // Xử lý URL Google Maps để lấy embed URL\n  const getEmbedUrl = (url: string): string => {\n    // Kiểm tra nếu URL đã là embed URL\n    if (url.includes('maps.google.com/maps/embed') || url.includes('www.google.com/maps/embed')) {\n      return url;\n    }\n\n    // Xử lý URL dạng https://maps.app.goo.gl/XXX hoặc https://goo.gl/maps/XXX\n    let embedUrl = '';\n    \n    try {\n      // Nếu là URL rút gọn, thử trích xuất tham số\n      if (url.includes('maps.app.goo.gl') || url.includes('goo.gl/maps')) {\n        // Đối với URL rút gọn, chúng ta sẽ sử dụng mode=streetview\n        embedUrl = `https://www.google.com/maps/embed?pb=!4v!1m3!1m2!1s${encodeURIComponent(url)}!2s!5e0!3m2!1svi!2s!4v1`;\n      } \n      // Nếu là URL đầy đủ với tham số\n      else if (url.includes('google.com/maps')) {\n        // Trích xuất tham số từ URL\n        const urlObj = new URL(url);\n        const params = new URLSearchParams(urlObj.search);\n        \n        // Nếu có tham số ll (latitude,longitude)\n        if (params.has('ll')) {\n          const ll = params.get('ll');\n          embedUrl = `https://www.google.com/maps/embed?pb=!4v!1m3!1m2!1s!2s${ll}!5e0!3m2!1svi!2s!4v1`;\n        }\n        // Nếu có tham số q (query/address)\n        else if (params.has('q')) {\n          const q = params.get('q');\n          embedUrl = `https://www.google.com/maps/embed?pb=!4v!1m3!1m2!1s!2s${q}!5e0!3m2!1svi!2s!4v1`;\n        }\n        // Nếu không có tham số phù hợp, sử dụng URL gốc\n        else {\n          embedUrl = `https://www.google.com/maps/embed/v1/streetview?key=YOUR_API_KEY&location=${encodeURIComponent(url)}`;\n        }\n      }\n      // Nếu không phải URL Google Maps hợp lệ\n      else {\n        console.error('URL không hợp lệ:', url);\n        embedUrl = url; // Sử dụng URL gốc\n      }\n    } catch (error) {\n      console.error('Lỗi khi xử lý URL:', error);\n      embedUrl = url; // Sử dụng URL gốc nếu có lỗi\n    }\n\n    return embedUrl;\n  };\n\n  // Xử lý khi nhấn nút fullscreen\n  const toggleFullscreen = () => {\n    if (!containerRef.current) return;\n\n    if (!isFullscreen) {\n      if (containerRef.current.requestFullscreen) {\n        containerRef.current.requestFullscreen();\n      }\n      setIsFullscreen(true);\n    } else {\n      if (document.exitFullscreen) {\n        document.exitFullscreen();\n      }\n      setIsFullscreen(false);\n    }\n  };\n\n  // Xử lý khi nhấn nút reset\n  const handleReset = () => {\n    if (iframeRef.current) {\n      // Reload iframe để reset view\n      const src = iframeRef.current.src;\n      iframeRef.current.src = '';\n      setTimeout(() => {\n        if (iframeRef.current) {\n          iframeRef.current.src = src;\n        }\n      }, 100);\n    }\n  };\n\n  // Theo dõi sự kiện fullscreenchange\n  useEffect(() => {\n    const handleFullscreenChange = () => {\n      setIsFullscreen(!!document.fullscreenElement);\n    };\n\n    document.addEventListener('fullscreenchange', handleFullscreenChange);\n    return () => {\n      document.removeEventListener('fullscreenchange', handleFullscreenChange);\n    };\n  }, []);\n\n  const embedUrl = getEmbedUrl(mapUrl);\n\n  return (\n    <Card className={`overflow-hidden border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs ${className}`}>\n      <CardContent className=\"p-0\">\n        <div ref={containerRef} className=\"relative\">\n          <div style={{ height, width }}>\n            <iframe\n              ref={iframeRef}\n              src={embedUrl}\n              width=\"100%\"\n              height=\"100%\"\n              style={{ border: 0 }}\n              allowFullScreen\n              loading=\"lazy\"\n              referrerPolicy=\"no-referrer-when-downgrade\"\n            ></iframe>\n          </div>\n\n          {/* Location Info */}\n          {(title || description) && (\n            <div className=\"absolute top-4 left-4 right-4 flex justify-between items-center\">\n              {title && (\n                <div className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm px-3 py-1.5 rounded-md flex items-center\">\n                  <MapPin className=\"h-4 w-4 mr-2 text-purple-600\" />\n                  <span className=\"font-medium text-sm\">{title}</span>\n                </div>\n              )}\n\n              {description && (\n                <div className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm px-3 py-1.5 rounded-md\">\n                  <span className=\"text-sm\">{description}</span>\n                </div>\n              )}\n            </div>\n          )}\n\n          {/* Controls */}\n          <div className=\"absolute bottom-4 right-4 flex gap-2\">\n            {resetButton && (\n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\"\n                onClick={handleReset}\n                title=\"Đặt lại góc nhìn\"\n              >\n                <RotateCcw className=\"h-4 w-4\" />\n              </Button>\n            )}\n\n            {fullscreenButton && (\n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\"\n                onClick={toggleFullscreen}\n                title={isFullscreen ? \"Thoát toàn màn hình\" : \"Toàn màn hình\"}\n              >\n                {isFullscreen ? (\n                  <Minimize className=\"h-4 w-4\" />\n                ) : (\n                  <Maximize className=\"h-4 w-4\" />\n                )}\n              </Button>\n            )}\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default GoogleMapsView;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AA8BO,MAAM,iBAAgD,CAAC,EAC5D,MAAM,EACN,YAAY,EAAE,EACd,SAAS,OAAO,EAChB,QAAQ,MAAM,EACd,mBAAmB,IAAI,EACvB,cAAc,IAAI,EAClB,KAAK,EACL,WAAW,EACZ;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAE5C,yCAAyC;IACzC,MAAM,cAAc,CAAC;QACnB,mCAAmC;QACnC,IAAI,IAAI,QAAQ,CAAC,iCAAiC,IAAI,QAAQ,CAAC,8BAA8B;YAC3F,OAAO;QACT;QAEA,0EAA0E;QAC1E,IAAI,WAAW;QAEf,IAAI;YACF,6CAA6C;YAC7C,IAAI,IAAI,QAAQ,CAAC,sBAAsB,IAAI,QAAQ,CAAC,gBAAgB;gBAClE,2DAA2D;gBAC3D,WAAW,CAAC,mDAAmD,EAAE,mBAAmB,KAAK,uBAAuB,CAAC;YACnH,OAEK,IAAI,IAAI,QAAQ,CAAC,oBAAoB;gBACxC,4BAA4B;gBAC5B,MAAM,SAAS,IAAI,IAAI;gBACvB,MAAM,SAAS,IAAI,gBAAgB,OAAO,MAAM;gBAEhD,yCAAyC;gBACzC,IAAI,OAAO,GAAG,CAAC,OAAO;oBACpB,MAAM,KAAK,OAAO,GAAG,CAAC;oBACtB,WAAW,CAAC,sDAAsD,EAAE,GAAG,oBAAoB,CAAC;gBAC9F,OAEK,IAAI,OAAO,GAAG,CAAC,MAAM;oBACxB,MAAM,IAAI,OAAO,GAAG,CAAC;oBACrB,WAAW,CAAC,sDAAsD,EAAE,EAAE,oBAAoB,CAAC;gBAC7F,OAEK;oBACH,WAAW,CAAC,0EAA0E,EAAE,mBAAmB,MAAM;gBACnH;YACF,OAEK;gBACH,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,WAAW,KAAK,kBAAkB;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,WAAW,KAAK,6BAA6B;QAC/C;QAEA,OAAO;IACT;IAEA,gCAAgC;IAChC,MAAM,mBAAmB;QACvB,IAAI,CAAC,aAAa,OAAO,EAAE;QAE3B,IAAI,CAAC,cAAc;YACjB,IAAI,aAAa,OAAO,CAAC,iBAAiB,EAAE;gBAC1C,aAAa,OAAO,CAAC,iBAAiB;YACxC;YACA,gBAAgB;QAClB,OAAO;YACL,IAAI,SAAS,cAAc,EAAE;gBAC3B,SAAS,cAAc;YACzB;YACA,gBAAgB;QAClB;IACF;IAEA,2BAA2B;IAC3B,MAAM,cAAc;QAClB,IAAI,UAAU,OAAO,EAAE;YACrB,8BAA8B;YAC9B,MAAM,MAAM,UAAU,OAAO,CAAC,GAAG;YACjC,UAAU,OAAO,CAAC,GAAG,GAAG;YACxB,WAAW;gBACT,IAAI,UAAU,OAAO,EAAE;oBACrB,UAAU,OAAO,CAAC,GAAG,GAAG;gBAC1B;YACF,GAAG;QACL;IACF;IAEA,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,yBAAyB;YAC7B,gBAAgB,CAAC,CAAC,SAAS,iBAAiB;QAC9C;QAEA,SAAS,gBAAgB,CAAC,oBAAoB;QAC9C,OAAO;YACL,SAAS,mBAAmB,CAAC,oBAAoB;QACnD;IACF,GAAG,EAAE;IAEL,MAAM,WAAW,YAAY;IAE7B,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAC,0GAA0G,EAAE,WAAW;kBACvI,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAI,KAAK;gBAAc,WAAU;;kCAChC,8OAAC;wBAAI,OAAO;4BAAE;4BAAQ;wBAAM;kCAC1B,cAAA,8OAAC;4BACC,KAAK;4BACL,KAAK;4BACL,OAAM;4BACN,QAAO;4BACP,OAAO;gCAAE,QAAQ;4BAAE;4BACnB,eAAe;4BACf,SAAQ;4BACR,gBAAe;;;;;;;;;;;oBAKlB,CAAC,SAAS,WAAW,mBACpB,8OAAC;wBAAI,WAAU;;4BACZ,uBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAAuB;;;;;;;;;;;;4BAI1C,6BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;;;;;;;kCAOnC,8OAAC;wBAAI,WAAU;;4BACZ,6BACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,OAAM;0CAEN,cAAA,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;4BAIxB,kCACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,OAAO,eAAe,wBAAwB;0CAE7C,6BACC,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;yDAEpB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStC;uCAEe", "debugId": null}}, {"offset": {"line": 768, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/features/view360/universal-view360.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { View360, PanoramaScene } from './view360';\nimport { GoogleMapsView } from './google-maps-view';\n\n// Định nghĩa các loại nguồn dữ liệu 360 độ\nexport type View360Source = \n  | { type: 'panorama'; scenes: PanoramaScene[]; initialSceneId?: string }\n  | { type: 'google-maps'; url: string; title?: string; description?: string };\n\ninterface UniversalView360Props {\n  source: View360Source;\n  className?: string;\n  height?: string;\n  width?: string;\n  fullscreenButton?: boolean;\n  resetButton?: boolean;\n  showSceneSelector?: boolean;\n}\n\n/**\n * UniversalView360 - Component tổng hợp để hiển thị cả ảnh panorama 360 độ và Google Maps Street View\n * \n * @param source Nguồn dữ liệu 360 độ (panorama hoặc google-maps)\n * @param className CSS class tùy chỉnh\n * @param height Chiều cao của component (mặc định: 400px)\n * @param width Chiều rộng của component (mặc định: 100%)\n * @param fullscreenButton Hiển thị nút toàn màn hình (mặc định: true)\n * @param resetButton Hiển thị nút reset (mặc định: true)\n * @param showSceneSelector Hiển thị bộ chọn cảnh (chỉ áp dụng cho panorama, mặc định: true)\n */\nexport const UniversalView360: React.FC<UniversalView360Props> = ({\n  source,\n  className = '',\n  height = '400px',\n  width = '100%',\n  fullscreenButton = true,\n  resetButton = true,\n  showSceneSelector = true,\n}) => {\n  // Hiển thị component tương ứng dựa trên loại nguồn dữ liệu\n  if (source.type === 'panorama') {\n    return (\n      <View360\n        scenes={source.scenes}\n        initialSceneId={source.initialSceneId}\n        className={className}\n        height={height}\n        width={width}\n        fullscreenButton={fullscreenButton}\n        resetButton={resetButton}\n        showSceneSelector={showSceneSelector}\n      />\n    );\n  } else if (source.type === 'google-maps') {\n    return (\n      <GoogleMapsView\n        mapUrl={source.url}\n        className={className}\n        height={height}\n        width={width}\n        fullscreenButton={fullscreenButton}\n        resetButton={resetButton}\n        title={source.title}\n        description={source.description}\n      />\n    );\n  }\n\n  // Trường hợp không có nguồn dữ liệu hợp lệ\n  return (\n    <div className={`flex items-center justify-center ${className}`} style={{ height, width }}>\n      <p className=\"text-muted-foreground\">Không có dữ liệu 360° hợp lệ</p>\n    </div>\n  );\n};\n\nexport default UniversalView360;\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAgCO,MAAM,mBAAoD,CAAC,EAChE,MAAM,EACN,YAAY,EAAE,EACd,SAAS,OAAO,EAChB,QAAQ,MAAM,EACd,mBAAmB,IAAI,EACvB,cAAc,IAAI,EAClB,oBAAoB,IAAI,EACzB;IACC,2DAA2D;IAC3D,IAAI,OAAO,IAAI,KAAK,YAAY;QAC9B,qBACE,8OAAC,sIAAA,CAAA,UAAO;YACN,QAAQ,OAAO,MAAM;YACrB,gBAAgB,OAAO,cAAc;YACrC,WAAW;YACX,QAAQ;YACR,OAAO;YACP,kBAAkB;YAClB,aAAa;YACb,mBAAmB;;;;;;IAGzB,OAAO,IAAI,OAAO,IAAI,KAAK,eAAe;QACxC,qBACE,8OAAC,qJAAA,CAAA,iBAAc;YACb,QAAQ,OAAO,GAAG;YAClB,WAAW;YACX,QAAQ;YACR,OAAO;YACP,kBAAkB;YAClB,aAAa;YACb,OAAO,OAAO,KAAK;YACnB,aAAa,OAAO,WAAW;;;;;;IAGrC;IAEA,2CAA2C;IAC3C,qBACE,8OAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;QAAE,OAAO;YAAE;YAAQ;QAAM;kBACtF,cAAA,8OAAC;YAAE,WAAU;sBAAwB;;;;;;;;;;;AAG3C;uCAEe", "debugId": null}}, {"offset": {"line": 840, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/components/ui/radix-ui/badge.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\n\nimport { cn } from '@/lib/utils';\n\nconst badgeVariants = cva(\n  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2',\n  {\n    variants: {\n      variant: {\n        default:\n          'border-transparent bg-primary text-primary-foreground hover:bg-primary/80',\n        secondary:\n          'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',\n        destructive:\n          'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80',\n        outline: 'text-foreground',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n    },\n  }\n);\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  );\n}\n\nexport { Badge, badgeVariants };\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,4KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 882, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/features/view360/view360-locations.ts"], "sourcesContent": ["/**\n * Predefined locations for the 360-degree view feature\n */\n\nexport interface View360Location {\n  id: string;\n  name: string;\n  city: string;\n  region: string;\n  description: string;\n  coordinates: {\n    lat: number;\n    lng: number;\n  };\n  googleMapsUrl: string;\n}\n\nexport const VIEW_360_LOCATIONS: View360Location[] = [\n  {\n    id: 'bai-sao',\n    name: 'Bãi Sao',\n    city: 'Phú Quốc',\n    region: 'Miền Nam',\n    description: 'Bãi biển cát trắng mịn nhất <PERSON>ú Quốc',\n    coordinates: {\n      lat: 10.05725757562915,\n      lng: 104.0363948436442\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747330345274!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQ09oN2JLblFF!2m2!1d10.05725757562915!2d104.0363948436442!3f252.2606279243012!4f-33.282491262245465!5f0.4000000000000002'\n  },\n  {\n    id: 'hon-thom',\n    name: 'Hòn Thơm',\n    city: 'Phú Quốc',\n    region: 'Miền Nam',\n    description: 'Cáp treo vượt biển dài nhất thế giới',\n    coordinates: {\n      lat: 9.954605838430725,\n      lng: 104.0178143976055\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747332749752!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQzR3NERDSUE.!2m2!1d9.954605838430725!2d104.0178143976055!3f352.99579097798187!4f-11.542141392533921!5f0.7820865974627469'\n  },\n  {\n    id: 'vinpearl',\n    name: 'Vinpearl Resort',\n    city: 'Phú Quốc',\n    region: 'Miền Nam',\n    description: 'Khu nghỉ dưỡng sang trọng tại Phú Quốc',\n    coordinates: {\n      lat: 10.33683427532572,\n      lng: 103.8555491298273\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747332930528!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQzRyZV9zUlE.!2m2!1d10.33683427532572!2d103.8555491298273!3f9.87975441837457!4f-61.96086477266688!5f0.7820865974627469'\n  },\n  {\n    id: 'rach-vem',\n    name: 'Rạch Vẹm',\n    city: 'Phú Quốc',\n    region: 'Miền Nam',\n    description: 'Làng chài yên bình với cảnh quan tuyệt đẹp',\n    coordinates: {\n      lat: 10.37304277793628,\n      lng: 103.9377705339461\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747333532742!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQzRqZGo5S0E.!2m2!1d10.37304277793628!2d103.9377705339461!3f216.24777645854576!4f-0.38721998348161435!5f0.7820865974627469'\n  },\n  {\n    id: 'ho-xuan-huong',\n    name: 'Hồ Xuân Hương',\n    city: 'Đà Lạt',\n    region: 'Tây Nguyên',\n    description: 'Hồ nước ngọt nhân tạo trung tâm Đà Lạt',\n    coordinates: {\n      lat: 11.9404,\n      lng: 108.4583\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747333532742!6m8!1m7!1sCAoSLEFGMVFpcE5XVnRXVXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXQ!2m2!1d11.9404!2d108.4583!3f0!4f0!5f0.7820865974627469'\n  },\n  {\n    id: 'vinh-ha-long',\n    name: 'Vịnh Hạ Long',\n    city: 'Hạ Long',\n    region: 'Miền Bắc',\n    description: 'Kỳ quan thiên nhiên thế giới với hàng nghìn hòn đảo đá vôi',\n    coordinates: {\n      lat: 20.9101,\n      lng: 107.0448\n    },\n    googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747333532742!6m8!1m7!1sCAoSLEFGMVFpcE5XVnRXVXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXQ!2m2!1d20.9101!2d107.0448!3f0!4f0!5f0.7820865974627469'\n  }\n];\n\n/**\n * Filter locations by search query\n * @param query Search query\n * @returns Filtered locations\n */\nexport function searchLocations(query: string): View360Location[] {\n  if (!query || query.trim() === '') {\n    return VIEW_360_LOCATIONS;\n  }\n  \n  const normalizedQuery = query.toLowerCase().trim();\n  \n  return VIEW_360_LOCATIONS.filter(location => \n    location.name.toLowerCase().includes(normalizedQuery) ||\n    location.city.toLowerCase().includes(normalizedQuery) ||\n    location.region.toLowerCase().includes(normalizedQuery) ||\n    location.description.toLowerCase().includes(normalizedQuery)\n  );\n}\n\n/**\n * Get locations by region\n * @param region Region name\n * @returns Locations in the specified region\n */\nexport function getLocationsByRegion(region: string): View360Location[] {\n  if (!region || region === 'all') {\n    return VIEW_360_LOCATIONS;\n  }\n  \n  return VIEW_360_LOCATIONS.filter(location => \n    location.region.toLowerCase() === region.toLowerCase()\n  );\n}\n\n/**\n * Get locations by city\n * @param city City name\n * @returns Locations in the specified city\n */\nexport function getLocationsByCity(city: string): View360Location[] {\n  if (!city || city === 'all') {\n    return VIEW_360_LOCATIONS;\n  }\n  \n  return VIEW_360_LOCATIONS.filter(location => \n    location.city.toLowerCase() === city.toLowerCase()\n  );\n}\n\n/**\n * Get all unique regions\n * @returns Array of unique region names\n */\nexport function getAllRegions(): string[] {\n  const regions = VIEW_360_LOCATIONS.map(location => location.region);\n  return [...new Set(regions)];\n}\n\n/**\n * Get all unique cities\n * @returns Array of unique city names\n */\nexport function getAllCities(): string[] {\n  const cities = VIEW_360_LOCATIONS.map(location => location.city);\n  return [...new Set(cities)];\n}\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;;;;AAeM,MAAM,qBAAwC;IACnD;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,aAAa;QACb,aAAa;YACX,KAAK;YACL,KAAK;QACP;QACA,eAAe;IACjB;CACD;AAOM,SAAS,gBAAgB,KAAa;IAC3C,IAAI,CAAC,SAAS,MAAM,IAAI,OAAO,IAAI;QACjC,OAAO;IACT;IAEA,MAAM,kBAAkB,MAAM,WAAW,GAAG,IAAI;IAEhD,OAAO,mBAAmB,MAAM,CAAC,CAAA,WAC/B,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,oBACrC,SAAS,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,oBACrC,SAAS,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,oBACvC,SAAS,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC;AAEhD;AAOO,SAAS,qBAAqB,MAAc;IACjD,IAAI,CAAC,UAAU,WAAW,OAAO;QAC/B,OAAO;IACT;IAEA,OAAO,mBAAmB,MAAM,CAAC,CAAA,WAC/B,SAAS,MAAM,CAAC,WAAW,OAAO,OAAO,WAAW;AAExD;AAOO,SAAS,mBAAmB,IAAY;IAC7C,IAAI,CAAC,QAAQ,SAAS,OAAO;QAC3B,OAAO;IACT;IAEA,OAAO,mBAAmB,MAAM,CAAC,CAAA,WAC/B,SAAS,IAAI,CAAC,WAAW,OAAO,KAAK,WAAW;AAEpD;AAMO,SAAS;IACd,MAAM,UAAU,mBAAmB,GAAG,CAAC,CAAA,WAAY,SAAS,MAAM;IAClE,OAAO;WAAI,IAAI,IAAI;KAAS;AAC9B;AAMO,SAAS;IACd,MAAM,SAAS,mBAAmB,GAAG,CAAC,CAAA,WAAY,SAAS,IAAI;IAC/D,OAAO;WAAI,IAAI,IAAI;KAAQ;AAC7B", "debugId": null}}, {"offset": {"line": 1003, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/features/view360/view360-search.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Search, MapPin, X } from 'lucide-react';\nimport { Input } from '@/components/ui/input';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/radix-ui/badge';\nimport { \n  VIEW_360_LOCATIONS, \n  View360Location, \n  searchLocations, \n  getAllRegions, \n  getAllCities \n} from './view360-locations';\n\ninterface View360SearchProps {\n  onSelectLocation: (location: View360Location) => void;\n  className?: string;\n}\n\nexport function View360Search({ onSelectLocation, className = '' }: View360SearchProps) {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [searchResults, setSearchResults] = useState<View360Location[]>([]);\n  const [showResults, setShowResults] = useState(false);\n  const [selectedRegion, setSelectedRegion] = useState<string>('all');\n  const [regions] = useState<string[]>(getAllRegions());\n  const [cities] = useState<string[]>(getAllCities());\n\n  // Perform search when query changes\n  useEffect(() => {\n    if (searchQuery.trim() === '') {\n      setSearchResults([]);\n      return;\n    }\n\n    const results = searchLocations(searchQuery);\n    setSearchResults(results);\n    setShowResults(true);\n  }, [searchQuery]);\n\n  // Handle search input change\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    setSearchQuery(e.target.value);\n    if (e.target.value === '') {\n      setShowResults(false);\n    }\n  };\n\n  // Handle location selection\n  const handleSelectLocation = (location: View360Location) => {\n    setSearchQuery(location.name);\n    setShowResults(false);\n    onSelectLocation(location);\n  };\n\n  // Clear search\n  const handleClearSearch = () => {\n    setSearchQuery('');\n    setShowResults(false);\n  };\n\n  // Filter by region\n  const handleRegionFilter = (region: string) => {\n    setSelectedRegion(region);\n    \n    // If a region is selected, show all locations in that region\n    if (region !== 'all') {\n      const filteredLocations = VIEW_360_LOCATIONS.filter(\n        location => location.region.toLowerCase() === region.toLowerCase()\n      );\n      setSearchResults(filteredLocations);\n      setShowResults(true);\n    } else {\n      // If 'all' is selected, clear results unless there's a search query\n      if (searchQuery.trim() === '') {\n        setSearchResults([]);\n        setShowResults(false);\n      } else {\n        const results = searchLocations(searchQuery);\n        setSearchResults(results);\n        setShowResults(true);\n      }\n    }\n  };\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      <div className=\"relative\">\n        <div className=\"relative\">\n          <Input\n            type=\"text\"\n            placeholder=\"Tìm kiếm địa điểm...\"\n            value={searchQuery}\n            onChange={handleSearchChange}\n            className=\"pl-10 pr-10 bg-white dark:bg-gray-950\"\n          />\n          <Search className=\"absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground\" />\n          {searchQuery && (\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7\"\n              onClick={handleClearSearch}\n            >\n              <X className=\"h-4 w-4\" />\n            </Button>\n          )}\n        </div>\n\n        {/* Region filters */}\n        <div className=\"flex flex-wrap gap-2 mt-2\">\n          <Badge\n            variant={selectedRegion === 'all' ? 'default' : 'outline'}\n            className=\"cursor-pointer\"\n            onClick={() => handleRegionFilter('all')}\n          >\n            Tất cả\n          </Badge>\n          {regions.map(region => (\n            <Badge\n              key={region}\n              variant={selectedRegion === region ? 'default' : 'outline'}\n              className=\"cursor-pointer\"\n              onClick={() => handleRegionFilter(region)}\n            >\n              {region}\n            </Badge>\n          ))}\n        </div>\n\n        {/* Search results */}\n        {showResults && searchResults.length > 0 && (\n          <Card className=\"absolute z-10 w-full mt-1 shadow-lg\">\n            <CardContent className=\"p-2 max-h-60 overflow-y-auto\">\n              {searchResults.map(location => (\n                <div\n                  key={location.id}\n                  className=\"p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md cursor-pointer\"\n                  onClick={() => handleSelectLocation(location)}\n                >\n                  <div className=\"flex items-start gap-2\">\n                    <MapPin className=\"h-4 w-4 text-purple-600 mt-0.5 flex-shrink-0\" />\n                    <div>\n                      <div className=\"font-medium\">{location.name}</div>\n                      <div className=\"text-xs text-muted-foreground\">\n                        {location.city}, {location.region}\n                      </div>\n                      <div className=\"text-xs mt-1\">{location.description}</div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </CardContent>\n          </Card>\n        )}\n\n        {showResults && searchResults.length === 0 && (\n          <Card className=\"absolute z-10 w-full mt-1 shadow-lg\">\n            <CardContent className=\"p-4 text-center text-muted-foreground\">\n              Không tìm thấy địa điểm nào\n            </CardContent>\n          </Card>\n        )}\n      </div>\n    </div>\n  );\n}\n\nexport default View360Search;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAqBO,SAAS,cAAc,EAAE,gBAAgB,EAAE,YAAY,EAAE,EAAsB;IACpF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC7D,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,CAAA,GAAA,kJAAA,CAAA,gBAAa,AAAD;IACjD,MAAM,CAAC,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,CAAA,GAAA,kJAAA,CAAA,eAAY,AAAD;IAE/C,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,IAAI,OAAO,IAAI;YAC7B,iBAAiB,EAAE;YACnB;QACF;QAEA,MAAM,UAAU,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD,EAAE;QAChC,iBAAiB;QACjB,eAAe;IACjB,GAAG;QAAC;KAAY;IAEhB,6BAA6B;IAC7B,MAAM,qBAAqB,CAAC;QAC1B,eAAe,EAAE,MAAM,CAAC,KAAK;QAC7B,IAAI,EAAE,MAAM,CAAC,KAAK,KAAK,IAAI;YACzB,eAAe;QACjB;IACF;IAEA,4BAA4B;IAC5B,MAAM,uBAAuB,CAAC;QAC5B,eAAe,SAAS,IAAI;QAC5B,eAAe;QACf,iBAAiB;IACnB;IAEA,eAAe;IACf,MAAM,oBAAoB;QACxB,eAAe;QACf,eAAe;IACjB;IAEA,mBAAmB;IACnB,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;QAElB,6DAA6D;QAC7D,IAAI,WAAW,OAAO;YACpB,MAAM,oBAAoB,kJAAA,CAAA,qBAAkB,CAAC,MAAM,CACjD,CAAA,WAAY,SAAS,MAAM,CAAC,WAAW,OAAO,OAAO,WAAW;YAElE,iBAAiB;YACjB,eAAe;QACjB,OAAO;YACL,oEAAoE;YACpE,IAAI,YAAY,IAAI,OAAO,IAAI;gBAC7B,iBAAiB,EAAE;gBACnB,eAAe;YACjB,OAAO;gBACL,MAAM,UAAU,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD,EAAE;gBAChC,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;kBACtC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,iIAAA,CAAA,QAAK;4BACJ,MAAK;4BACL,aAAY;4BACZ,OAAO;4BACP,UAAU;4BACV,WAAU;;;;;;sCAEZ,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;wBACjB,6BACC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS;sCAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAMnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gJAAA,CAAA,QAAK;4BACJ,SAAS,mBAAmB,QAAQ,YAAY;4BAChD,WAAU;4BACV,SAAS,IAAM,mBAAmB;sCACnC;;;;;;wBAGA,QAAQ,GAAG,CAAC,CAAA,uBACX,8OAAC,gJAAA,CAAA,QAAK;gCAEJ,SAAS,mBAAmB,SAAS,YAAY;gCACjD,WAAU;gCACV,SAAS,IAAM,mBAAmB;0CAEjC;+BALI;;;;;;;;;;;gBAWV,eAAe,cAAc,MAAM,GAAG,mBACrC,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCACpB,cAAc,GAAG,CAAC,CAAA,yBACjB,8OAAC;gCAEC,WAAU;gCACV,SAAS,IAAM,qBAAqB;0CAEpC,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAe,SAAS,IAAI;;;;;;8DAC3C,8OAAC;oDAAI,WAAU;;wDACZ,SAAS,IAAI;wDAAC;wDAAG,SAAS,MAAM;;;;;;;8DAEnC,8OAAC;oDAAI,WAAU;8DAAgB,SAAS,WAAW;;;;;;;;;;;;;;;;;;+BAXlD,SAAS,EAAE;;;;;;;;;;;;;;;gBAoBzB,eAAe,cAAc,MAAM,KAAK,mBACvC,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;kCAAwC;;;;;;;;;;;;;;;;;;;;;;AAQ3E;uCAEe", "debugId": null}}, {"offset": {"line": 1269, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/components/ui/radix-ui/tabs.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as TabsPrimitive from '@radix-ui/react-tabs';\n\nimport { cn } from '@/lib/utils';\n\nconst Tabs = TabsPrimitive.Root;\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      'inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground',\n      className\n    )}\n    {...props}\n  />\n));\nTabsList.displayName = TabsPrimitive.List.displayName;\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-xs',\n      className\n    )}\n    {...props}\n  />\n));\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      'mt-2 ring-offset-background focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',\n      className\n    )}\n    {...props}\n  />\n));\nTabsContent.displayName = TabsPrimitive.Content.displayName;\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent };\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1322, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/features/view360/view360-location-tabs.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/radix-ui/tabs';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { Map } from 'lucide-react';\nimport { GoogleMapsView } from './google-maps-view';\nimport { View360Search } from './view360-search';\nimport { View360Location, VIEW_360_LOCATIONS } from './view360-locations';\n\ninterface View360LocationTabsProps {\n  defaultLocation?: string;\n  className?: string;\n}\n\nexport function View360LocationTabs({ defaultLocation = 'bai-sao', className = '' }: View360LocationTabsProps) {\n  const [selectedLocation, setSelectedLocation] = useState<View360Location | null>(null);\n  const [activeTab, setActiveTab] = useState<string>(defaultLocation);\n\n  // Initialize with default location\n  useEffect(() => {\n    const defaultLoc = VIEW_360_LOCATIONS.find(loc => loc.id === defaultLocation);\n    if (defaultLoc) {\n      setSelectedLocation(defaultLoc);\n    }\n  }, [defaultLocation]);\n\n  // Handle location selection from search\n  const handleSelectLocation = (location: View360Location) => {\n    setSelectedLocation(location);\n    setActiveTab(location.id);\n  };\n\n  // Get predefined tabs for the most common locations\n  const getLocationTabs = () => {\n    // Get locations from Phú Quốc for the tabs\n    const phuQuocLocations = VIEW_360_LOCATIONS.filter(loc => loc.city === 'Phú Quốc');\n    return phuQuocLocations.map(location => (\n      <TabsTrigger key={location.id} value={location.id}>\n        {location.name}\n      </TabsTrigger>\n    ));\n  };\n\n  return (\n    <div className={className}>\n      {/* Search component */}\n      <View360Search \n        onSelectLocation={handleSelectLocation} \n        className=\"mb-6\"\n      />\n\n      {/* Location tabs */}\n      <Tabs value={activeTab} onValueChange={setActiveTab}>\n        <TabsList>\n          {getLocationTabs()}\n        </TabsList>\n\n        {/* Tab content for each location */}\n        {VIEW_360_LOCATIONS.map(location => (\n          <TabsContent key={location.id} value={location.id} className=\"mt-4\">\n            <div className=\"space-y-6\">\n              <GoogleMapsView\n                mapUrl={location.googleMapsUrl}\n                height=\"500px\"\n                title={location.name}\n                description={location.description}\n              />\n\n              <Card className=\"border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs\">\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"flex-shrink-0 w-12 h-12 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center\">\n                      <Map className=\"h-6 w-6 text-purple-600 dark:text-purple-300\" />\n                    </div>\n                    <div>\n                      <h4 className=\"font-medium\">{location.name}</h4>\n                      <p className=\"text-sm text-muted-foreground\">\n                        Xem hình ảnh 360° thực tế từ Google Maps. Bạn có thể di chuyển trong hình ảnh bằng cách kéo chuột hoặc sử dụng các điều khiển trên màn hình.\n                      </p>\n                      <Button\n                        variant=\"link\"\n                        className=\"p-0 h-auto text-purple-600 dark:text-purple-400\"\n                        onClick={() => window.open(location.googleMapsUrl, '_blank')}\n                      >\n                        Mở trong Google Maps\n                      </Button>\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          </TabsContent>\n        ))}\n      </Tabs>\n    </div>\n  );\n}\n\nexport default View360LocationTabs;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAgBO,SAAS,oBAAoB,EAAE,kBAAkB,SAAS,EAAE,YAAY,EAAE,EAA4B;IAC3G,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IACjF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAEnD,mCAAmC;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,kJAAA,CAAA,qBAAkB,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC7D,IAAI,YAAY;YACd,oBAAoB;QACtB;IACF,GAAG;QAAC;KAAgB;IAEpB,wCAAwC;IACxC,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;QACpB,aAAa,SAAS,EAAE;IAC1B;IAEA,oDAAoD;IACpD,MAAM,kBAAkB;QACtB,2CAA2C;QAC3C,MAAM,mBAAmB,kJAAA,CAAA,qBAAkB,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QACvE,OAAO,iBAAiB,GAAG,CAAC,CAAA,yBAC1B,8OAAC,+IAAA,CAAA,cAAW;gBAAmB,OAAO,SAAS,EAAE;0BAC9C,SAAS,IAAI;eADE,SAAS,EAAE;;;;;IAIjC;IAEA,qBACE,8OAAC;QAAI,WAAW;;0BAEd,8OAAC,gJAAA,CAAA,gBAAa;gBACZ,kBAAkB;gBAClB,WAAU;;;;;;0BAIZ,8OAAC,+IAAA,CAAA,OAAI;gBAAC,OAAO;gBAAW,eAAe;;kCACrC,8OAAC,+IAAA,CAAA,WAAQ;kCACN;;;;;;oBAIF,kJAAA,CAAA,qBAAkB,CAAC,GAAG,CAAC,CAAA,yBACtB,8OAAC,+IAAA,CAAA,cAAW;4BAAmB,OAAO,SAAS,EAAE;4BAAE,WAAU;sCAC3D,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,qJAAA,CAAA,iBAAc;wCACb,QAAQ,SAAS,aAAa;wCAC9B,QAAO;wCACP,OAAO,SAAS,IAAI;wCACpB,aAAa,SAAS,WAAW;;;;;;kDAGnC,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;kEAEjB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAe,SAAS,IAAI;;;;;;0EAC1C,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;0EAG7C,8OAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,WAAU;gEACV,SAAS,IAAM,OAAO,IAAI,CAAC,SAAS,aAAa,EAAE;0EACpD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;2BAxBK,SAAS,EAAE;;;;;;;;;;;;;;;;;AAqCvC;uCAEe", "debugId": null}}, {"offset": {"line": 1515, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/features/view360/index.ts"], "sourcesContent": ["export * from './view360';\nexport * from './google-maps-view';\nexport * from './universal-view360';\nexport * from './view360-search';\nexport * from './view360-locations';\nexport * from './view360-location-tabs';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1548, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/app/%28social-travel-trip%29/view360/page.tsx"], "sourcesContent": ["'use client';\n\nimport { PageHeader } from '@/components/ui/page-header';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { View360LocationTabs } from '@/features/view360';\n\nexport default function View360Page() {\n  return (\n    <div className=\"container mx-auto py-6 space-y-6\">\n      <PageHeader\n        heading=\"View 360°\"\n        description=\"Khám phá các địa điểm du lịch với góc nhìn 360 độ\"\n      />\n\n      <Card className=\"border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs\">\n        <CardContent className=\"p-6\">\n          <View360LocationTabs />\n        </CardContent>\n      </Card>\n\n      <Card className=\"border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs\">\n        <CardContent className=\"p-6\">\n          <h2 className=\"text-xl font-semibold mb-4\">H<PERSON>ớng dẫn sử dụng</h2>\n          <div className=\"prose dark:prose-invert max-w-none\">\n            <h3>C<PERSON>ch sử dụng View 360°</h3>\n            <p>\n              Tính năng View 360° cho phép bạn khám phá các địa điểm du lịch với góc nhìn 360 độ từ Google Maps Street View.\n            </p>\n\n            <h4>Tìm kiếm địa điểm</h4>\n            <p>\n              Sử dụng thanh tìm kiếm phía trên để tìm kiếm các địa điểm có sẵn. Bạn có thể tìm kiếm theo tên địa điểm, thành phố, hoặc vùng miền.\n            </p>\n\n            <h4>Lọc theo vùng miền</h4>\n            <p>\n              Bạn có thể lọc các địa điểm theo vùng miền bằng cách nhấp vào các nhãn vùng miền phía dưới thanh tìm kiếm.\n            </p>\n\n            <h4>Điều hướng trong hình ảnh 360°</h4>\n            <p>\n              Khi xem hình ảnh 360°, bạn có thể:\n            </p>\n            <ul>\n              <li>Kéo chuột để xoay góc nhìn</li>\n              <li>Sử dụng bánh xe chuột để phóng to/thu nhỏ</li>\n              <li>Nhấp vào các điều khiển trên màn hình để di chuyển</li>\n              <li>Nhấp vào nút toàn màn hình để xem ở chế độ toàn màn hình</li>\n            </ul>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAMe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0IAAA,CAAA,aAAU;gBACT,SAAQ;gBACR,aAAY;;;;;;0BAGd,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,8OAAC,0JAAA,CAAA,sBAAmB;;;;;;;;;;;;;;;0BAIxB,8OAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAC3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAE;;;;;;8CAIH,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAE;;;;;;8CAIH,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAE;;;;;;8CAIH,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAE;;;;;;8CAGH,8OAAC;;sDACC,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;sDACJ,8OAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB", "debugId": null}}]}