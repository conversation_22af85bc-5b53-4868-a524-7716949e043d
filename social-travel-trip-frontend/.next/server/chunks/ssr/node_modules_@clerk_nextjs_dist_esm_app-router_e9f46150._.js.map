{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40clerk/nextjs/dist/esm/app-router/keyless-actions.js"], "sourcesContent": ["\"use server\";\nimport { cookies, headers } from \"next/headers\";\nimport { redirect, RedirectType } from \"next/navigation\";\nimport { errorThrower } from \"../server/errorThrower\";\nimport { detectClerkMiddleware } from \"../server/headers-utils\";\nimport { getKeylessCookieName, getKeylessCookieValue } from \"../server/keyless\";\nimport { canUseKeyless } from \"../utils/feature-flags\";\nconst keylessCookieConfig = {\n  secure: false,\n  httpOnly: false,\n  sameSite: \"lax\"\n};\nasync function syncKeylessConfigAction(args) {\n  const { claimUrl, publishableKey, secretKey, returnUrl } = args;\n  const cookieStore = await cookies();\n  const request = new Request(\"https://placeholder.com\", { headers: await headers() });\n  const keyless = await getKeylessCookieValue((name) => {\n    var _a;\n    return (_a = cookieStore.get(name)) == null ? void 0 : _a.value;\n  });\n  const pksMatch = (keyless == null ? void 0 : keyless.publishableKey) === publishableKey;\n  const sksMatch = (keyless == null ? void 0 : keyless.secretKey) === secretKey;\n  if (pksMatch && sksMatch) {\n    return;\n  }\n  cookieStore.set(\n    await getKeylessCookieName(),\n    JSON.stringify({ claimUrl, publishableKey, secretKey }),\n    keylessCookieConfig\n  );\n  if (detectClerkMiddleware(request)) {\n    redirect(`/clerk-sync-keyless?returnUrl=${returnUrl}`, RedirectType.replace);\n    return;\n  }\n  return;\n}\nasync function createOrReadKeylessAction() {\n  if (!canUseKeyless) {\n    return null;\n  }\n  const result = await import(\"../server/keyless-node.js\").then((m) => m.createOrReadKeyless()).catch(() => null);\n  if (!result) {\n    errorThrower.throwMissingPublishableKeyError();\n    return null;\n  }\n  const { clerkDevelopmentCache, createKeylessModeMessage } = await import(\"../server/keyless-log-cache.js\");\n  clerkDevelopmentCache == null ? void 0 : clerkDevelopmentCache.log({\n    cacheKey: result.publishableKey,\n    msg: createKeylessModeMessage(result)\n  });\n  const { claimUrl, publishableKey, secretKey, apiKeysUrl } = result;\n  void (await cookies()).set(\n    await getKeylessCookieName(),\n    JSON.stringify({ claimUrl, publishableKey, secretKey }),\n    keylessCookieConfig\n  );\n  return {\n    claimUrl,\n    publishableKey,\n    apiKeysUrl\n  };\n}\nasync function deleteKeylessAction() {\n  if (!canUseKeyless) {\n    return;\n  }\n  await import(\"../server/keyless-node.js\").then((m) => m.removeKeyless()).catch(() => {\n  });\n  return;\n}\nexport {\n  createOrReadKeylessAction,\n  deleteKeylessAction,\n  syncKeylessConfigAction\n};\n"], "names": [], "mappings": ";;;;;;IAuEE,4BAAA,WAAA,GAAA,CAAA,GAAA,sNAAA,CAAA,wBAAA,EAAA,8CAAA,sNAAA,CAAA,aAAA,EAAA,KAAA,GAAA,sNAAA,CAAA,mBAAA,EAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 20, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40clerk/nextjs/src/app-router/client/keyless-creator-reader.tsx"], "sourcesContent": ["import { useSelectedLayoutSegments } from 'next/navigation';\nimport React, { useEffect } from 'react';\n\nimport type { NextClerkProviderProps } from '../../types';\nimport { createOrReadKeylessAction } from '../keyless-actions';\n\nexport const KeylessCreatorOrReader = (props: NextClerkProviderProps) => {\n  const { children } = props;\n  const segments = useSelectedLayoutSegments();\n  const isNotFoundRoute = segments[0]?.startsWith('/_not-found') || false;\n  const [state, fetchKeys] = React.useActionState(createOrReadKeylessAction, null);\n  useEffect(() => {\n    if (isNotFoundRoute) {\n      return;\n    }\n    React.startTransition(() => {\n      fetchKeys();\n    });\n  }, [isNotFoundRoute]);\n\n  if (!React.isValidElement(children)) {\n    return children;\n  }\n\n  return React.cloneElement(children, {\n    key: state?.publishableKey,\n    publishableKey: state?.publishableKey,\n    __internal_keyless_claimKeylessApplicationUrl: state?.claimUrl,\n    __internal_keyless_copyInstanceKeysUrl: state?.apiKeysUrl,\n    __internal_bypassMissingPublishableKey: true,\n  } as any);\n};\n"], "names": [], "mappings": ";;;AAAA,SAAS,iCAAiC;AAC1C,OAAO,SAAS,iBAAiB;AAGjC,SAAS,iCAAiC;;;;;AAEnC,MAAM,yBAAyB,CAAC,UAAkC;IANzE,IAAA;IAOE,MAAM,EAAE,QAAA,CAAS,CAAA,GAAI;IACrB,MAAM,kJAAW,4BAAA,CAA0B;IAC3C,MAAM,kBAAA,CAAA,CAAkB,KAAA,QAAA,CAAS,CAAC,CAAA,KAAV,OAAA,KAAA,IAAA,GAAa,UAAA,CAAW,cAAA,KAAkB;IAClE,MAAM,CAAC,OAAO,SAAS,CAAA,GAAI,gNAAA,CAAM,cAAA,2MAAe,4BAAA,EAA2B,IAAI;IAC/E,CAAA,GAAA,qMAAA,CAAA,YAAA,EAAU,MAAM;QACd,IAAI,iBAAiB;YACnB;QACF;QACA,qMAAA,CAAA,UAAA,CAAM,eAAA,CAAgB,MAAM;YAC1B,UAAU;QACZ,CAAC;IACH,GAAG;QAAC,eAAe;KAAC;IAEpB,IAAI,uMAAC,UAAA,CAAM,cAAA,CAAe,QAAQ,GAAG;QACnC,OAAO;IACT;IAEA,6MAAO,UAAA,CAAM,YAAA,CAAa,UAAU;QAClC,KAAK,SAAA,OAAA,KAAA,IAAA,MAAO,cAAA;QACZ,gBAAgB,SAAA,OAAA,KAAA,IAAA,MAAO,cAAA;QACvB,+CAA+C,SAAA,OAAA,KAAA,IAAA,MAAO,QAAA;QACtD,wCAAwC,SAAA,OAAA,KAAA,IAAA,MAAO,UAAA;QAC/C,wCAAwC;IAC1C,CAAQ;AACV", "ignoreList": [0], "debugId": null}}]}