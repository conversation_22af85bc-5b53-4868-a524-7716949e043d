{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/app/global-error.tsx"], "sourcesContent": ["'use client';\nimport { useEffect } from 'react';\nimport { Alert, Button } from 'antd';\n\nexport default function GlobalError({\n  error,\n  reset,\n}: {\n  error: Error & { digest?: string };\n  reset: () => void;\n}) {\n  const isDev = process.env.NODE_ENV === 'development';\n  useEffect(() => {\n    // Log the error to an error reporting service\n    console.error(error);\n  }, [error]);\n\n  return (\n    <html>\n      <body>\n        <div className=\"flex flex-col items-center justify-center mt-20\">\n          <div className=\"bg-white shadow-md rounded-lg p-8 w-full text-center flex flex-col gap-3\">\n            <div className=\"text-2xl\">Ôi không! Đ<PERSON> có lỗi xảy ra rồi 🥺</div>\n            {isDev && <Alert message=\"Error\" description={error.message} type=\"error\" showIcon />}\n            <Button\n              type=\"primary\"\n              onClick={\n                // Attempt to recover by trying to re-render the segment\n                () => reset()\n              }\n            >\n              <PERSON><PERSON><PERSON> lại\n            </Button>\n          </div>\n        </div>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAFA;;;;AAIe,SAAS,YAAY,EAClC,KAAK,EACL,KAAK,EAIN;IACC,MAAM,QAAQ,oDAAyB;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8CAA8C;QAC9C,QAAQ,KAAK,CAAC;IAChB,GAAG;QAAC;KAAM;IAEV,qBACE,8OAAC;kBACC,cAAA,8OAAC;sBACC,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAW;;;;;;wBACzB,uBAAS,8OAAC,gLAAA,CAAA,QAAK;4BAAC,SAAQ;4BAAQ,aAAa,MAAM,OAAO;4BAAE,MAAK;4BAAQ,QAAQ;;;;;;sCAClF,8OAAC,kMAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SACE,wDAAwD;4BACxD,IAAM;sCAET;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}]}