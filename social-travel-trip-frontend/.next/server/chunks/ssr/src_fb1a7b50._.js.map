{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/components/ui/radix-ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nInput.displayName = 'Input';\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uXACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/components/ui/radix-ui/tabs.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as TabsPrimitive from '@radix-ui/react-tabs';\n\nimport { cn } from '@/lib/utils';\n\nconst Tabs = TabsPrimitive.Root;\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      'inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground',\n      className\n    )}\n    {...props}\n  />\n));\nTabsList.displayName = TabsPrimitive.List.displayName;\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-xs',\n      className\n    )}\n    {...props}\n  />\n));\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      'mt-2 ring-offset-background focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',\n      className\n    )}\n    {...props}\n  />\n));\nTabsContent.displayName = TabsPrimitive.Content.displayName;\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent };\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/components/ui/radix-ui/label.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\nimport { cva, type VariantProps } from 'class-variance-authority';\n\nimport { cn } from '@/lib/utils';\n\nconst labelVariants = cva(\n  'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'\n);\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n));\nLabel.displayName = LabelPrimitive.Root.displayName;\n\nexport { Label };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/components/ui/radix-ui/form.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\nimport { Slot } from '@radix-ui/react-slot';\nimport {\n  Controller,\n  ControllerProps,\n  FieldPath,\n  FieldValues,\n  FormProvider,\n  useFormContext,\n} from 'react-hook-form';\n\nimport { cn } from '@/lib/utils';\nimport { Label } from '@/components/ui/radix-ui/label';\n\nconst Form = FormProvider;\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\n> = {\n  name: TName;\n};\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n);\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  );\n};\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext);\n  const itemContext = React.useContext(FormItemContext);\n  const { getFieldState, formState } = useFormContext();\n\n  const fieldState = getFieldState(fieldContext.name, formState);\n\n  if (!fieldContext) {\n    throw new Error('useFormField should be used within <FormField>');\n  }\n\n  const { id } = itemContext;\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState,\n  };\n};\n\ntype FormItemContextValue = {\n  id: string;\n};\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n);\n\nconst FormItem = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => {\n  const id = React.useId();\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div ref={ref} className={cn('space-y-2', className)} {...props} />\n    </FormItemContext.Provider>\n  );\n});\nFormItem.displayName = 'FormItem';\n\nconst FormLabel = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>\n>(({ className, ...props }, ref) => {\n  const { error, formItemId } = useFormField();\n\n  return (\n    <Label\n      ref={ref}\n      className={cn(error && 'text-destructive', className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  );\n});\nFormLabel.displayName = 'FormLabel';\n\nconst FormControl = React.forwardRef<\n  React.ElementRef<typeof Slot>,\n  React.ComponentPropsWithoutRef<typeof Slot>\n>(({ ...props }, ref) => {\n  const { error, formItemId, formDescriptionId, formMessageId } =\n    useFormField();\n\n  return (\n    <Slot\n      ref={ref}\n      id={formItemId}\n      aria-describedby={\n        !error\n          ? `${formDescriptionId}`\n          : `${formDescriptionId} ${formMessageId}`\n      }\n      aria-invalid={!!error}\n      {...props}\n    />\n  );\n});\nFormControl.displayName = 'FormControl';\n\nconst FormDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => {\n  const { formDescriptionId } = useFormField();\n\n  return (\n    <p\n      ref={ref}\n      id={formDescriptionId}\n      className={cn('text-sm text-muted-foreground', className)}\n      {...props}\n    />\n  );\n});\nFormDescription.displayName = 'FormDescription';\n\nconst FormMessage = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, children, ...props }, ref) => {\n  const { error, formMessageId } = useFormField();\n  const body = error ? String(error?.message) : children;\n\n  if (!body) {\n    return null;\n  }\n\n  return (\n    <p\n      ref={ref}\n      id={formMessageId}\n      className={cn('text-sm font-medium text-destructive', className)}\n      {...props}\n    >\n      {body}\n    </p>\n  );\n});\nFormMessage.displayName = 'FormMessage';\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AASA;AACA;AAfA;;;;;;;AAiBA,MAAM,OAAO,8JAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,8OAAC,8JAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IAElD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAErB,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,8OAAC;YAAI,KAAK;YAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAa,GAAG,KAAK;;;;;;;;;;;AAGrE;AACA,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,8OAAC,gJAAA,CAAA,QAAK;QACJ,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,SAAS,oBAAoB;QAC3C,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AACA,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,GAAG,OAAO,EAAE;IACf,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAC3D;IAEF,qBACE,8OAAC,gKAAA,CAAA,OAAI;QACH,KAAK;QACL,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AACA,YAAY,WAAW,GAAG;AAE1B,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AACA,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpC,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW;IAE9C,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;kBAER;;;;;;AAGP;AACA,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 274, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/auth/AuthForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { But<PERSON> } from '@/components/ui/radix-ui/button';\nimport { Input } from '@/components/ui/radix-ui/input';\nimport { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/radix-ui/tabs';\nimport { useRouter } from 'next/navigation';\nimport { z } from 'zod';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport {\n  Form,\n  FormControl,\n  FormField,\n  FormItem,\n  FormLabel,\n  FormMessage,\n} from '@/components/ui/radix-ui/form';\n\n// Login form schema\nconst loginSchema = z.object({\n  email: z.string().email('Email không hợp lệ'),\n  password: z.string().min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),\n});\n\n// Registration form schema\nconst registerSchema = z.object({\n  name: z.string().min(2, '<PERSON><PERSON><PERSON> ph<PERSON>i có ít nhất 2 ký tự'),\n  email: z.string().email('<PERSON><PERSON> không hợp lệ'),\n  password: z.string().min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),\n  confirmPassword: z.string().min(6, 'Mật khẩu phải có ít nhất 6 ký tự'),\n}).refine((data) => data.password === data.confirmPassword, {\n  message: 'Mật khẩu không khớp',\n  path: ['confirmPassword'],\n});\n\ntype LoginFormValues = z.infer<typeof loginSchema>;\ntype RegisterFormValues = z.infer<typeof registerSchema>;\n\nexport default function AuthForm() {\n  const [activeTab, setActiveTab] = useState<string>('login');\n  const router = useRouter();\n\n  // Login form\n  const loginForm = useForm<LoginFormValues>({\n    resolver: zodResolver(loginSchema),\n    defaultValues: {\n      email: '',\n      password: '',\n    },\n  });\n\n  // Register form\n  const registerForm = useForm<RegisterFormValues>({\n    resolver: zodResolver(registerSchema),\n    defaultValues: {\n      name: '',\n      email: '',\n      password: '',\n      confirmPassword: '',\n    },\n  });\n\n  const onLoginSubmit = async (values: LoginFormValues) => {\n    console.log('Login values:', values);\n    // Implement your login logic here\n    // For example:\n    // await signIn(values.email, values.password);\n    // router.push('/dashboard');\n  };\n\n  const onRegisterSubmit = async (values: RegisterFormValues) => {\n    console.log('Register values:', values);\n    // Implement your registration logic here\n    // For example:\n    // await register(values.name, values.email, values.password);\n    // setActiveTab('login');\n  };\n\n  return (\n    <div className=\"w-full max-w-md mx-auto p-6 bg-card text-card-foreground rounded-lg\">\n      <Tabs value={activeTab} onValueChange={setActiveTab} className=\"w-full\">\n        <TabsList className=\"grid w-full grid-cols-2 mb-6\">\n          <TabsTrigger value=\"login\" className=\"text-base\">Đăng nhập</TabsTrigger>\n          <TabsTrigger value=\"register\" className=\"text-base\">Đăng ký</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"login\">\n          <Form {...loginForm}>\n            <form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className=\"space-y-4\">\n              <FormField\n                control={loginForm.control}\n                name=\"email\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Email</FormLabel>\n                    <FormControl>\n                      <Input placeholder=\"<EMAIL>\" {...field} />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={loginForm.control}\n                name=\"password\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Mật khẩu</FormLabel>\n                    <FormControl>\n                      <Input type=\"password\" placeholder=\"******\" {...field} />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              <Button\n                type=\"submit\"\n                className=\"w-full bg-primary hover:bg-primary/90 text-primary-foreground\"\n              >\n                Đăng nhập\n              </Button>\n            </form>\n          </Form>\n        </TabsContent>\n\n        <TabsContent value=\"register\">\n          <Form {...registerForm}>\n            <form onSubmit={registerForm.handleSubmit(onRegisterSubmit)} className=\"space-y-4\">\n              <FormField\n                control={registerForm.control}\n                name=\"name\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Họ tên</FormLabel>\n                    <FormControl>\n                      <Input placeholder=\"Nguyễn Văn A\" {...field} />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={registerForm.control}\n                name=\"email\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Email</FormLabel>\n                    <FormControl>\n                      <Input placeholder=\"<EMAIL>\" {...field} />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={registerForm.control}\n                name=\"password\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Mật khẩu</FormLabel>\n                    <FormControl>\n                      <Input type=\"password\" placeholder=\"******\" {...field} />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              <FormField\n                control={registerForm.control}\n                name=\"confirmPassword\"\n                render={({ field }) => (\n                  <FormItem>\n                    <FormLabel>Xác nhận mật khẩu</FormLabel>\n                    <FormControl>\n                      <Input type=\"password\" placeholder=\"******\" {...field} />\n                    </FormControl>\n                    <FormMessage />\n                  </FormItem>\n                )}\n              />\n\n              <Button\n                type=\"submit\"\n                className=\"w-full bg-secondary hover:bg-secondary/90 text-secondary-foreground\"\n              >\n                Đăng ký\n              </Button>\n            </form>\n          </Form>\n        </TabsContent>\n      </Tabs>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA;;;;;;;;;;;AAmBA,oBAAoB;AACpB,MAAM,cAAc,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAEA,2BAA2B;AAC3B,MAAM,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9B,MAAM,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AACrC,GAAG,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IAC1D,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAKe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACnD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,aAAa;IACb,MAAM,YAAY,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAmB;QACzC,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,OAAO;YACP,UAAU;QACZ;IACF;IAEA,gBAAgB;IAChB,MAAM,eAAe,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAsB;QAC/C,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,MAAM;YACN,OAAO;YACP,UAAU;YACV,iBAAiB;QACnB;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,QAAQ,GAAG,CAAC,iBAAiB;IAC7B,kCAAkC;IAClC,eAAe;IACf,+CAA+C;IAC/C,6BAA6B;IAC/B;IAEA,MAAM,mBAAmB,OAAO;QAC9B,QAAQ,GAAG,CAAC,oBAAoB;IAChC,yCAAyC;IACzC,eAAe;IACf,8DAA8D;IAC9D,yBAAyB;IAC3B;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,+IAAA,CAAA,OAAI;YAAC,OAAO;YAAW,eAAe;YAAc,WAAU;;8BAC7D,8OAAC,+IAAA,CAAA,WAAQ;oBAAC,WAAU;;sCAClB,8OAAC,+IAAA,CAAA,cAAW;4BAAC,OAAM;4BAAQ,WAAU;sCAAY;;;;;;sCACjD,8OAAC,+IAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCAAY;;;;;;;;;;;;8BAGtD,8OAAC,+IAAA,CAAA,cAAW;oBAAC,OAAM;8BACjB,cAAA,8OAAC,+IAAA,CAAA,OAAI;wBAAE,GAAG,SAAS;kCACjB,cAAA,8OAAC;4BAAK,UAAU,UAAU,YAAY,CAAC;4BAAgB,WAAU;;8CAC/D,8OAAC,+IAAA,CAAA,YAAS;oCACR,SAAS,UAAU,OAAO;oCAC1B,MAAK;oCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,+IAAA,CAAA,WAAQ;;8DACP,8OAAC,+IAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,+IAAA,CAAA,cAAW;8DACV,cAAA,8OAAC,gJAAA,CAAA,QAAK;wDAAC,aAAY;wDAAqB,GAAG,KAAK;;;;;;;;;;;8DAElD,8OAAC,+IAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8CAKlB,8OAAC,+IAAA,CAAA,YAAS;oCACR,SAAS,UAAU,OAAO;oCAC1B,MAAK;oCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,+IAAA,CAAA,WAAQ;;8DACP,8OAAC,+IAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,+IAAA,CAAA,cAAW;8DACV,cAAA,8OAAC,gJAAA,CAAA,QAAK;wDAAC,MAAK;wDAAW,aAAY;wDAAU,GAAG,KAAK;;;;;;;;;;;8DAEvD,8OAAC,+IAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8CAKlB,8OAAC,iJAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;8BAOP,8OAAC,+IAAA,CAAA,cAAW;oBAAC,OAAM;8BACjB,cAAA,8OAAC,+IAAA,CAAA,OAAI;wBAAE,GAAG,YAAY;kCACpB,cAAA,8OAAC;4BAAK,UAAU,aAAa,YAAY,CAAC;4BAAmB,WAAU;;8CACrE,8OAAC,+IAAA,CAAA,YAAS;oCACR,SAAS,aAAa,OAAO;oCAC7B,MAAK;oCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,+IAAA,CAAA,WAAQ;;8DACP,8OAAC,+IAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,+IAAA,CAAA,cAAW;8DACV,cAAA,8OAAC,gJAAA,CAAA,QAAK;wDAAC,aAAY;wDAAgB,GAAG,KAAK;;;;;;;;;;;8DAE7C,8OAAC,+IAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8CAKlB,8OAAC,+IAAA,CAAA,YAAS;oCACR,SAAS,aAAa,OAAO;oCAC7B,MAAK;oCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,+IAAA,CAAA,WAAQ;;8DACP,8OAAC,+IAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,+IAAA,CAAA,cAAW;8DACV,cAAA,8OAAC,gJAAA,CAAA,QAAK;wDAAC,aAAY;wDAAqB,GAAG,KAAK;;;;;;;;;;;8DAElD,8OAAC,+IAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8CAKlB,8OAAC,+IAAA,CAAA,YAAS;oCACR,SAAS,aAAa,OAAO;oCAC7B,MAAK;oCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,+IAAA,CAAA,WAAQ;;8DACP,8OAAC,+IAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,+IAAA,CAAA,cAAW;8DACV,cAAA,8OAAC,gJAAA,CAAA,QAAK;wDAAC,MAAK;wDAAW,aAAY;wDAAU,GAAG,KAAK;;;;;;;;;;;8DAEvD,8OAAC,+IAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8CAKlB,8OAAC,+IAAA,CAAA,YAAS;oCACR,SAAS,aAAa,OAAO;oCAC7B,MAAK;oCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,+IAAA,CAAA,WAAQ;;8DACP,8OAAC,+IAAA,CAAA,YAAS;8DAAC;;;;;;8DACX,8OAAC,+IAAA,CAAA,cAAW;8DACV,cAAA,8OAAC,gJAAA,CAAA,QAAK;wDAAC,MAAK;wDAAW,aAAY;wDAAU,GAAG,KAAK;;;;;;;;;;;8DAEvD,8OAAC,+IAAA,CAAA,cAAW;;;;;;;;;;;;;;;;8CAKlB,8OAAC,iJAAA,CAAA,SAAM;oCACL,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 723, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/components/ui/radix-ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-lg border bg-card text-card-foreground shadow-xs',\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 804, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/app/%28auth%29/auth/login/page.tsx"], "sourcesContent": ["'use client';\n\nimport AuthForm from '@/features/auth/AuthForm';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/radix-ui/card';\n\nexport default function LoginPage() {\n  return (\n    <div className=\"flex min-h-screen items-center justify-center bg-gradient-to-br from-primary/5 to-secondary/5 dark:from-primary/10 dark:to-secondary/10 p-4\">\n      <Card className=\"w-full max-w-md border-primary/20\">\n        <CardHeader className=\"text-center\">\n          <CardTitle className=\"text-2xl font-bold text-primary\">TripTribe</CardTitle>\n          <CardDescription>Đăng nhập hoặc đăng ký tài khoản</CardDescription>\n        </CardHeader>\n        <CardContent>\n          <AuthForm />\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,+IAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,+IAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,8OAAC,+IAAA,CAAA,YAAS;4BAAC,WAAU;sCAAkC;;;;;;sCACvD,8OAAC,+IAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;;8BAEnB,8OAAC,+IAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,oIAAA,CAAA,UAAQ;;;;;;;;;;;;;;;;;;;;;AAKnB", "debugId": null}}]}