{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/components/ui/page-header.tsx"], "sourcesContent": ["interface PageHeaderProps {\n  title: string;\n  description?: string;\n}\n\nexport function PageHeader({ title, description }: PageHeaderProps) {\n  return (\n    <div className=\"space-y-1\">\n      <h1 className=\"text-2xl md:text-3xl font-bold text-purple-800 dark:text-purple-400\">{title}</h1>\n      {description && (\n        <p className=\"text-muted-foreground\">{description}</p>\n      )}\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;;AAKO,SAAS,WAAW,EAAE,KAAK,EAAE,WAAW,EAAmB;IAChE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAG,WAAU;0BAAuE;;;;;;YACpF,6BACC,8OAAC;gBAAE,WAAU;0BAAyB;;;;;;;;;;;;AAI9C", "debugId": null}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/components/ui/radix-ui/badge.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\n\nimport { cn } from '@/lib/utils';\n\nconst badgeVariants = cva(\n  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2',\n  {\n    variants: {\n      variant: {\n        default:\n          'border-transparent bg-primary text-primary-foreground hover:bg-primary/80',\n        secondary:\n          'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',\n        destructive:\n          'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80',\n        outline: 'text-foreground',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n    },\n  }\n);\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  );\n}\n\nexport { Badge, badgeVariants };\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,4KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/components/ui/radix-ui/tabs.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as TabsPrimitive from '@radix-ui/react-tabs';\n\nimport { cn } from '@/lib/utils';\n\nconst Tabs = TabsPrimitive.Root;\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      'inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground',\n      className\n    )}\n    {...props}\n  />\n));\nTabsList.displayName = TabsPrimitive.List.displayName;\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-xs',\n      className\n    )}\n    {...props}\n  />\n));\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      'mt-2 ring-offset-background focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',\n      className\n    )}\n    {...props}\n  />\n));\nTabsContent.displayName = TabsPrimitive.Content.displayName;\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent };\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 221, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/components/ui/radix-ui/avatar.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as AvatarPrimitive from '@radix-ui/react-avatar';\n\nimport { cn } from '@/lib/utils';\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      'relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full',\n      className\n    )}\n    {...props}\n  />\n));\nAvatar.displayName = AvatarPrimitive.Root.displayName;\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn('aspect-square h-full w-full', className)}\n    {...props}\n  />\n));\nAvatarImage.displayName = AvatarPrimitive.Image.displayName;\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      'flex h-full w-full items-center justify-center rounded-full bg-muted',\n      className\n    )}\n    {...props}\n  />\n));\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName;\n\nexport { Avatar, AvatarImage, AvatarFallback };\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,kKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/features/view360/view360.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useState, useEffect } from 'react';\nimport { ReactPhotoSphereViewer } from 'react-photo-sphere-viewer';\nimport { MarkersPlugin } from '@photo-sphere-viewer/markers-plugin';\n// Bỏ import CSS vì nó không tồn tại trong package\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Maximize, Minimize, RotateCcw, ChevronLeft, ChevronRight, MapPin, ArrowRight } from 'lucide-react';\n\nexport interface PanoramaScene {\n  id: string;\n  name: string;\n  image: string;\n  description?: string;\n  position?: {\n    lat?: number;\n    lng?: number;\n  };\n  // Các điểm liên kết đến các scene khác\n  hotspots?: {\n    id: string; // ID của scene đích\n    name: string;\n    position: { yaw: string; pitch: string }; // Vị trí của hotspot trên ảnh 360\n    tooltip?: string;\n  }[];\n}\n\ninterface View360Props {\n  scenes: PanoramaScene[];\n  initialSceneId?: string;\n  className?: string;\n  height?: string;\n  width?: string;\n  fullscreenButton?: boolean;\n  resetButton?: boolean;\n  showSceneSelector?: boolean;\n}\n\nexport const View360: React.FC<View360Props> = ({\n  scenes,\n  initialSceneId,\n  className = '',\n  height = '400px',\n  width = '100%',\n  fullscreenButton = true,\n  resetButton = true,\n  showSceneSelector = true,\n}) => {\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [currentSceneIndex, setCurrentSceneIndex] = useState(\n    initialSceneId\n      ? scenes.findIndex(scene => scene.id === initialSceneId)\n      : 0\n  );\n  const viewerRef = useRef<any>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n\n  // Xử lý khi component unmount\n  useEffect(() => {\n    return () => {\n      if (viewerRef.current) {\n        try {\n          viewerRef.current.destroy();\n        } catch (error) {\n          console.error('Error destroying viewer:', error);\n        }\n      }\n    };\n  }, []);\n\n  // Xử lý khi nhấn nút fullscreen\n  const toggleFullscreen = () => {\n    if (!containerRef.current) return;\n\n    if (!isFullscreen) {\n      if (containerRef.current.requestFullscreen) {\n        containerRef.current.requestFullscreen();\n      }\n      setIsFullscreen(true);\n    } else {\n      if (document.exitFullscreen) {\n        document.exitFullscreen();\n      }\n      setIsFullscreen(false);\n    }\n  };\n\n  // Xử lý khi nhấn nút reset\n  const handleReset = () => {\n    if (viewerRef.current) {\n      viewerRef.current.animate({\n        yaw: 0,\n        pitch: 0,\n        zoom: 50,\n        speed: '10rpm',\n      });\n    }\n  };\n\n  // Xử lý sự kiện fullscreen change\n  useEffect(() => {\n    const handleFullscreenChange = () => {\n      setIsFullscreen(!!document.fullscreenElement);\n    };\n\n    document.addEventListener('fullscreenchange', handleFullscreenChange);\n    return () => {\n      document.removeEventListener('fullscreenchange', handleFullscreenChange);\n    };\n  }, []);\n\n  // Chuyển đến scene trước đó\n  const goToPreviousScene = () => {\n    if (currentSceneIndex > 0) {\n      setCurrentSceneIndex(currentSceneIndex - 1);\n    } else {\n      setCurrentSceneIndex(scenes.length - 1);\n    }\n  };\n\n  // Chuyển đến scene tiếp theo\n  const goToNextScene = () => {\n    if (currentSceneIndex < scenes.length - 1) {\n      setCurrentSceneIndex(currentSceneIndex + 1);\n    } else {\n      setCurrentSceneIndex(0);\n    }\n  };\n\n  // Chuyển đến scene cụ thể theo index\n  const goToScene = (index: number) => {\n    if (index >= 0 && index < scenes.length) {\n      setCurrentSceneIndex(index);\n    }\n  };\n\n  // Chuyển đến scene cụ thể theo ID\n  const goToSceneById = (sceneId: string) => {\n    const sceneIndex = scenes.findIndex(scene => scene.id === sceneId);\n    if (sceneIndex !== -1) {\n      setCurrentSceneIndex(sceneIndex);\n    }\n  };\n\n  // Xử lý khi nhấp vào hotspot\n  const handleHotspotClick = (hotspotId: string) => {\n    // Chuyển đến scene khác trong cùng địa điểm\n    goToSceneById(hotspotId);\n  };\n\n  // Lấy scene hiện tại\n  const currentScene = scenes[currentSceneIndex];\n\n  return (\n    <Card className={`overflow-hidden border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs ${className}`}>\n      <CardContent className=\"p-0\">\n        <div ref={containerRef} className=\"relative\">\n          <div style={{ height, width }}>\n            <ReactPhotoSphereViewer\n              key={currentScene.id} // Quan trọng: key thay đổi khi scene thay đổi để re-render component\n              ref={viewerRef}\n              src={currentScene.image}\n              height={isFullscreen ? '100vh' : height}\n              width={width}\n              littlePlanet={false}\n              container={containerRef.current || undefined}\n              plugins={[\n                [MarkersPlugin, {\n                  markers: currentScene.hotspots?.map(hotspot => ({\n                    id: hotspot.id,\n                    position: hotspot.position,\n                    tooltip: {\n                      content: hotspot.tooltip || hotspot.name,\n                      position: 'bottom'\n                    },\n                    html: `\n                      <div style=\"\n                        display: flex;\n                        align-items: center;\n                        background-color: rgba(255, 255, 255, 0.8);\n                        padding: 5px 10px;\n                        border-radius: 20px;\n                        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);\n                        cursor: pointer;\n                        transition: all 0.2s ease;\n                      \">\n                        <div style=\"\n                          margin-right: 5px;\n                          color: rgb(147, 51, 234);\n                        \">\n                          <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" stroke=\"currentColor\" stroke-width=\"2\" stroke-linecap=\"round\" stroke-linejoin=\"round\">\n                            <path d=\"M5 12h14\"></path>\n                            <path d=\"m12 5 7 7-7 7\"></path>\n                          </svg>\n                        </div>\n                        <div style=\"\n                          font-weight: 500;\n                          font-size: 14px;\n                        \">${hotspot.name}</div>\n                      </div>\n                    `,\n                    data: { sceneId: hotspot.id }\n                  })) || []\n                }]\n              ]}\n              onReady={(instance) => {\n                // Xử lý sự kiện click vào marker\n                if (currentScene.hotspots && currentScene.hotspots.length > 0) {\n                  const markersPlugin = instance.getPlugin(MarkersPlugin);\n                  if (markersPlugin) {\n                    markersPlugin.addEventListener('select-marker', (e: any) => {\n                      if (e.marker && e.marker.data && e.marker.data.sceneId) {\n                        handleHotspotClick(e.marker.data.sceneId);\n                      }\n                    });\n                  }\n                }\n              }}\n            />\n          </div>\n\n\n\n          {/* Scene Info */}\n          <div className=\"absolute top-4 left-4 right-4 flex justify-between items-center\">\n            <div className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm px-3 py-1.5 rounded-md flex items-center\">\n              <MapPin className=\"h-4 w-4 mr-2 text-purple-600\" />\n              <span className=\"font-medium text-sm\">{currentScene.name}</span>\n            </div>\n\n            {currentScene.description && (\n              <div className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm px-3 py-1.5 rounded-md\">\n                <span className=\"text-sm\">{currentScene.description}</span>\n              </div>\n            )}\n          </div>\n\n          {/* Scene Navigation */}\n          {scenes.length > 1 && (\n            <div className=\"absolute top-1/2 left-4 right-4 -translate-y-1/2 flex justify-between\">\n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\"\n                onClick={goToPreviousScene}\n                title=\"Cảnh trước đó\"\n              >\n                <ChevronLeft className=\"h-4 w-4\" />\n              </Button>\n\n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\"\n                onClick={goToNextScene}\n                title=\"Cảnh tiếp theo\"\n              >\n                <ChevronRight className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          )}\n\n          {/* Controls */}\n          <div className=\"absolute bottom-4 right-4 flex gap-2\">\n            {resetButton && (\n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\"\n                onClick={handleReset}\n                title=\"Đặt lại góc nhìn\"\n              >\n                <RotateCcw className=\"h-4 w-4\" />\n              </Button>\n            )}\n\n            {fullscreenButton && (\n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\"\n                onClick={toggleFullscreen}\n                title={isFullscreen ? \"Thoát toàn màn hình\" : \"Toàn màn hình\"}\n              >\n                {isFullscreen ? (\n                  <Minimize className=\"h-4 w-4\" />\n                ) : (\n                  <Maximize className=\"h-4 w-4\" />\n                )}\n              </Button>\n            )}\n          </div>\n\n          {/* Scene Selector */}\n          {showSceneSelector && scenes.length > 1 && (\n            <div className=\"absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2\">\n              {scenes.map((scene, index) => (\n                <Button\n                  key={scene.id}\n                  variant={index === currentSceneIndex ? \"default\" : \"outline\"}\n                  size=\"sm\"\n                  className={`bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm ${\n                    index === currentSceneIndex ? \"bg-purple-600 text-white\" : \"\"\n                  }`}\n                  onClick={() => goToScene(index)}\n                >\n                  {scene.name}\n                </Button>\n              ))}\n            </div>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default View360;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA,kDAAkD;AAClD;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;AAuCO,MAAM,UAAkC,CAAC,EAC9C,MAAM,EACN,cAAc,EACd,YAAY,EAAE,EACd,SAAS,OAAO,EAChB,QAAQ,MAAM,EACd,mBAAmB,IAAI,EACvB,cAAc,IAAI,EAClB,oBAAoB,IAAI,EACzB;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACvD,iBACI,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,kBACvC;IAEN,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAO;IAC9B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE5C,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,IAAI,UAAU,OAAO,EAAE;gBACrB,IAAI;oBACF,UAAU,OAAO,CAAC,OAAO;gBAC3B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,4BAA4B;gBAC5C;YACF;QACF;IACF,GAAG,EAAE;IAEL,gCAAgC;IAChC,MAAM,mBAAmB;QACvB,IAAI,CAAC,aAAa,OAAO,EAAE;QAE3B,IAAI,CAAC,cAAc;YACjB,IAAI,aAAa,OAAO,CAAC,iBAAiB,EAAE;gBAC1C,aAAa,OAAO,CAAC,iBAAiB;YACxC;YACA,gBAAgB;QAClB,OAAO;YACL,IAAI,SAAS,cAAc,EAAE;gBAC3B,SAAS,cAAc;YACzB;YACA,gBAAgB;QAClB;IACF;IAEA,2BAA2B;IAC3B,MAAM,cAAc;QAClB,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,OAAO,CAAC;gBACxB,KAAK;gBACL,OAAO;gBACP,MAAM;gBACN,OAAO;YACT;QACF;IACF;IAEA,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,yBAAyB;YAC7B,gBAAgB,CAAC,CAAC,SAAS,iBAAiB;QAC9C;QAEA,SAAS,gBAAgB,CAAC,oBAAoB;QAC9C,OAAO;YACL,SAAS,mBAAmB,CAAC,oBAAoB;QACnD;IACF,GAAG,EAAE;IAEL,4BAA4B;IAC5B,MAAM,oBAAoB;QACxB,IAAI,oBAAoB,GAAG;YACzB,qBAAqB,oBAAoB;QAC3C,OAAO;YACL,qBAAqB,OAAO,MAAM,GAAG;QACvC;IACF;IAEA,6BAA6B;IAC7B,MAAM,gBAAgB;QACpB,IAAI,oBAAoB,OAAO,MAAM,GAAG,GAAG;YACzC,qBAAqB,oBAAoB;QAC3C,OAAO;YACL,qBAAqB;QACvB;IACF;IAEA,qCAAqC;IACrC,MAAM,YAAY,CAAC;QACjB,IAAI,SAAS,KAAK,QAAQ,OAAO,MAAM,EAAE;YACvC,qBAAqB;QACvB;IACF;IAEA,kCAAkC;IAClC,MAAM,gBAAgB,CAAC;QACrB,MAAM,aAAa,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QAC1D,IAAI,eAAe,CAAC,GAAG;YACrB,qBAAqB;QACvB;IACF;IAEA,6BAA6B;IAC7B,MAAM,qBAAqB,CAAC;QAC1B,4CAA4C;QAC5C,cAAc;IAChB;IAEA,qBAAqB;IACrB,MAAM,eAAe,MAAM,CAAC,kBAAkB;IAE9C,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAC,0GAA0G,EAAE,WAAW;kBACvI,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAI,KAAK;gBAAc,WAAU;;kCAChC,8OAAC;wBAAI,OAAO;4BAAE;4BAAQ;wBAAM;kCAC1B,cAAA,8OAAC,mKAAA,CAAA,yBAAsB;4BAErB,KAAK;4BACL,KAAK,aAAa,KAAK;4BACvB,QAAQ,eAAe,UAAU;4BACjC,OAAO;4BACP,cAAc;4BACd,WAAW,aAAa,OAAO,IAAI;4BACnC,SAAS;gCACP;oCAAC,mLAAA,CAAA,gBAAa;oCAAE;wCACd,SAAS,aAAa,QAAQ,EAAE,IAAI,CAAA,UAAW,CAAC;gDAC9C,IAAI,QAAQ,EAAE;gDACd,UAAU,QAAQ,QAAQ;gDAC1B,SAAS;oDACP,SAAS,QAAQ,OAAO,IAAI,QAAQ,IAAI;oDACxC,UAAU;gDACZ;gDACA,MAAM,CAAC;;;;;;;;;;;;;;;;;;;;;;;0BAuBD,EAAE,QAAQ,IAAI,CAAC;;oBAErB,CAAC;gDACD,MAAM;oDAAE,SAAS,QAAQ,EAAE;gDAAC;4CAC9B,CAAC,MAAM,EAAE;oCACX;iCAAE;6BACH;4BACD,SAAS,CAAC;gCACR,iCAAiC;gCACjC,IAAI,aAAa,QAAQ,IAAI,aAAa,QAAQ,CAAC,MAAM,GAAG,GAAG;oCAC7D,MAAM,gBAAgB,SAAS,SAAS,CAAC,mLAAA,CAAA,gBAAa;oCACtD,IAAI,eAAe;wCACjB,cAAc,gBAAgB,CAAC,iBAAiB,CAAC;4CAC/C,IAAI,EAAE,MAAM,IAAI,EAAE,MAAM,CAAC,IAAI,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE;gDACtD,mBAAmB,EAAE,MAAM,CAAC,IAAI,CAAC,OAAO;4CAC1C;wCACF;oCACF;gCACF;4BACF;2BA1DK,aAAa,EAAE;;;;;;;;;;kCAiExB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAAuB,aAAa,IAAI;;;;;;;;;;;;4BAGzD,aAAa,WAAW,kBACvB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAW,aAAa,WAAW;;;;;;;;;;;;;;;;;oBAMxD,OAAO,MAAM,GAAG,mBACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,OAAM;0CAEN,cAAA,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;0CAGzB,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,OAAM;0CAEN,cAAA,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAM9B,8OAAC;wBAAI,WAAU;;4BACZ,6BACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,OAAM;0CAEN,cAAA,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;4BAIxB,kCACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,OAAO,eAAe,wBAAwB;0CAE7C,6BACC,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;yDAEpB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAO3B,qBAAqB,OAAO,MAAM,GAAG,mBACpC,8OAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,OAAO,sBAClB,8OAAC,kIAAA,CAAA,SAAM;gCAEL,SAAS,UAAU,oBAAoB,YAAY;gCACnD,MAAK;gCACL,WAAW,CAAC,iDAAiD,EAC3D,UAAU,oBAAoB,6BAA6B,IAC3D;gCACF,SAAS,IAAM,UAAU;0CAExB,MAAM,IAAI;+BARN,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AAiB/B;uCAEe", "debugId": null}}, {"offset": {"line": 659, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/features/view360/google-maps-view.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useRef, useState, useEffect } from 'react';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Maximize, Minimize, RotateCcw, MapPin } from 'lucide-react';\n\ninterface GoogleMapsViewProps {\n  mapUrl: string;\n  className?: string;\n  height?: string;\n  width?: string;\n  fullscreenButton?: boolean;\n  resetButton?: boolean;\n  title?: string;\n  description?: string;\n}\n\n/**\n * GoogleMapsView - Component để nhúng Google Maps Street View 360 độ\n * \n * @param mapUrl URL của Google Maps Street View (dạng https://maps.app.goo.gl/XXX hoặc https://goo.gl/maps/XXX)\n * @param className CSS class tùy chỉnh\n * @param height Chiều cao của component (mặc định: 400px)\n * @param width Chiều rộng của component (mặc định: 100%)\n * @param fullscreenButton Hiển thị nút toàn màn hình (mặc định: true)\n * @param resetButton Hiển thị nút reset (mặc định: true)\n * @param title Tiêu đề hiển thị phía trên Street View\n * @param description Mô tả hiển thị phía trên Street View\n */\nexport const GoogleMapsView: React.FC<GoogleMapsViewProps> = ({\n  mapUrl,\n  className = '',\n  height = '400px',\n  width = '100%',\n  fullscreenButton = true,\n  resetButton = true,\n  title,\n  description,\n}) => {\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const containerRef = useRef<HTMLDivElement>(null);\n  const iframeRef = useRef<HTMLIFrameElement>(null);\n\n  // Xử lý URL Google Maps để lấy embed URL\n  const getEmbedUrl = (url: string): string => {\n    // Kiểm tra nếu URL đã là embed URL\n    if (url.includes('maps.google.com/maps/embed') || url.includes('www.google.com/maps/embed')) {\n      return url;\n    }\n\n    // Xử lý URL dạng https://maps.app.goo.gl/XXX hoặc https://goo.gl/maps/XXX\n    let embedUrl = '';\n    \n    try {\n      // Nếu là URL rút gọn, thử trích xuất tham số\n      if (url.includes('maps.app.goo.gl') || url.includes('goo.gl/maps')) {\n        // Đối với URL rút gọn, chúng ta sẽ sử dụng mode=streetview\n        embedUrl = `https://www.google.com/maps/embed?pb=!4v!1m3!1m2!1s${encodeURIComponent(url)}!2s!5e0!3m2!1svi!2s!4v1`;\n      } \n      // Nếu là URL đầy đủ với tham số\n      else if (url.includes('google.com/maps')) {\n        // Trích xuất tham số từ URL\n        const urlObj = new URL(url);\n        const params = new URLSearchParams(urlObj.search);\n        \n        // Nếu có tham số ll (latitude,longitude)\n        if (params.has('ll')) {\n          const ll = params.get('ll');\n          embedUrl = `https://www.google.com/maps/embed?pb=!4v!1m3!1m2!1s!2s${ll}!5e0!3m2!1svi!2s!4v1`;\n        }\n        // Nếu có tham số q (query/address)\n        else if (params.has('q')) {\n          const q = params.get('q');\n          embedUrl = `https://www.google.com/maps/embed?pb=!4v!1m3!1m2!1s!2s${q}!5e0!3m2!1svi!2s!4v1`;\n        }\n        // Nếu không có tham số phù hợp, sử dụng URL gốc\n        else {\n          embedUrl = `https://www.google.com/maps/embed/v1/streetview?key=YOUR_API_KEY&location=${encodeURIComponent(url)}`;\n        }\n      }\n      // Nếu không phải URL Google Maps hợp lệ\n      else {\n        console.error('URL không hợp lệ:', url);\n        embedUrl = url; // Sử dụng URL gốc\n      }\n    } catch (error) {\n      console.error('Lỗi khi xử lý URL:', error);\n      embedUrl = url; // Sử dụng URL gốc nếu có lỗi\n    }\n\n    return embedUrl;\n  };\n\n  // Xử lý khi nhấn nút fullscreen\n  const toggleFullscreen = () => {\n    if (!containerRef.current) return;\n\n    if (!isFullscreen) {\n      if (containerRef.current.requestFullscreen) {\n        containerRef.current.requestFullscreen();\n      }\n      setIsFullscreen(true);\n    } else {\n      if (document.exitFullscreen) {\n        document.exitFullscreen();\n      }\n      setIsFullscreen(false);\n    }\n  };\n\n  // Xử lý khi nhấn nút reset\n  const handleReset = () => {\n    if (iframeRef.current) {\n      // Reload iframe để reset view\n      const src = iframeRef.current.src;\n      iframeRef.current.src = '';\n      setTimeout(() => {\n        if (iframeRef.current) {\n          iframeRef.current.src = src;\n        }\n      }, 100);\n    }\n  };\n\n  // Theo dõi sự kiện fullscreenchange\n  useEffect(() => {\n    const handleFullscreenChange = () => {\n      setIsFullscreen(!!document.fullscreenElement);\n    };\n\n    document.addEventListener('fullscreenchange', handleFullscreenChange);\n    return () => {\n      document.removeEventListener('fullscreenchange', handleFullscreenChange);\n    };\n  }, []);\n\n  const embedUrl = getEmbedUrl(mapUrl);\n\n  return (\n    <Card className={`overflow-hidden border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs ${className}`}>\n      <CardContent className=\"p-0\">\n        <div ref={containerRef} className=\"relative\">\n          <div style={{ height, width }}>\n            <iframe\n              ref={iframeRef}\n              src={embedUrl}\n              width=\"100%\"\n              height=\"100%\"\n              style={{ border: 0 }}\n              allowFullScreen\n              loading=\"lazy\"\n              referrerPolicy=\"no-referrer-when-downgrade\"\n            ></iframe>\n          </div>\n\n          {/* Location Info */}\n          {(title || description) && (\n            <div className=\"absolute top-4 left-4 right-4 flex justify-between items-center\">\n              {title && (\n                <div className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm px-3 py-1.5 rounded-md flex items-center\">\n                  <MapPin className=\"h-4 w-4 mr-2 text-purple-600\" />\n                  <span className=\"font-medium text-sm\">{title}</span>\n                </div>\n              )}\n\n              {description && (\n                <div className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm px-3 py-1.5 rounded-md\">\n                  <span className=\"text-sm\">{description}</span>\n                </div>\n              )}\n            </div>\n          )}\n\n          {/* Controls */}\n          <div className=\"absolute bottom-4 right-4 flex gap-2\">\n            {resetButton && (\n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\"\n                onClick={handleReset}\n                title=\"Đặt lại góc nhìn\"\n              >\n                <RotateCcw className=\"h-4 w-4\" />\n              </Button>\n            )}\n\n            {fullscreenButton && (\n              <Button\n                variant=\"outline\"\n                size=\"icon\"\n                className=\"bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm\"\n                onClick={toggleFullscreen}\n                title={isFullscreen ? \"Thoát toàn màn hình\" : \"Toàn màn hình\"}\n              >\n                {isFullscreen ? (\n                  <Minimize className=\"h-4 w-4\" />\n                ) : (\n                  <Maximize className=\"h-4 w-4\" />\n                )}\n              </Button>\n            )}\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default GoogleMapsView;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AA8BO,MAAM,iBAAgD,CAAC,EAC5D,MAAM,EACN,YAAY,EAAE,EACd,SAAS,OAAO,EAChB,QAAQ,MAAM,EACd,mBAAmB,IAAI,EACvB,cAAc,IAAI,EAClB,KAAK,EACL,WAAW,EACZ;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAE5C,yCAAyC;IACzC,MAAM,cAAc,CAAC;QACnB,mCAAmC;QACnC,IAAI,IAAI,QAAQ,CAAC,iCAAiC,IAAI,QAAQ,CAAC,8BAA8B;YAC3F,OAAO;QACT;QAEA,0EAA0E;QAC1E,IAAI,WAAW;QAEf,IAAI;YACF,6CAA6C;YAC7C,IAAI,IAAI,QAAQ,CAAC,sBAAsB,IAAI,QAAQ,CAAC,gBAAgB;gBAClE,2DAA2D;gBAC3D,WAAW,CAAC,mDAAmD,EAAE,mBAAmB,KAAK,uBAAuB,CAAC;YACnH,OAEK,IAAI,IAAI,QAAQ,CAAC,oBAAoB;gBACxC,4BAA4B;gBAC5B,MAAM,SAAS,IAAI,IAAI;gBACvB,MAAM,SAAS,IAAI,gBAAgB,OAAO,MAAM;gBAEhD,yCAAyC;gBACzC,IAAI,OAAO,GAAG,CAAC,OAAO;oBACpB,MAAM,KAAK,OAAO,GAAG,CAAC;oBACtB,WAAW,CAAC,sDAAsD,EAAE,GAAG,oBAAoB,CAAC;gBAC9F,OAEK,IAAI,OAAO,GAAG,CAAC,MAAM;oBACxB,MAAM,IAAI,OAAO,GAAG,CAAC;oBACrB,WAAW,CAAC,sDAAsD,EAAE,EAAE,oBAAoB,CAAC;gBAC7F,OAEK;oBACH,WAAW,CAAC,0EAA0E,EAAE,mBAAmB,MAAM;gBACnH;YACF,OAEK;gBACH,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,WAAW,KAAK,kBAAkB;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,WAAW,KAAK,6BAA6B;QAC/C;QAEA,OAAO;IACT;IAEA,gCAAgC;IAChC,MAAM,mBAAmB;QACvB,IAAI,CAAC,aAAa,OAAO,EAAE;QAE3B,IAAI,CAAC,cAAc;YACjB,IAAI,aAAa,OAAO,CAAC,iBAAiB,EAAE;gBAC1C,aAAa,OAAO,CAAC,iBAAiB;YACxC;YACA,gBAAgB;QAClB,OAAO;YACL,IAAI,SAAS,cAAc,EAAE;gBAC3B,SAAS,cAAc;YACzB;YACA,gBAAgB;QAClB;IACF;IAEA,2BAA2B;IAC3B,MAAM,cAAc;QAClB,IAAI,UAAU,OAAO,EAAE;YACrB,8BAA8B;YAC9B,MAAM,MAAM,UAAU,OAAO,CAAC,GAAG;YACjC,UAAU,OAAO,CAAC,GAAG,GAAG;YACxB,WAAW;gBACT,IAAI,UAAU,OAAO,EAAE;oBACrB,UAAU,OAAO,CAAC,GAAG,GAAG;gBAC1B;YACF,GAAG;QACL;IACF;IAEA,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,yBAAyB;YAC7B,gBAAgB,CAAC,CAAC,SAAS,iBAAiB;QAC9C;QAEA,SAAS,gBAAgB,CAAC,oBAAoB;QAC9C,OAAO;YACL,SAAS,mBAAmB,CAAC,oBAAoB;QACnD;IACF,GAAG,EAAE;IAEL,MAAM,WAAW,YAAY;IAE7B,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAC,0GAA0G,EAAE,WAAW;kBACvI,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAI,KAAK;gBAAc,WAAU;;kCAChC,8OAAC;wBAAI,OAAO;4BAAE;4BAAQ;wBAAM;kCAC1B,cAAA,8OAAC;4BACC,KAAK;4BACL,KAAK;4BACL,OAAM;4BACN,QAAO;4BACP,OAAO;gCAAE,QAAQ;4BAAE;4BACnB,eAAe;4BACf,SAAQ;4BACR,gBAAe;;;;;;;;;;;oBAKlB,CAAC,SAAS,WAAW,mBACpB,8OAAC;wBAAI,WAAU;;4BACZ,uBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAAuB;;;;;;;;;;;;4BAI1C,6BACC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAW;;;;;;;;;;;;;;;;;kCAOnC,8OAAC;wBAAI,WAAU;;4BACZ,6BACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,OAAM;0CAEN,cAAA,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;;;;;;4BAIxB,kCACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;gCACT,OAAO,eAAe,wBAAwB;0CAE7C,6BACC,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;yDAEpB,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStC;uCAEe", "debugId": null}}, {"offset": {"line": 914, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/features/view360/universal-view360.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { View360, PanoramaScene } from './view360';\nimport { GoogleMapsView } from './google-maps-view';\n\n// Định nghĩa các loại nguồn dữ liệu 360 độ\nexport type View360Source = \n  | { type: 'panorama'; scenes: PanoramaScene[]; initialSceneId?: string }\n  | { type: 'google-maps'; url: string; title?: string; description?: string };\n\ninterface UniversalView360Props {\n  source: View360Source;\n  className?: string;\n  height?: string;\n  width?: string;\n  fullscreenButton?: boolean;\n  resetButton?: boolean;\n  showSceneSelector?: boolean;\n}\n\n/**\n * UniversalView360 - Component tổng hợp để hiển thị cả ảnh panorama 360 độ và Google Maps Street View\n * \n * @param source Nguồn dữ liệu 360 độ (panorama hoặc google-maps)\n * @param className CSS class tùy chỉnh\n * @param height Chiều cao của component (mặc định: 400px)\n * @param width Chiều rộng của component (mặc định: 100%)\n * @param fullscreenButton Hiển thị nút toàn màn hình (mặc định: true)\n * @param resetButton Hiển thị nút reset (mặc định: true)\n * @param showSceneSelector Hiển thị bộ chọn cảnh (chỉ áp dụng cho panorama, mặc định: true)\n */\nexport const UniversalView360: React.FC<UniversalView360Props> = ({\n  source,\n  className = '',\n  height = '400px',\n  width = '100%',\n  fullscreenButton = true,\n  resetButton = true,\n  showSceneSelector = true,\n}) => {\n  // Hiển thị component tương ứng dựa trên loại nguồn dữ liệu\n  if (source.type === 'panorama') {\n    return (\n      <View360\n        scenes={source.scenes}\n        initialSceneId={source.initialSceneId}\n        className={className}\n        height={height}\n        width={width}\n        fullscreenButton={fullscreenButton}\n        resetButton={resetButton}\n        showSceneSelector={showSceneSelector}\n      />\n    );\n  } else if (source.type === 'google-maps') {\n    return (\n      <GoogleMapsView\n        mapUrl={source.url}\n        className={className}\n        height={height}\n        width={width}\n        fullscreenButton={fullscreenButton}\n        resetButton={resetButton}\n        title={source.title}\n        description={source.description}\n      />\n    );\n  }\n\n  // Trường hợp không có nguồn dữ liệu hợp lệ\n  return (\n    <div className={`flex items-center justify-center ${className}`} style={{ height, width }}>\n      <p className=\"text-muted-foreground\">Không có dữ liệu 360° hợp lệ</p>\n    </div>\n  );\n};\n\nexport default UniversalView360;\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAgCO,MAAM,mBAAoD,CAAC,EAChE,MAAM,EACN,YAAY,EAAE,EACd,SAAS,OAAO,EAChB,QAAQ,MAAM,EACd,mBAAmB,IAAI,EACvB,cAAc,IAAI,EAClB,oBAAoB,IAAI,EACzB;IACC,2DAA2D;IAC3D,IAAI,OAAO,IAAI,KAAK,YAAY;QAC9B,qBACE,8OAAC,sIAAA,CAAA,UAAO;YACN,QAAQ,OAAO,MAAM;YACrB,gBAAgB,OAAO,cAAc;YACrC,WAAW;YACX,QAAQ;YACR,OAAO;YACP,kBAAkB;YAClB,aAAa;YACb,mBAAmB;;;;;;IAGzB,OAAO,IAAI,OAAO,IAAI,KAAK,eAAe;QACxC,qBACE,8OAAC,qJAAA,CAAA,iBAAc;YACb,QAAQ,OAAO,GAAG;YAClB,WAAW;YACX,QAAQ;YACR,OAAO;YACP,kBAAkB;YAClB,aAAa;YACb,OAAO,OAAO,KAAK;YACnB,aAAa,OAAO,WAAW;;;;;;IAGrC;IAEA,2CAA2C;IAC3C,qBACE,8OAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;QAAE,OAAO;YAAE;YAAQ;QAAM;kBACtF,cAAA,8OAAC;YAAE,WAAU;sBAAwB;;;;;;;;;;;AAG3C;uCAEe", "debugId": null}}, {"offset": {"line": 986, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/components/ui/radix-ui/separator.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as SeparatorPrimitive from '@radix-ui/react-separator';\n\nimport { cn } from '@/lib/utils';\n\nconst Separator = React.forwardRef<\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\n>(\n  (\n    { className, orientation = 'horizontal', decorative = true, ...props },\n    ref\n  ) => (\n    <SeparatorPrimitive.Root\n      ref={ref}\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        'shrink-0 bg-border',\n        orientation === 'horizontal' ? 'h-px w-full' : 'h-full w-px',\n        className\n      )}\n      {...props}\n    />\n  )\n);\nSeparator.displayName = SeparatorPrimitive.Root.displayName;\n\nexport { Separator };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,8OAAC,qKAAA,CAAA,OAAuB;QACtB,KAAK;QACL,YAAY;QACZ,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,gBAAgB,eAC/C;QAED,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG,qKAAA,CAAA,OAAuB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1017, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/features/view360/interactive-map-360.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { GoogleMapsView } from './google-maps-view';\nimport { Separator } from '@/components/ui/radix-ui/separator';\nimport { MapPin, X, Maximize, Minimize } from 'lucide-react';\n\n// Định nghĩa kiểu dữ liệu cho điểm đánh dấu 360°\nexport interface Marker360 {\n  id: string;\n  name: string;\n  description?: string;\n  location: {\n    lat: number;\n    lng: number;\n  };\n  streetViewUrl: string;\n}\n\ninterface InteractiveMap360Props {\n  markers: Marker360[];\n  mapCenter?: { lat: number; lng: number };\n  mapZoom?: number;\n  height?: string;\n  width?: string;\n  className?: string;\n}\n\n/**\n * InteractiveMap360 - Component bản đồ tương tác với các điểm đánh dấu 360°\n * \n * @param markers Danh sách các điểm đánh dấu 360°\n * @param mapCenter Tọa độ trung tâm của bản đồ (mặc định: tọa độ của marker đầu tiên)\n * @param mapZoom Mức zoom của bản đồ (mặc định: 14)\n * @param height Chiều cao của component (mặc định: 500px)\n * @param width Chiều rộng của component (mặc định: 100%)\n * @param className CSS class tùy chỉnh\n */\nexport const InteractiveMap360: React.FC<InteractiveMap360Props> = ({\n  markers,\n  mapCenter,\n  mapZoom = 14,\n  height = '500px',\n  width = '100%',\n  className = '',\n}) => {\n  const [selectedMarker, setSelectedMarker] = useState<Marker360 | null>(null);\n  const [isFullscreen, setIsFullscreen] = useState(false);\n  const [mapLoaded, setMapLoaded] = useState(false);\n  const mapRef = useRef<google.maps.Map | null>(null);\n  const containerRef = useRef<HTMLDivElement>(null);\n  const mapContainerRef = useRef<HTMLDivElement>(null);\n  const markerRefs = useRef<{ [key: string]: google.maps.Marker }>({});\n\n  // Xác định tọa độ trung tâm của bản đồ\n  const center = mapCenter || (markers.length > 0 ? markers[0].location : { lat: 10.2896, lng: 103.9567 });\n\n  // Khởi tạo bản đồ Google Maps\n  useEffect(() => {\n    if (typeof window !== 'undefined' && window.google && window.google.maps && mapContainerRef.current && !mapRef.current) {\n      const map = new google.maps.Map(mapContainerRef.current, {\n        center,\n        zoom: mapZoom,\n        mapTypeId: google.maps.MapTypeId.ROADMAP,\n        mapTypeControl: true,\n        streetViewControl: false,\n        fullscreenControl: false,\n      });\n\n      mapRef.current = map;\n      setMapLoaded(true);\n\n      // Thêm các marker vào bản đồ\n      markers.forEach(marker => {\n        const mapMarker = new google.maps.Marker({\n          position: marker.location,\n          map,\n          title: marker.name,\n          icon: {\n            path: google.maps.SymbolPath.CIRCLE,\n            fillColor: '#6366F1',\n            fillOpacity: 1,\n            strokeColor: '#FFFFFF',\n            strokeWeight: 2,\n            scale: 8,\n          },\n        });\n\n        // Lưu reference đến marker\n        markerRefs.current[marker.id] = mapMarker;\n\n        // Thêm sự kiện click cho marker\n        mapMarker.addListener('click', () => {\n          setSelectedMarker(marker);\n        });\n      });\n    }\n  }, [center, mapZoom, markers]);\n\n  // Xử lý khi nhấn nút fullscreen\n  const toggleFullscreen = () => {\n    if (!containerRef.current) return;\n\n    if (!isFullscreen) {\n      if (containerRef.current.requestFullscreen) {\n        containerRef.current.requestFullscreen();\n      }\n      setIsFullscreen(true);\n    } else {\n      if (document.exitFullscreen) {\n        document.exitFullscreen();\n      }\n      setIsFullscreen(false);\n    }\n  };\n\n  // Theo dõi sự kiện fullscreenchange\n  useEffect(() => {\n    const handleFullscreenChange = () => {\n      setIsFullscreen(!!document.fullscreenElement);\n    };\n\n    document.addEventListener('fullscreenchange', handleFullscreenChange);\n    return () => {\n      document.removeEventListener('fullscreenchange', handleFullscreenChange);\n    };\n  }, []);\n\n  // Xử lý khi chọn một marker\n  const handleMarkerSelect = (marker: Marker360) => {\n    setSelectedMarker(marker);\n    \n    // Di chuyển bản đồ đến vị trí của marker\n    if (mapRef.current) {\n      mapRef.current.panTo(marker.location);\n      mapRef.current.setZoom(18);\n    }\n  };\n\n  // Xử lý khi đóng Street View\n  const handleCloseStreetView = () => {\n    setSelectedMarker(null);\n  };\n\n  return (\n    <Card className={`overflow-hidden border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs ${className}`}>\n      <CardContent className=\"p-0\">\n        <div ref={containerRef} className=\"relative\" style={{ height, width }}>\n          {/* Hiển thị bản đồ */}\n          <div \n            ref={mapContainerRef} \n            className=\"w-full h-full\"\n            style={{ \n              display: selectedMarker ? 'none' : 'block',\n              height: '100%',\n              width: '100%'\n            }}\n          ></div>\n\n          {/* Hiển thị Street View khi chọn marker */}\n          {selectedMarker && (\n            <div className=\"absolute inset-0 z-10\">\n              <div className=\"relative h-full\">\n                <GoogleMapsView\n                  mapUrl={selectedMarker.streetViewUrl}\n                  height=\"100%\"\n                  width=\"100%\"\n                  fullscreenButton={false}\n                  resetButton={true}\n                  title={selectedMarker.name}\n                  description={selectedMarker.description}\n                />\n                \n                <Button\n                  variant=\"outline\"\n                  size=\"icon\"\n                  className=\"absolute top-4 right-4 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm z-20\"\n                  onClick={handleCloseStreetView}\n                  title=\"Đóng Street View\"\n                >\n                  <X className=\"h-4 w-4\" />\n                </Button>\n              </div>\n            </div>\n          )}\n\n          {/* Danh sách các điểm đánh dấu */}\n          <div className=\"absolute top-4 left-4 z-10 max-w-xs\">\n            <Card className=\"bg-white/90 dark:bg-gray-950/90 backdrop-blur-sm border-purple-100 dark:border-purple-900\">\n              <CardContent className=\"p-4\">\n                <h3 className=\"font-medium text-sm mb-2\">Các điểm 360°</h3>\n                <Separator className=\"mb-2\" />\n                <div className=\"space-y-2 max-h-60 overflow-y-auto pr-2\">\n                  {markers.map(marker => (\n                    <Button\n                      key={marker.id}\n                      variant={selectedMarker?.id === marker.id ? \"default\" : \"outline\"}\n                      size=\"sm\"\n                      className=\"w-full justify-start\"\n                      onClick={() => handleMarkerSelect(marker)}\n                    >\n                      <MapPin className=\"h-3 w-3 mr-2\" />\n                      <span className=\"truncate\">{marker.name}</span>\n                    </Button>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* Nút toàn màn hình */}\n          <Button\n            variant=\"outline\"\n            size=\"icon\"\n            className=\"absolute bottom-4 right-4 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm z-10\"\n            onClick={toggleFullscreen}\n            title={isFullscreen ? \"Thoát toàn màn hình\" : \"Toàn màn hình\"}\n          >\n            {isFullscreen ? (\n              <Minimize className=\"h-4 w-4\" />\n            ) : (\n              <Maximize className=\"h-4 w-4\" />\n            )}\n          </Button>\n        </div>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport default InteractiveMap360;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAPA;;;;;;;;AAwCO,MAAM,oBAAsD,CAAC,EAClE,OAAO,EACP,SAAS,EACT,UAAU,EAAE,EACZ,SAAS,OAAO,EAChB,QAAQ,MAAM,EACd,YAAY,EAAE,EACf;IACC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IACvE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAA0B;IAC9C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC/C,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyC,CAAC;IAElE,uCAAuC;IACvC,MAAM,SAAS,aAAa,CAAC,QAAQ,MAAM,GAAG,IAAI,OAAO,CAAC,EAAE,CAAC,QAAQ,GAAG;QAAE,KAAK;QAAS,KAAK;IAAS,CAAC;IAEvG,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,uCAAwH;;QAqCxH;IACF,GAAG;QAAC;QAAQ;QAAS;KAAQ;IAE7B,gCAAgC;IAChC,MAAM,mBAAmB;QACvB,IAAI,CAAC,aAAa,OAAO,EAAE;QAE3B,IAAI,CAAC,cAAc;YACjB,IAAI,aAAa,OAAO,CAAC,iBAAiB,EAAE;gBAC1C,aAAa,OAAO,CAAC,iBAAiB;YACxC;YACA,gBAAgB;QAClB,OAAO;YACL,IAAI,SAAS,cAAc,EAAE;gBAC3B,SAAS,cAAc;YACzB;YACA,gBAAgB;QAClB;IACF;IAEA,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,yBAAyB;YAC7B,gBAAgB,CAAC,CAAC,SAAS,iBAAiB;QAC9C;QAEA,SAAS,gBAAgB,CAAC,oBAAoB;QAC9C,OAAO;YACL,SAAS,mBAAmB,CAAC,oBAAoB;QACnD;IACF,GAAG,EAAE;IAEL,4BAA4B;IAC5B,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;QAElB,yCAAyC;QACzC,IAAI,OAAO,OAAO,EAAE;YAClB,OAAO,OAAO,CAAC,KAAK,CAAC,OAAO,QAAQ;YACpC,OAAO,OAAO,CAAC,OAAO,CAAC;QACzB;IACF;IAEA,6BAA6B;IAC7B,MAAM,wBAAwB;QAC5B,kBAAkB;IACpB;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;QAAC,WAAW,CAAC,0GAA0G,EAAE,WAAW;kBACvI,cAAA,8OAAC,gIAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,8OAAC;gBAAI,KAAK;gBAAc,WAAU;gBAAW,OAAO;oBAAE;oBAAQ;gBAAM;;kCAElE,8OAAC;wBACC,KAAK;wBACL,WAAU;wBACV,OAAO;4BACL,SAAS,iBAAiB,SAAS;4BACnC,QAAQ;4BACR,OAAO;wBACT;;;;;;oBAID,gCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,qJAAA,CAAA,iBAAc;oCACb,QAAQ,eAAe,aAAa;oCACpC,QAAO;oCACP,OAAM;oCACN,kBAAkB;oCAClB,aAAa;oCACb,OAAO,eAAe,IAAI;oCAC1B,aAAa,eAAe,WAAW;;;;;;8CAGzC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;oCACT,OAAM;8CAEN,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAOrB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,8OAAC,oJAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;kDACrB,8OAAC;wCAAI,WAAU;kDACZ,QAAQ,GAAG,CAAC,CAAA,uBACX,8OAAC,kIAAA,CAAA,SAAM;gDAEL,SAAS,gBAAgB,OAAO,OAAO,EAAE,GAAG,YAAY;gDACxD,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,mBAAmB;;kEAElC,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC;wDAAK,WAAU;kEAAY,OAAO,IAAI;;;;;;;+CAPlC,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;kCAgB1B,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;wBACT,OAAO,eAAe,wBAAwB;kCAE7C,6BACC,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;iDAEpB,8OAAC,0MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlC;uCAEe", "debugId": null}}, {"offset": {"line": 1292, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/features/view360/index.ts"], "sourcesContent": ["export * from './view360';\nexport * from './google-maps-view';\nexport * from './universal-view360';\nexport * from './interactive-map-360';\nexport * from './location-search';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1352, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/src/app/%28social-travel-trip%29/explore/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { PageHeader } from '@/components/ui/page-header';\nimport { Card, CardContent } from '@/components/ui/card';\nimport { Badge } from '@/components/ui/radix-ui/badge';\nimport { But<PERSON> } from '@/components/ui/button';\nimport { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/radix-ui/tabs';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/radix-ui/avatar';\nimport { Heart, MapPin, Users, Calendar, Star, Share2, Bookmark, Camera, View, Map, Search } from 'lucide-react';\n// import { Map as MapGL, Marker } from 'react-map-gl';\n// import 'mapbox-gl/dist/mapbox-gl.css';\nimport { useParams } from 'next/navigation';\nimport { GoogleMapsView, InteractiveMap360, Marker360, LocationSearch, LocationSearchResult } from '@/features/view360';\nimport { Separator } from '@/components/ui/radix-ui/separator';\n\ntype Review = {\n  id: string;\n  user: {\n    name: string;\n    avatar: string;\n  };\n  rating: number;\n  content: string;\n  date: string;\n  images?: string[];\n};\n\nexport default function DestinationPage() {\n  const params = useParams();\n  const id = params.id as string;\n\n  // State cho Google Maps API\n  const [isGoogleMapsLoaded, setIsGoogleMapsLoaded] = useState(false);\n  const [selectedLocation, setSelectedLocation] = useState<LocationSearchResult | null>(null);\n  const [customMapUrl, setCustomMapUrl] = useState<string | null>(null);\n\n  // Kiểm tra xem Google Maps API đã được load chưa\n  useEffect(() => {\n    const checkGoogleMapsLoaded = () => {\n      if (typeof window !== 'undefined' && window.google && window.google.maps) {\n        setIsGoogleMapsLoaded(true);\n      } else {\n        // Nếu chưa load, thêm script Google Maps API\n        const script = document.createElement('script');\n        script.src = `https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&libraries=places`;\n        script.async = true;\n        script.defer = true;\n        script.onload = () => setIsGoogleMapsLoaded(true);\n        document.head.appendChild(script);\n      }\n    };\n\n    checkGoogleMapsLoaded();\n  }, []);\n\n  // Xử lý khi người dùng chọn một địa điểm từ kết quả tìm kiếm\n  const handleSelectLocation = (location: LocationSearchResult) => {\n    setSelectedLocation(location);\n\n    // Tạo URL embed cho Google Maps Street View\n    const embedUrl = `https://www.google.com/maps/embed?pb=!4v!1m3!1m2!1s!2s${location.location.lat},${location.location.lng}!5e0!3m2!1svi!2s!4v1`;\n\n    setCustomMapUrl(embedUrl);\n  };\n\n  // Dữ liệu cho các địa điểm khác nhau\n  const destinationsData = {\n    '1': {\n      id: '1',\n      name: 'Phú Quốc',\n      description: `Phú Quốc - hòn đảo thiên đường với bãi biển cát trắng mịn, nước biển trong xanh và những khu resort sang trọng. Đây là điểm đến lý tưởng cho những ai yêu thích biển, muốn tận hưởng kỳ nghỉ thư giãn hoặc khám phá văn hóa địa phương.\n\nNhững điểm tham quan nổi bật:\n- Bãi Sao với cát trắng mịn\n- Hòn Thơm với cáp treo vượt biển\n- Suối Tranh với thác nước trong lành\n- Làng chài Hàm Ninh\n- Vườn tiêu Phú Quốc\n- Nhà thùng nước mắm\n\nThời điểm lý tưởng để đến Phú Quốc là từ tháng 11 đến tháng 4 năm sau, khi thời tiết khô ráo và nắng đẹp.`,\n      location: 'Kiên Giang',\n      coordinates: [103.9567, 10.2896],\n      googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747330345274!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQ09oN2JLblFF!2m2!1d10.05725757562915!2d104.0363948436442!3f252.2606279243012!4f-33.282491262245465!5f0.4000000000000002',\n      images: [\n        'https://images.pexels.com/photos/338504/pexels-photo-338504.jpeg?auto=compress&cs=tinysrgb&w=1200',\n        'https://images.pexels.com/photos/1174732/pexels-photo-1174732.jpeg?auto=compress&cs=tinysrgb&w=1200',\n        'https://images.pexels.com/photos/1450353/pexels-photo-1450353.jpeg?auto=compress&cs=tinysrgb&w=1200',\n        'https://images.pexels.com/photos/1591373/pexels-photo-1591373.jpeg?auto=compress&cs=tinysrgb&w=1200'\n      ],\n      panoramaScenes: [\n        {\n          id: 'beach',\n          name: 'Bãi Sao',\n          image: 'https://photo-sphere-viewer-data.netlify.app/assets/sphere.jpg',\n          description: 'Bãi biển cát trắng mịn nhất Phú Quốc',\n          position: { lat: 10.2896, lng: 103.9567 },\n          hotspots: [\n            {\n              id: 'resort',\n              name: 'Vinpearl Resort',\n              position: { yaw: '45deg', pitch: '0deg' },\n              tooltip: 'Đi đến Vinpearl Resort'\n            },\n            {\n              id: 'cable-car',\n              name: 'Cáp treo Hòn Thơm',\n              position: { yaw: '120deg', pitch: '10deg' },\n              tooltip: 'Đi đến Cáp treo Hòn Thơm'\n            }\n          ]\n        },\n        {\n          id: 'resort',\n          name: 'Vinpearl Resort',\n          image: 'https://pannellum.org/images/alma.jpg',\n          description: 'Khu nghỉ dưỡng sang trọng',\n          position: { lat: 10.3112, lng: 103.8405 },\n          hotspots: [\n            {\n              id: 'beach',\n              name: 'Bãi Sao',\n              position: { yaw: '225deg', pitch: '0deg' },\n              tooltip: 'Quay lại Bãi Sao'\n            },\n            {\n              id: 'fishing-village',\n              name: 'Làng chài Hàm Ninh',\n              position: { yaw: '90deg', pitch: '5deg' },\n              tooltip: 'Đi đến Làng chài Hàm Ninh'\n            }\n          ]\n        },\n        {\n          id: 'cable-car',\n          name: 'Cáp treo Hòn Thơm',\n          image: 'https://photo-sphere-viewer-data.netlify.app/assets/sphere-small.jpg',\n          description: 'Cáp treo vượt biển dài nhất thế giới',\n          position: { lat: 10.1689, lng: 103.8921 },\n          hotspots: [\n            {\n              id: 'beach',\n              name: 'Bãi Sao',\n              position: { yaw: '300deg', pitch: '-10deg' },\n              tooltip: 'Quay lại Bãi Sao'\n            },\n            {\n              id: 'fishing-village',\n              name: 'Làng chài Hàm Ninh',\n              position: { yaw: '180deg', pitch: '0deg' },\n              tooltip: 'Đi đến Làng chài Hàm Ninh'\n            }\n          ]\n        },\n        {\n          id: 'fishing-village',\n          name: 'Làng chài Hàm Ninh',\n          image: 'https://pannellum.org/images/cerro-toco-0.jpg',\n          description: 'Làng chài cổ với hải sản tươi ngon',\n          position: { lat: 10.3456, lng: 103.8765 },\n          hotspots: [\n            {\n              id: 'resort',\n              name: 'Vinpearl Resort',\n              position: { yaw: '270deg', pitch: '5deg' },\n              tooltip: 'Đi đến Vinpearl Resort'\n            },\n            {\n              id: 'cable-car',\n              name: 'Cáp treo Hòn Thơm',\n              position: { yaw: '0deg', pitch: '0deg' },\n              tooltip: 'Đi đến Cáp treo Hòn Thơm'\n            }\n          ]\n        }\n      ],\n      tags: ['Biển', 'Đảo', 'Nghỉ dưỡng', 'Ẩm thực', 'Hoàng hôn'],\n      rating: 4.8,\n      reviews: 1245,\n      visitors: 8754,\n      weather: {\n        current: 'Nắng nhẹ',\n        temperature: '28°C',\n        humidity: '75%'\n      },\n      bestTimeToVisit: 'Tháng 11 - Tháng 4',\n      activities: [\n        'Tắm biển',\n        'Lặn ngắm san hô',\n        'Câu cá',\n        'Thăm làng chài',\n        'Khám phá đảo hoang',\n        'Thưởng thức hải sản'\n      ]\n    },\n    '2': {\n      id: '2',\n      name: 'Đà Lạt',\n      description: `Đà Lạt - thành phố ngàn hoa với khí hậu mát mẻ quanh năm, cảnh quan thiên nhiên tuyệt đẹp và kiến trúc độc đáo. Đây là điểm đến lý tưởng cho những ai yêu thích thiên nhiên, muốn tận hưởng không khí trong lành và khám phá văn hóa Tây Nguyên.\n\nNhững điểm tham quan nổi bật:\n- Hồ Xuân Hương\n- Thung lũng Tình Yêu\n- Đồi Robin\n- Ga Đà Lạt\n- Vườn hoa thành phố\n- Làng Cù Lần\n\nThời điểm lý tưởng để đến Đà Lạt là từ tháng 12 đến tháng 3 năm sau, khi thời tiết khô ráo và mát mẻ.`,\n      location: 'Lâm Đồng',\n      coordinates: [108.4583, 11.9404],\n      googleMapsUrl: 'https://maps.app.goo.gl/Yx9Qd4Yx9Qd4Yx9Qd4',\n      images: [\n        'https://images.pexels.com/photos/5191371/pexels-photo-5191371.jpeg?auto=compress&cs=tinysrgb&w=1200',\n        'https://images.pexels.com/photos/5191362/pexels-photo-5191362.jpeg?auto=compress&cs=tinysrgb&w=1200',\n        'https://images.pexels.com/photos/5191367/pexels-photo-5191367.jpeg?auto=compress&cs=tinysrgb&w=1200',\n        'https://images.pexels.com/photos/5191369/pexels-photo-5191369.jpeg?auto=compress&cs=tinysrgb&w=1200'\n      ],\n      panoramaScenes: [\n        {\n          id: 'lake',\n          name: 'Hồ Xuân Hương',\n          image: 'https://pannellum.org/images/cerro-toco-0.jpg',\n          description: 'Hồ nước ngọt nhân tạo trung tâm Đà Lạt',\n          position: { lat: 11.9404, lng: 108.4583 },\n          hotspots: [\n            {\n              id: 'flower-garden',\n              name: 'Vườn hoa thành phố',\n              position: { yaw: '45deg', pitch: '0deg' },\n              tooltip: 'Đi đến Vườn hoa thành phố'\n            },\n            {\n              id: 'valley',\n              name: 'Thung lũng Tình Yêu',\n              position: { yaw: '120deg', pitch: '10deg' },\n              tooltip: 'Đi đến Thung lũng Tình Yêu'\n            }\n          ]\n        },\n        {\n          id: 'flower-garden',\n          name: 'Vườn hoa thành phố',\n          image: 'https://photo-sphere-viewer-data.netlify.app/assets/sphere-small.jpg',\n          description: 'Vườn hoa đẹp nhất Đà Lạt',\n          position: { lat: 11.9456, lng: 108.4623 },\n          hotspots: [\n            {\n              id: 'lake',\n              name: 'Hồ Xuân Hương',\n              position: { yaw: '225deg', pitch: '0deg' },\n              tooltip: 'Quay lại Hồ Xuân Hương'\n            },\n            {\n              id: 'station',\n              name: 'Ga Đà Lạt',\n              position: { yaw: '90deg', pitch: '5deg' },\n              tooltip: 'Đi đến Ga Đà Lạt'\n            }\n          ]\n        },\n        {\n          id: 'valley',\n          name: 'Thung lũng Tình Yêu',\n          image: 'https://pannellum.org/images/alma.jpg',\n          description: 'Thung lũng lãng mạn của Đà Lạt',\n          position: { lat: 11.9234, lng: 108.4456 },\n          hotspots: [\n            {\n              id: 'lake',\n              name: 'Hồ Xuân Hương',\n              position: { yaw: '300deg', pitch: '-10deg' },\n              tooltip: 'Quay lại Hồ Xuân Hương'\n            },\n            {\n              id: 'station',\n              name: 'Ga Đà Lạt',\n              position: { yaw: '180deg', pitch: '0deg' },\n              tooltip: 'Đi đến Ga Đà Lạt'\n            }\n          ]\n        },\n        {\n          id: 'station',\n          name: 'Ga Đà Lạt',\n          image: 'https://photo-sphere-viewer-data.netlify.app/assets/sphere.jpg',\n          description: 'Nhà ga cổ kính nhất Việt Nam',\n          position: { lat: 11.9432, lng: 108.4587 },\n          hotspots: [\n            {\n              id: 'flower-garden',\n              name: 'Vườn hoa thành phố',\n              position: { yaw: '270deg', pitch: '5deg' },\n              tooltip: 'Đi đến Vườn hoa thành phố'\n            },\n            {\n              id: 'valley',\n              name: 'Thung lũng Tình Yêu',\n              position: { yaw: '0deg', pitch: '0deg' },\n              tooltip: 'Đi đến Thung lũng Tình Yêu'\n            }\n          ]\n        }\n      ],\n      tags: ['Núi', 'Hồ', 'Mát mẻ', 'Hoa', 'Cà phê'],\n      rating: 4.7,\n      reviews: 1876,\n      visitors: 9543,\n      weather: {\n        current: 'Mát mẻ',\n        temperature: '22°C',\n        humidity: '85%'\n      },\n      bestTimeToVisit: 'Tháng 12 - Tháng 3',\n      activities: [\n        'Tham quan vườn hoa',\n        'Chèo thuyền trên hồ',\n        'Cắm trại',\n        'Thưởng thức cà phê',\n        'Khám phá thác nước',\n        'Đi xe ngựa'\n      ]\n    },\n    '3': {\n      id: '3',\n      name: 'Hạ Long',\n      description: `Vịnh Hạ Long - kỳ quan thiên nhiên thế giới với hàng nghìn hòn đảo đá vôi và hang động kỳ thú. Đây là điểm đến nổi tiếng nhất của Việt Nam, thu hút hàng triệu du khách trong và ngoài nước mỗi năm.\n\nNhững điểm tham quan nổi bật:\n- Hang Sửng Sốt\n- Đảo Ti Tốp\n- Hang Đầu Gỗ\n- Làng chài Cửa Vạn\n- Đảo Tuần Châu\n- Vịnh Bái Tử Long\n\nThời điểm lý tưởng để đến Hạ Long là từ tháng 10 đến tháng 4 năm sau, khi thời tiết mát mẻ và ít mưa.`,\n      location: 'Quảng Ninh',\n      coordinates: [107.0448, 20.9101],\n      googleMapsUrl: 'https://maps.app.goo.gl/Yx9Qd4Yx9Qd4Yx9Qd4',\n      images: [\n        'https://images.pexels.com/photos/2583852/pexels-photo-2583852.jpeg?auto=compress&cs=tinysrgb&w=1200',\n        'https://images.pexels.com/photos/4350631/pexels-photo-4350631.jpeg?auto=compress&cs=tinysrgb&w=1200',\n        'https://images.pexels.com/photos/4871012/pexels-photo-4871012.jpeg?auto=compress&cs=tinysrgb&w=1200',\n        'https://images.pexels.com/photos/4350626/pexels-photo-4350626.jpeg?auto=compress&cs=tinysrgb&w=1200'\n      ],\n      panoramaScenes: [\n        {\n          id: 'bay-view',\n          name: 'Toàn cảnh Vịnh',\n          image: 'https://photo-sphere-viewer-data.netlify.app/assets/sphere.jpg',\n          description: 'Ngắm nhìn toàn cảnh Vịnh Hạ Long',\n          position: { lat: 20.9101, lng: 107.0448 },\n          hotspots: [\n            {\n              id: 'titop-island',\n              name: 'Đảo Ti Tốp',\n              position: { yaw: '45deg', pitch: '0deg' },\n              tooltip: 'Đi đến Đảo Ti Tốp'\n            },\n            {\n              id: 'surprise-cave',\n              name: 'Hang Sửng Sốt',\n              position: { yaw: '120deg', pitch: '10deg' },\n              tooltip: 'Đi đến Hang Sửng Sốt'\n            }\n          ]\n        },\n        {\n          id: 'titop-island',\n          name: 'Đảo Ti Tốp',\n          image: 'https://pannellum.org/images/alma.jpg',\n          description: 'Đảo nhỏ với bãi tắm đẹp và view toàn cảnh',\n          position: { lat: 20.8991, lng: 107.0673 },\n          hotspots: [\n            {\n              id: 'bay-view',\n              name: 'Toàn cảnh Vịnh',\n              position: { yaw: '225deg', pitch: '0deg' },\n              tooltip: 'Quay lại ngắm toàn cảnh'\n            },\n            {\n              id: 'fishing-village',\n              name: 'Làng chài Cửa Vạn',\n              position: { yaw: '90deg', pitch: '5deg' },\n              tooltip: 'Đi đến Làng chài Cửa Vạn'\n            }\n          ]\n        },\n        {\n          id: 'surprise-cave',\n          name: 'Hang Sửng Sốt',\n          image: 'https://pannellum.org/images/cerro-toco-0.jpg',\n          description: 'Hang động lớn nhất và đẹp nhất Hạ Long',\n          position: { lat: 20.9012, lng: 107.0541 },\n          hotspots: [\n            {\n              id: 'bay-view',\n              name: 'Toàn cảnh Vịnh',\n              position: { yaw: '300deg', pitch: '-10deg' },\n              tooltip: 'Quay lại ngắm toàn cảnh'\n            },\n            {\n              id: 'fishing-village',\n              name: 'Làng chài Cửa Vạn',\n              position: { yaw: '180deg', pitch: '0deg' },\n              tooltip: 'Đi đến Làng chài Cửa Vạn'\n            }\n          ]\n        },\n        {\n          id: 'fishing-village',\n          name: 'Làng chài Cửa Vạn',\n          image: 'https://photo-sphere-viewer-data.netlify.app/assets/sphere-small.jpg',\n          description: 'Làng chài nổi trên vịnh Hạ Long',\n          position: { lat: 20.8876, lng: 107.0512 },\n          hotspots: [\n            {\n              id: 'titop-island',\n              name: 'Đảo Ti Tốp',\n              position: { yaw: '270deg', pitch: '5deg' },\n              tooltip: 'Đi đến Đảo Ti Tốp'\n            },\n            {\n              id: 'surprise-cave',\n              name: 'Hang Sửng Sốt',\n              position: { yaw: '0deg', pitch: '0deg' },\n              tooltip: 'Đi đến Hang Sửng Sốt'\n            }\n          ]\n        }\n      ],\n      tags: ['Vịnh', 'Di sản', 'Hang động', 'Đảo', 'Du thuyền'],\n      rating: 4.9,\n      reviews: 3245,\n      visitors: 15754,\n      weather: {\n        current: 'Nắng nhẹ',\n        temperature: '25°C',\n        humidity: '80%'\n      },\n      bestTimeToVisit: 'Tháng 10 - Tháng 4',\n      activities: [\n        'Du thuyền',\n        'Chèo thuyền kayak',\n        'Tham quan hang động',\n        'Tắm biển',\n        'Leo núi',\n        'Ngắm hoàng hôn'\n      ]\n    }\n  };\n\n  // Lấy dữ liệu dựa trên ID, nếu không có thì mặc định là Phú Quốc (ID: 1)\n  const [destination] = useState(destinationsData[id] || destinationsData['1']);\n\n  // Tạo danh sách các điểm đánh dấu 360° cho bản đồ tương tác\n  const [markers360] = useState<Marker360[]>([\n    {\n      id: 'bai-sao',\n      name: 'Bãi Sao',\n      description: 'Bãi biển cát trắng nổi tiếng ở Phú Quốc',\n      location: { lat: 10.05725757562915, lng: 104.0363948436442 },\n      streetViewUrl: 'https://www.google.com/maps/embed?pb=!4v1747330345274!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQ09oN2JLblFF!2m2!1d10.05725757562915!2d104.0363948436442!3f252.2606279243012!4f-33.282491262245465!5f0.4000000000000002'\n    },\n    {\n      id: 'hon-thom',\n      name: 'Hòn Thơm',\n      description: 'Hòn đảo xinh đẹp với cáp treo vượt biển',\n      location: { lat: 9.954605838430725, lng: 104.0178143976055 },\n      streetViewUrl: 'https://www.google.com/maps/embed?pb=!4v1747332749752!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQzR3NERDSUE.!2m2!1d9.954605838430725!2d104.0178143976055!3f352.99579097798187!4f-11.542141392533921!5f0.7820865974627469'\n    },\n    {\n      id: 'vinpearl-resort',\n      name: 'Vinpearl Resort',\n      description: 'Khu nghỉ dưỡng sang trọng tại Phú Quốc',\n      location: { lat: 10.33683427532572, lng: 103.8555491298273 },\n      streetViewUrl: 'https://www.google.com/maps/embed?pb=!4v1747332930528!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQzRyZV9zUlE.!2m2!1d10.33683427532572!2d103.8555491298273!3f9.87975441837457!4f-61.96086477266688!5f0.7820865974627469'\n    },\n    {\n      id: 'rach-vem',\n      name: 'Rạch Vẹm',\n      description: 'Làng chài yên bình với cảnh quan tuyệt đẹp',\n      location: { lat: 10.37304277793628, lng: 103.9377705339461 },\n      streetViewUrl: 'https://www.google.com/maps/embed?pb=!4v1747333532742!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQzRqZGo5S0E.!2m2!1d10.37304277793628!2d103.9377705339461!3f216.24777645854576!4f-0.38721998348161435!5f0.7820865974627469'\n    }\n  ]);\n\n  const [reviews] = useState<Review[]>([\n    {\n      id: '1',\n      user: {\n        name: 'Nguyễn Minh',\n        avatar: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?auto=compress&cs=tinysrgb&w=120'\n      },\n      rating: 5,\n      content: 'Phú Quốc thực sự là một thiên đường! Bãi biển tuyệt đẹp, nước trong vắt. Đặc biệt là hoàng hôn ở Sunset Beach Bar thật không thể quên.',\n      date: '2 ngày trước',\n      images: [\n        'https://images.pexels.com/photos/1591373/pexels-photo-1591373.jpeg?auto=compress&cs=tinysrgb&w=600',\n        'https://images.pexels.com/photos/1450353/pexels-photo-1450353.jpeg?auto=compress&cs=tinysrgb&w=600'\n      ]\n    },\n    {\n      id: '2',\n      user: {\n        name: 'Thu Hà',\n        avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=120'\n      },\n      rating: 4,\n      content: 'Chuyến đi rất tuyệt vời. Hải sản tươi ngon, giá cả hợp lý. Tuy nhiên một số khu vực hơi đông du khách.',\n      date: '1 tuần trước'\n    }\n  ]);\n\n  return (\n    <div className=\"container mx-auto\">\n      <PageHeader\n        title={destination.name}\n        description={`Khám phá vẻ đẹp của ${destination.name} - ${destination.location}`}\n      />\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        <div className=\"lg:col-span-2 space-y-6\">\n          <Card className=\"overflow-hidden border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs\">\n            <div className=\"grid grid-cols-2 gap-2 p-2\">\n              {destination.images.map((image, index) => (\n                <div\n                  key={index}\n                  className={`relative rounded-lg overflow-hidden ${\n                    index === 0 ? 'col-span-2 aspect-2/1' : 'aspect-square'\n                  }`}\n                >\n                  {/* eslint-disable-next-line */}\n                  <img\n                    src={image}\n                    alt={`${destination.name} ${index + 1}`}\n                    className=\"w-full h-full object-cover\"\n                  />\n                </div>\n              ))}\n            </div>\n          </Card>\n\n          <Tabs defaultValue=\"overview\">\n            <TabsList>\n              <TabsTrigger value=\"overview\">Tổng quan</TabsTrigger>\n              <TabsTrigger value=\"view360\">View 360°</TabsTrigger>\n              <TabsTrigger value=\"reviews\">Đánh giá</TabsTrigger>\n              <TabsTrigger value=\"map\">Bản đồ</TabsTrigger>\n            </TabsList>\n\n            <TabsContent value=\"overview\" className=\"mt-6\">\n              <Card className=\"border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs\">\n                <CardContent className=\"p-6\">\n                  <div className=\"prose dark:prose-invert max-w-none\">\n                    <p className=\"whitespace-pre-line\">{destination.description}</p>\n\n                    <h3>Hoạt động nổi bật</h3>\n                    <ul>\n                      {destination.activities.map((activity, index) => (\n                        <li key={index}>{activity}</li>\n                      ))}\n                    </ul>\n                  </div>\n                </CardContent>\n              </Card>\n            </TabsContent>\n\n            <TabsContent value=\"view360\" className=\"mt-6\">\n              <div className=\"space-y-6\">\n                <Card className=\"border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs\">\n                  <CardContent className=\"p-6\">\n                    <div className=\"flex items-center space-x-4 mb-4\">\n                      <div className=\"flex-shrink-0 w-12 h-12 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center\">\n                        <Map className=\"h-6 w-6 text-purple-600 dark:text-purple-300\" />\n                      </div>\n                      <div>\n                        <h4 className=\"font-medium\">Khám phá {destination.name} với góc nhìn 360°</h4>\n                        <p className=\"text-sm text-muted-foreground\">\n                          Nhấn vào các điểm đánh dấu trên bản đồ để xem hình ảnh 360° thực tế từ Google Maps.\n                        </p>\n                      </div>\n                    </div>\n\n                    {isGoogleMapsLoaded ? (\n                      <InteractiveMap360\n                        markers={markers360}\n                        mapCenter={{ lat: 10.2896, lng: 103.9567 }}\n                        mapZoom={11}\n                        height=\"600px\"\n                      />\n                    ) : (\n                      <div className=\"h-[600px] flex items-center justify-center bg-gray-100 dark:bg-gray-800 rounded-lg\">\n                        <div className=\"text-center\">\n                          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto\"></div>\n                          <p className=\"mt-4 text-purple-600 dark:text-purple-400\">Đang tải bản đồ...</p>\n                        </div>\n                      </div>\n                    )}\n                  </CardContent>\n                </Card>\n\n                <Card className=\"border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs\">\n                  <CardContent className=\"p-6\">\n                    <div className=\"flex items-center space-x-4 mb-4\">\n                      <div className=\"flex-shrink-0 w-12 h-12 rounded-full bg-purple-100 dark:bg-purple-900 flex items-center justify-center\">\n                        <Search className=\"h-6 w-6 text-purple-600 dark:text-purple-300\" />\n                      </div>\n                      <div>\n                        <h4 className=\"font-medium\">Tìm kiếm địa điểm 360°</h4>\n                        <p className=\"text-sm text-muted-foreground\">\n                          Nhập tên địa điểm bạn muốn xem hình ảnh 360° từ Google Maps Street View.\n                        </p>\n                      </div>\n                    </div>\n\n                    {isGoogleMapsLoaded ? (\n                      <LocationSearch\n                        onSelectLocation={handleSelectLocation}\n                        placeholder=\"Nhập tên địa điểm (ví dụ: Bãi Sao, Phú Quốc)\"\n                        buttonText=\"Tìm kiếm\"\n                      />\n                    ) : (\n                      <div className=\"text-center py-8\">\n                        <p className=\"text-muted-foreground\">Đang tải Google Maps API...</p>\n                      </div>\n                    )}\n\n                    {selectedLocation && customMapUrl && (\n                      <div className=\"mt-6 space-y-4\">\n                        <Separator />\n\n                        <div className=\"flex items-center justify-between mt-4\">\n                          <div>\n                            <h3 className=\"text-lg font-medium\">{selectedLocation.name}</h3>\n                            <p className=\"text-sm text-muted-foreground\">{selectedLocation.address}</p>\n                          </div>\n                          <Button\n                            variant=\"outline\"\n                            size=\"sm\"\n                            onClick={() => {\n                              window.open(`https://www.google.com/maps/@?api=1&map_action=pano&viewpoint=${selectedLocation.location.lat},${selectedLocation.location.lng}`, '_blank');\n                            }}\n                          >\n                            Mở trong Google Maps\n                          </Button>\n                        </div>\n\n                        <GoogleMapsView\n                          mapUrl={customMapUrl}\n                          height=\"500px\"\n                          title={selectedLocation.name}\n                          description={selectedLocation.address}\n                        />\n                      </div>\n                    )}\n                  </CardContent>\n                </Card>\n              </div>\n            </TabsContent>\n\n            <TabsContent value=\"reviews\" className=\"mt-6 space-y-4\">\n              {reviews.map((review) => (\n                <Card key={review.id} className=\"border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs\">\n                  <CardContent className=\"p-6\">\n                    <div className=\"flex items-start justify-between\">\n                      <div className=\"flex items-center space-x-4\">\n                        <Avatar>\n                          <AvatarImage src={review.user.avatar} alt={review.user.name} />\n                          <AvatarFallback>{review.user.name[0]}</AvatarFallback>\n                        </Avatar>\n                        <div>\n                          <div className=\"font-medium\">{review.user.name}</div>\n                          <div className=\"text-sm text-muted-foreground\">{review.date}</div>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center\">\n                        {Array.from({ length: 5 }).map((_, i) => (\n                          <Star\n                            key={i}\n                            className={`h-4 w-4 ${\n                              i < review.rating\n                                ? 'text-yellow-400 fill-yellow-400'\n                                : 'text-gray-300'\n                            }`}\n                          />\n                        ))}\n                      </div>\n                    </div>\n                    <p className=\"mt-4\">{review.content}</p>\n                    {review.images && (\n                      <div className=\"mt-4 grid grid-cols-2 gap-2\">\n                        {review.images.map((image, index) => (\n                          <div key={index} className=\"relative aspect-4/3 rounded-lg overflow-hidden\">\n                            {/* eslint-disable-next-line */}\n                            <img\n                              src={image}\n                              alt={`Review image ${index + 1}`}\n                              className=\"w-full h-full object-cover\"\n                            />\n                          </div>\n                        ))}\n                      </div>\n                    )}\n                  </CardContent>\n                </Card>\n              ))}\n            </TabsContent>\n\n            <TabsContent value=\"map\" className=\"mt-6\">\n              <Card className=\"border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs overflow-hidden\">\n                <div className=\"h-[400px]\">\n                  map view ne\n                  {/* <MapGL\n                    initialViewState={{\n                      longitude: destination.coordinates[0],\n                      latitude: destination.coordinates[1],\n                      zoom: 11\n                    }}\n                    mapStyle=\"mapbox://styles/mapbox/streets-v11\"\n                    mapboxAccessToken={process.env.NEXT_PUBLIC_MAPBOX_TOKEN}\n                  >\n                    <Marker\n                      longitude={destination.coordinates[0]}\n                      latitude={destination.coordinates[1]}\n                      anchor=\"bottom\"\n                    >\n                      <MapPin className=\"h-6 w-6 text-purple-600\" />\n                    </Marker>\n                  </MapGL> */}\n                </div>\n              </Card>\n            </TabsContent>\n          </Tabs>\n        </div>\n\n        <div className=\"space-y-6\">\n          <Card className=\"border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs\">\n            <CardContent className=\"p-6 space-y-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex items-center\">\n                  <Star className=\"h-5 w-5 text-yellow-400 fill-yellow-400 mr-1\" />\n                  <span className=\"font-medium text-lg\">{destination.rating}</span>\n                  <span className=\"text-sm text-muted-foreground ml-1\">\n                    ({destination.reviews} đánh giá)\n                  </span>\n                </div>\n                <div className=\"flex items-center text-sm text-muted-foreground\">\n                  <Users className=\"h-4 w-4 mr-1\" />\n                  <span>{destination.visitors} lượt ghé thăm</span>\n                </div>\n              </div>\n\n              <div className=\"flex flex-wrap gap-2\">\n                {destination.tags.map((tag) => (\n                  <Badge\n                    key={tag}\n                    variant=\"outline\"\n                    className=\"bg-purple-100/50 hover:bg-purple-200/50 text-purple-700 dark:bg-purple-900/30 dark:hover:bg-purple-800/30 dark:text-purple-300 border-purple-200 dark:border-purple-800\"\n                  >\n                    {tag}\n                  </Badge>\n                ))}\n              </div>\n\n              <div className=\"space-y-4 pt-4 border-t border-purple-100 dark:border-purple-900\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"text-sm text-muted-foreground\">Thời tiết hiện tại</div>\n                  <div className=\"font-medium\">{destination.weather.current}</div>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"text-sm text-muted-foreground\">Nhiệt độ</div>\n                  <div className=\"font-medium\">{destination.weather.temperature}</div>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"text-sm text-muted-foreground\">Độ ẩm</div>\n                  <div className=\"font-medium\">{destination.weather.humidity}</div>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"text-sm text-muted-foreground\">Thời điểm đẹp nhất</div>\n                  <div className=\"font-medium\">{destination.bestTimeToVisit}</div>\n                </div>\n              </div>\n\n              <div className=\"flex flex-col space-y-2\">\n                <Button className=\"bg-purple-600 hover:bg-purple-700 text-white\">\n                  <Camera className=\"h-4 w-4 mr-2\" />\n                  Chia sẻ trải nghiệm\n                </Button>\n                <Button variant=\"outline\">\n                  <Share2 className=\"h-4 w-4 mr-2\" />\n                  Chia sẻ địa điểm\n                </Button>\n                <Button variant=\"outline\">\n                  <Bookmark className=\"h-4 w-4 mr-2\" />\n                  Lưu địa điểm\n                </Button>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs\">\n            <CardContent className=\"p-6\">\n              <h3 className=\"font-medium mb-4\">Địa điểm lân cận</h3>\n              <div className=\"space-y-4\">\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"h-16 w-16 rounded-lg overflow-hidden shrink-0\">\n                    {/* eslint-disable-next-line */}\n                    <img\n                      src=\"https://images.pexels.com/photos/1450353/pexels-photo-1450353.jpeg?auto=compress&cs=tinysrgb&w=300\"\n                      alt=\"Hòn Thơm\"\n                      className=\"w-full h-full object-cover\"\n                    />\n                  </div>\n                  <div>\n                    <h4 className=\"font-medium\">Hòn Thơm</h4>\n                    <div className=\"text-sm text-muted-foreground flex items-center\">\n                      <MapPin className=\"h-3 w-3 mr-1\" />\n                      <span>Cách 5km</span>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"h-16 w-16 rounded-lg overflow-hidden shrink-0\">\n                    {/* eslint-disable-next-line */}\n                    <img\n                      src=\"https://images.pexels.com/photos/1591373/pexels-photo-1591373.jpeg?auto=compress&cs=tinysrgb&w=300\"\n                      alt=\"Bãi Sao\"\n                      className=\"w-full h-full object-cover\"\n                    />\n                  </div>\n                  <div>\n                    <h4 className=\"font-medium\">Bãi Sao</h4>\n                    <div className=\"text-sm text-muted-foreground flex items-center\">\n                      <MapPin className=\"h-3 w-3 mr-1\" />\n                      <span>Cách 8km</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA,uDAAuD;AACvD,yCAAyC;AACzC;AACA;AAAA;AAAA;AAAA;AACA;AAdA;;;;;;;;;;;;;AA4Be,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,KAAK,OAAO,EAAE;IAEpB,4BAA4B;IAC5B,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IACtF,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhE,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,wBAAwB;YAC5B,uCAA0E;;YAE1E,OAAO;gBACL,6CAA6C;gBAC7C,MAAM,SAAS,SAAS,aAAa,CAAC;gBACtC,OAAO,GAAG,GAAG,CAAC,yEAAyE,CAAC;gBACxF,OAAO,KAAK,GAAG;gBACf,OAAO,KAAK,GAAG;gBACf,OAAO,MAAM,GAAG,IAAM,sBAAsB;gBAC5C,SAAS,IAAI,CAAC,WAAW,CAAC;YAC5B;QACF;QAEA;IACF,GAAG,EAAE;IAEL,6DAA6D;IAC7D,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;QAEpB,4CAA4C;QAC5C,MAAM,WAAW,CAAC,sDAAsD,EAAE,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,QAAQ,CAAC,GAAG,CAAC,oBAAoB,CAAC;QAE9I,gBAAgB;IAClB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,KAAK;YACH,IAAI;YACJ,MAAM;YACN,aAAa,CAAC;;;;;;;;;;yGAUqF,CAAC;YACpG,UAAU;YACV,aAAa;gBAAC;gBAAU;aAAQ;YAChC,eAAe;YACf,QAAQ;gBACN;gBACA;gBACA;gBACA;aACD;YACD,gBAAgB;gBACd;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,UAAU;wBAAE,KAAK;wBAAS,KAAK;oBAAS;oBACxC,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAS,OAAO;4BAAO;4BACxC,SAAS;wBACX;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAU,OAAO;4BAAQ;4BAC1C,SAAS;wBACX;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,UAAU;wBAAE,KAAK;wBAAS,KAAK;oBAAS;oBACxC,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAU,OAAO;4BAAO;4BACzC,SAAS;wBACX;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAS,OAAO;4BAAO;4BACxC,SAAS;wBACX;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,UAAU;wBAAE,KAAK;wBAAS,KAAK;oBAAS;oBACxC,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAU,OAAO;4BAAS;4BAC3C,SAAS;wBACX;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAU,OAAO;4BAAO;4BACzC,SAAS;wBACX;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,UAAU;wBAAE,KAAK;wBAAS,KAAK;oBAAS;oBACxC,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAU,OAAO;4BAAO;4BACzC,SAAS;wBACX;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAQ,OAAO;4BAAO;4BACvC,SAAS;wBACX;qBACD;gBACH;aACD;YACD,MAAM;gBAAC;gBAAQ;gBAAO;gBAAc;gBAAW;aAAY;YAC3D,QAAQ;YACR,SAAS;YACT,UAAU;YACV,SAAS;gBACP,SAAS;gBACT,aAAa;gBACb,UAAU;YACZ;YACA,iBAAiB;YACjB,YAAY;gBACV;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA,KAAK;YACH,IAAI;YACJ,MAAM;YACN,aAAa,CAAC;;;;;;;;;;qGAUiF,CAAC;YAChG,UAAU;YACV,aAAa;gBAAC;gBAAU;aAAQ;YAChC,eAAe;YACf,QAAQ;gBACN;gBACA;gBACA;gBACA;aACD;YACD,gBAAgB;gBACd;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,UAAU;wBAAE,KAAK;wBAAS,KAAK;oBAAS;oBACxC,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAS,OAAO;4BAAO;4BACxC,SAAS;wBACX;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAU,OAAO;4BAAQ;4BAC1C,SAAS;wBACX;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,UAAU;wBAAE,KAAK;wBAAS,KAAK;oBAAS;oBACxC,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAU,OAAO;4BAAO;4BACzC,SAAS;wBACX;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAS,OAAO;4BAAO;4BACxC,SAAS;wBACX;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,UAAU;wBAAE,KAAK;wBAAS,KAAK;oBAAS;oBACxC,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAU,OAAO;4BAAS;4BAC3C,SAAS;wBACX;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAU,OAAO;4BAAO;4BACzC,SAAS;wBACX;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,UAAU;wBAAE,KAAK;wBAAS,KAAK;oBAAS;oBACxC,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAU,OAAO;4BAAO;4BACzC,SAAS;wBACX;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAQ,OAAO;4BAAO;4BACvC,SAAS;wBACX;qBACD;gBACH;aACD;YACD,MAAM;gBAAC;gBAAO;gBAAM;gBAAU;gBAAO;aAAS;YAC9C,QAAQ;YACR,SAAS;YACT,UAAU;YACV,SAAS;gBACP,SAAS;gBACT,aAAa;gBACb,UAAU;YACZ;YACA,iBAAiB;YACjB,YAAY;gBACV;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;QACA,KAAK;YACH,IAAI;YACJ,MAAM;YACN,aAAa,CAAC;;;;;;;;;;qGAUiF,CAAC;YAChG,UAAU;YACV,aAAa;gBAAC;gBAAU;aAAQ;YAChC,eAAe;YACf,QAAQ;gBACN;gBACA;gBACA;gBACA;aACD;YACD,gBAAgB;gBACd;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,UAAU;wBAAE,KAAK;wBAAS,KAAK;oBAAS;oBACxC,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAS,OAAO;4BAAO;4BACxC,SAAS;wBACX;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAU,OAAO;4BAAQ;4BAC1C,SAAS;wBACX;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,UAAU;wBAAE,KAAK;wBAAS,KAAK;oBAAS;oBACxC,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAU,OAAO;4BAAO;4BACzC,SAAS;wBACX;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAS,OAAO;4BAAO;4BACxC,SAAS;wBACX;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,UAAU;wBAAE,KAAK;wBAAS,KAAK;oBAAS;oBACxC,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAU,OAAO;4BAAS;4BAC3C,SAAS;wBACX;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAU,OAAO;4BAAO;4BACzC,SAAS;wBACX;qBACD;gBACH;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,OAAO;oBACP,aAAa;oBACb,UAAU;wBAAE,KAAK;wBAAS,KAAK;oBAAS;oBACxC,UAAU;wBACR;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAU,OAAO;4BAAO;4BACzC,SAAS;wBACX;wBACA;4BACE,IAAI;4BACJ,MAAM;4BACN,UAAU;gCAAE,KAAK;gCAAQ,OAAO;4BAAO;4BACvC,SAAS;wBACX;qBACD;gBACH;aACD;YACD,MAAM;gBAAC;gBAAQ;gBAAU;gBAAa;gBAAO;aAAY;YACzD,QAAQ;YACR,SAAS;YACT,UAAU;YACV,SAAS;gBACP,SAAS;gBACT,aAAa;gBACb,UAAU;YACZ;YACA,iBAAiB;YACjB,YAAY;gBACV;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;QACH;IACF;IAEA,yEAAyE;IACzE,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,gBAAgB,CAAC,GAAG,IAAI,gBAAgB,CAAC,IAAI;IAE5E,4DAA4D;IAC5D,MAAM,CAAC,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QACzC;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,UAAU;gBAAE,KAAK;gBAAmB,KAAK;YAAkB;YAC3D,eAAe;QACjB;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,UAAU;gBAAE,KAAK;gBAAmB,KAAK;YAAkB;YAC3D,eAAe;QACjB;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,UAAU;gBAAE,KAAK;gBAAmB,KAAK;YAAkB;YAC3D,eAAe;QACjB;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,UAAU;gBAAE,KAAK;gBAAmB,KAAK;YAAkB;YAC3D,eAAe;QACjB;KACD;IAED,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;QACnC;YACE,IAAI;YACJ,MAAM;gBACJ,MAAM;gBACN,QAAQ;YACV;YACA,QAAQ;YACR,SAAS;YACT,MAAM;YACN,QAAQ;gBACN;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;gBACJ,MAAM;gBACN,QAAQ;YACV;YACA,QAAQ;YACR,SAAS;YACT,MAAM;QACR;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0IAAA,CAAA,aAAU;gBACT,OAAO,YAAY,IAAI;gBACvB,aAAa,CAAC,oBAAoB,EAAE,YAAY,IAAI,CAAC,GAAG,EAAE,YAAY,QAAQ,EAAE;;;;;;0BAGlF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC;oCAAI,WAAU;8CACZ,YAAY,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC9B,8OAAC;4CAEC,WAAW,CAAC,oCAAoC,EAC9C,UAAU,IAAI,0BAA0B,iBACxC;sDAGF,cAAA,8OAAC;gDACC,KAAK;gDACL,KAAK,GAAG,YAAY,IAAI,CAAC,CAAC,EAAE,QAAQ,GAAG;gDACvC,WAAU;;;;;;2CATP;;;;;;;;;;;;;;;0CAgBb,8OAAC,+IAAA,CAAA,OAAI;gCAAC,cAAa;;kDACjB,8OAAC,+IAAA,CAAA,WAAQ;;0DACP,8OAAC,+IAAA,CAAA,cAAW;gDAAC,OAAM;0DAAW;;;;;;0DAC9B,8OAAC,+IAAA,CAAA,cAAW;gDAAC,OAAM;0DAAU;;;;;;0DAC7B,8OAAC,+IAAA,CAAA,cAAW;gDAAC,OAAM;0DAAU;;;;;;0DAC7B,8OAAC,+IAAA,CAAA,cAAW;gDAAC,OAAM;0DAAM;;;;;;;;;;;;kDAG3B,8OAAC,+IAAA,CAAA,cAAW;wCAAC,OAAM;wCAAW,WAAU;kDACtC,cAAA,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAE,WAAU;sEAAuB,YAAY,WAAW;;;;;;sEAE3D,8OAAC;sEAAG;;;;;;sEACJ,8OAAC;sEACE,YAAY,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU,sBACrC,8OAAC;8EAAgB;mEAAR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQrB,8OAAC,+IAAA,CAAA,cAAW;wCAAC,OAAM;wCAAU,WAAU;kDACrC,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,gIAAA,CAAA,OAAI;oDAAC,WAAU;8DACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wDAAC,WAAU;;0EACrB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;4EAAC,WAAU;;;;;;;;;;;kFAEjB,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;;oFAAc;oFAAU,YAAY,IAAI;oFAAC;;;;;;;0FACvD,8OAAC;gFAAE,WAAU;0FAAgC;;;;;;;;;;;;;;;;;;4DAMhD,mCACC,8OAAC,wJAAA,CAAA,oBAAiB;gEAChB,SAAS;gEACT,WAAW;oEAAE,KAAK;oEAAS,KAAK;gEAAS;gEACzC,SAAS;gEACT,QAAO;;;;;qFAGT,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;4EAAE,WAAU;sFAA4C;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAOnE,8OAAC,gIAAA,CAAA,OAAI;oDAAC,WAAU;8DACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wDAAC,WAAU;;0EACrB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;;;;;;kFAEpB,8OAAC;;0FACC,8OAAC;gFAAG,WAAU;0FAAc;;;;;;0FAC5B,8OAAC;gFAAE,WAAU;0FAAgC;;;;;;;;;;;;;;;;;;4DAMhD,mCACC,8OAAC,oJAAA,CAAA,iBAAc;gEACb,kBAAkB;gEAClB,aAAY;gEACZ,YAAW;;;;;qFAGb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;4DAIxC,oBAAoB,8BACnB,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,oJAAA,CAAA,YAAS;;;;;kFAEV,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;;kGACC,8OAAC;wFAAG,WAAU;kGAAuB,iBAAiB,IAAI;;;;;;kGAC1D,8OAAC;wFAAE,WAAU;kGAAiC,iBAAiB,OAAO;;;;;;;;;;;;0FAExE,8OAAC,kIAAA,CAAA,SAAM;gFACL,SAAQ;gFACR,MAAK;gFACL,SAAS;oFACP,OAAO,IAAI,CAAC,CAAC,8DAA8D,EAAE,iBAAiB,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE,iBAAiB,QAAQ,CAAC,GAAG,EAAE,EAAE;gFACjJ;0FACD;;;;;;;;;;;;kFAKH,8OAAC,qJAAA,CAAA,iBAAc;wEACb,QAAQ;wEACR,QAAO;wEACP,OAAO,iBAAiB,IAAI;wEAC5B,aAAa,iBAAiB,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDASnD,8OAAC,+IAAA,CAAA,cAAW;wCAAC,OAAM;wCAAU,WAAU;kDACpC,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,gIAAA,CAAA,OAAI;gDAAiB,WAAU;0DAC9B,cAAA,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,iJAAA,CAAA,SAAM;;8FACL,8OAAC,iJAAA,CAAA,cAAW;oFAAC,KAAK,OAAO,IAAI,CAAC,MAAM;oFAAE,KAAK,OAAO,IAAI,CAAC,IAAI;;;;;;8FAC3D,8OAAC,iJAAA,CAAA,iBAAc;8FAAE,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;sFAEtC,8OAAC;;8FACC,8OAAC;oFAAI,WAAU;8FAAe,OAAO,IAAI,CAAC,IAAI;;;;;;8FAC9C,8OAAC;oFAAI,WAAU;8FAAiC,OAAO,IAAI;;;;;;;;;;;;;;;;;;8EAG/D,8OAAC;oEAAI,WAAU;8EACZ,MAAM,IAAI,CAAC;wEAAE,QAAQ;oEAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC,kMAAA,CAAA,OAAI;4EAEH,WAAW,CAAC,QAAQ,EAClB,IAAI,OAAO,MAAM,GACb,oCACA,iBACJ;2EALG;;;;;;;;;;;;;;;;sEAUb,8OAAC;4DAAE,WAAU;sEAAQ,OAAO,OAAO;;;;;;wDAClC,OAAO,MAAM,kBACZ,8OAAC;4DAAI,WAAU;sEACZ,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBACzB,8OAAC;oEAAgB,WAAU;8EAEzB,cAAA,8OAAC;wEACC,KAAK;wEACL,KAAK,CAAC,aAAa,EAAE,QAAQ,GAAG;wEAChC,WAAU;;;;;;mEALJ;;;;;;;;;;;;;;;;+CA9BT,OAAO,EAAE;;;;;;;;;;kDA8CxB,8OAAC,+IAAA,CAAA,cAAW;wCAAC,OAAM;wCAAM,WAAU;kDACjC,cAAA,8OAAC,gIAAA,CAAA,OAAI;4CAAC,WAAU;sDACd,cAAA,8OAAC;gDAAI,WAAU;0DAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAyBnC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;sEAChB,8OAAC;4DAAK,WAAU;sEAAuB,YAAY,MAAM;;;;;;sEACzD,8OAAC;4DAAK,WAAU;;gEAAqC;gEACjD,YAAY,OAAO;gEAAC;;;;;;;;;;;;;8DAG1B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;sEACjB,8OAAC;;gEAAM,YAAY,QAAQ;gEAAC;;;;;;;;;;;;;;;;;;;sDAIhC,8OAAC;4CAAI,WAAU;sDACZ,YAAY,IAAI,CAAC,GAAG,CAAC,CAAC,oBACrB,8OAAC,gJAAA,CAAA,QAAK;oDAEJ,SAAQ;oDACR,WAAU;8DAET;mDAJI;;;;;;;;;;sDASX,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAgC;;;;;;sEAC/C,8OAAC;4DAAI,WAAU;sEAAe,YAAY,OAAO,CAAC,OAAO;;;;;;;;;;;;8DAE3D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAgC;;;;;;sEAC/C,8OAAC;4DAAI,WAAU;sEAAe,YAAY,OAAO,CAAC,WAAW;;;;;;;;;;;;8DAE/D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAgC;;;;;;sEAC/C,8OAAC;4DAAI,WAAU;sEAAe,YAAY,OAAO,CAAC,QAAQ;;;;;;;;;;;;8DAE5D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAAgC;;;;;;sEAC/C,8OAAC;4DAAI,WAAU;sEAAe,YAAY,eAAe;;;;;;;;;;;;;;;;;;sDAI7D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kIAAA,CAAA,SAAM;oDAAC,WAAU;;sEAChB,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGrC,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;;sEACd,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGrC,8OAAC,kIAAA,CAAA,SAAM;oDAAC,SAAQ;;sEACd,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;0CAO7C,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAG,WAAU;sDAAmB;;;;;;sDACjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAEb,cAAA,8OAAC;gEACC,KAAI;gEACJ,KAAI;gEACJ,WAAU;;;;;;;;;;;sEAGd,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAc;;;;;;8EAC5B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;sFAClB,8OAAC;sFAAK;;;;;;;;;;;;;;;;;;;;;;;;8DAIZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAEb,cAAA,8OAAC;gEACC,KAAI;gEACJ,KAAI;gEACJ,WAAU;;;;;;;;;;;sEAGd,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAc;;;;;;8EAC5B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;sFAClB,8OAAC;sFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW5B", "debugId": null}}]}