{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/components/ui/radix-ui/avatar.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as AvatarPrimitive from '@radix-ui/react-avatar';\n\nimport { cn } from '@/lib/utils';\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      'relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full',\n      className\n    )}\n    {...props}\n  />\n));\nAvatar.displayName = AvatarPrimitive.Root.displayName;\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn('aspect-square h-full w-full', className)}\n    {...props}\n  />\n));\nAvatarImage.displayName = AvatarPrimitive.Image.displayName;\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      'flex h-full w-full items-center justify-center rounded-full bg-muted',\n      className\n    )}\n    {...props}\n  />\n));\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName;\n\nexport { Avatar, AvatarImage, AvatarFallback };\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,kKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/components/ui/radix-ui/input.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nInput.displayName = 'Input';\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uXACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/components/ui/radix-ui/badge.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\n\nimport { cn } from '@/lib/utils';\n\nconst badgeVariants = cva(\n  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2',\n  {\n    variants: {\n      variant: {\n        default:\n          'border-transparent bg-primary text-primary-foreground hover:bg-primary/80',\n        secondary:\n          'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',\n        destructive:\n          'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80',\n        outline: 'text-foreground',\n      },\n    },\n    defaultVariants: {\n      variant: 'default',\n    },\n  }\n);\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  );\n}\n\nexport { Badge, badgeVariants };\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,4KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 129, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/trips/GroupChatList.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { TripGroup } from './mock-trip-groups';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/radix-ui/avatar';\nimport { Button } from '@/components/ui/radix-ui/button';\nimport { Input } from '@/components/ui/radix-ui/input';\nimport { ScrollArea } from '@/components/ui/radix-ui/scroll-area';\nimport { Search, ArrowLeft, Users, MapPin } from 'lucide-react';\nimport { Badge } from '@/components/ui/radix-ui/badge';\nimport Link from 'next/link';\n\ntype GroupChatListProps = {\n  groups: TripGroup[];\n  selectedGroupId: string;\n  onSelectGroup: (group: TripGroup) => void;\n};\n\nexport function GroupChatList({ groups, selectedGroupId, onSelectGroup }: GroupChatListProps) {\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const filteredGroups = groups.filter(group =>\n    group.title.toLowerCase().includes(searchQuery.toLowerCase())\n  );\n\n  return (\n    <div className=\"flex flex-col h-full\">\n      {/* Header with back button - Hiển thị đầy đủ trên desktop, ẩn trên tablet */}\n      <div className=\"p-3 border-b border-purple-100 dark:border-purple-900 bg-purple-50/50 dark:bg-purple-900/10 flex items-center justify-between md:hidden lg:flex\">\n        <div className=\"flex items-center gap-2\">\n          <Link href=\"/trips\">\n            <Button variant=\"ghost\" size=\"icon\" className=\"h-8 w-8 rounded-full\">\n              <ArrowLeft className=\"h-4 w-4\" />\n            </Button>\n          </Link>\n          <h2 className=\"font-semibold truncate\">Nhóm của tôi</h2>\n        </div>\n        <Link href=\"/trips\">\n          <Button variant=\"ghost\" size=\"sm\" className=\"text-xs h-8 whitespace-nowrap text-purple-600 hover:text-purple-700 hover:bg-purple-50 dark:text-purple-400 dark:hover:bg-purple-900/20\">\n            Tất cả nhóm\n          </Button>\n        </Link>\n      </div>\n\n      {/* Header thu gọn cho tablet */}\n      <div className=\"hidden md:flex lg:hidden items-center justify-center p-3 border-b border-purple-100 dark:border-purple-900 bg-purple-50/50 dark:bg-purple-900/10\">\n        <h2 className=\"font-semibold text-center\">Nhóm</h2>\n      </div>\n\n      {/* Search bar - Hiển thị đầy đủ trên desktop, ẩn trên tablet */}\n      <div className=\"p-3 md:hidden lg:block\">\n        <div className=\"relative\">\n          <Search className=\"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\" />\n          <Input\n            type=\"search\"\n            placeholder=\"Tìm kiếm nhóm...\"\n            className=\"pl-9 bg-secondary/50 focus-visible:ring-purple-500\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n          />\n        </div>\n      </div>\n\n      {/* Group list - Hiển thị đầy đủ trên desktop */}\n      <ScrollArea className=\"flex-1 hidden md:hidden lg:block\">\n        <div className=\"p-2 space-y-1\">\n          {filteredGroups.map((group) => (\n            <button\n              key={group.id}\n              className={`w-full flex items-center gap-2 p-2 rounded-lg transition-colors overflow-hidden ${\n                group.id === selectedGroupId\n                  ? 'bg-purple-100 dark:bg-purple-900/30'\n                  : 'hover:bg-purple-50 dark:hover:bg-purple-900/10'\n              }`}\n              onClick={() => onSelectGroup(group)}\n            >\n              <Avatar className=\"h-9 w-9 shrink-0 border border-purple-100 dark:border-purple-800 shadow-xs\">\n                <AvatarImage src={group.image} alt={group.title} />\n                <AvatarFallback>{group.title[0]}</AvatarFallback>\n              </Avatar>\n\n              <div className=\"flex-1 text-left overflow-hidden\">\n                <div className=\"font-medium truncate text-sm\">{group.title}</div>\n                <div className=\"text-xs text-muted-foreground truncate flex items-center gap-1 mt-0.5 max-w-full\">\n                  <Users className=\"h-3 w-3 flex-shrink-0\" />\n                  <span className=\"truncate\">{group.members.count} thành viên</span>\n                  <span className=\"mx-1 flex-shrink-0\">•</span>\n                  <MapPin className=\"h-3 w-3 flex-shrink-0\" />\n                  <span className=\"truncate\">{group.location.split(',')[0]}</span>\n                </div>\n              </div>\n\n              {group.hasPlan && (\n                <Badge variant=\"outline\" className=\"bg-green-50 text-green-700 border-green-200 text-[10px] h-5 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800 flex-shrink-0\">\n                  Có KH\n                </Badge>\n              )}\n            </button>\n          ))}\n        </div>\n      </ScrollArea>\n\n      {/* Group list thu gọn cho tablet - Chỉ hiển thị avatar */}\n      <ScrollArea className=\"flex-1 hidden md:block lg:hidden\">\n        <div className=\"p-2 flex flex-col items-center space-y-3\">\n          {filteredGroups.map((group) => (\n            <button\n              key={group.id}\n              className={`flex flex-col items-center p-2 rounded-lg transition-colors ${\n                group.id === selectedGroupId\n                  ? 'bg-purple-100 dark:bg-purple-900/30'\n                  : 'hover:bg-purple-50 dark:hover:bg-purple-900/10'\n              }`}\n              onClick={() => onSelectGroup(group)}\n              title={group.title}\n            >\n              <Avatar className=\"h-10 w-10 shrink-0 border border-purple-100 dark:border-purple-800 shadow-xs\">\n                <AvatarImage src={group.image} alt={group.title} />\n                <AvatarFallback>{group.title[0]}</AvatarFallback>\n              </Avatar>\n\n              {group.hasPlan && (\n                <Badge variant=\"outline\" className=\"mt-1 bg-green-50 text-green-700 border-green-200 text-[8px] h-4 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800 flex-shrink-0\">\n                  KH\n                </Badge>\n              )}\n            </button>\n          ))}\n\n          {filteredGroups.length === 0 && (\n            <div className=\"py-12 text-center text-muted-foreground\">\n              <div className=\"mb-2\">🔍</div>\n              <p>Không tìm thấy nhóm nào</p>\n            </div>\n          )}\n        </div>\n      </ScrollArea>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAVA;;;;;;;;;;AAkBO,SAAS,cAAc,EAAE,MAAM,EAAE,eAAe,EAAE,aAAa,EAAsB;IAC1F,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAA,QACnC,MAAM,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;IAG5D,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,iJAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,WAAU;8CAC5C,cAAA,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;0CAGzB,8OAAC;gCAAG,WAAU;0CAAyB;;;;;;;;;;;;kCAEzC,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,8OAAC,iJAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAK,WAAU;sCAA0I;;;;;;;;;;;;;;;;;0BAO1L,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BAA4B;;;;;;;;;;;0BAI5C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC,gJAAA,CAAA,QAAK;4BACJ,MAAK;4BACL,aAAY;4BACZ,WAAU;4BACV,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;0BAMpD,8OAAC,yJAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;8BACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC;4BAEC,WAAW,CAAC,gFAAgF,EAC1F,MAAM,EAAE,KAAK,kBACT,wCACA,kDACJ;4BACF,SAAS,IAAM,cAAc;;8CAE7B,8OAAC,iJAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,8OAAC,iJAAA,CAAA,cAAW;4CAAC,KAAK,MAAM,KAAK;4CAAE,KAAK,MAAM,KAAK;;;;;;sDAC/C,8OAAC,iJAAA,CAAA,iBAAc;sDAAE,MAAM,KAAK,CAAC,EAAE;;;;;;;;;;;;8CAGjC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAgC,MAAM,KAAK;;;;;;sDAC1D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;oDAAK,WAAU;;wDAAY,MAAM,OAAO,CAAC,KAAK;wDAAC;;;;;;;8DAChD,8OAAC;oDAAK,WAAU;8DAAqB;;;;;;8DACrC,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;oDAAK,WAAU;8DAAY,MAAM,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;;;;;;;gCAI3D,MAAM,OAAO,kBACZ,8OAAC,gJAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAA2I;;;;;;;2BAzB3K,MAAM,EAAE;;;;;;;;;;;;;;;0BAmCrB,8OAAC,yJAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC;gCAEC,WAAW,CAAC,4DAA4D,EACtE,MAAM,EAAE,KAAK,kBACT,wCACA,kDACJ;gCACF,SAAS,IAAM,cAAc;gCAC7B,OAAO,MAAM,KAAK;;kDAElB,8OAAC,iJAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,8OAAC,iJAAA,CAAA,cAAW;gDAAC,KAAK,MAAM,KAAK;gDAAE,KAAK,MAAM,KAAK;;;;;;0DAC/C,8OAAC,iJAAA,CAAA,iBAAc;0DAAE,MAAM,KAAK,CAAC,EAAE;;;;;;;;;;;;oCAGhC,MAAM,OAAO,kBACZ,8OAAC,gJAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;kDAA+I;;;;;;;+BAf/K,MAAM,EAAE;;;;;wBAsBhB,eAAe,MAAM,KAAK,mBACzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAO;;;;;;8CACtB,8OAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB", "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/components/ui/radix-ui/card.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      'rounded-lg border bg-card text-card-foreground shadow-xs',\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n};\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 577, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/trips/pinned-messages.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/radix-ui/card';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/radix-ui/avatar';\nimport { Button } from '@/components/ui/radix-ui/button';\nimport { ChevronDown, ChevronUp, Pin } from 'lucide-react';\nimport { ScrollArea } from '@/components/ui/radix-ui/scroll-area';\nimport { Message } from './mock-chat-data';\n\ntype PinnedMessagesProps = {\n  messages: Message[];\n  onUnpin: (messageId: string) => void;\n  onScrollToMessage: (messageId: string) => void;\n  isTablet?: boolean;\n};\n\nexport function PinnedMessages({ messages, onUnpin, onScrollToMessage, isTablet = false }: PinnedMessagesProps) {\n  const [isExpanded, setIsExpanded] = useState(false);\n  const pinnedMessages = messages.filter(message => message.pinned);\n\n  if (pinnedMessages.length === 0) {\n    return null;\n  }\n\n  return (\n    <Card className={`sticky top-0 z-10 ${isTablet ? 'mb-3' : 'mb-4'} border-purple-100 dark:border-purple-900 bg-purple-50/50 dark:bg-purple-900/10 shadow-xs`}>\n      <CardHeader className={`${isTablet ? 'py-1.5 px-3' : 'py-2 px-4'} flex flex-row items-center justify-between`}>\n        <CardTitle className={`${isTablet ? 'text-xs' : 'text-sm'} font-medium flex items-center`}>\n          <Pin className={`${isTablet ? 'h-3 w-3 mr-1.5' : 'h-4 w-4 mr-2'} text-purple-600`} />\n          Tin nhắn đã ghim ({pinnedMessages.length})\n        </CardTitle>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className={`${isTablet ? 'h-6 w-6' : 'h-8 w-8'} p-0 hover:bg-purple-100 dark:hover:bg-purple-900/20`}\n          onClick={() => setIsExpanded(!isExpanded)}\n        >\n          {isExpanded ? <ChevronUp className={`${isTablet ? 'h-3 w-3' : 'h-4 w-4'}`} /> : <ChevronDown className={`${isTablet ? 'h-3 w-3' : 'h-4 w-4'}`} />}\n        </Button>\n      </CardHeader>\n\n      {isExpanded && (\n        <CardContent className={`${isTablet ? 'py-1.5 px-3' : 'py-2 px-4'}`}>\n          <ScrollArea className={`${isTablet ? 'max-h-32' : 'max-h-40'}`}>\n            <div className={`${isTablet ? 'space-y-1.5' : 'space-y-2'}`}>\n              {pinnedMessages.map((message) => (\n                <div\n                  key={message.id}\n                  className={`flex items-start ${isTablet ? 'gap-1.5 p-1.5' : 'gap-2 p-2'} rounded-md bg-white dark:bg-gray-900 border border-purple-100 dark:border-purple-800 hover:bg-purple-50 dark:hover:bg-purple-900/20 cursor-pointer transition-colors shadow-xs`}\n                  onClick={() => onScrollToMessage(message.id)}\n                >\n                  <Avatar className={`${isTablet ? 'h-6 w-6' : 'h-7 w-7'} border border-purple-100 dark:border-purple-800`}>\n                    <AvatarImage src={message.sender.avatar} alt={message.sender.name} />\n                    <AvatarFallback>{message.sender.name[0]}</AvatarFallback>\n                  </Avatar>\n\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center gap-2\">\n                      <span className=\"text-xs font-medium\">{message.sender.name}</span>\n                      <span className=\"text-xs text-muted-foreground\">{message.timestamp}</span>\n                    </div>\n                    <p className=\"text-xs truncate message-content\">{message.content}</p>\n\n                    {message.attachments && message.attachments.length > 0 && (\n                      <div className=\"flex items-center gap-1 mt-1\">\n                        <span className=\"text-xs text-purple-600 dark:text-purple-400\">\n                          {message.attachments.length} {message.attachments.length === 1 ? 'tệp đính kèm' : 'tệp đính kèm'}\n                        </span>\n                      </div>\n                    )}\n                  </div>\n\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    className={`${isTablet ? 'h-5 w-5' : 'h-6 w-6'} p-0 text-purple-600 hover:text-purple-700 hover:bg-purple-100 dark:text-purple-400 dark:hover:bg-purple-900/20 rounded-full`}\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      onUnpin(message.id);\n                    }}\n                    title=\"Bỏ ghim\"\n                  >\n                    <Pin className={`${isTablet ? 'h-2.5 w-2.5' : 'h-3 w-3'} fill-current`} />\n                  </Button>\n                </div>\n              ))}\n            </div>\n          </ScrollArea>\n        </CardContent>\n      )}\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAPA;;;;;;;;AAiBO,SAAS,eAAe,EAAE,QAAQ,EAAE,OAAO,EAAE,iBAAiB,EAAE,WAAW,KAAK,EAAuB;IAC5G,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,iBAAiB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,MAAM;IAEhE,IAAI,eAAe,MAAM,KAAK,GAAG;QAC/B,OAAO;IACT;IAEA,qBACE,8OAAC,+IAAA,CAAA,OAAI;QAAC,WAAW,CAAC,kBAAkB,EAAE,WAAW,SAAS,OAAO,yFAAyF,CAAC;;0BACzJ,8OAAC,+IAAA,CAAA,aAAU;gBAAC,WAAW,GAAG,WAAW,gBAAgB,YAAY,2CAA2C,CAAC;;kCAC3G,8OAAC,+IAAA,CAAA,YAAS;wBAAC,WAAW,GAAG,WAAW,YAAY,UAAU,8BAA8B,CAAC;;0CACvF,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAW,GAAG,WAAW,mBAAmB,eAAe,gBAAgB,CAAC;;;;;;4BAAI;4BAClE,eAAe,MAAM;4BAAC;;;;;;;kCAE3C,8OAAC,iJAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAW,GAAG,WAAW,YAAY,UAAU,oDAAoD,CAAC;wBACpG,SAAS,IAAM,cAAc,CAAC;kCAE7B,2BAAa,8OAAC,gNAAA,CAAA,YAAS;4BAAC,WAAW,GAAG,WAAW,YAAY,WAAW;;;;;iDAAO,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAW,GAAG,WAAW,YAAY,WAAW;;;;;;;;;;;;;;;;;YAIhJ,4BACC,8OAAC,+IAAA,CAAA,cAAW;gBAAC,WAAW,GAAG,WAAW,gBAAgB,aAAa;0BACjE,cAAA,8OAAC,yJAAA,CAAA,aAAU;oBAAC,WAAW,GAAG,WAAW,aAAa,YAAY;8BAC5D,cAAA,8OAAC;wBAAI,WAAW,GAAG,WAAW,gBAAgB,aAAa;kCACxD,eAAe,GAAG,CAAC,CAAC,wBACnB,8OAAC;gCAEC,WAAW,CAAC,iBAAiB,EAAE,WAAW,kBAAkB,YAAY,+KAA+K,CAAC;gCACxP,SAAS,IAAM,kBAAkB,QAAQ,EAAE;;kDAE3C,8OAAC,iJAAA,CAAA,SAAM;wCAAC,WAAW,GAAG,WAAW,YAAY,UAAU,gDAAgD,CAAC;;0DACtG,8OAAC,iJAAA,CAAA,cAAW;gDAAC,KAAK,QAAQ,MAAM,CAAC,MAAM;gDAAE,KAAK,QAAQ,MAAM,CAAC,IAAI;;;;;;0DACjE,8OAAC,iJAAA,CAAA,iBAAc;0DAAE,QAAQ,MAAM,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;kDAGzC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAuB,QAAQ,MAAM,CAAC,IAAI;;;;;;kEAC1D,8OAAC;wDAAK,WAAU;kEAAiC,QAAQ,SAAS;;;;;;;;;;;;0DAEpE,8OAAC;gDAAE,WAAU;0DAAoC,QAAQ,OAAO;;;;;;4CAE/D,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,MAAM,GAAG,mBACnD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;;wDACb,QAAQ,WAAW,CAAC,MAAM;wDAAC;wDAAE,QAAQ,WAAW,CAAC,MAAM,KAAK,IAAI,iBAAiB;;;;;;;;;;;;;;;;;;kDAM1F,8OAAC,iJAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAW,GAAG,WAAW,YAAY,UAAU,4HAA4H,CAAC;wCAC5K,SAAS,CAAC;4CACR,EAAE,eAAe;4CACjB,QAAQ,QAAQ,EAAE;wCACpB;wCACA,OAAM;kDAEN,cAAA,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAW,GAAG,WAAW,gBAAgB,UAAU,aAAa,CAAC;;;;;;;;;;;;+BAnCnE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;AA6CjC", "debugId": null}}, {"offset": {"line": 807, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/components/ui/radix-ui/popover.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as PopoverPrimitive from '@radix-ui/react-popover';\n\nimport { cn } from '@/lib/utils';\n\nconst Popover = PopoverPrimitive.Root;\n\nconst PopoverTrigger = PopoverPrimitive.Trigger;\n\nconst PopoverContent = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\n>(({ className, align = 'center', sideOffset = 4, ...props }, ref) => (\n  <PopoverPrimitive.Portal>\n    <PopoverPrimitive.Content\n      ref={ref}\n      align={align}\n      sideOffset={sideOffset}\n      className={cn(\n        'z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-hidden data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',\n        className\n      )}\n      {...props}\n    />\n  </PopoverPrimitive.Portal>\n));\nPopoverContent.displayName = PopoverPrimitive.Content.displayName;\n\nexport { Popover, PopoverTrigger, PopoverContent };\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,UAAU,mKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,mKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,KAAK;YACL,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gbACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,mKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 848, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/trips/emoji-picker.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport dynamic from 'next/dynamic';\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/radix-ui/popover';\nimport { Button } from '@/components/ui/radix-ui/button';\nimport { Smile } from 'lucide-react';\n\n// Dynamically import the emoji picker to avoid SSR issues\nconst EmojiPickerComponent = dynamic(\n  () => import('emoji-picker-react').then(mod => mod.default),\n  { ssr: false }\n);\n\ntype EmojiPickerProps = {\n  onEmojiSelect: (emoji: string) => void;\n};\n\nexport function EmojiPicker({ onEmojiSelect }: EmojiPickerProps) {\n  const [open, setOpen] = useState(false);\n  const buttonRef = useRef<HTMLButtonElement>(null);\n  \n  // Close the picker when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (\n        buttonRef.current && \n        !buttonRef.current.contains(event.target as Node) && \n        !(event.target as Element).closest('.EmojiPickerReact')\n      ) {\n        setOpen(false);\n      }\n    };\n    \n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n  \n  const handleEmojiClick = (emojiData: any) => {\n    onEmojiSelect(emojiData.emoji);\n    setOpen(false);\n  };\n  \n  return (\n    <Popover open={open} onOpenChange={setOpen}>\n      <PopoverTrigger asChild>\n        <Button \n          ref={buttonRef}\n          variant=\"ghost\" \n          size=\"icon\"\n          onClick={() => setOpen(!open)}\n        >\n          <Smile className=\"h-5 w-5\" />\n        </Button>\n      </PopoverTrigger>\n      <PopoverContent \n        className=\"w-full p-0 border-none shadow-lg\" \n        sideOffset={5}\n        align=\"end\"\n      >\n        <EmojiPickerComponent\n          onEmojiClick={handleEmojiClick}\n          searchPlaceholder=\"Tìm emoji...\"\n          previewConfig={{ showPreview: false }}\n          width=\"100%\"\n          height=\"350px\"\n        />\n      </PopoverContent>\n    </Popover>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;AANA;;;;;;;AAQA,0DAA0D;AAC1D,MAAM,uBAAuB,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAE/B,KAAK;;AAOF,SAAS,YAAY,EAAE,aAAa,EAAoB;IAC7D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAE5C,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IACE,UAAU,OAAO,IACjB,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,KACxC,CAAC,AAAC,MAAM,MAAM,CAAa,OAAO,CAAC,sBACnC;gBACA,QAAQ;YACV;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB,CAAC;QACxB,cAAc,UAAU,KAAK;QAC7B,QAAQ;IACV;IAEA,qBACE,8OAAC,kJAAA,CAAA,UAAO;QAAC,MAAM;QAAM,cAAc;;0BACjC,8OAAC,kJAAA,CAAA,iBAAc;gBAAC,OAAO;0BACrB,cAAA,8OAAC,iJAAA,CAAA,SAAM;oBACL,KAAK;oBACL,SAAQ;oBACR,MAAK;oBACL,SAAS,IAAM,QAAQ,CAAC;8BAExB,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGrB,8OAAC,kJAAA,CAAA,iBAAc;gBACb,WAAU;gBACV,YAAY;gBACZ,OAAM;0BAEN,cAAA,8OAAC;oBACC,cAAc;oBACd,mBAAkB;oBAClB,eAAe;wBAAE,aAAa;oBAAM;oBACpC,OAAM;oBACN,QAAO;;;;;;;;;;;;;;;;;AAKjB", "debugId": null}}, {"offset": {"line": 956, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/trips/mock-chat-data.ts"], "sourcesContent": ["// Mock data cho các tin nhắn chat\n\nexport type Message = {\n  id: string;\n  content: string;\n  sender: {\n    id: string;\n    name: string;\n    avatar: string;\n  };\n  timestamp: string;\n  pinned?: boolean;\n  replyTo?: {\n    id: string;\n    content: string;\n    sender: {\n      id: string;\n      name: string;\n    };\n  };\n  attachments?: {\n    type: 'image' | 'file';\n    url: string;\n    name: string;\n    size?: number;\n  }[];\n};\n\nexport type Member = {\n  id: string;\n  name: string;\n  avatar: string;\n  role?: 'admin' | 'member';\n};\n\n// Mock messages for each trip group\nexport const MOCK_CHAT_MESSAGES: Record<string, Message[]> = {\n  // Đà Lạt group messages\n  '1': [\n    {\n      id: '1',\n      content: 'Mình đã đặt được khách sạn cho chuyến đi Đà Lạt rồi nhé mọi người!',\n      sender: {\n        id: '1',\n        name: '<PERSON><PERSON><PERSON><PERSON>',\n        avatar: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1',\n      },\n      timestamp: '10:30',\n      pinned: true,\n      attachments: [\n        {\n          type: 'image',\n          url: 'https://images.pexels.com/photos/258154/pexels-photo-258154.jpeg?auto=compress&cs=tinysrgb&w=600',\n          name: 'hotel-dalat.jpg',\n        },\n      ],\n    },\n    {\n      id: '2',\n      content: 'Tuyệt vời! Khách sạn nhìn đẹp quá.',\n      sender: {\n        id: '2',\n        name: 'Trần Hà',\n        avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1',\n      },\n      timestamp: '10:32',\n    },\n    {\n      id: '3',\n      content: 'Mình đã đặt vé xe khách cho cả nhóm rồi đó.',\n      sender: {\n        id: '3',\n        name: 'Lê Hoàng',\n        avatar: 'https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1',\n      },\n      timestamp: '10:45',\n    },\n    {\n      id: '4',\n      content: 'Cảm ơn bạn nhiều nhé!',\n      sender: {\n        id: '2',\n        name: 'Trần Hà',\n        avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1',\n      },\n      timestamp: '10:47',\n      replyTo: {\n        id: '3',\n        content: 'Mình đã đặt vé xe khách cho cả nhóm rồi đó.',\n        sender: {\n          id: '3',\n          name: 'Lê Hoàng',\n        },\n      },\n    },\n  ],\n\n  // Nha Trang group messages\n  '2': [\n    {\n      id: '1',\n      content: 'Chào mọi người, mình đã đặt tour lặn biển cho ngày thứ 2 rồi nhé!',\n      sender: {\n        id: '4',\n        name: 'Ngọc Mai',\n        avatar: 'https://images.pexels.com/photos/733872/pexels-photo-733872.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1',\n      },\n      timestamp: '09:15',\n      pinned: true,\n      attachments: [\n        {\n          type: 'image',\n          url: 'https://images.pexels.com/photos/3046637/pexels-photo-3046637.jpeg?auto=compress&cs=tinysrgb&w=600',\n          name: 'diving-tour.jpg',\n        },\n      ],\n    },\n    {\n      id: '2',\n      content: 'Tuyệt vời! Mình rất háo hức được lặn biển.',\n      sender: {\n        id: '1',\n        name: 'Nguyễn Minh',\n        avatar: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1',\n      },\n      timestamp: '09:20',\n    },\n    {\n      id: '3',\n      content: 'Mình đã đặt nhà hàng hải sản cho tối ngày đầu tiên rồi.',\n      sender: {\n        id: '4',\n        name: 'Ngọc Mai',\n        avatar: 'https://images.pexels.com/photos/733872/pexels-photo-733872.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1',\n      },\n      timestamp: '09:30',\n      attachments: [\n        {\n          type: 'file',\n          url: '#',\n          name: 'menu-nha-trang.pdf',\n          size: 2048,\n        },\n      ],\n    },\n  ],\n\n  // Sapa group messages\n  '3': [\n    {\n      id: '1',\n      content: 'Mọi người chuẩn bị áo ấm đầy đủ nhé, Sapa mùa đông rất lạnh!',\n      sender: {\n        id: '3',\n        name: 'Lê Hoàng',\n        avatar: 'https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1',\n      },\n      timestamp: '14:20',\n      pinned: true,\n    },\n    {\n      id: '2',\n      content: 'Mình đã đặt tour leo Fansipan cho ngày thứ 3 rồi.',\n      sender: {\n        id: '3',\n        name: 'Lê Hoàng',\n        avatar: 'https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1',\n      },\n      timestamp: '14:25',\n      attachments: [\n        {\n          type: 'image',\n          url: 'https://images.pexels.com/photos/2440024/pexels-photo-2440024.jpeg?auto=compress&cs=tinysrgb&w=600',\n          name: 'fansipan.jpg',\n        },\n      ],\n    },\n    {\n      id: '3',\n      content: 'Mình sẽ mang theo máy ảnh để chụp cảnh đẹp.',\n      sender: {\n        id: '4',\n        name: 'Ngọc Mai',\n        avatar: 'https://images.pexels.com/photos/733872/pexels-photo-733872.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1',\n      },\n      timestamp: '14:30',\n    },\n  ],\n\n  // Hội An group messages\n  '4': [\n    {\n      id: '1',\n      content: 'Chào mọi người, chúng ta sẽ khám phá phố cổ Hội An vào ngày đầu tiên nhé!',\n      sender: {\n        id: '5',\n        name: 'Phạm Tuấn',\n        avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1',\n      },\n      timestamp: '15:10',\n      pinned: true,\n      attachments: [\n        {\n          type: 'image',\n          url: 'https://images.pexels.com/photos/5191371/pexels-photo-5191371.jpeg?auto=compress&cs=tinysrgb&w=600',\n          name: 'hoian-old-town.jpg',\n        },\n      ],\n    },\n    {\n      id: '2',\n      content: 'Tuyệt vời! Mình rất thích ẩm thực Hội An.',\n      sender: {\n        id: '1',\n        name: 'Nguyễn Minh',\n        avatar: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1',\n      },\n      timestamp: '15:15',\n    },\n    {\n      id: '3',\n      content: 'Mình đã đặt lịch học nấu ăn tại Hội An vào ngày thứ 2 rồi đó.',\n      sender: {\n        id: '5',\n        name: 'Phạm Tuấn',\n        avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1',\n      },\n      timestamp: '15:30',\n      attachments: [\n        {\n          type: 'file',\n          url: '#',\n          name: 'cooking-class-schedule.pdf',\n          size: 1536,\n        },\n      ],\n    },\n  ],\n\n  // Hạ Long group messages\n  '5': [\n    {\n      id: '1',\n      content: 'Mình đã đặt tour du thuyền 2 ngày 1 đêm trên vịnh Hạ Long rồi nhé!',\n      sender: {\n        id: '2',\n        name: 'Trần Hà',\n        avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1',\n      },\n      timestamp: '11:05',\n      pinned: true,\n      attachments: [\n        {\n          type: 'image',\n          url: 'https://images.pexels.com/photos/2132180/pexels-photo-2132180.jpeg?auto=compress&cs=tinysrgb&w=600',\n          name: 'halong-cruise.jpg',\n        },\n      ],\n    },\n    {\n      id: '2',\n      content: 'Tuyệt vời! Mình sẽ mang theo máy ảnh để chụp cảnh đẹp.',\n      sender: {\n        id: '4',\n        name: 'Ngọc Mai',\n        avatar: 'https://images.pexels.com/photos/733872/pexels-photo-733872.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1',\n      },\n      timestamp: '11:10',\n    },\n    {\n      id: '3',\n      content: 'Mình đã chuẩn bị danh sách các hang động đẹp nhất để khám phá.',\n      sender: {\n        id: '3',\n        name: 'Lê Hoàng',\n        avatar: 'https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1',\n      },\n      timestamp: '11:20',\n      attachments: [\n        {\n          type: 'file',\n          url: '#',\n          name: 'halong-caves-list.pdf',\n          size: 1024,\n        },\n      ],\n    },\n  ],\n};\n"], "names": [], "mappings": "AAAA,kCAAkC;;;;AAoC3B,MAAM,qBAAgD;IAC3D,wBAAwB;IACxB,KAAK;QACH;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,QAAQ;YACV;YACA,WAAW;YACX,QAAQ;YACR,aAAa;gBACX;oBACE,MAAM;oBACN,KAAK;oBACL,MAAM;gBACR;aACD;QACH;QACA;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,QAAQ;YACV;YACA,WAAW;QACb;QACA;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,QAAQ;YACV;YACA,WAAW;QACb;QACA;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,QAAQ;YACV;YACA,WAAW;YACX,SAAS;gBACP,IAAI;gBACJ,SAAS;gBACT,QAAQ;oBACN,IAAI;oBACJ,MAAM;gBACR;YACF;QACF;KACD;IAED,2BAA2B;IAC3B,KAAK;QACH;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,QAAQ;YACV;YACA,WAAW;YACX,QAAQ;YACR,aAAa;gBACX;oBACE,MAAM;oBACN,KAAK;oBACL,MAAM;gBACR;aACD;QACH;QACA;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,QAAQ;YACV;YACA,WAAW;QACb;QACA;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,QAAQ;YACV;YACA,WAAW;YACX,aAAa;gBACX;oBACE,MAAM;oBACN,KAAK;oBACL,MAAM;oBACN,MAAM;gBACR;aACD;QACH;KACD;IAED,sBAAsB;IACtB,KAAK;QACH;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,QAAQ;YACV;YACA,WAAW;YACX,QAAQ;QACV;QACA;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,QAAQ;YACV;YACA,WAAW;YACX,aAAa;gBACX;oBACE,MAAM;oBACN,KAAK;oBACL,MAAM;gBACR;aACD;QACH;QACA;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,QAAQ;YACV;YACA,WAAW;QACb;KACD;IAED,wBAAwB;IACxB,KAAK;QACH;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,QAAQ;YACV;YACA,WAAW;YACX,QAAQ;YACR,aAAa;gBACX;oBACE,MAAM;oBACN,KAAK;oBACL,MAAM;gBACR;aACD;QACH;QACA;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,QAAQ;YACV;YACA,WAAW;QACb;QACA;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,QAAQ;YACV;YACA,WAAW;YACX,aAAa;gBACX;oBACE,MAAM;oBACN,KAAK;oBACL,MAAM;oBACN,MAAM;gBACR;aACD;QACH;KACD;IAED,yBAAyB;IACzB,KAAK;QACH;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,QAAQ;YACV;YACA,WAAW;YACX,QAAQ;YACR,aAAa;gBACX;oBACE,MAAM;oBACN,KAAK;oBACL,MAAM;gBACR;aACD;QACH;QACA;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,QAAQ;YACV;YACA,WAAW;QACb;QACA;YACE,IAAI;YACJ,SAAS;YACT,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,QAAQ;YACV;YACA,WAAW;YACX,aAAa;gBACX;oBACE,MAAM;oBACN,KAAK;oBACL,MAAM;oBACN,MAAM;gBACR;aACD;QACH;KACD;AACH", "debugId": null}}, {"offset": {"line": 1215, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/trips/trip-chat.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\nimport { ScrollArea } from '@/components/ui/radix-ui/scroll-area';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/radix-ui/avatar';\nimport { Input } from '@/components/ui/radix-ui/input';\nimport { Button } from '@/components/ui/radix-ui/button';\nimport { useUser } from '@clerk/nextjs';\nimport {\n  SendHorizontal,\n  Image as ImageIcon,\n  Paperclip,\n  MoreVertical,\n  Pin,\n  Reply,\n  X,\n  MessageSquareQuote,\n  Download,\n  File as FileIcon\n} from 'lucide-react';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/radix-ui/dropdown-menu';\nimport { PinnedMessages } from './pinned-messages';\nimport { EmojiPicker } from './emoji-picker';\nimport { Message, Member, MOCK_CHAT_MESSAGES } from './mock-chat-data';\n\ntype TripChatProps = {\n  tripId: string;\n  members?: Member[];\n  isTablet?: boolean;\n  isVerticalLayout?: boolean;\n};\n\nexport function TripChat({ tripId, isTablet = false, isVerticalLayout = false }: TripChatProps) {\n  const { user } = useUser();\n  // Get messages for the specific trip group\n  const [messages, setMessages] = useState<Message[]>(() => {\n    // If messages exist for this trip, use them, otherwise return an empty array\n    return MOCK_CHAT_MESSAGES[tripId] || [];\n  });\n\n  const [newMessage, setNewMessage] = useState('');\n  const [selectedImages, setSelectedImages] = useState<File[]>([]);\n  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);\n  const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>([]);\n  const [replyingTo, setReplyingTo] = useState<Message | null>(null);\n  const imageInputRef = useRef<HTMLInputElement>(null);\n  const fileInputRef = useRef<HTMLInputElement>(null);\n  const messageEndRef = useRef<HTMLDivElement>(null);\n\n  // Update messages when tripId changes\n  useEffect(() => {\n    setMessages(MOCK_CHAT_MESSAGES[tripId] || []);\n  }, [tripId]);\n\n  // Scroll to bottom when messages change\n  useEffect(() => {\n    if (messageEndRef.current) {\n      messageEndRef.current.scrollIntoView({ behavior: 'smooth' });\n    }\n  }, [messages]);\n\n  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    if (e.target.files) {\n      const filesArray = Array.from(e.target.files);\n      const newImagePreviewUrls = filesArray.map(file => URL.createObjectURL(file));\n\n      setSelectedImages([...selectedImages, ...filesArray]);\n      setImagePreviewUrls([...imagePreviewUrls, ...newImagePreviewUrls]);\n    }\n  };\n\n  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    if (e.target.files) {\n      const filesArray = Array.from(e.target.files);\n      setSelectedFiles([...selectedFiles, ...filesArray]);\n    }\n  };\n\n  const handleRemoveFile = (index: number) => {\n    const newSelectedFiles = [...selectedFiles];\n    newSelectedFiles.splice(index, 1);\n    setSelectedFiles(newSelectedFiles);\n  };\n\n  const handleEmojiSelect = (emoji: string) => {\n    setNewMessage(prev => prev + emoji);\n  };\n\n  const handleRemoveImage = (index: number) => {\n    const newSelectedImages = [...selectedImages];\n    const newImagePreviewUrls = [...imagePreviewUrls];\n\n    // Revoke the object URL to avoid memory leaks\n    URL.revokeObjectURL(newImagePreviewUrls[index]);\n\n    newSelectedImages.splice(index, 1);\n    newImagePreviewUrls.splice(index, 1);\n\n    setSelectedImages(newSelectedImages);\n    setImagePreviewUrls(newImagePreviewUrls);\n  };\n\n  const handlePinMessage = (messageId: string) => {\n    setMessages(messages.map(message =>\n      message.id === messageId\n        ? { ...message, pinned: !message.pinned }\n        : message\n    ));\n  };\n\n  const handleReplyMessage = (message: Message) => {\n    setReplyingTo(message);\n  };\n\n  const cancelReply = () => {\n    setReplyingTo(null);\n  };\n\n  const scrollToMessage = (messageId: string) => {\n    const messageElement = document.getElementById(`message-${messageId}`);\n    if (messageElement) {\n      messageElement.scrollIntoView({ behavior: 'smooth' });\n      // Add a highlight effect\n      messageElement.classList.add('bg-purple-100', 'dark:bg-purple-900/30');\n      setTimeout(() => {\n        messageElement.classList.remove('bg-purple-100', 'dark:bg-purple-900/30');\n      }, 2000);\n    }\n  };\n\n  const handleSendMessage = async () => {\n    if ((!newMessage.trim() && selectedImages.length === 0 && selectedFiles.length === 0)) return;\n\n    // Create attachments from selected images and files\n    let attachments = [];\n\n    // Add images to attachments\n    if (selectedImages.length > 0) {\n      const imageAttachments = await Promise.all(selectedImages.map(async (file, index) => {\n        // In a real app, you would upload the file to a server and get a URL\n        // For now, we'll just use the object URL\n        return {\n          type: 'image' as const,\n          url: imagePreviewUrls[index],\n          name: file.name,\n          size: file.size,\n        };\n      }));\n      attachments.push(...imageAttachments);\n    }\n\n    // Add files to attachments\n    if (selectedFiles.length > 0) {\n      const fileAttachments = selectedFiles.map(file => ({\n        type: 'file' as const,\n        url: URL.createObjectURL(file), // In a real app, this would be a server URL\n        name: file.name,\n        size: file.size,\n      }));\n      attachments.push(...fileAttachments);\n    }\n\n    // Sử dụng ID mặc định '1' nếu user chưa đăng nhập\n    const userId = user?.id || '1';\n    const userName = user?.fullName || 'Đức Anh'; // Tên mặc định nếu không có user\n    const userAvatar = user?.imageUrl || 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1'; // Avatar mặc định\n\n    const message: Message = {\n      id: Date.now().toString(),\n      content: newMessage,\n      sender: {\n        id: userId,\n        name: userName,\n        avatar: userAvatar,\n      },\n      timestamp: new Date().toLocaleTimeString('vi-VN', { hour: '2-digit', minute: '2-digit' }),\n      attachments,\n      ...(replyingTo && {\n        replyTo: {\n          id: replyingTo.id,\n          content: replyingTo.content,\n          sender: {\n            id: replyingTo.sender.id,\n            name: replyingTo.sender.name,\n          },\n        },\n      }),\n    };\n\n    setMessages([...messages, message]);\n    setNewMessage('');\n    setSelectedImages([]);\n    setSelectedFiles([]);\n    setImagePreviewUrls([]);\n    setReplyingTo(null);\n  };\n\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  return (\n    <div className=\"flex flex-col h-full\">\n      <ScrollArea className={`flex-1 ${isTablet ? 'p-2' : 'p-3'} ${isVerticalLayout ? 'pt-1' : ''}`}>\n        <PinnedMessages\n          messages={messages}\n          onUnpin={handlePinMessage}\n          onScrollToMessage={scrollToMessage}\n          isTablet={isTablet}\n        />\n\n        <div className={`${isTablet ? 'space-y-2' : 'space-y-3'}`}>\n          {messages.map((message) => (\n            <div\n              id={`message-${message.id}`}\n              key={message.id}\n              className={`flex ${isTablet ? 'gap-1.5' : 'gap-2'} transition-colors duration-300 ${\n                message.sender.id === (user?.id || '1') ? 'flex-row-reverse' : ''\n              }`}\n            >\n              <Avatar className={`${isTablet ? 'h-6 w-6' : 'h-8 w-8'} shrink-0 border border-white shadow-xs`}>\n                <AvatarImage src={message.sender.avatar} alt={message.sender.name} />\n                <AvatarFallback>{message.sender.name[0]}</AvatarFallback>\n              </Avatar>\n\n              <div className={`flex flex-col ${isTablet ? 'gap-0.5' : 'gap-1'} max-w-[80%] ${\n                message.sender.id === (user?.id || '1') ? 'items-end' : ''\n              }`}>\n                <div className=\"flex items-center gap-1.5\">\n                  <span className=\"text-xs font-medium\">{message.sender.name}</span>\n                  <span className=\"text-xs text-muted-foreground\">{message.timestamp}</span>\n\n                  {message.pinned && (\n                    <Pin className=\"h-3 w-3 text-purple-600\" />\n                  )}\n                </div>\n\n                <div className={`relative rounded-lg ${isTablet ? 'p-2' : 'p-2.5'} group ${\n                  message.sender.id === (user?.id || '1')\n                    ? 'bg-purple-600 text-white shadow-xs'\n                    : 'bg-secondary shadow-xs'\n                }`}>\n                  {message.replyTo && (\n                    <div className={`${isTablet ? 'mb-1.5 p-1.5' : 'mb-2 p-2'} rounded text-xs flex items-start gap-1 ${\n                      message.sender.id === (user?.id || '1')\n                        ? 'bg-purple-700/50'\n                        : 'bg-secondary-foreground/10'\n                    }`}>\n                      <MessageSquareQuote className=\"h-3 w-3 shrink-0 mt-0.5\" />\n                      <div>\n                        <div className=\"font-medium\">{message.replyTo.sender.name}</div>\n                        <div className=\"truncate message-content\">{message.replyTo.content}</div>\n                      </div>\n                    </div>\n                  )}\n\n                  <p className=\"text-sm message-content\">{message.content}</p>\n\n                  {message.attachments && message.attachments.length > 0 && (\n                    <div className={`${isTablet ? 'mt-1.5 space-y-1.5' : 'mt-2 space-y-2'}`}>\n                      {message.attachments.map((attachment, index) => (\n                        attachment.type === 'image' ? (\n                          <div key={index} className=\"rounded-md overflow-hidden border border-white/20\">\n                            {/* eslint-disable-next-line @next/next/no-img-element */}\n                            <img\n                              src={attachment.url}\n                              alt={attachment.name}\n                              className={`${isTablet ? 'max-w-[200px]' : 'max-w-sm'} object-cover`}\n                            />\n                          </div>\n                        ) : (\n                          <div key={index} className={`flex items-center justify-between gap-2 text-sm bg-secondary-foreground/10 ${isTablet ? 'p-1.5' : 'p-2'} rounded`}>\n                            <div className=\"flex items-center gap-2\">\n                              <FileIcon className=\"h-4 w-4\" />\n                              <div className=\"flex flex-col\">\n                                <span className={`truncate ${isTablet ? 'max-w-[120px]' : 'max-w-[150px]'}`}>{attachment.name}</span>\n                                <span className=\"text-xs text-muted-foreground\">\n                                  {attachment.size ? `${Math.round(attachment.size / 1024)} KB` : ''}\n                                </span>\n                              </div>\n                            </div>\n                            <a\n                              href={attachment.url}\n                              download={attachment.name}\n                              target=\"_blank\"\n                              rel=\"noopener noreferrer\"\n                              className=\"p-1 hover:bg-secondary-foreground/20 rounded\"\n                              onClick={(e) => e.stopPropagation()}\n                            >\n                              <Download className=\"h-3 w-3\" />\n                            </a>\n                          </div>\n                        )\n                      ))}\n                    </div>\n                  )}\n\n                  <div className={`absolute ${message.sender.id === (user?.id || '1') ? 'left-0' : 'right-0'} top-1/2 -translate-y-1/2 ${message.sender.id === (user?.id || '1') ? '-translate-x-full' : 'translate-x-full'} opacity-0 group-hover:opacity-100 transition-opacity`}>\n                    <DropdownMenu>\n                      <DropdownMenuTrigger asChild>\n                        <Button variant=\"ghost\" size=\"icon\" className={`${isTablet ? 'h-6 w-6' : 'h-8 w-8'} rounded-full bg-background/80 backdrop-blur-xs shadow-xs`}>\n                          <MoreVertical className={`${isTablet ? 'h-3 w-3' : 'h-4 w-4'}`} />\n                        </Button>\n                      </DropdownMenuTrigger>\n                      <DropdownMenuContent align={message.sender.id === (user?.id || '1') ? \"end\" : \"start\"}>\n                        <DropdownMenuItem onClick={() => handleReplyMessage(message)}>\n                          <Reply className=\"h-4 w-4 mr-2\" />\n                          Phản hồi\n                        </DropdownMenuItem>\n                        <DropdownMenuItem onClick={() => handlePinMessage(message.id)}>\n                          <Pin className=\"h-4 w-4 mr-2\" />\n                          {message.pinned ? 'Bỏ ghim' : 'Ghim tin nhắn'}\n                        </DropdownMenuItem>\n                      </DropdownMenuContent>\n                    </DropdownMenu>\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n          <div ref={messageEndRef} />\n        </div>\n      </ScrollArea>\n\n      <div className={`${isTablet ? 'p-1.5' : 'p-2'} ${isVerticalLayout ? 'border-t-0' : 'border-t'} border-purple-100 dark:border-purple-900 bg-purple-50/30 dark:bg-purple-900/10`}>\n        {/* Image preview area */}\n        {imagePreviewUrls.length > 0 && (\n          <div className={`${isTablet ? 'mb-1' : 'mb-1.5'}`}>\n            <div className=\"text-xs text-muted-foreground mb-1\">Hình ảnh ({imagePreviewUrls.length})</div>\n            <div className=\"flex flex-wrap gap-1.5\">\n              {imagePreviewUrls.map((url, index) => (\n                <div key={index} className={`relative ${isTablet ? 'w-10 h-10' : 'w-12 h-12'} rounded-md overflow-hidden bg-secondary border border-purple-100 dark:border-purple-800`}>\n                  {/* eslint-disable-next-line @next/next/no-img-element */}\n                  <img src={url} alt=\"preview\" className=\"w-full h-full object-cover\" />\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    className=\"absolute top-0 right-0 h-4 w-4 p-0 bg-black/50 rounded-full\"\n                    onClick={() => handleRemoveImage(index)}\n                  >\n                    <X className=\"h-2.5 w-2.5 text-white\" />\n                  </Button>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* File preview area */}\n        {selectedFiles.length > 0 && (\n          <div className={`${isTablet ? 'mb-1' : 'mb-1.5'}`}>\n            <div className=\"text-xs text-muted-foreground mb-1\">Tệp đính kèm ({selectedFiles.length})</div>\n            <div className=\"space-y-1\">\n              {selectedFiles.map((file, index) => (\n                <div key={index} className=\"flex items-center justify-between p-1 rounded bg-secondary/50 border border-purple-100 dark:border-purple-800\">\n                  <div className=\"flex items-center gap-1.5\">\n                    <FileIcon className=\"h-3 w-3 text-purple-500\" />\n                    <div className=\"flex flex-col\">\n                      <span className={`text-xs truncate ${isTablet ? 'max-w-[150px]' : 'max-w-[180px]'}`}>{file.name}</span>\n                      <span className=\"text-[10px] text-muted-foreground\">{Math.round(file.size / 1024)} KB</span>\n                    </div>\n                  </div>\n                  <Button\n                    variant=\"ghost\"\n                    size=\"icon\"\n                    className=\"h-4 w-4 p-0\"\n                    onClick={() => handleRemoveFile(index)}\n                  >\n                    <X className=\"h-2.5 w-2.5\" />\n                  </Button>\n                </div>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Reply preview */}\n        {replyingTo && (\n          <div className={`${isTablet ? 'mb-1 p-1' : 'mb-1.5 p-1'} rounded-md bg-secondary/50 border border-purple-100 dark:border-purple-800 flex items-center justify-between`}>\n            <div className=\"flex items-center gap-1.5\">\n              <MessageSquareQuote className=\"h-3 w-3 text-purple-500\" />\n              <div>\n                <div className=\"text-xs font-medium\">\n                  Đang trả lời {replyingTo.sender.name}\n                </div>\n                <div className={`text-xs text-muted-foreground truncate message-content ${isTablet ? 'max-w-[150px]' : 'max-w-[180px]'}`}>\n                  {replyingTo.content}\n                </div>\n              </div>\n            </div>\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"h-4 w-4 p-0\"\n              onClick={cancelReply}\n            >\n              <X className=\"h-2.5 w-2.5\" />\n            </Button>\n          </div>\n        )}\n\n        <div className={`flex items-center ${isTablet ? 'gap-0.5' : 'gap-1'}`}>\n          <input\n            type=\"file\"\n            accept=\"image/*\"\n            multiple\n            className=\"hidden\"\n            ref={imageInputRef}\n            onChange={handleImageChange}\n          />\n          <input\n            type=\"file\"\n            multiple\n            className=\"hidden\"\n            ref={fileInputRef}\n            onChange={handleFileChange}\n          />\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={() => imageInputRef.current?.click()}\n            title=\"Tải lên hình ảnh\"\n            className={`${isTablet ? 'h-6 w-6' : 'h-7 w-7'} text-purple-600 hover:text-purple-700 hover:bg-purple-100 dark:text-purple-400 dark:hover:bg-purple-900/20`}\n          >\n            <ImageIcon className={`${isTablet ? 'h-3 w-3' : 'h-3.5 w-3.5'}`} />\n          </Button>\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={() => fileInputRef.current?.click()}\n            title=\"Đính kèm tệp\"\n            className={`${isTablet ? 'h-6 w-6' : 'h-7 w-7'} text-purple-600 hover:text-purple-700 hover:bg-purple-100 dark:text-purple-400 dark:hover:bg-purple-900/20`}\n          >\n            <Paperclip className={`${isTablet ? 'h-3 w-3' : 'h-3.5 w-3.5'}`} />\n          </Button>\n          <EmojiPicker onEmojiSelect={handleEmojiSelect} />\n\n          <Input\n            placeholder=\"Nhập tin nhắn...\"\n            value={newMessage}\n            onChange={(e) => setNewMessage(e.target.value)}\n            onKeyDown={handleKeyDown}\n            className={`flex-1 ${isTablet ? 'h-6 text-xs' : 'h-7'} bg-white dark:bg-gray-900 border-purple-100 dark:border-purple-800 focus-visible:ring-purple-500`}\n          />\n\n          <Button\n            onClick={handleSendMessage}\n            disabled={!newMessage.trim() && selectedImages.length === 0 && selectedFiles.length === 0}\n            className={`${isTablet ? 'h-6' : 'h-7'} bg-purple-600 hover:bg-purple-700 text-white`}\n          >\n            <SendHorizontal className={`${isTablet ? 'h-3 w-3' : 'h-3.5 w-3.5'}`} />\n          </Button>\n        </div>\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAMA;AACA;AACA;AA5BA;;;;;;;;;;;;;AAqCO,SAAS,SAAS,EAAE,MAAM,EAAE,WAAW,KAAK,EAAE,mBAAmB,KAAK,EAAiB;IAC5F,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,UAAO,AAAD;IACvB,2CAA2C;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;QAClD,6EAA6E;QAC7E,OAAO,gJAAA,CAAA,qBAAkB,CAAC,OAAO,IAAI,EAAE;IACzC;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAC7D,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC/C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC9C,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE7C,sCAAsC;IACtC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY,gJAAA,CAAA,qBAAkB,CAAC,OAAO,IAAI,EAAE;IAC9C,GAAG;QAAC;KAAO;IAEX,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,cAAc,OAAO,EAAE;YACzB,cAAc,OAAO,CAAC,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC5D;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB,CAAC;QACzB,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE;YAClB,MAAM,aAAa,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK;YAC5C,MAAM,sBAAsB,WAAW,GAAG,CAAC,CAAA,OAAQ,IAAI,eAAe,CAAC;YAEvE,kBAAkB;mBAAI;mBAAmB;aAAW;YACpD,oBAAoB;mBAAI;mBAAqB;aAAoB;QACnE;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,EAAE,MAAM,CAAC,KAAK,EAAE;YAClB,MAAM,aAAa,MAAM,IAAI,CAAC,EAAE,MAAM,CAAC,KAAK;YAC5C,iBAAiB;mBAAI;mBAAkB;aAAW;QACpD;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,mBAAmB;eAAI;SAAc;QAC3C,iBAAiB,MAAM,CAAC,OAAO;QAC/B,iBAAiB;IACnB;IAEA,MAAM,oBAAoB,CAAC;QACzB,cAAc,CAAA,OAAQ,OAAO;IAC/B;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,oBAAoB;eAAI;SAAe;QAC7C,MAAM,sBAAsB;eAAI;SAAiB;QAEjD,8CAA8C;QAC9C,IAAI,eAAe,CAAC,mBAAmB,CAAC,MAAM;QAE9C,kBAAkB,MAAM,CAAC,OAAO;QAChC,oBAAoB,MAAM,CAAC,OAAO;QAElC,kBAAkB;QAClB,oBAAoB;IACtB;IAEA,MAAM,mBAAmB,CAAC;QACxB,YAAY,SAAS,GAAG,CAAC,CAAA,UACvB,QAAQ,EAAE,KAAK,YACX;gBAAE,GAAG,OAAO;gBAAE,QAAQ,CAAC,QAAQ,MAAM;YAAC,IACtC;IAER;IAEA,MAAM,qBAAqB,CAAC;QAC1B,cAAc;IAChB;IAEA,MAAM,cAAc;QAClB,cAAc;IAChB;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,iBAAiB,SAAS,cAAc,CAAC,CAAC,QAAQ,EAAE,WAAW;QACrE,IAAI,gBAAgB;YAClB,eAAe,cAAc,CAAC;gBAAE,UAAU;YAAS;YACnD,yBAAyB;YACzB,eAAe,SAAS,CAAC,GAAG,CAAC,iBAAiB;YAC9C,WAAW;gBACT,eAAe,SAAS,CAAC,MAAM,CAAC,iBAAiB;YACnD,GAAG;QACL;IACF;IAEA,MAAM,oBAAoB;QACxB,IAAK,CAAC,WAAW,IAAI,MAAM,eAAe,MAAM,KAAK,KAAK,cAAc,MAAM,KAAK,GAAI;QAEvF,oDAAoD;QACpD,IAAI,cAAc,EAAE;QAEpB,4BAA4B;QAC5B,IAAI,eAAe,MAAM,GAAG,GAAG;YAC7B,MAAM,mBAAmB,MAAM,QAAQ,GAAG,CAAC,eAAe,GAAG,CAAC,OAAO,MAAM;gBACzE,qEAAqE;gBACrE,yCAAyC;gBACzC,OAAO;oBACL,MAAM;oBACN,KAAK,gBAAgB,CAAC,MAAM;oBAC5B,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;gBACjB;YACF;YACA,YAAY,IAAI,IAAI;QACtB;QAEA,2BAA2B;QAC3B,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,MAAM,kBAAkB,cAAc,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACjD,MAAM;oBACN,KAAK,IAAI,eAAe,CAAC;oBACzB,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;gBACjB,CAAC;YACD,YAAY,IAAI,IAAI;QACtB;QAEA,kDAAkD;QAClD,MAAM,SAAS,MAAM,MAAM;QAC3B,MAAM,WAAW,MAAM,YAAY,WAAW,iCAAiC;QAC/E,MAAM,aAAa,MAAM,YAAY,gHAAgH,kBAAkB;QAEvK,MAAM,UAAmB;YACvB,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,SAAS;YACT,QAAQ;gBACN,IAAI;gBACJ,MAAM;gBACN,QAAQ;YACV;YACA,WAAW,IAAI,OAAO,kBAAkB,CAAC,SAAS;gBAAE,MAAM;gBAAW,QAAQ;YAAU;YACvF;YACA,GAAI,cAAc;gBAChB,SAAS;oBACP,IAAI,WAAW,EAAE;oBACjB,SAAS,WAAW,OAAO;oBAC3B,QAAQ;wBACN,IAAI,WAAW,MAAM,CAAC,EAAE;wBACxB,MAAM,WAAW,MAAM,CAAC,IAAI;oBAC9B;gBACF;YACF,CAAC;QACH;QAEA,YAAY;eAAI;YAAU;SAAQ;QAClC,cAAc;QACd,kBAAkB,EAAE;QACpB,iBAAiB,EAAE;QACnB,oBAAoB,EAAE;QACtB,cAAc;IAChB;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yJAAA,CAAA,aAAU;gBAAC,WAAW,CAAC,OAAO,EAAE,WAAW,QAAQ,MAAM,CAAC,EAAE,mBAAmB,SAAS,IAAI;;kCAC3F,8OAAC,+IAAA,CAAA,iBAAc;wBACb,UAAU;wBACV,SAAS;wBACT,mBAAmB;wBACnB,UAAU;;;;;;kCAGZ,8OAAC;wBAAI,WAAW,GAAG,WAAW,cAAc,aAAa;;4BACtD,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;oCACC,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE;oCAE3B,WAAW,CAAC,KAAK,EAAE,WAAW,YAAY,QAAQ,gCAAgC,EAChF,QAAQ,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,MAAM,GAAG,IAAI,qBAAqB,IAC/D;;sDAEF,8OAAC,iJAAA,CAAA,SAAM;4CAAC,WAAW,GAAG,WAAW,YAAY,UAAU,uCAAuC,CAAC;;8DAC7F,8OAAC,iJAAA,CAAA,cAAW;oDAAC,KAAK,QAAQ,MAAM,CAAC,MAAM;oDAAE,KAAK,QAAQ,MAAM,CAAC,IAAI;;;;;;8DACjE,8OAAC,iJAAA,CAAA,iBAAc;8DAAE,QAAQ,MAAM,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;sDAGzC,8OAAC;4CAAI,WAAW,CAAC,cAAc,EAAE,WAAW,YAAY,QAAQ,aAAa,EAC3E,QAAQ,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,MAAM,GAAG,IAAI,cAAc,IACxD;;8DACA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAuB,QAAQ,MAAM,CAAC,IAAI;;;;;;sEAC1D,8OAAC;4DAAK,WAAU;sEAAiC,QAAQ,SAAS;;;;;;wDAEjE,QAAQ,MAAM,kBACb,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;;8DAInB,8OAAC;oDAAI,WAAW,CAAC,oBAAoB,EAAE,WAAW,QAAQ,QAAQ,OAAO,EACvE,QAAQ,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,MAAM,GAAG,IAClC,uCACA,0BACJ;;wDACC,QAAQ,OAAO,kBACd,8OAAC;4DAAI,WAAW,GAAG,WAAW,iBAAiB,WAAW,wCAAwC,EAChG,QAAQ,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,MAAM,GAAG,IAClC,qBACA,8BACJ;;8EACA,8OAAC,sOAAA,CAAA,qBAAkB;oEAAC,WAAU;;;;;;8EAC9B,8OAAC;;sFACC,8OAAC;4EAAI,WAAU;sFAAe,QAAQ,OAAO,CAAC,MAAM,CAAC,IAAI;;;;;;sFACzD,8OAAC;4EAAI,WAAU;sFAA4B,QAAQ,OAAO,CAAC,OAAO;;;;;;;;;;;;;;;;;;sEAKxE,8OAAC;4DAAE,WAAU;sEAA2B,QAAQ,OAAO;;;;;;wDAEtD,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,MAAM,GAAG,mBACnD,8OAAC;4DAAI,WAAW,GAAG,WAAW,uBAAuB,kBAAkB;sEACpE,QAAQ,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY,QACpC,WAAW,IAAI,KAAK,wBAClB,8OAAC;oEAAgB,WAAU;8EAEzB,cAAA,8OAAC;wEACC,KAAK,WAAW,GAAG;wEACnB,KAAK,WAAW,IAAI;wEACpB,WAAW,GAAG,WAAW,kBAAkB,WAAW,aAAa,CAAC;;;;;;mEAL9D;;;;yFASV,8OAAC;oEAAgB,WAAW,CAAC,2EAA2E,EAAE,WAAW,UAAU,MAAM,QAAQ,CAAC;;sFAC5I,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,kMAAA,CAAA,OAAQ;oFAAC,WAAU;;;;;;8FACpB,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAK,WAAW,CAAC,SAAS,EAAE,WAAW,kBAAkB,iBAAiB;sGAAG,WAAW,IAAI;;;;;;sGAC7F,8OAAC;4FAAK,WAAU;sGACb,WAAW,IAAI,GAAG,GAAG,KAAK,KAAK,CAAC,WAAW,IAAI,GAAG,MAAM,GAAG,CAAC,GAAG;;;;;;;;;;;;;;;;;;sFAItE,8OAAC;4EACC,MAAM,WAAW,GAAG;4EACpB,UAAU,WAAW,IAAI;4EACzB,QAAO;4EACP,KAAI;4EACJ,WAAU;4EACV,SAAS,CAAC,IAAM,EAAE,eAAe;sFAEjC,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;;;;;;;mEAlBd;;;;;;;;;;sEA0BlB,8OAAC;4DAAI,WAAW,CAAC,SAAS,EAAE,QAAQ,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,MAAM,GAAG,IAAI,WAAW,UAAU,0BAA0B,EAAE,QAAQ,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,MAAM,GAAG,IAAI,sBAAsB,mBAAmB,qDAAqD,CAAC;sEAC9P,cAAA,8OAAC,2JAAA,CAAA,eAAY;;kFACX,8OAAC,2JAAA,CAAA,sBAAmB;wEAAC,OAAO;kFAC1B,cAAA,8OAAC,iJAAA,CAAA,SAAM;4EAAC,SAAQ;4EAAQ,MAAK;4EAAO,WAAW,GAAG,WAAW,YAAY,UAAU,yDAAyD,CAAC;sFAC3I,cAAA,8OAAC,0NAAA,CAAA,eAAY;gFAAC,WAAW,GAAG,WAAW,YAAY,WAAW;;;;;;;;;;;;;;;;kFAGlE,8OAAC,2JAAA,CAAA,sBAAmB;wEAAC,OAAO,QAAQ,MAAM,CAAC,EAAE,KAAK,CAAC,MAAM,MAAM,GAAG,IAAI,QAAQ;;0FAC5E,8OAAC,2JAAA,CAAA,mBAAgB;gFAAC,SAAS,IAAM,mBAAmB;;kGAClD,8OAAC,oMAAA,CAAA,QAAK;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;0FAGpC,8OAAC,2JAAA,CAAA,mBAAgB;gFAAC,SAAS,IAAM,iBAAiB,QAAQ,EAAE;;kGAC1D,8OAAC,gMAAA,CAAA,MAAG;wFAAC,WAAU;;;;;;oFACd,QAAQ,MAAM,GAAG,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCAhGrC,QAAQ,EAAE;;;;;0CAyGnB,8OAAC;gCAAI,KAAK;;;;;;;;;;;;;;;;;;0BAId,8OAAC;gBAAI,WAAW,GAAG,WAAW,UAAU,MAAM,CAAC,EAAE,mBAAmB,eAAe,WAAW,+EAA+E,CAAC;;oBAE3K,iBAAiB,MAAM,GAAG,mBACzB,8OAAC;wBAAI,WAAW,GAAG,WAAW,SAAS,UAAU;;0CAC/C,8OAAC;gCAAI,WAAU;;oCAAqC;oCAAW,iBAAiB,MAAM;oCAAC;;;;;;;0CACvF,8OAAC;gCAAI,WAAU;0CACZ,iBAAiB,GAAG,CAAC,CAAC,KAAK,sBAC1B,8OAAC;wCAAgB,WAAW,CAAC,SAAS,EAAE,WAAW,cAAc,YAAY,wFAAwF,CAAC;;0DAEpK,8OAAC;gDAAI,KAAK;gDAAK,KAAI;gDAAU,WAAU;;;;;;0DACvC,8OAAC,iJAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,kBAAkB;0DAEjC,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;uCATP;;;;;;;;;;;;;;;;oBAkBjB,cAAc,MAAM,GAAG,mBACtB,8OAAC;wBAAI,WAAW,GAAG,WAAW,SAAS,UAAU;;0CAC/C,8OAAC;gCAAI,WAAU;;oCAAqC;oCAAe,cAAc,MAAM;oCAAC;;;;;;;0CACxF,8OAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kMAAA,CAAA,OAAQ;wDAAC,WAAU;;;;;;kEACpB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAW,CAAC,iBAAiB,EAAE,WAAW,kBAAkB,iBAAiB;0EAAG,KAAK,IAAI;;;;;;0EAC/F,8OAAC;gEAAK,WAAU;;oEAAqC,KAAK,KAAK,CAAC,KAAK,IAAI,GAAG;oEAAM;;;;;;;;;;;;;;;;;;;0DAGtF,8OAAC,iJAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,iBAAiB;0DAEhC,cAAA,8OAAC,4LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;uCAdP;;;;;;;;;;;;;;;;oBAuBjB,4BACC,8OAAC;wBAAI,WAAW,GAAG,WAAW,aAAa,aAAa,6GAA6G,CAAC;;0CACpK,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,sOAAA,CAAA,qBAAkB;wCAAC,WAAU;;;;;;kDAC9B,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;oDAAsB;oDACrB,WAAW,MAAM,CAAC,IAAI;;;;;;;0DAEtC,8OAAC;gDAAI,WAAW,CAAC,uDAAuD,EAAE,WAAW,kBAAkB,iBAAiB;0DACrH,WAAW,OAAO;;;;;;;;;;;;;;;;;;0CAIzB,8OAAC,iJAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CAET,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAKnB,8OAAC;wBAAI,WAAW,CAAC,kBAAkB,EAAE,WAAW,YAAY,SAAS;;0CACnE,8OAAC;gCACC,MAAK;gCACL,QAAO;gCACP,QAAQ;gCACR,WAAU;gCACV,KAAK;gCACL,UAAU;;;;;;0CAEZ,8OAAC;gCACC,MAAK;gCACL,QAAQ;gCACR,WAAU;gCACV,KAAK;gCACL,UAAU;;;;;;0CAEZ,8OAAC,iJAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,cAAc,OAAO,EAAE;gCACtC,OAAM;gCACN,WAAW,GAAG,WAAW,YAAY,UAAU,2GAA2G,CAAC;0CAE3J,cAAA,8OAAC,oMAAA,CAAA,QAAS;oCAAC,WAAW,GAAG,WAAW,YAAY,eAAe;;;;;;;;;;;0CAEjE,8OAAC,iJAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,aAAa,OAAO,EAAE;gCACrC,OAAM;gCACN,WAAW,GAAG,WAAW,YAAY,UAAU,2GAA2G,CAAC;0CAE3J,cAAA,8OAAC,4MAAA,CAAA,YAAS;oCAAC,WAAW,GAAG,WAAW,YAAY,eAAe;;;;;;;;;;;0CAEjE,8OAAC,4IAAA,CAAA,cAAW;gCAAC,eAAe;;;;;;0CAE5B,8OAAC,gJAAA,CAAA,QAAK;gCACJ,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAW;gCACX,WAAW,CAAC,OAAO,EAAE,WAAW,gBAAgB,MAAM,iGAAiG,CAAC;;;;;;0CAG1J,8OAAC,iJAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,CAAC,WAAW,IAAI,MAAM,eAAe,MAAM,KAAK,KAAK,cAAc,MAAM,KAAK;gCACxF,WAAW,GAAG,WAAW,QAAQ,MAAM,6CAA6C,CAAC;0CAErF,cAAA,8OAAC,0NAAA,CAAA,iBAAc;oCAAC,WAAW,GAAG,WAAW,YAAY,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhF", "debugId": null}}, {"offset": {"line": 2123, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/components/ui/radix-ui/dialog.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\nimport { X } from 'lucide-react';\n\nimport { cn } from '@/lib/utils';\n\nconst Dialog = DialogPrimitive.Root;\n\nconst DialogTrigger = DialogPrimitive.Trigger;\n\nconst DialogPortal = DialogPrimitive.Portal;\n\nconst DialogClose = DialogPrimitive.Close;\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      'fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',\n      className\n    )}\n    {...props}\n  />\n));\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName;\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Title>\n      Tiêu đề nè\n    </DialogPrimitive.Title>\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        'fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg',\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n));\nDialogContent.displayName = DialogPrimitive.Content.displayName;\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      'flex flex-col space-y-1.5 text-center sm:text-left',\n      className\n    )}\n    {...props}\n  />\n);\nDialogHeader.displayName = 'DialogHeader';\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      'flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2',\n      className\n    )}\n    {...props}\n  />\n);\nDialogFooter.displayName = 'DialogFooter';\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      'text-lg font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nDialogTitle.displayName = DialogPrimitive.Title.displayName;\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nDialogDescription.displayName = DialogPrimitive.Description.displayName;\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2JACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,QAAqB;0BAAC;;;;;;0BAGvB,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+fACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;0CACb,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EACpB,SAAS,EACT,GAAG,OACkC,iBACrC,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2262, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/components/ui/radix-ui/label.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as LabelPrimitive from '@radix-ui/react-label';\nimport { cva, type VariantProps } from 'class-variance-authority';\n\nimport { cn } from '@/lib/utils';\n\nconst labelVariants = cva(\n  'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'\n);\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n));\nLabel.displayName = LabelPrimitive.Root.displayName;\n\nexport { Label };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2294, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/trips/invite-members-dialog.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport {\n  <PERSON>alog,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle,\n  DialogTrigger,\n  DialogFooter\n} from '@/components/ui/radix-ui/dialog';\nimport { Button } from '@/components/ui/radix-ui/button';\nimport { Input } from '@/components/ui/radix-ui/input';\nimport { Label } from '@/components/ui/radix-ui/label';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/radix-ui/avatar';\nimport { Users, Search, UserPlus, X } from 'lucide-react';\nimport { ScrollArea } from '@/components/ui/radix-ui/scroll-area';\n\ntype Member = {\n  id: string;\n  name: string;\n  avatar: string;\n};\n\ntype InviteMembersDialogProps = {\n  tripId: string;\n  currentMembers: Member[];\n  maxMembers: number;\n  onInvite: (newMembers: Member[]) => void;\n  children?: React.ReactNode; // Add support for custom trigger button\n  open?: boolean; // For controlled mode\n  onOpenChange?: (open: boolean) => void; // For controlled mode\n};\n\n// Mock user data for search\nconst mockUsers = [\n  { id: '4', name: 'Phạm Anh', avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1' },\n  { id: '5', name: 'Nguyễn Thảo', avatar: 'https://images.pexels.com/photos/415829/pexels-photo-415829.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1' },\n  { id: '6', name: 'Trần Dũng', avatar: 'https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1' },\n  { id: '7', name: 'Lê Hương', avatar: 'https://images.pexels.com/photos/1239291/pexels-photo-1239291.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1' },\n  { id: '8', name: 'Vũ Minh', avatar: 'https://images.pexels.com/photos/1516680/pexels-photo-1516680.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1' },\n];\n\nexport function InviteMembersDialog({\n  tripId,\n  currentMembers,\n  maxMembers,\n  onInvite,\n  children,\n  open,\n  onOpenChange\n}: InviteMembersDialogProps) {\n  const [isOpen, setIsOpen] = useState(false);\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedUsers, setSelectedUsers] = useState<Member[]>([]);\n\n  // Determine if we're in controlled or uncontrolled mode\n  const isControlled = open !== undefined && onOpenChange !== undefined;\n  const isDialogOpen = isControlled ? open : isOpen;\n\n  const handleOpenChange = (newOpen: boolean) => {\n    if (isControlled) {\n      onOpenChange(newOpen);\n    } else {\n      setIsOpen(newOpen);\n    }\n  };\n\n  const currentMemberIds = currentMembers.map(member => member.id);\n  const availableSlots = maxMembers - currentMembers.length;\n\n  // Filter users who are not already members and match search query\n  const filteredUsers = mockUsers.filter(user =>\n    !currentMemberIds.includes(user.id) &&\n    user.name.toLowerCase().includes(searchQuery.toLowerCase())\n  );\n\n  const handleSelectUser = (user: Member) => {\n    if (selectedUsers.length < availableSlots) {\n      setSelectedUsers([...selectedUsers, user]);\n    }\n  };\n\n  const handleRemoveUser = (userId: string) => {\n    setSelectedUsers(selectedUsers.filter(user => user.id !== userId));\n  };\n\n  const handleInvite = () => {\n    if (selectedUsers.length > 0) {\n      onInvite(selectedUsers);\n      setSelectedUsers([]);\n\n      // Đóng dialog\n      if (isControlled) {\n        onOpenChange(false);\n      } else {\n        setIsOpen(false);\n      }\n    }\n  };\n\n  return (\n    <Dialog\n      open={isControlled ? open : isOpen}\n      onOpenChange={isControlled ? onOpenChange : setIsOpen}\n    >\n      {!isControlled && (\n        <DialogTrigger asChild>\n          {children || (\n            <Button className=\"w-full bg-purple-600 hover:bg-purple-700 text-white\">\n              <Users className=\"h-4 w-4 mr-2\" />\n              Mời thêm thành viên\n            </Button>\n          )}\n        </DialogTrigger>\n      )}\n      <DialogContent className=\"sm:max-w-[425px]\">\n        <DialogHeader>\n          <DialogTitle>Mời thành viên</DialogTitle>\n          <DialogDescription>\n            Mời bạn bè tham gia chuyến đi của bạn. Còn {availableSlots} vị trí trống.\n          </DialogDescription>\n        </DialogHeader>\n\n        <div className=\"space-y-4 py-4\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"relative flex-1\">\n              <Search className=\"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground\" />\n              <Input\n                placeholder=\"Tìm kiếm theo tên hoặc ID người dùng\"\n                value={searchQuery}\n                onChange={(e) => setSearchQuery(e.target.value)}\n                className=\"pl-8\"\n              />\n            </div>\n          </div>\n\n          {selectedUsers.length > 0 && (\n            <div>\n              <Label className=\"text-sm text-muted-foreground mb-2 block\">\n                Đã chọn ({selectedUsers.length})\n              </Label>\n              <div className=\"flex flex-wrap gap-2\">\n                {selectedUsers.map(user => (\n                  <div\n                    key={user.id}\n                    className=\"flex items-center gap-1 bg-secondary rounded-full pl-1 pr-2 py-1\"\n                  >\n                    <Avatar className=\"h-5 w-5\">\n                      <AvatarImage src={user.avatar} alt={user.name} />\n                      <AvatarFallback>{user.name[0]}</AvatarFallback>\n                    </Avatar>\n                    <span className=\"text-xs\">{user.name}</span>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      className=\"h-4 w-4 p-0 ml-1\"\n                      onClick={() => handleRemoveUser(user.id)}\n                    >\n                      <X className=\"h-3 w-3\" />\n                    </Button>\n                  </div>\n                ))}\n              </div>\n            </div>\n          )}\n\n          <div>\n            <Label className=\"text-sm text-muted-foreground mb-2 block\">\n              Kết quả tìm kiếm\n            </Label>\n            <ScrollArea className=\"h-[200px]\">\n              <div className=\"space-y-2\">\n                {filteredUsers.length > 0 ? (\n                  filteredUsers.map(user => {\n                    const isSelected = selectedUsers.some(selected => selected.id === user.id);\n                    return (\n                      <div\n                        key={user.id}\n                        className={`flex items-center justify-between p-2 rounded-md ${\n                          isSelected ? 'bg-secondary' : 'hover:bg-secondary/50'\n                        } cursor-pointer`}\n                        onClick={() => !isSelected && handleSelectUser(user)}\n                      >\n                        <div className=\"flex items-center gap-2\">\n                          <Avatar className=\"h-8 w-8\">\n                            <AvatarImage src={user.avatar} alt={user.name} />\n                            <AvatarFallback>{user.name[0]}</AvatarFallback>\n                          </Avatar>\n                          <div>\n                            <p className=\"text-sm font-medium\">{user.name}</p>\n                            <p className=\"text-xs text-muted-foreground\">ID: {user.id}</p>\n                          </div>\n                        </div>\n\n                        <Button\n                          variant={isSelected ? \"secondary\" : \"ghost\"}\n                          size=\"sm\"\n                          className=\"h-8 w-8 p-0\"\n                          disabled={isSelected}\n                        >\n                          <UserPlus className=\"h-4 w-4\" />\n                        </Button>\n                      </div>\n                    );\n                  })\n                ) : (\n                  <div className=\"text-center py-4 text-muted-foreground\">\n                    {searchQuery ? 'Không tìm thấy người dùng' : 'Nhập tên để tìm kiếm'}\n                  </div>\n                )}\n              </div>\n            </ScrollArea>\n          </div>\n        </div>\n\n        <DialogFooter>\n          <Button\n            variant=\"outline\"\n            onClick={() => {\n              if (isControlled) {\n                onOpenChange(false);\n              } else {\n                setIsOpen(false);\n              }\n            }}\n          >\n            Hủy\n          </Button>\n          <Button\n            onClick={handleInvite}\n            disabled={selectedUsers.length === 0}\n            className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n          >\n            Mời {selectedUsers.length > 0 ? `(${selectedUsers.length})` : ''}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AASA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAjBA;;;;;;;;;;AAmCA,4BAA4B;AAC5B,MAAM,YAAY;IAChB;QAAE,IAAI;QAAK,MAAM;QAAY,QAAQ;IAA+G;IACpJ;QAAE,IAAI;QAAK,MAAM;QAAe,QAAQ;IAA+G;IACvJ;QAAE,IAAI;QAAK,MAAM;QAAa,QAAQ;IAAiH;IACvJ;QAAE,IAAI;QAAK,MAAM;QAAY,QAAQ;IAAiH;IACtJ;QAAE,IAAI;QAAK,MAAM;QAAW,QAAQ;IAAiH;CACtJ;AAEM,SAAS,oBAAoB,EAClC,MAAM,EACN,cAAc,EACd,UAAU,EACV,QAAQ,EACR,QAAQ,EACR,IAAI,EACJ,YAAY,EACa;IACzB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,wDAAwD;IACxD,MAAM,eAAe,SAAS,aAAa,iBAAiB;IAC5D,MAAM,eAAe,eAAe,OAAO;IAE3C,MAAM,mBAAmB,CAAC;QACxB,IAAI,cAAc;YAChB,aAAa;QACf,OAAO;YACL,UAAU;QACZ;IACF;IAEA,MAAM,mBAAmB,eAAe,GAAG,CAAC,CAAA,SAAU,OAAO,EAAE;IAC/D,MAAM,iBAAiB,aAAa,eAAe,MAAM;IAEzD,kEAAkE;IAClE,MAAM,gBAAgB,UAAU,MAAM,CAAC,CAAA,OACrC,CAAC,iBAAiB,QAAQ,CAAC,KAAK,EAAE,KAClC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;IAG1D,MAAM,mBAAmB,CAAC;QACxB,IAAI,cAAc,MAAM,GAAG,gBAAgB;YACzC,iBAAiB;mBAAI;gBAAe;aAAK;QAC3C;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,iBAAiB,cAAc,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAC5D;IAEA,MAAM,eAAe;QACnB,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,SAAS;YACT,iBAAiB,EAAE;YAEnB,cAAc;YACd,IAAI,cAAc;gBAChB,aAAa;YACf,OAAO;gBACL,UAAU;YACZ;QACF;IACF;IAEA,qBACE,8OAAC,iJAAA,CAAA,SAAM;QACL,MAAM,eAAe,OAAO;QAC5B,cAAc,eAAe,eAAe;;YAE3C,CAAC,8BACA,8OAAC,iJAAA,CAAA,gBAAa;gBAAC,OAAO;0BACnB,0BACC,8OAAC,iJAAA,CAAA,SAAM;oBAAC,WAAU;;sCAChB,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;0BAM1C,8OAAC,iJAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,8OAAC,iJAAA,CAAA,eAAY;;0CACX,8OAAC,iJAAA,CAAA,cAAW;0CAAC;;;;;;0CACb,8OAAC,iJAAA,CAAA,oBAAiB;;oCAAC;oCAC2B;oCAAe;;;;;;;;;;;;;kCAI/D,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC,gJAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO;4CACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC9C,WAAU;;;;;;;;;;;;;;;;;4BAKf,cAAc,MAAM,GAAG,mBACtB,8OAAC;;kDACC,8OAAC,gJAAA,CAAA,QAAK;wCAAC,WAAU;;4CAA2C;4CAChD,cAAc,MAAM;4CAAC;;;;;;;kDAEjC,8OAAC;wCAAI,WAAU;kDACZ,cAAc,GAAG,CAAC,CAAA,qBACjB,8OAAC;gDAEC,WAAU;;kEAEV,8OAAC,iJAAA,CAAA,SAAM;wDAAC,WAAU;;0EAChB,8OAAC,iJAAA,CAAA,cAAW;gEAAC,KAAK,KAAK,MAAM;gEAAE,KAAK,KAAK,IAAI;;;;;;0EAC7C,8OAAC,iJAAA,CAAA,iBAAc;0EAAE,KAAK,IAAI,CAAC,EAAE;;;;;;;;;;;;kEAE/B,8OAAC;wDAAK,WAAU;kEAAW,KAAK,IAAI;;;;;;kEACpC,8OAAC,iJAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,SAAS,IAAM,iBAAiB,KAAK,EAAE;kEAEvC,cAAA,8OAAC,4LAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;;;;;;;+CAdV,KAAK,EAAE;;;;;;;;;;;;;;;;0CAsBtB,8OAAC;;kDACC,8OAAC,gJAAA,CAAA,QAAK;wCAAC,WAAU;kDAA2C;;;;;;kDAG5D,8OAAC,yJAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,8OAAC;4CAAI,WAAU;sDACZ,cAAc,MAAM,GAAG,IACtB,cAAc,GAAG,CAAC,CAAA;gDAChB,MAAM,aAAa,cAAc,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK,KAAK,EAAE;gDACzE,qBACE,8OAAC;oDAEC,WAAW,CAAC,iDAAiD,EAC3D,aAAa,iBAAiB,wBAC/B,eAAe,CAAC;oDACjB,SAAS,IAAM,CAAC,cAAc,iBAAiB;;sEAE/C,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iJAAA,CAAA,SAAM;oEAAC,WAAU;;sFAChB,8OAAC,iJAAA,CAAA,cAAW;4EAAC,KAAK,KAAK,MAAM;4EAAE,KAAK,KAAK,IAAI;;;;;;sFAC7C,8OAAC,iJAAA,CAAA,iBAAc;sFAAE,KAAK,IAAI,CAAC,EAAE;;;;;;;;;;;;8EAE/B,8OAAC;;sFACC,8OAAC;4EAAE,WAAU;sFAAuB,KAAK,IAAI;;;;;;sFAC7C,8OAAC;4EAAE,WAAU;;gFAAgC;gFAAK,KAAK,EAAE;;;;;;;;;;;;;;;;;;;sEAI7D,8OAAC,iJAAA,CAAA,SAAM;4DACL,SAAS,aAAa,cAAc;4DACpC,MAAK;4DACL,WAAU;4DACV,UAAU;sEAEV,cAAA,8OAAC,8MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;mDAvBjB,KAAK,EAAE;;;;;4CA2BlB,mBAEA,8OAAC;gDAAI,WAAU;0DACZ,cAAc,8BAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQzD,8OAAC,iJAAA,CAAA,eAAY;;0CACX,8OAAC,iJAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;oCACP,IAAI,cAAc;wCAChB,aAAa;oCACf,OAAO;wCACL,UAAU;oCACZ;gCACF;0CACD;;;;;;0CAGD,8OAAC,iJAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,cAAc,MAAM,KAAK;gCACnC,WAAU;;oCACX;oCACM,cAAc,MAAM,GAAG,IAAI,CAAC,CAAC,EAAE,cAAc,MAAM,CAAC,CAAC,CAAC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;AAM1E", "debugId": null}}, {"offset": {"line": 2754, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/components/ui/radix-ui/tabs.tsx"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport * as TabsPrimitive from '@radix-ui/react-tabs';\n\nimport { cn } from '@/lib/utils';\n\nconst Tabs = TabsPrimitive.Root;\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      'inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground',\n      className\n    )}\n    {...props}\n  />\n));\nTabsList.displayName = TabsPrimitive.List.displayName;\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      'inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-xs',\n      className\n    )}\n    {...props}\n  />\n));\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      'mt-2 ring-offset-background focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',\n      className\n    )}\n    {...props}\n  />\n));\nTabsContent.displayName = TabsPrimitive.Content.displayName;\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent };\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2807, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/planning/mock-data.ts"], "sourcesContent": ["// Mock data for travel plan templates\n\nexport type ActivityType =\n  | 'Ăn sáng'\n  | 'Ăn trưa'\n  | 'Ăn tối'\n  | 'Cà phê'\n  | 'Tham quan'\n  | 'Mua sắm'\n  | 'Nghỉ ngơi'\n  | 'Di chuyển'\n  | 'Khác';\n\nexport type Activity = {\n  id: string;\n  time: string;\n  title: string;\n  description: string;\n  location: string;\n  mainLocation: string; // For chart display\n  type?: ActivityType; // Loại hoạt động (tùy chọn)\n};\n\nexport type Day = {\n  id: string;\n  date: Date | null; // Null for template, will be set when applied\n  activities: Activity[];\n};\n\nexport type TravelPlanTemplate = {\n  id: string;\n  name: string;\n  destination: string;\n  region: 'Miền Bắc' | 'Miền Trung' | 'Miền Nam';\n  description: string;\n  duration: number; // Number of days\n  image: string;\n  tags: string[];\n  days: Day[];\n  authorId?: string;\n  authorName?: string;\n  isPublic: boolean;\n  rating: number;\n  usageCount: number;\n};\n\n// Mock travel plan templates for Vietnamese destinations\nexport const TRAVEL_PLAN_TEMPLATES: TravelPlanTemplate[] = [\n  {\n    id: 'template-dalat',\n    name: 'Khám phá Đà Lạt',\n    destination: 'Đà Lạt, Lâm Đồng',\n    region: 'Miền Trung',\n    description: 'Kế hoạch 3 ngày khám phá thành phố ngàn hoa Đà Lạt với các điểm tham quan nổi tiếng và ẩm thực đặc sắc.',\n    duration: 3,\n    image: 'https://images.pexels.com/photos/5746250/pexels-photo-5746250.jpeg?auto=compress&cs=tinysrgb&w=600',\n    tags: ['DaLat', 'ThanhPhoNganHoa', 'MienNui'],\n    days: [\n      {\n        id: 'day-1',\n        date: null,\n        activities: [\n          {\n            id: 'activity-1',\n            time: '08:00',\n            title: 'Ăn sáng tại chợ Đà Lạt',\n            description: 'Thưởng thức bánh căn, bánh mì xíu mại và cà phê Đà Lạt tại chợ trung tâm.',\n            location: 'Chợ Đà Lạt, 66 Nguyễn Thị Minh Khai',\n            mainLocation: 'Chợ Đà Lạt'\n          },\n          {\n            id: 'activity-2',\n            time: '09:30',\n            title: 'Tham quan Quảng trường Lâm Viên',\n            description: 'Check-in tại công trình hoa dã quỳ và bông atiso khổng lồ.',\n            location: 'Quảng trường Lâm Viên'\n          ,\n            mainLocation: 'Quảng trường Lâm Viên'\n          },\n          {\n            id: 'activity-3',\n            time: '11:00',\n            title: 'Thăm Nhà thờ Con Gà',\n            description: 'Tham quan nhà thờ mang kiến trúc Pháp nổi tiếng với chiếc chuông và chú gà trên đỉnh.',\n            location: 'Nhà thờ Chính tòa Đà Lạt'\n          ,\n            mainLocation: 'Nhà thờ Chính tòa Đà Lạt'\n          },\n          {\n            id: 'activity-4',\n            time: '12:30',\n            title: 'Ăn trưa',\n            description: 'Thưởng thức bánh ướt lòng gà - đặc sản Đà Lạt.',\n            location: 'Bánh ướt lòng gà Trang, 15 Tăng Bạt Hổ'\n          ,\n            mainLocation: 'Bánh ướt lòng gà Trang'\n          },\n          {\n            id: 'activity-5',\n            time: '14:00',\n            title: 'Tham quan Crazy House',\n            description: 'Khám phá công trình kiến trúc độc đáo như trong truyện cổ tích.',\n            location: 'Crazy House, 3 Đường Huỳnh Thúc Kháng'\n          ,\n            mainLocation: 'Crazy House'\n          },\n          {\n            id: 'activity-6',\n            time: '16:30',\n            title: 'Hồ Xuân Hương',\n            description: 'Đạp xe quanh hồ hoặc thuê thuyền đạp vịt trên hồ.',\n            location: 'Hồ Xuân Hương'\n          ,\n            mainLocation: 'Hồ Xuân Hương'\n          },\n          {\n            id: 'activity-7',\n            time: '18:30',\n            title: 'Ăn tối tại chợ đêm',\n            description: 'Thưởng thức các món ăn đường phố như bánh tráng nướng, sữa đậu nành nóng.',\n            location: 'Chợ đêm Đà Lạt'\n          ,\n            mainLocation: 'Chợ đêm Đà Lạt'\n          }\n        ]\n      },\n      {\n        id: 'day-2',\n        date: null,\n        activities: [\n          {\n            id: 'activity-8',\n            time: '07:00',\n            title: 'Ngắm bình minh tại đồi Đa Phú',\n            description: 'Ngắm cảnh bình minh và biển mây tuyệt đẹp.',\n            location: 'Đồi Đa Phú'\n          ,\n            mainLocation: 'Đồi Đa Phú'\n          },\n          {\n            id: 'activity-9',\n            time: '09:00',\n            title: 'Tham quan Thung lũng Tình Yêu',\n            description: 'Tham quan khu du lịch với khung cảnh lãng mạn và nhiều hoạt động giải trí.',\n            location: 'Thung lũng Tình Yêu'\n          ,\n            mainLocation: 'Thung lũng Tình Yêu'\n          },\n          {\n            id: 'activity-10',\n            time: '11:30',\n            title: 'Tham quan Thiền viện Trúc Lâm',\n            description: 'Tham quan thiền viện và ngắm cảnh Hồ Tuyền Lâm từ trên cao.',\n            location: 'Thiền viện Trúc Lâm'\n          ,\n            mainLocation: 'Thiền viện Trúc Lâm'\n          },\n          {\n            id: 'activity-11',\n            time: '13:00',\n            title: 'Ăn trưa',\n            description: 'Thưởng thức ẩm thực chay tại nhà hàng gần thiền viện.',\n            location: 'Nhà hàng chay An Nhiên'\n          ,\n            mainLocation: 'Nhà hàng chay An Nhiên'\n          },\n          {\n            id: 'activity-12',\n            time: '15:00',\n            title: 'Tham quan Thác Datanla',\n            description: 'Trải nghiệm máng trượt Alpine Coaster và ngắm thác.',\n            location: 'Thác Datanla'\n          ,\n            mainLocation: 'Thác Datanla'\n          },\n          {\n            id: 'activity-13',\n            time: '18:00',\n            title: 'Ăn tối',\n            description: 'Thưởng thức lẩu bò Đà Lạt.',\n            location: 'Nhà hàng Lẩu Bò Ngọc Dung'\n          ,\n            mainLocation: 'Nhà hàng Lẩu Bò Ngọc Dung'\n          },\n          {\n            id: 'activity-14',\n            time: '20:00',\n            title: 'Cà phê đêm',\n            description: 'Thưởng thức cà phê và ngắm cảnh đêm Đà Lạt.',\n            location: 'Cà phê Tùng, 10 Phù Đổng Thiên Vương'\n          ,\n            mainLocation: 'Cà phê Tùng'\n          }\n        ]\n      },\n      {\n        id: 'day-3',\n        date: null,\n        activities: [\n          {\n            id: 'activity-15',\n            time: '08:00',\n            title: 'Ăn sáng',\n            description: 'Thưởng thức phở và bánh mì nóng.',\n            location: 'Phở 75, Đường 3 tháng 2'\n          ,\n            mainLocation: 'Phở 75'\n          },\n          {\n            id: 'activity-16',\n            time: '09:30',\n            title: 'Tham quan Vườn hoa thành phố',\n            description: 'Ngắm nhìn và chụp ảnh với hàng trăm loài hoa đẹp.',\n            location: 'Vườn hoa thành phố Đà Lạt'\n          ,\n            mainLocation: 'Vườn hoa thành phố Đà Lạt'\n          },\n          {\n            id: 'activity-17',\n            time: '11:30',\n            title: 'Tham quan Ga Đà Lạt',\n            description: 'Tham quan nhà ga cổ và có thể đi tàu hỏa leo núi đến Trại Mát.',\n            location: 'Ga Đà Lạt'\n          ,\n            mainLocation: 'Ga Đà Lạt'\n          },\n          {\n            id: 'activity-18',\n            time: '13:00',\n            title: 'Ăn trưa',\n            description: 'Thưởng thức nem nướng Đà Lạt.',\n            location: 'Nem nướng Bà Hùng, 254 Phan Đình Phùng'\n          ,\n            mainLocation: 'Nem nướng Bà Hùng'\n          },\n          {\n            id: 'activity-19',\n            time: '14:30',\n            title: 'Tham quan Làng Cù Lần',\n            description: 'Trải nghiệm không gian làng quê và các hoạt động ngoài trời.',\n            location: 'Làng Cù Lần'\n          ,\n            mainLocation: 'Làng Cù Lần'\n          },\n          {\n            id: 'activity-20',\n            time: '17:30',\n            title: 'Mua đồ lưu niệm',\n            description: 'Mua sắm đặc sản và quà lưu niệm Đà Lạt.',\n            location: 'Chợ Đà Lạt'\n          ,\n            mainLocation: 'Chợ Đà Lạt'\n          },\n          {\n            id: 'activity-21',\n            time: '19:00',\n            title: 'Ăn tối chia tay',\n            description: 'Thưởng thức bữa tối với các món đặc sản Đà Lạt.',\n            location: 'Nhà hàng Ẩm thực Đà Lạt, 15 Nguyễn Chí Thanh'\n          ,\n            mainLocation: 'Nhà hàng Ẩm thực Đà Lạt'\n          }\n        ]\n      }\n    ],\n    authorId: '1',\n    authorName: 'Nguyễn Minh',\n    isPublic: true,\n    rating: 4.8,\n    usageCount: 1245\n  },\n  {\n    id: 'template-phuquoc',\n    name: 'Khám phá Phú Quốc',\n    destination: 'Phú Quốc, Kiên Giang',\n    region: 'Miền Nam',\n    description: 'Kế hoạch 4 ngày tận hưởng thiên đường biển đảo Phú Quốc với các bãi biển đẹp, hoạt động thú vị và ẩm thực hải sản tươi ngon.',\n    duration: 4,\n    image: 'https://images.pexels.com/photos/1174732/pexels-photo-1174732.jpeg?auto=compress&cs=tinysrgb&w=600',\n    tags: ['PhuQuoc', 'Bien', 'DaoNgoc'],\n    days: [\n      {\n        id: 'day-1',\n        date: null,\n        activities: [\n          {\n            id: 'activity-1',\n            time: '08:00',\n            title: 'Ăn sáng tại khách sạn',\n            description: 'Thưởng thức bữa sáng tại khách sạn để chuẩn bị cho ngày khám phá đầu tiên.',\n            location: 'Khách sạn'\n          ,\n            mainLocation: 'Khách sạn'\n          },\n          {\n            id: 'activity-2',\n            time: '09:30',\n            title: 'Tham quan Bãi Sao',\n            description: 'Tắm biển và thư giãn tại một trong những bãi biển đẹp nhất Phú Quốc với cát trắng mịn và nước biển trong xanh.',\n            location: 'Bãi Sao, Phú Quốc'\n          ,\n            mainLocation: 'Bãi Sao'\n          },\n          {\n            id: 'activity-3',\n            time: '12:30',\n            title: 'Ăn trưa',\n            description: 'Thưởng thức hải sản tươi sống tại nhà hàng ven biển.',\n            location: 'Nhà hàng Bãi Sao'\n          ,\n            mainLocation: 'Nhà hàng Bãi Sao'\n          },\n          {\n            id: 'activity-4',\n            time: '14:30',\n            title: 'Tham quan Nhà thùng nước mắm',\n            description: 'Tìm hiểu về quy trình sản xuất nước mắm truyền thống của Phú Quốc.',\n            location: 'Nhà thùng nước mắm Khải Hoàn'\n          ,\n            mainLocation: 'Nhà thùng nước mắm Khải Hoàn'\n          },\n          {\n            id: 'activity-5',\n            time: '16:30',\n            title: 'Tham quan Dinh Cậu',\n            description: 'Tham quan ngôi đền linh thiêng nằm trên những tảng đá lớn nhô ra biển.',\n            location: 'Dinh Cậu, Phú Quốc'\n          ,\n            mainLocation: 'Dinh Cậu'\n          },\n          {\n            id: 'activity-6',\n            time: '18:30',\n            title: 'Ăn tối tại chợ đêm Dinh Cậu',\n            description: 'Thưởng thức các món hải sản tươi ngon và đặc sản địa phương.',\n            location: 'Chợ đêm Dinh Cậu'\n          ,\n            mainLocation: 'Chợ đêm Dinh Cậu'\n          }\n        ]\n      },\n      {\n        id: 'day-2',\n        date: null,\n        activities: [\n          {\n            id: 'activity-7',\n            time: '07:30',\n            title: 'Ăn sáng',\n            description: 'Thưởng thức bún quậy - đặc sản Phú Quốc.',\n            location: 'Bún quậy Kiến Xây'\n          ,\n            mainLocation: 'Bún quậy Kiến Xây'\n          },\n          {\n            id: 'activity-8',\n            time: '09:00',\n            title: 'Tour câu cá & lặn ngắm san hô',\n            description: 'Tham gia tour câu cá và lặn ngắm san hô tại các hòn đảo phía Nam Phú Quốc.',\n            location: 'Cảng An Thới'\n          ,\n            mainLocation: 'Cảng An Thới'\n          },\n          {\n            id: 'activity-9',\n            time: '12:00',\n            title: 'Ăn trưa trên tàu',\n            description: 'Thưởng thức bữa trưa với hải sản tươi ngon được chế biến ngay trên tàu.',\n            location: 'Trên tàu'\n          ,\n            mainLocation: 'Trên tàu'\n          },\n          {\n            id: 'activity-10',\n            time: '15:00',\n            title: 'Tham quan Hòn Thơm',\n            description: 'Đi cáp treo vượt biển dài nhất thế giới và tham gia các hoạt động giải trí tại Hòn Thơm.',\n            location: 'Hòn Thơm, Phú Quốc'\n          ,\n            mainLocation: 'Hòn Thơm'\n          },\n          {\n            id: 'activity-11',\n            time: '18:30',\n            title: 'Ăn tối',\n            description: 'Thưởng thức hải sản tươi ngon tại nhà hàng địa phương.',\n            location: 'Nhà hàng Biển Xanh'\n          ,\n            mainLocation: 'Nhà hàng Biển Xanh'\n          },\n          {\n            id: 'activity-12',\n            time: '20:30',\n            title: 'Dạo biển đêm',\n            description: 'Dạo bộ dọc bãi biển và thưởng thức không khí trong lành của biển đêm.',\n            location: 'Bãi biển Dương Đông'\n          ,\n            mainLocation: 'Bãi biển Dương Đông'\n          }\n        ]\n      },\n      {\n        id: 'day-3',\n        date: null,\n        activities: [\n          {\n            id: 'activity-13',\n            time: '08:00',\n            title: 'Ăn sáng',\n            description: 'Thưởng thức bánh canh ghẹ - đặc sản Phú Quốc.',\n            location: 'Quán bánh canh ghẹ Phú Quốc'\n          ,\n            mainLocation: 'Quán bánh canh ghẹ Phú Quốc'\n          },\n          {\n            id: 'activity-14',\n            time: '09:30',\n            title: 'Tham quan Vinpearl Safari',\n            description: 'Khám phá vườn thú bán hoang dã đầu tiên của Việt Nam với nhiều loài động vật quý hiếm.',\n            location: 'Vinpearl Safari, Phú Quốc'\n          ,\n            mainLocation: 'Vinpearl Safari'\n          },\n          {\n            id: 'activity-15',\n            time: '12:30',\n            title: 'Ăn trưa',\n            description: 'Thưởng thức bữa trưa tại nhà hàng trong khu Safari.',\n            location: 'Nhà hàng Vinpearl Safari'\n          ,\n            mainLocation: 'Nhà hàng Vinpearl Safari'\n          },\n          {\n            id: 'activity-16',\n            time: '14:30',\n            title: 'Tham quan Vườn tiêu Phú Quốc',\n            description: 'Tìm hiểu về quy trình trồng và chế biến tiêu Phú Quốc nổi tiếng.',\n            location: 'Vườn tiêu Phú Quốc'\n          ,\n            mainLocation: 'Vườn tiêu Phú Quốc'\n          },\n          {\n            id: 'activity-17',\n            time: '16:30',\n            title: 'Tham quan Suối Tranh',\n            description: 'Tham quan và tắm suối tại một trong những thác nước đẹp nhất Phú Quốc.',\n            location: 'Suối Tranh, Phú Quốc'\n          ,\n            mainLocation: 'Suối Tranh'\n          },\n          {\n            id: 'activity-18',\n            time: '19:00',\n            title: 'Ăn tối',\n            description: 'Thưởng thức các món đặc sản Phú Quốc như gỏi cá trích, nhum biển, ghẹ hấp.',\n            location: 'Nhà hàng Quán Ăn Ngon'\n          ,\n            mainLocation: 'Nhà hàng Quán Ăn Ngon'\n          }\n        ]\n      },\n      {\n        id: 'day-4',\n        date: null,\n        activities: [\n          {\n            id: 'activity-19',\n            time: '08:00',\n            title: 'Ăn sáng',\n            description: 'Thưởng thức bữa sáng tại khách sạn.',\n            location: 'Khách sạn'\n          ,\n            mainLocation: 'Khách sạn'\n          },\n          {\n            id: 'activity-20',\n            time: '09:30',\n            title: 'Tham quan Làng chài Hàm Ninh',\n            description: 'Tham quan làng chài cổ và thưởng thức hải sản tươi sống.',\n            location: 'Làng chài Hàm Ninh'\n          ,\n            mainLocation: 'Làng chài Hàm Ninh'\n          },\n          {\n            id: 'activity-21',\n            time: '11:30',\n            title: 'Mua sắm đặc sản',\n            description: 'Mua sắm các đặc sản Phú Quốc như nước mắm, tiêu, rượu sim, mật ong.',\n            location: 'Chợ Dương Đông'\n          ,\n            mainLocation: 'Chợ Dương Đông'\n          },\n          {\n            id: 'activity-22',\n            time: '13:00',\n            title: 'Ăn trưa',\n            description: 'Thưởng thức bữa trưa với các món đặc sản địa phương.',\n            location: 'Nhà hàng Vườn Táo'\n          ,\n            mainLocation: 'Nhà hàng Vườn Táo'\n          },\n          {\n            id: 'activity-23',\n            time: '15:00',\n            title: 'Tham quan Bãi Dài',\n            description: 'Tắm biển và thư giãn tại bãi biển hoang sơ với cát trắng mịn và nước biển trong xanh.',\n            location: 'Bãi Dài, Phú Quốc'\n          ,\n            mainLocation: 'Bãi Dài'\n          },\n          {\n            id: 'activity-24',\n            time: '18:00',\n            title: 'Ăn tối chia tay',\n            description: 'Thưởng thức bữa tối hải sản tươi ngon để kết thúc chuyến đi.',\n            location: 'Nhà hàng Biển Xanh'\n          ,\n            mainLocation: 'Nhà hàng Biển Xanh'\n          }\n        ]\n      }\n    ],\n    authorId: '3',\n    authorName: 'Lê Hoàng',\n    isPublic: true,\n    rating: 4.9,\n    usageCount: 987\n  },\n  {\n    id: 'template-sapa',\n    name: 'Khám phá Sapa',\n    destination: 'Sapa, Lào Cai',\n    region: 'Miền Bắc',\n    description: 'Kế hoạch 3 ngày khám phá Sapa với những ruộng bậc thang tuyệt đẹp, văn hóa dân tộc đặc sắc và chinh phục đỉnh Fansipan.',\n    duration: 3,\n    image: 'https://images.pexels.com/photos/4350383/pexels-photo-4350383.jpeg?auto=compress&cs=tinysrgb&w=600',\n    tags: ['Sapa', 'RuongBacThang', 'Fansipan'],\n    days: [\n      {\n        id: 'day-1',\n        date: null,\n        activities: [\n          {\n            id: 'activity-1',\n            time: '08:00',\n            title: 'Ăn sáng',\n            description: 'Thưởng thức bữa sáng tại khách sạn với các món đặc sản vùng núi.',\n            location: 'Khách sạn'\n          ,\n            mainLocation: 'Khách sạn'\n          },\n          {\n            id: 'activity-2',\n            time: '09:30',\n            title: 'Tham quan thị trấn Sapa',\n            description: 'Dạo quanh thị trấn, tham quan nhà thờ đá cổ và quảng trường trung tâm.',\n            location: 'Thị trấn Sapa'\n          ,\n            mainLocation: 'Thị trấn Sapa'\n          },\n          {\n            id: 'activity-3',\n            time: '11:30',\n            title: 'Tham quan Cổng Trời',\n            description: 'Ngắm nhìn toàn cảnh Sapa từ trên cao với khung cảnh hùng vĩ.',\n            location: 'Cổng Trời, Sapa'\n          ,\n            mainLocation: 'Cổng Trời'\n          },\n          {\n            id: 'activity-4',\n            time: '13:00',\n            title: 'Ăn trưa',\n            description: 'Thưởng thức các món đặc sản vùng núi như thắng cố, cá suối, rau rừng.',\n            location: 'Nhà hàng A Quỳnh'\n          ,\n            mainLocation: 'Nhà hàng A Quỳnh'\n          },\n          {\n            id: 'activity-5',\n            time: '14:30',\n            title: 'Tham quan bản Cát Cát',\n            description: 'Khám phá đời sống và văn hóa của người H\\'Mông tại bản làng cổ.',\n            location: 'Bản Cát Cát',\n            mainLocation: 'Bản Cát Cát'\n          },\n          {\n            id: 'activity-6',\n            time: '17:30',\n            title: 'Tham quan thác Cát Cát',\n            description: 'Ngắm nhìn thác nước tuyệt đẹp và các công trình thủy điện cổ.',\n            location: 'Thác Cát Cát'\n          ,\n            mainLocation: 'Thác Cát Cát'\n          },\n          {\n            id: 'activity-7',\n            time: '19:00',\n            title: 'Ăn tối',\n            description: 'Thưởng thức các món lẩu và nướng đặc trưng vùng núi Tây Bắc.',\n            location: 'Nhà hàng Sapa Moment'\n          ,\n            mainLocation: 'Nhà hàng Sapa Moment'\n          }\n        ]\n      },\n      {\n        id: 'day-2',\n        date: null,\n        activities: [\n          {\n            id: 'activity-8',\n            time: '07:00',\n            title: 'Ăn sáng',\n            description: 'Thưởng thức bữa sáng tại khách sạn.',\n            location: 'Khách sạn'\n          ,\n            mainLocation: 'Khách sạn'\n          },\n          {\n            id: 'activity-9',\n            time: '08:30',\n            title: 'Chinh phục đỉnh Fansipan',\n            description: 'Đi cáp treo lên đỉnh Fansipan - \"Nóc nhà Đông Dương\" và tham quan quần thể tâm linh.',\n            location: 'Đỉnh Fansipan'\n          ,\n            mainLocation: 'Đỉnh Fansipan'\n          },\n          {\n            id: 'activity-10',\n            time: '12:30',\n            title: 'Ăn trưa',\n            description: 'Thưởng thức bữa trưa tại nhà hàng trên đỉnh Fansipan.',\n            location: 'Nhà hàng Fansipan'\n          ,\n            mainLocation: 'Nhà hàng Fansipan'\n          },\n          {\n            id: 'activity-11',\n            time: '14:00',\n            title: 'Tham quan Vườn hoa Hàm Rồng',\n            description: 'Khám phá khu vườn với nhiều loài hoa đẹp và ngắm toàn cảnh Sapa.',\n            location: 'Vườn hoa Hàm Rồng'\n          ,\n            mainLocation: 'Vườn hoa Hàm Rồng'\n          },\n          {\n            id: 'activity-12',\n            time: '17:00',\n            title: 'Mua sắm tại chợ Sapa',\n            description: 'Mua sắm các sản phẩm thổ cẩm, đồ lưu niệm và đặc sản địa phương.',\n            location: 'Chợ Sapa'\n          ,\n            mainLocation: 'Chợ Sapa'\n          },\n          {\n            id: 'activity-13',\n            time: '19:00',\n            title: 'Ăn tối và xem biểu diễn văn nghệ',\n            description: 'Thưởng thức bữa tối kèm chương trình biểu diễn văn nghệ dân tộc.',\n            location: 'Nhà hàng Anh Dũng'\n          ,\n            mainLocation: 'Nhà hàng Anh Dũng'\n          }\n        ]\n      },\n      {\n        id: 'day-3',\n        date: null,\n        activities: [\n          {\n            id: 'activity-14',\n            time: '07:30',\n            title: 'Ăn sáng',\n            description: 'Thưởng thức bữa sáng tại khách sạn.',\n            location: 'Khách sạn'\n          ,\n            mainLocation: 'Khách sạn'\n          },\n          {\n            id: 'activity-15',\n            time: '08:30',\n            title: 'Tham quan ruộng bậc thang Lao Chải - Tả Van',\n            description: 'Đi bộ trekking và ngắm nhìn những thửa ruộng bậc thang tuyệt đẹp.',\n            location: 'Lao Chải - Tả Van'\n          ,\n            mainLocation: 'Lao Chải - Tả Van'\n          },\n          {\n            id: 'activity-16',\n            time: '12:00',\n            title: 'Ăn trưa tại bản làng',\n            description: 'Thưởng thức bữa trưa đơn giản tại nhà người dân địa phương.',\n            location: 'Bản Tả Van'\n          ,\n            mainLocation: 'Bản Tả Van'\n          },\n          {\n            id: 'activity-17',\n            time: '13:30',\n            title: 'Tham quan bản Tả Phìn',\n            description: 'Khám phá văn hóa của người Dao Đỏ và tham quan các xưởng thổ cẩm.',\n            location: 'Bản Tả Phìn'\n          ,\n            mainLocation: 'Bản Tả Phìn'\n          },\n          {\n            id: 'activity-18',\n            time: '16:00',\n            title: 'Tắm lá thuốc người Dao',\n            description: 'Trải nghiệm tắm lá thuốc truyền thống của người Dao để thư giãn.',\n            location: 'Bản Tả Phìn'\n          ,\n            mainLocation: 'Bản Tả Phìn'\n          },\n          {\n            id: 'activity-19',\n            time: '18:30',\n            title: 'Ăn tối chia tay',\n            description: 'Thưởng thức bữa tối với các món đặc sản vùng núi để kết thúc chuyến đi.',\n            location: 'Nhà hàng Sapa Moment'\n          ,\n            mainLocation: 'Nhà hàng Sapa Moment'\n          }\n        ]\n      }\n    ],\n    authorId: '2',\n    authorName: 'Trần Thu Hà',\n    isPublic: true,\n    rating: 4.7,\n    usageCount: 856\n  },\n  // Hạ Long template\n  {\n    id: 'template-halong',\n    name: 'Khám phá Vịnh Hạ Long',\n    destination: 'Hạ Long, Quảng Ninh',\n    region: 'Miền Bắc',\n    description: 'Kế hoạch 3 ngày khám phá kỳ quan thiên nhiên thế giới Vịnh Hạ Long với tour du thuyền, khám phá hang động và các hoạt động thú vị trên biển.',\n    duration: 3,\n    image: 'https://images.pexels.com/photos/2132180/pexels-photo-2132180.jpeg?auto=compress&cs=tinysrgb&w=600',\n    tags: ['HaLong', 'DiSanThienNhien', 'KyQuan'],\n    days: [\n      {\n        id: 'day-1',\n        date: null,\n        activities: [\n          {\n            id: 'activity-1',\n            time: '08:00',\n            title: 'Khởi hành từ Hà Nội',\n            description: 'Di chuyển từ Hà Nội đến Hạ Long bằng xe limousine.',\n            location: 'Hà Nội'\n          ,\n            mainLocation: 'Hà Nội'\n          },\n          {\n            id: 'activity-2',\n            time: '12:00',\n            title: 'Đến cảng Tuần Châu',\n            description: 'Làm thủ tục lên du thuyền và nhận phòng.',\n            location: 'Cảng Tuần Châu, Hạ Long'\n          ,\n            mainLocation: 'Cảng Tuần Châu'\n          },\n          {\n            id: 'activity-3',\n            time: '13:00',\n            title: 'Ăn trưa trên du thuyền',\n            description: 'Thưởng thức bữa trưa hải sản tươi ngon trong khi du thuyền bắt đầu hành trình.',\n            location: 'Du thuyền'\n          ,\n            mainLocation: 'Du thuyền'\n          },\n          {\n            id: 'activity-4',\n            time: '15:00',\n            title: 'Tham quan hang Sửng Sốt',\n            description: 'Khám phá một trong những hang động đẹp nhất Vịnh Hạ Long với nhiều nhũ đá có hình thù độc đáo.',\n            location: 'Hang Sửng Sốt'\n          ,\n            mainLocation: 'Hang Sửng Sốt'\n          },\n          {\n            id: 'activity-5',\n            time: '17:00',\n            title: 'Chèo thuyền kayak',\n            description: 'Trải nghiệm chèo thuyền kayak quanh các đảo đá và khám phá vẻ đẹp của vịnh.',\n            location: 'Vịnh Hạ Long'\n          ,\n            mainLocation: 'Vịnh Hạ Long'\n          },\n          {\n            id: 'activity-6',\n            time: '19:00',\n            title: 'Ăn tối trên du thuyền',\n            description: 'Thưởng thức bữa tối hải sản tươi ngon với view hoàng hôn tuyệt đẹp.',\n            location: 'Du thuyền'\n          ,\n            mainLocation: 'Du thuyền'\n          },\n          {\n            id: 'activity-7',\n            time: '20:30',\n            title: 'Câu mực đêm',\n            description: 'Tham gia hoạt động câu mực đêm thú vị hoặc thư giãn trên boong tàu ngắm sao.',\n            location: 'Du thuyền'\n          ,\n            mainLocation: 'Du thuyền'\n          }\n        ]\n      },\n      {\n        id: 'day-2',\n        date: null,\n        activities: [\n          {\n            id: 'activity-8',\n            time: '06:00',\n            title: 'Ngắm bình minh và tập Tai Chi',\n            description: 'Dậy sớm ngắm bình minh trên vịnh và tham gia lớp Tai Chi buổi sáng.',\n            location: 'Du thuyền'\n          ,\n            mainLocation: 'Du thuyền'\n          },\n          {\n            id: 'activity-9',\n            time: '07:30',\n            title: 'Ăn sáng',\n            description: 'Thưởng thức bữa sáng trên du thuyền.',\n            location: 'Du thuyền'\n          ,\n            mainLocation: 'Du thuyền'\n          },\n          {\n            id: 'activity-10',\n            time: '09:00',\n            title: 'Tham quan hang Tiên Ông',\n            description: 'Khám phá hang động với nhiều truyền thuyết thú vị và hệ thống nhũ đá đẹp mắt.',\n            location: 'Hang Tiên Ông'\n          ,\n            mainLocation: 'Hang Tiên Ông'\n          },\n          {\n            id: 'activity-11',\n            time: '11:30',\n            title: 'Tham quan làng chài Cửa Vạn',\n            description: 'Tìm hiểu về cuộc sống của ngư dân trên làng nổi giữa vịnh Hạ Long.',\n            location: 'Làng chài Cửa Vạn'\n          ,\n            mainLocation: 'Làng chài Cửa Vạn'\n          },\n          {\n            id: 'activity-12',\n            time: '13:00',\n            title: 'Ăn trưa trên du thuyền',\n            description: 'Thưởng thức bữa trưa hải sản tươi ngon trong khi du thuyền di chuyển.',\n            location: 'Du thuyền'\n          ,\n            mainLocation: 'Du thuyền'\n          },\n          {\n            id: 'activity-13',\n            time: '15:00',\n            title: 'Tắm biển tại Bãi Tắm Ti Tốp',\n            description: 'Tắm biển và thư giãn tại bãi biển đẹp hoặc leo lên đỉnh núi Ti Tốp để ngắm toàn cảnh vịnh.',\n            location: 'Đảo Ti Tốp'\n          ,\n            mainLocation: 'Đảo Ti Tốp'\n          },\n          {\n            id: 'activity-14',\n            time: '18:00',\n            title: 'Ăn tối trên du thuyền',\n            description: 'Thưởng thức bữa tối với các món hải sản tươi ngon.',\n            location: 'Du thuyền'\n          ,\n            mainLocation: 'Du thuyền'\n          },\n          {\n            id: 'activity-15',\n            time: '20:00',\n            title: 'Hoạt động buổi tối',\n            description: 'Tham gia các hoạt động giải trí trên du thuyền như xem phim, hát karaoke hoặc thư giãn ngắm sao.',\n            location: 'Du thuyền'\n          ,\n            mainLocation: 'Du thuyền'\n          }\n        ]\n      },\n      {\n        id: 'day-3',\n        date: null,\n        activities: [\n          {\n            id: 'activity-16',\n            time: '07:00',\n            title: 'Ăn sáng',\n            description: 'Thưởng thức bữa sáng nhẹ trên du thuyền.',\n            location: 'Du thuyền'\n          ,\n            mainLocation: 'Du thuyền'\n          },\n          {\n            id: 'activity-17',\n            time: '08:30',\n            title: 'Tham quan hang Luồn',\n            description: 'Chèo thuyền kayak qua hang Luồn để khám phá vùng nước yên bình bên trong.',\n            location: 'Hang Luồn'\n          ,\n            mainLocation: 'Hang Luồn'\n          },\n          {\n            id: 'activity-18',\n            time: '10:30',\n            title: 'Lớp học nấu ăn',\n            description: 'Tham gia lớp học nấu các món ăn truyền thống Việt Nam trên du thuyền.',\n            location: 'Du thuyền'\n          ,\n            mainLocation: 'Du thuyền'\n          },\n          {\n            id: 'activity-19',\n            time: '12:00',\n            title: 'Ăn trưa và trả phòng',\n            description: 'Thưởng thức bữa trưa nhẹ trên du thuyền và làm thủ tục trả phòng.',\n            location: 'Du thuyền'\n          ,\n            mainLocation: 'Du thuyền'\n          },\n          {\n            id: 'activity-20',\n            time: '13:30',\n            title: 'Về đến cảng Tuần Châu',\n            description: 'Kết thúc hành trình du thuyền và quay về đất liền.',\n            location: 'Cảng Tuần Châu'\n          ,\n            mainLocation: 'Cảng Tuần Châu'\n          },\n          {\n            id: 'activity-21',\n            time: '14:30',\n            title: 'Tham quan Bảo tàng Quảng Ninh',\n            description: 'Tìm hiểu về lịch sử, văn hóa và địa chất của vùng đất Quảng Ninh.',\n            location: 'Bảo tàng Quảng Ninh'\n          ,\n            mainLocation: 'Bảo tàng Quảng Ninh'\n          },\n          {\n            id: 'activity-22',\n            time: '16:30',\n            title: 'Mua sắm đặc sản',\n            description: 'Mua sắm các đặc sản Quảng Ninh như hải sản khô, chả mực, ngọc trai.',\n            location: 'Chợ Hạ Long'\n          ,\n            mainLocation: 'Chợ Hạ Long'\n          },\n          {\n            id: 'activity-23',\n            time: '18:00',\n            title: 'Ăn tối chia tay',\n            description: 'Thưởng thức bữa tối hải sản tươi ngon để kết thúc chuyến đi.',\n            location: 'Nhà hàng Hương Biển'\n          ,\n            mainLocation: 'Nhà hàng Hương Biển'\n          },\n          {\n            id: 'activity-24',\n            time: '20:00',\n            title: 'Khởi hành về Hà Nội',\n            description: 'Di chuyển từ Hạ Long về Hà Nội bằng xe limousine.',\n            location: 'Hạ Long'\n          ,\n            mainLocation: 'Hạ Long'\n          }\n        ]\n      }\n    ],\n    authorId: '2',\n    authorName: 'Trần Hà',\n    isPublic: true,\n    rating: 4.9,\n    usageCount: 1356\n  },\n  // Hội An template\n  {\n    id: 'template-hoian',\n    name: 'Khám phá Hội An',\n    destination: 'Hội An, Quảng Nam',\n    region: 'Miền Trung',\n    description: 'Kế hoạch 3 ngày khám phá phố cổ Hội An - Di sản văn hóa thế giới với kiến trúc cổ, ẩm thực đặc sắc và các làng nghề truyền thống.',\n    duration: 3,\n    image: 'https://images.pexels.com/photos/5191371/pexels-photo-5191371.jpeg?auto=compress&cs=tinysrgb&w=600',\n    tags: ['HoiAn', 'PhoCo', 'DiSanVanHoa'],\n    days: [\n      {\n        id: 'day-1',\n        date: null,\n        activities: [\n          {\n            id: 'activity-1',\n            time: '08:00',\n            title: 'Ăn sáng',\n            description: 'Thưởng thức bữa sáng với món Cao lầu - đặc sản Hội An.',\n            location: 'Quán Cao lầu Bà Bé, 16 Thái Phiên'\n          ,\n            mainLocation: 'Quán Cao lầu Bà Bé'\n          },\n          {\n            id: 'activity-2',\n            time: '09:30',\n            title: 'Tham quan phố cổ Hội An',\n            description: 'Dạo bộ tham quan các điểm di tích nổi tiếng trong phố cổ như Chùa Cầu, Hội quán Phúc Kiến, nhà cổ Tấn Ký.',\n            location: 'Phố cổ Hội An'\n          ,\n            mainLocation: 'Phố cổ Hội An'\n          },\n          {\n            id: 'activity-3',\n            time: '12:00',\n            title: 'Ăn trưa',\n            description: 'Thưởng thức các món đặc sản Hội An như Cao lầu, Hoành thánh.',\n            location: 'Nhà hàng Morning Glory, 106 Nguyễn Thái Học'\n          ,\n            mainLocation: 'Nhà hàng Morning Glory'\n          },\n          {\n            id: 'activity-4',\n            time: '14:00',\n            title: 'Tham quan làng dệt lụa Tăng Thanh Hà',\n            description: 'Tìm hiểu về nghề dệt lụa truyền thống và mua sắm các sản phẩm lụa chất lượng cao.',\n            location: 'Làng lụa Tăng Thanh Hà'\n          ,\n            mainLocation: 'Làng lụa Tăng Thanh Hà'\n          },\n          {\n            id: 'activity-5',\n            time: '16:30',\n            title: 'Tham quan Bảo tàng Văn hóa dân gian Hội An',\n            description: 'Tìm hiểu về lịch sử và văn hóa của Hội An qua các hiện vật và tài liệu quý.',\n            location: 'Bảo tàng Văn hóa dân gian Hội An'\n          ,\n            mainLocation: 'Bảo tàng Văn hóa dân gian Hội An'\n          },\n          {\n            id: 'activity-6',\n            time: '18:00',\n            title: 'Ngắm hoàng hôn trên sông Hoài',\n            description: 'Dạo bộ dọc bờ sông Hoài, thả đèn hoa đăng và ngắm cảnh hoàng hôn tuyệt đẹp.',\n            location: 'Sông Hoài'\n          ,\n            mainLocation: 'Sông Hoài'\n          },\n          {\n            id: 'activity-7',\n            time: '19:30',\n            title: 'Ăn tối và xem biểu diễn nhạc truyền thống',\n            description: 'Thưởng thức bữa tối với các món đặc sản và xem biểu diễn nhạc truyền thống.',\n            location: 'Nhà hàng Cargo Club, 107-109 Nguyễn Thái Học'\n          ,\n            mainLocation: 'Nhà hàng Cargo Club'\n          },\n          {\n            id: 'activity-8',\n            time: '21:00',\n            title: 'Khám phá phố cổ về đêm',\n            description: 'Dạo bộ trong phố cổ về đêm với ánh đèn lồng và không khí lãng mạn.',\n            location: 'Phố cổ Hội An'\n          ,\n            mainLocation: 'Phố cổ Hội An'\n          }\n        ]\n      },\n      {\n        id: 'day-2',\n        date: null,\n        activities: [\n          {\n            id: 'activity-9',\n            time: '07:30',\n            title: 'Ăn sáng',\n            description: 'Thưởng thức mì Quảng - đặc sản của vùng Quảng Nam.',\n            location: 'Mì Quảng Bà Mua, 20 Trần Phú'\n          ,\n            mainLocation: 'Mì Quảng Bà Mua'\n          },\n          {\n            id: 'activity-10',\n            time: '09:00',\n            title: 'Tham quan làng gốm Thanh Hà',\n            description: 'Tìm hiểu về nghề làm gốm truyền thống và trải nghiệm làm gốm.',\n            location: 'Làng gốm Thanh Hà'\n          ,\n            mainLocation: 'Làng gốm Thanh Hà'\n          },\n          {\n            id: 'activity-11',\n            time: '11:30',\n            title: 'Tham quan rừng dừa Bảy Mẫu',\n            description: 'Chèo thuyền thùng qua rừng dừa Bảy Mẫu và trải nghiệm cuộc sống của ngư dân địa phương.',\n            location: 'Rừng dừa Bảy Mẫu'\n          ,\n            mainLocation: 'Rừng dừa Bảy Mẫu'\n          },\n          {\n            id: 'activity-12',\n            time: '13:00',\n            title: 'Ăn trưa',\n            description: 'Thưởng thức bữa trưa với các món hải sản tươi ngon.',\n            location: 'Nhà hàng Bé Thân, Cẩm An'\n          ,\n            mainLocation: 'Nhà hàng Bé Thân'\n          },\n          {\n            id: 'activity-13',\n            time: '15:00',\n            title: 'Tắm biển An Bàng',\n            description: 'Thư giãn và tắm biển tại bãi biển An Bàng - một trong những bãi biển đẹp nhất Việt Nam.',\n            location: 'Bãi biển An Bàng'\n          ,\n            mainLocation: 'Bãi biển An Bàng'\n          },\n          {\n            id: 'activity-14',\n            time: '18:00',\n            title: 'Lớp học nấu ăn',\n            description: 'Tham gia lớp học nấu các món ăn đặc trưng của Hội An.',\n            location: 'Trường dạy nấu ăn Red Bridge'\n          ,\n            mainLocation: 'Trường dạy nấu ăn Red Bridge'\n          },\n          {\n            id: 'activity-15',\n            time: '20:00',\n            title: 'Ăn tối',\n            description: 'Thưởng thức bữa tối với các món ăn tự tay mình nấu.',\n            location: 'Trường dạy nấu ăn Red Bridge'\n          ,\n            mainLocation: 'Trường dạy nấu ăn Red Bridge'\n          }\n        ]\n      },\n      {\n        id: 'day-3',\n        date: null,\n        activities: [\n          {\n            id: 'activity-16',\n            time: '07:00',\n            title: 'Ăn sáng',\n            description: 'Thưởng thức bánh vạc (White Rose) - đặc sản Hội An.',\n            location: 'Nhà hàng White Rose, 533 Hai Bà Trưng'\n          ,\n            mainLocation: 'Nhà hàng White Rose'\n          },\n          {\n            id: 'activity-17',\n            time: '08:30',\n            title: 'Tham quan Thánh địa Mỹ Sơn',\n            description: 'Khám phá khu di tích đền tháp Chăm Pa - Di sản văn hóa thế giới.',\n            location: 'Thánh địa Mỹ Sơn'\n          ,\n            mainLocation: 'Thánh địa Mỹ Sơn'\n          },\n          {\n            id: 'activity-18',\n            time: '12:00',\n            title: 'Ăn trưa',\n            description: 'Thưởng thức bữa trưa với các món ăn đặc sản địa phương.',\n            location: 'Nhà hàng gần Thánh địa Mỹ Sơn'\n          ,\n            mainLocation: 'Nhà hàng gần Thánh địa Mỹ Sơn'\n          },\n          {\n            id: 'activity-19',\n            time: '14:00',\n            title: 'Tham quan làng rau Trà Quế',\n            description: 'Tham quan làng rau hữu cơ nổi tiếng và tìm hiểu về kỹ thuật canh tác truyền thống.',\n            location: 'Làng rau Trà Quế'\n          ,\n            mainLocation: 'Làng rau Trà Quế'\n          },\n          {\n            id: 'activity-20',\n            time: '16:00',\n            title: 'Mua sắm đồ lưu niệm',\n            description: 'Mua sắm các sản phẩm thủ công mỹ nghệ, quần áo may đo và các đặc sản địa phương.',\n            location: 'Chợ Hội An'\n          ,\n            mainLocation: 'Chợ Hội An'\n          },\n          {\n            id: 'activity-21',\n            time: '18:00',\n            title: 'Tham dự đêm Hội An - Đêm rằm tháng 7 âm lịch',\n            description: 'Trải nghiệm lễ hội truyền thống của Hội An với đèn lồng, ánh nến và các hoạt động văn hóa.',\n            location: 'Phố cổ Hội An'\n          ,\n            mainLocation: 'Phố cổ Hội An'\n          },\n          {\n            id: 'activity-22',\n            time: '20:00',\n            title: 'Ăn tối chia tay',\n            description: 'Thưởng thức bữa tối với các món đặc sản Hội An để kết thúc chuyến đi.',\n            location: 'Nhà hàng Mango Mango, 45 Nguyễn Phúc Chu'\n          ,\n            mainLocation: 'Nhà hàng Mango Mango'\n          }\n        ]\n      }\n    ],\n    authorId: '1',\n    authorName: 'Nguyễn Minh',\n    isPublic: true,\n    rating: 4.8,\n    usageCount: 1024\n  }\n];\n"], "names": [], "mappings": "AAAA,sCAAsC;;;;AA+C/B,MAAM,wBAA8C;IACzD;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,aAAa;QACb,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAS;YAAmB;SAAU;QAC7C,MAAM;YACJ;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;iBACD;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;iBACD;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;iBACD;YACH;SACD;QACD,UAAU;QACV,YAAY;QACZ,UAAU;QACV,QAAQ;QACR,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,aAAa;QACb,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAW;YAAQ;SAAU;QACpC,MAAM;YACJ;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;iBACD;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;iBACD;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;iBACD;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;iBACD;YACH;SACD;QACD,UAAU;QACV,YAAY;QACZ,UAAU;QACV,QAAQ;QACR,YAAY;IACd;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,aAAa;QACb,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAQ;YAAiB;SAAW;QAC3C,MAAM;YACJ;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;iBACD;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;iBACD;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;iBACD;YACH;SACD;QACD,UAAU;QACV,YAAY;QACZ,UAAU;QACV,QAAQ;QACR,YAAY;IACd;IACA,mBAAmB;IACnB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,aAAa;QACb,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAU;YAAmB;SAAS;QAC7C,MAAM;YACJ;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;iBACD;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;iBACD;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;iBACD;YACH;SACD;QACD,UAAU;QACV,YAAY;QACZ,UAAU;QACV,QAAQ;QACR,YAAY;IACd;IACA,kBAAkB;IAClB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,aAAa;QACb,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAS;YAAS;SAAc;QACvC,MAAM;YACJ;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;iBACD;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;iBACD;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBAEV,cAAc;oBAChB;iBACD;YACH;SACD;QACD,UAAU;QACV,YAAY;QACZ,UAAU;QACV,QAAQ;QACR,YAAY;IACd;CACD", "debugId": null}}, {"offset": {"line": 3902, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/trips/member-list-dialog.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { TripMember } from './mock-trip-groups';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/radix-ui/avatar';\nimport { Badge } from '@/components/ui/radix-ui/badge';\nimport { Button } from '@/components/ui/radix-ui/button';\nimport { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/radix-ui/dialog';\nimport { ScrollArea } from '@/components/ui/radix-ui/scroll-area';\nimport { Search, UserPlus, X } from 'lucide-react';\nimport { Input } from '@/components/ui/radix-ui/input';\n\ntype MemberListDialogProps = {\n  members: TripMember[];\n  maxMembers: number;\n  isOpen: boolean;\n  onClose: () => void;\n  onInvite: () => void;\n};\n\nexport function MemberListDialog({ members, maxMembers, isOpen, onClose, onInvite }: MemberListDialogProps) {\n  const [searchQuery, setSearchQuery] = useState('');\n\n  const filteredMembers = searchQuery\n    ? members.filter(member =>\n        member.name.toLowerCase().includes(searchQuery.toLowerCase())\n      )\n    : members;\n\n  return (\n    <Dialog open={isOpen} onOpenChange={onClose}>\n      <DialogContent className=\"max-w-md\">\n        <DialogHeader>\n          <DialogTitle className=\"text-xl flex items-center gap-2\">\n            Danh sách thành viên\n            <Badge variant=\"outline\" className=\"ml-2\">\n              {members.length}/{maxMembers}\n            </Badge>\n          </DialogTitle>\n        </DialogHeader>\n\n        <div className=\"relative mb-4\">\n          <Search className=\"absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground\" />\n          <Input\n            placeholder=\"Tìm kiếm thành viên...\"\n            className=\"pl-9\"\n            value={searchQuery}\n            onChange={(e) => setSearchQuery(e.target.value)}\n          />\n          {searchQuery && (\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              className=\"absolute right-1 top-1 h-7 w-7\"\n              onClick={() => setSearchQuery('')}\n            >\n              <X className=\"h-4 w-4\" />\n            </Button>\n          )}\n        </div>\n\n        <ScrollArea className=\"h-[300px] pr-4\">\n          <div className=\"space-y-2\">\n            {filteredMembers.map((member) => (\n              <div\n                key={member.id}\n                className=\"flex items-center justify-between p-2 rounded-md hover:bg-secondary/50 transition-colors\"\n              >\n                <div className=\"flex items-center gap-3\">\n                  <Avatar className=\"h-10 w-10 border border-purple-100 dark:border-purple-800\">\n                    <AvatarImage src={member.avatar} alt={member.name} />\n                    <AvatarFallback>{member.name[0]}</AvatarFallback>\n                  </Avatar>\n                  <div>\n                    <div className=\"font-medium text-sm\">{member.name}</div>\n                    {member.role === 'admin' && (\n                      <div className=\"text-xs text-muted-foreground\">Quản trị viên</div>\n                    )}\n                  </div>\n                </div>\n\n                {member.role === 'admin' ? (\n                  <Badge variant=\"outline\" className=\"bg-purple-50 text-purple-700 border-purple-200 dark:bg-purple-900/20 dark:text-purple-400 dark:border-purple-800\">\n                    Admin\n                  </Badge>\n                ) : (\n                  <Badge variant=\"outline\" className=\"bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-900/20 dark:text-gray-400 dark:border-gray-800\">\n                    Thành viên\n                  </Badge>\n                )}\n              </div>\n            ))}\n          </div>\n        </ScrollArea>\n\n        {members.length < maxMembers && (\n          <Button\n            onClick={() => {\n              onClose(); // Đóng dialog danh sách thành viên\n              onInvite(); // Mở dialog mời thành viên\n            }}\n            className=\"w-full mt-2 bg-purple-600 hover:bg-purple-700 text-white\"\n          >\n            <UserPlus className=\"h-4 w-4 mr-2\" />\n            Mời thêm thành viên\n          </Button>\n        )}\n      </DialogContent>\n    </Dialog>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAVA;;;;;;;;;;AAoBO,SAAS,iBAAiB,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAyB;IACxG,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,kBAAkB,cACpB,QAAQ,MAAM,CAAC,CAAA,SACb,OAAO,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAE5D;IAEJ,qBACE,8OAAC,iJAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc;kBAClC,cAAA,8OAAC,iJAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,iJAAA,CAAA,eAAY;8BACX,cAAA,8OAAC,iJAAA,CAAA,cAAW;wBAAC,WAAU;;4BAAkC;0CAEvD,8OAAC,gJAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAU,WAAU;;oCAChC,QAAQ,MAAM;oCAAC;oCAAE;;;;;;;;;;;;;;;;;;8BAKxB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC,gJAAA,CAAA,QAAK;4BACJ,aAAY;4BACZ,WAAU;4BACV,OAAO;4BACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;wBAE/C,6BACC,8OAAC,iJAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,eAAe;sCAE9B,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKnB,8OAAC,yJAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,iJAAA,CAAA,SAAM;gDAAC,WAAU;;kEAChB,8OAAC,iJAAA,CAAA,cAAW;wDAAC,KAAK,OAAO,MAAM;wDAAE,KAAK,OAAO,IAAI;;;;;;kEACjD,8OAAC,iJAAA,CAAA,iBAAc;kEAAE,OAAO,IAAI,CAAC,EAAE;;;;;;;;;;;;0DAEjC,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAuB,OAAO,IAAI;;;;;;oDAChD,OAAO,IAAI,KAAK,yBACf,8OAAC;wDAAI,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;oCAKpD,OAAO,IAAI,KAAK,wBACf,8OAAC,gJAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;kDAAmH;;;;;6DAItJ,8OAAC,gJAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;kDAAuG;;;;;;;+BArBvI,OAAO,EAAE;;;;;;;;;;;;;;;gBA8BrB,QAAQ,MAAM,GAAG,4BAChB,8OAAC,iJAAA,CAAA,SAAM;oBACL,SAAS;wBACP,WAAW,mCAAmC;wBAC9C,YAAY,2BAA2B;oBACzC;oBACA,WAAU;;sCAEV,8OAAC,8MAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;wBAAiB;;;;;;;;;;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 4143, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/components/ui/radix-ui/textarea.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { cn } from '@/lib/utils';\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    );\n  }\n);\nTextarea.displayName = 'Textarea';\n\nexport { Textarea };\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0SACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 4171, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/trips/context/TripPlanContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';\nimport { TripPlan, Activity, SaveState } from '../types';\n\ninterface TripPlanContextType {\n  plan: TripPlan | null;\n  originalPlan: TripPlan | null;\n  isEditing: boolean;\n  setIsEditing: (isEditing: boolean) => void;\n  saveState: SaveState;\n  lastSaved: Date | null;\n  saveError: Error | null;\n  updatePlan: (updates: Partial<TripPlan>) => void;\n  updateActivities: (dayIndex: number, activities: Activity[]) => void;\n  savePlan: () => Promise<void>;\n  revertChanges: () => void;\n  hasUnsavedChanges: boolean;\n}\n\nconst TripPlanContext = createContext<TripPlanContextType | undefined>(undefined);\n\nexport function TripPlanProvider({\n  children,\n  initialPlan,\n  onSave\n}: {\n  children: ReactNode;\n  initialPlan: TripPlan;\n  onSave?: (plan: TripPlan) => Promise<void>;\n}) {\n  const [plan, setPlan] = useState<TripPlan>(initialPlan);\n  const [originalPlan, setOriginalPlan] = useState<TripPlan>(initialPlan);\n  const [isEditing, setIsEditing] = useState(false);\n  const [saveState, setSaveState] = useState<SaveState>('idle');\n  const [lastSaved, setLastSaved] = useState<Date | null>(null);\n  const [saveError, setSaveError] = useState<Error | null>(null);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n\n  // Reset context when plan changes\n  useEffect(() => {\n    setPlan(initialPlan);\n    setOriginalPlan(initialPlan);\n    setIsEditing(false);\n    setSaveState('idle');\n    setLastSaved(null);\n    setSaveError(null);\n    setHasUnsavedChanges(false);\n  }, [initialPlan]);\n\n  // Check for unsaved changes\n  useEffect(() => {\n    const hasChanges = JSON.stringify(plan) !== JSON.stringify(originalPlan);\n    setHasUnsavedChanges(hasChanges);\n  }, [plan, originalPlan]);\n\n  // Update plan with partial data\n  const updatePlan = (updates: Partial<TripPlan>) => {\n    setPlan(prev => {\n      if (!prev) return prev;\n      return { ...prev, ...updates };\n    });\n\n    // Không tự động lưu, chỉ cập nhật trạng thái\n    if (isEditing) {\n      // Đánh dấu có thay đổi chưa lưu\n      setSaveState('idle');\n    }\n  };\n\n  // Update activities for a specific day\n  const updateActivities = (dayIndex: number, activities: Activity[]) => {\n    setPlan(prev => {\n      if (!prev) return prev;\n\n      const updatedDays = [...prev.days];\n      updatedDays[dayIndex] = {\n        ...updatedDays[dayIndex],\n        activities\n      };\n\n      return {\n        ...prev,\n        days: updatedDays\n      };\n    });\n\n    // Không tự động lưu, chỉ cập nhật trạng thái\n    if (isEditing) {\n      // Đánh dấu có thay đổi chưa lưu\n      setSaveState('idle');\n    }\n  };\n\n  // Save plan to server/storage\n  const savePlan = async () => {\n    if (!plan) return;\n\n    setSaveState('saving');\n    setSaveError(null);\n\n    try {\n      // Call the onSave callback if provided\n      if (onSave) {\n        await onSave(plan);\n      } else {\n        // Simulate API call if no callback provided\n        await new Promise(resolve => setTimeout(resolve, 1000));\n      }\n\n      // Update original plan after successful save\n      setOriginalPlan(plan);\n      setSaveState('saved');\n      setLastSaved(new Date());\n\n      // Reset to idle after a delay\n      setTimeout(() => {\n        setSaveState('idle');\n      }, 3000);\n\n      // Return the current plan for external use\n      return plan;\n    } catch (error) {\n      setSaveState('error');\n      setSaveError(error instanceof Error ? error : new Error('Unknown error'));\n      throw error;\n    }\n  };\n\n  // Revert changes to original plan\n  const revertChanges = () => {\n    setPlan(originalPlan);\n    setSaveState('idle');\n    setSaveError(null);\n  };\n\n  return (\n    <TripPlanContext.Provider\n      value={{\n        plan,\n        originalPlan,\n        isEditing,\n        setIsEditing,\n        saveState,\n        lastSaved,\n        saveError,\n        updatePlan,\n        updateActivities,\n        savePlan,\n        revertChanges,\n        hasUnsavedChanges\n      }}\n    >\n      {children}\n    </TripPlanContext.Provider>\n  );\n}\n\nexport function useTripPlan() {\n  const context = useContext(TripPlanContext);\n  if (context === undefined) {\n    throw new Error('useTripPlan must be used within a TripPlanProvider');\n  }\n  return context;\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAoBA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAmC;AAEhE,SAAS,iBAAiB,EAC/B,QAAQ,EACR,WAAW,EACX,MAAM,EAKP;IACC,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IACxD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,kCAAkC;IAClC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;QACR,gBAAgB;QAChB,aAAa;QACb,aAAa;QACb,aAAa;QACb,aAAa;QACb,qBAAqB;IACvB,GAAG;QAAC;KAAY;IAEhB,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,KAAK,SAAS,CAAC,UAAU,KAAK,SAAS,CAAC;QAC3D,qBAAqB;IACvB,GAAG;QAAC;QAAM;KAAa;IAEvB,gCAAgC;IAChC,MAAM,aAAa,CAAC;QAClB,QAAQ,CAAA;YACN,IAAI,CAAC,MAAM,OAAO;YAClB,OAAO;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC;QAC/B;QAEA,6CAA6C;QAC7C,IAAI,WAAW;YACb,gCAAgC;YAChC,aAAa;QACf;IACF;IAEA,uCAAuC;IACvC,MAAM,mBAAmB,CAAC,UAAkB;QAC1C,QAAQ,CAAA;YACN,IAAI,CAAC,MAAM,OAAO;YAElB,MAAM,cAAc;mBAAI,KAAK,IAAI;aAAC;YAClC,WAAW,CAAC,SAAS,GAAG;gBACtB,GAAG,WAAW,CAAC,SAAS;gBACxB;YACF;YAEA,OAAO;gBACL,GAAG,IAAI;gBACP,MAAM;YACR;QACF;QAEA,6CAA6C;QAC7C,IAAI,WAAW;YACb,gCAAgC;YAChC,aAAa;QACf;IACF;IAEA,8BAA8B;IAC9B,MAAM,WAAW;QACf,IAAI,CAAC,MAAM;QAEX,aAAa;QACb,aAAa;QAEb,IAAI;YACF,uCAAuC;YACvC,IAAI,QAAQ;gBACV,MAAM,OAAO;YACf,OAAO;gBACL,4CAA4C;gBAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;YAEA,6CAA6C;YAC7C,gBAAgB;YAChB,aAAa;YACb,aAAa,IAAI;YAEjB,8BAA8B;YAC9B,WAAW;gBACT,aAAa;YACf,GAAG;YAEH,2CAA2C;YAC3C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,aAAa;YACb,aAAa,iBAAiB,QAAQ,QAAQ,IAAI,MAAM;YACxD,MAAM;QACR;IACF;IAEA,kCAAkC;IAClC,MAAM,gBAAgB;QACpB,QAAQ;QACR,aAAa;QACb,aAAa;IACf;IAEA,qBACE,8OAAC,gBAAgB,QAAQ;QACvB,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBAEC;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 4316, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/trips/AutoSaveIndicator.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\nimport { Button } from '@/components/ui/radix-ui/button';\nimport { Clock, Check, AlertCircle, RefreshCw } from 'lucide-react';\nimport { SaveState } from './types';\n\ninterface AutoSaveIndicatorProps {\n  state: SaveState;\n  lastSaved: Date | null;\n  error: Error | null;\n  onRetry?: () => void;\n  className?: string;\n}\n\nexport function AutoSaveIndicator({\n  state,\n  lastSaved,\n  error,\n  onRetry,\n  className\n}: AutoSaveIndicatorProps) {\n  // Format the last saved time\n  const formatLastSaved = () => {\n    if (!lastSaved) return '';\n    \n    const now = new Date();\n    const diffMs = now.getTime() - lastSaved.getTime();\n    const diffSec = Math.floor(diffMs / 1000);\n    const diffMin = Math.floor(diffSec / 60);\n    \n    if (diffSec < 60) {\n      return 'vừa xong';\n    } else if (diffMin < 60) {\n      return `${diffMin} phút trước`;\n    } else {\n      const hours = lastSaved.getHours().toString().padStart(2, '0');\n      const minutes = lastSaved.getMinutes().toString().padStart(2, '0');\n      return `lúc ${hours}:${minutes}`;\n    }\n  };\n\n  return (\n    <div className={cn(\"flex items-center text-xs gap-1.5\", className)}>\n      {state === 'saving' && (\n        <>\n          <Clock className=\"h-3 w-3 animate-spin text-muted-foreground\" />\n          <span className=\"text-muted-foreground\">Đang lưu...</span>\n        </>\n      )}\n      \n      {state === 'saved' && (\n        <>\n          <Check className=\"h-3 w-3 text-green-500\" />\n          <span className=\"text-muted-foreground\">\n            Đã lưu {lastSaved ? formatLastSaved() : ''}\n          </span>\n        </>\n      )}\n      \n      {state === 'error' && (\n        <>\n          <AlertCircle className=\"h-3 w-3 text-red-500\" />\n          <span className=\"text-red-500\">\n            Lỗi khi lưu\n          </span>\n          {onRetry && (\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"h-5 px-1 text-xs\"\n              onClick={onRetry}\n            >\n              <RefreshCw className=\"h-3 w-3 mr-1\" />\n              Thử lại\n            </Button>\n          )}\n        </>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;AAgBO,SAAS,kBAAkB,EAChC,KAAK,EACL,SAAS,EACT,KAAK,EACL,OAAO,EACP,SAAS,EACc;IACvB,6BAA6B;IAC7B,MAAM,kBAAkB;QACtB,IAAI,CAAC,WAAW,OAAO;QAEvB,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,IAAI,OAAO,KAAK,UAAU,OAAO;QAChD,MAAM,UAAU,KAAK,KAAK,CAAC,SAAS;QACpC,MAAM,UAAU,KAAK,KAAK,CAAC,UAAU;QAErC,IAAI,UAAU,IAAI;YAChB,OAAO;QACT,OAAO,IAAI,UAAU,IAAI;YACvB,OAAO,GAAG,QAAQ,WAAW,CAAC;QAChC,OAAO;YACL,MAAM,QAAQ,UAAU,QAAQ,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;YAC1D,MAAM,UAAU,UAAU,UAAU,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG;YAC9D,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,SAAS;QAClC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;;YACrD,UAAU,0BACT;;kCACE,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,8OAAC;wBAAK,WAAU;kCAAwB;;;;;;;;YAI3C,UAAU,yBACT;;kCACE,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,8OAAC;wBAAK,WAAU;;4BAAwB;4BAC9B,YAAY,oBAAoB;;;;;;;;;YAK7C,UAAU,yBACT;;kCACE,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;;;;;;kCACvB,8OAAC;wBAAK,WAAU;kCAAe;;;;;;oBAG9B,yBACC,8OAAC,iJAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;;0CAET,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;AAQpD", "debugId": null}}, {"offset": {"line": 4445, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/trips/TripPlanEditor.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useCallback } from 'react';\nimport { TripPlan, Activity } from './types';\nimport { Button } from '@/components/ui/radix-ui/button';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/radix-ui/card';\nimport { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/radix-ui/tabs';\nimport { Input } from '@/components/ui/radix-ui/input';\nimport { Textarea } from '@/components/ui/radix-ui/textarea';\nimport { Badge } from '@/components/ui/radix-ui/badge';\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/radix-ui/dialog';\nimport { Label } from '@/components/ui/radix-ui/label';\nimport { Calendar, MapPin, Star, Users, ArrowLeft, Edit, Save, Plus, Trash2, Share, Heart, Clock } from 'lucide-react';\nimport { TripPlanProvider, useTripPlan } from './context/TripPlanContext';\nimport { AutoSaveIndicator } from './AutoSaveIndicator';\nimport { toast } from 'sonner';\nimport dynamic from 'next/dynamic';\n\nconst InteractiveScheduleChart = dynamic(\n  () => import('../planning/InteractiveScheduleChart').then((mod) => mod.InteractiveScheduleChart),\n  { ssr: false }\n);\n\ninterface TripPlanEditorProps {\n  plan: TripPlan;\n  isOpen: boolean;\n  onClose: () => void;\n  onSave?: (plan: TripPlan) => Promise<void>;\n}\n\nexport default function TripPlanEditor({\n  plan: initialPlan,\n  isOpen,\n  onClose,\n  onSave\n}: TripPlanEditorProps) {\n  const [internalOpen, setInternalOpen] = useState(isOpen);\n\n  // Cập nhật trạng thái mở khi prop isOpen thay đổi\n  useEffect(() => {\n    setInternalOpen(isOpen);\n  }, [isOpen]);\n\n  // Xử lý đóng dialog\n  const handleCloseDialog = () => {\n    onClose();\n  };\n\n  return (\n    <Dialog\n      open={internalOpen}\n      onOpenChange={(open) => {\n        if (open === false) {\n          // Khi dialog được đóng từ bên ngoài (nhấn Escape hoặc nhấn bên ngoài)\n          handleCloseDialog();\n        }\n        setInternalOpen(open);\n      }}\n    >\n      <DialogContent className=\"max-w-[95vw] md:max-w-[85vw] lg:max-w-[80vw] xl:max-w-[75vw] max-h-[95vh] overflow-auto p-0\">\n        <TripPlanProvider initialPlan={initialPlan} onSave={onSave}>\n          <TripPlanEditorContent onClose={handleCloseDialog} />\n        </TripPlanProvider>\n      </DialogContent>\n    </Dialog>\n  );\n}\n\nfunction TripPlanEditorContent({ onClose }: { onClose: () => void }) {\n  const {\n    plan,\n    isEditing,\n    setIsEditing,\n    saveState,\n    lastSaved,\n    saveError,\n    updatePlan,\n    updateActivities,\n    savePlan,\n    revertChanges,\n    hasUnsavedChanges\n  } = useTripPlan();\n\n  const [showUnsavedChangesDialog, setShowUnsavedChangesDialog] = useState(false);\n  const [isClosing, setIsClosing] = useState(false);\n  const [activeTab, setActiveTab] = useState('chart');\n\n  // Xử lý đóng dialog\n  const handleClose = () => {\n    if (isEditing && hasUnsavedChanges) {\n      setIsClosing(true);\n      setShowUnsavedChangesDialog(true);\n    } else {\n      onClose();\n    }\n  };\n\n  const [editingActivity, setEditingActivity] = useState<{ activity: Activity; dayIndex: number } | null>(null);\n  const [isAddingActivity, setIsAddingActivity] = useState<{ dayIndex: number } | null>(null);\n  const [newActivity, setNewActivity] = useState<Partial<Activity>>({\n    time: '12:00',\n    title: '',\n    description: '',\n    location: ''\n  });\n\n  // Unsaved changes warning\n  const handleBeforeUnload = useCallback((e: BeforeUnloadEvent) => {\n    if (hasUnsavedChanges) {\n      e.preventDefault();\n      const message = 'Bạn có thay đổi chưa lưu. Bạn có chắc chắn muốn rời đi?';\n      e.returnValue = message;\n      return message;\n    }\n  }, [hasUnsavedChanges]);\n\n  useEffect(() => {\n    window.addEventListener('beforeunload', handleBeforeUnload);\n    return () => {\n      window.removeEventListener('beforeunload', handleBeforeUnload);\n    };\n  }, [handleBeforeUnload]);\n\n  // Bắt đầu chỉnh sửa\n  const handleStartEditing = () => {\n    setIsEditing(true);\n  };\n\n  // Xử lý khi người dùng muốn thoát khỏi chế độ chỉnh sửa\n  const handleExitEditMode = () => {\n    if (hasUnsavedChanges) {\n      setShowUnsavedChangesDialog(true);\n    } else {\n      setIsEditing(false);\n    }\n  };\n\n  // Xử lý khi người dùng nhấn nút X hoặc nhấn bên ngoài dialog\n  useEffect(() => {\n    const handleEscapeKey = (e: KeyboardEvent) => {\n      if (e.key === 'Escape' && isEditing && hasUnsavedChanges) {\n        e.preventDefault();\n        setIsClosing(true);\n        setShowUnsavedChangesDialog(true);\n      }\n    };\n\n    window.addEventListener('keydown', handleEscapeKey);\n    return () => {\n      window.removeEventListener('keydown', handleEscapeKey);\n    };\n  }, [isEditing, hasUnsavedChanges]);\n\n  // Hủy chỉnh sửa\n  const handleCancelEditing = () => {\n    if (hasUnsavedChanges) {\n      setShowUnsavedChangesDialog(true);\n    } else {\n      revertChanges();\n      setIsEditing(false);\n    }\n  };\n\n  // Lưu thay đổi\n  const handleSaveChanges = async () => {\n    await savePlan();\n    setIsEditing(false);\n    toast.success('Đã lưu thay đổi');\n  };\n\n  // Cập nhật thông tin cơ bản\n  const handleBasicInfoChange = (field: keyof TripPlan, value: any) => {\n    updatePlan({ [field]: value });\n  };\n\n  // Mở dialog chỉnh sửa hoạt động\n  const handleEditActivity = (activity: Activity, dayIndex: number) => {\n    setEditingActivity({ activity, dayIndex });\n    setNewActivity({\n      time: activity.time,\n      title: activity.title,\n      description: activity.description,\n      location: activity.location\n    });\n  };\n\n  // Thêm hoạt động mới\n  const handleAddActivity = (dayIndex: number) => {\n    setIsAddingActivity({ dayIndex });\n    setNewActivity({\n      time: '12:00',\n      title: '',\n      description: '',\n      location: ''\n    });\n  };\n\n  // Lưu hoạt động\n  const handleSaveActivity = () => {\n    if (!plan) return;\n\n    // Validate\n    if (!newActivity.title || !newActivity.time || !newActivity.location) {\n      toast.error('Vui lòng điền đầy đủ thông tin');\n      return;\n    }\n\n    const updatedDays = [...plan.days];\n\n    if (editingActivity) {\n      // Update existing activity\n      const { dayIndex, activity } = editingActivity;\n      const activityIndex = updatedDays[dayIndex].activities.findIndex(a => a.id === activity.id);\n\n      if (activityIndex !== -1) {\n        updatedDays[dayIndex].activities[activityIndex] = {\n          ...activity,\n          title: newActivity.title!,\n          time: newActivity.time!,\n          description: newActivity.description || '',\n          location: newActivity.location!,\n          mainLocation: newActivity.location!\n        };\n      }\n    } else if (isAddingActivity) {\n      // Add new activity\n      const newActivityObj: Activity = {\n        id: `activity-${Date.now()}`,\n        title: newActivity.title!,\n        time: newActivity.time!,\n        description: newActivity.description || '',\n        location: newActivity.location!,\n        mainLocation: newActivity.location!\n      };\n\n      updatedDays[isAddingActivity.dayIndex].activities.push(newActivityObj);\n    }\n\n    // Update the plan with the new days\n    updatePlan({\n      days: updatedDays\n    });\n\n    setEditingActivity(null);\n    setIsAddingActivity(null);\n\n    toast.success(editingActivity ? 'Đã cập nhật hoạt động' : 'Đã thêm hoạt động mới');\n  };\n\n  // Xóa hoạt động\n  const handleDeleteActivity = (activityId: string, dayIndex: number) => {\n    if (!plan) return;\n\n    const updatedDays = [...plan.days];\n    updatedDays[dayIndex].activities = updatedDays[dayIndex].activities.filter(\n      (activity: Activity) => activity.id !== activityId\n    );\n\n    // Update the plan with the new days\n    updatePlan({\n      days: updatedDays\n    });\n\n    toast.success('Đã xóa hoạt động');\n  };\n\n  if (!plan) return null;\n\n  return (\n    <div className=\"flex flex-col h-full\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center p-4 border-b\">\n        <div className=\"flex flex-col\">\n          <h2 className=\"text-xl font-semibold\">{plan.name}</h2>\n          <p className=\"text-sm text-muted-foreground\">{plan.destination}</p>\n        </div>\n\n        <div className=\"flex items-center gap-2\">\n          {isEditing && (\n            <AutoSaveIndicator\n              state={saveState}\n              lastSaved={lastSaved}\n              error={saveError}\n              onRetry={savePlan}\n            />\n          )}\n\n          {isEditing ? (\n            <>\n              <Button\n                variant=\"outline\"\n                onClick={handleCancelEditing}\n                size=\"sm\"\n              >\n                Hủy\n              </Button>\n              <Button\n                className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n                onClick={handleSaveChanges}\n                size=\"sm\"\n                disabled={saveState === 'saving'}\n              >\n                {saveState === 'saving' ? (\n                  <>\n                    <Clock className=\"h-3.5 w-3.5 mr-1.5 animate-spin\" />\n                    Đang lưu...\n                  </>\n                ) : (\n                  <>\n                    <Save className=\"h-3.5 w-3.5 mr-1.5\" />\n                    Lưu thay đổi\n                  </>\n                )}\n              </Button>\n            </>\n          ) : (\n            <Button variant=\"outline\" onClick={handleStartEditing} size=\"sm\">\n              <Edit className=\"h-3.5 w-3.5 mr-1.5\" />\n              Chỉnh sửa\n            </Button>\n          )}\n        </div>\n      </div>\n\n      {/* Content */}\n      <div className=\"flex-1 p-4 overflow-auto\">\n        <Tabs value={activeTab} onValueChange={setActiveTab}>\n          <TabsList className=\"grid w-full grid-cols-3 mb-4\">\n            <TabsTrigger value=\"info\" className=\"text-sm py-2\">Thông tin chuyến đi</TabsTrigger>\n            <TabsTrigger value=\"schedule\" className=\"text-sm py-2\">Lịch trình chi tiết</TabsTrigger>\n            <TabsTrigger value=\"chart\" className=\"text-sm py-2\">Biểu đồ</TabsTrigger>\n          </TabsList>\n\n          {/* Tab 1: Thông tin chuyến đi */}\n          <TabsContent value=\"info\" className=\"space-y-4 pt-3\">\n            <Card className=\"shadow-xs\">\n              <CardHeader className=\"py-3 px-4\">\n                <CardTitle className=\"text-base\">Mô tả</CardTitle>\n              </CardHeader>\n              <CardContent className=\"py-2 px-4\">\n                {isEditing ? (\n                  <Textarea\n                    value={plan.description}\n                    onChange={(e) => handleBasicInfoChange('description', e.target.value)}\n                    className=\"min-h-[80px] text-sm\"\n                  />\n                ) : (\n                  <p className=\"text-sm text-muted-foreground\">{plan.description}</p>\n                )}\n              </CardContent>\n            </Card>\n\n            <Card className=\"shadow-xs\">\n              <CardHeader className=\"py-3 px-4\">\n                <CardTitle className=\"text-base\">Thẻ</CardTitle>\n              </CardHeader>\n              <CardContent className=\"py-2 px-4\">\n                <div className=\"flex flex-wrap gap-1.5\">\n                  {plan.tags.map((tag: string) => (\n                    <Badge key={tag} variant=\"outline\" className=\"bg-purple-100/50 hover:bg-purple-200/50 text-purple-700 dark:bg-purple-900/30 dark:hover:bg-purple-800/30 dark:text-purple-300 border-purple-200 dark:border-purple-800 text-xs\">\n                      #{tag}\n                    </Badge>\n                  ))}\n                  {isEditing && (\n                    <Badge variant=\"outline\" className=\"bg-purple-100/50 hover:bg-purple-200/50 text-purple-700 cursor-pointer text-xs\">\n                      <Plus className=\"h-2.5 w-2.5 mr-1\" /> Thêm thẻ\n                    </Badge>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          {/* Tab 2: Lịch trình chi tiết */}\n          <TabsContent value=\"schedule\" className=\"pt-3\">\n            <Card className=\"shadow-xs\">\n              <CardHeader className=\"py-3 px-4 flex flex-row items-center justify-between\">\n                <CardTitle className=\"text-base\">Lịch trình chi tiết</CardTitle>\n                {isEditing && (\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    className=\"h-7 text-xs\"\n                    onClick={() => handleAddActivity(0)}\n                  >\n                    <Plus className=\"h-3 w-3 mr-1\" />\n                    Thêm hoạt động\n                  </Button>\n                )}\n              </CardHeader>\n              <CardContent className=\"py-2 px-4\">\n                <Tabs defaultValue={`day-0`} className=\"w-full\">\n                  <TabsList className=\"grid grid-cols-3 mb-3\">\n                    {plan.days.map((day: any, index: number) => (\n                      <TabsTrigger key={day.id} value={`day-${index}`} className=\"text-xs py-1\">\n                        Ngày {index + 1}\n                      </TabsTrigger>\n                    ))}\n                  </TabsList>\n\n                  {plan.days.map((day: any, dayIndex: number) => (\n                    <TabsContent key={day.id} value={`day-${dayIndex}`} className=\"space-y-3\">\n                      <div className=\"space-y-3\">\n                        {day.activities.map((activity: Activity) => (\n                          <div\n                            key={activity.id}\n                            className=\"border border-purple-100 dark:border-purple-900 rounded-md p-3 space-y-2 shadow-xs\"\n                          >\n                            <div className=\"flex justify-between items-start\">\n                              <div className=\"flex items-center gap-2\">\n                                <span className=\"font-medium text-purple-600 dark:text-purple-400 w-12 text-sm\">\n                                  {activity.time}\n                                </span>\n                                <span className=\"font-medium text-sm\">{activity.title}</span>\n                              </div>\n                              {isEditing && (\n                                <div className=\"flex gap-1\">\n                                  <Button\n                                    variant=\"ghost\"\n                                    size=\"sm\"\n                                    className=\"h-7 w-7 p-0\"\n                                    onClick={() => handleEditActivity(activity, dayIndex)}\n                                  >\n                                    <Edit className=\"h-3.5 w-3.5\" />\n                                  </Button>\n                                  <Button\n                                    variant=\"ghost\"\n                                    size=\"sm\"\n                                    className=\"h-7 w-7 p-0 text-red-500\"\n                                    onClick={() => handleDeleteActivity(activity.id, dayIndex)}\n                                  >\n                                    <Trash2 className=\"h-3.5 w-3.5\" />\n                                  </Button>\n                                </div>\n                              )}\n                            </div>\n                            <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\n                              <MapPin className=\"h-3 w-3\" />\n                              <span>{activity.location}</span>\n                            </div>\n                            {activity.description && (\n                              <p className=\"text-xs text-muted-foreground\">{activity.description}</p>\n                            )}\n                          </div>\n                        ))}\n                      </div>\n                    </TabsContent>\n                  ))}\n                </Tabs>\n              </CardContent>\n            </Card>\n          </TabsContent>\n\n          {/* Tab 3: Biểu đồ */}\n          <TabsContent value=\"chart\" className=\"pt-3\">\n            <Card className=\"shadow-xs\">\n              <CardHeader className=\"py-3 px-4\">\n                <CardTitle className=\"text-base\">Biểu đồ lịch trình</CardTitle>\n              </CardHeader>\n              <CardContent className=\"py-2 px-2 md:px-4\">\n                <div className=\"relative min-h-[500px] md:min-h-[600px]\">\n                  <InteractiveScheduleChart\n                    days={plan.days}\n                    isEditing={isEditing}\n                    onUpdateActivities={(dayIndex, activities) => {\n                      updateActivities(dayIndex, activities);\n                    }}\n                  />\n\n                  {isEditing && (\n                    <div className=\"absolute top-2 right-2 z-10\">\n                      <AutoSaveIndicator\n                        state={saveState}\n                        lastSaved={lastSaved}\n                        error={saveError}\n                        onRetry={savePlan}\n                        className=\"bg-white dark:bg-gray-800 shadow-xs border border-gray-200 dark:border-gray-700 rounded-md px-2 py-1\"\n                      />\n                    </div>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n        </Tabs>\n      </div>\n\n      {/* Dialog xác nhận khi có thay đổi chưa lưu */}\n      <Dialog\n        open={showUnsavedChangesDialog}\n        onOpenChange={(open) => {\n          if (!open) {\n            setShowUnsavedChangesDialog(false);\n          }\n        }}\n      >\n        <DialogContent className=\"max-w-md\">\n          <DialogHeader>\n            <DialogTitle className=\"text-base\">Thay đổi chưa lưu</DialogTitle>\n          </DialogHeader>\n\n          <div className=\"py-3\">\n            <p className=\"text-sm text-muted-foreground\">\n              Bạn có thay đổi chưa lưu. Bạn có muốn lưu thay đổi trước khi thoát?\n            </p>\n          </div>\n\n          <DialogFooter className=\"gap-2\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => {\n                revertChanges();\n                setIsEditing(false);\n                setShowUnsavedChangesDialog(false);\n\n                if (isClosing) {\n                  onClose();\n                }\n\n                setIsClosing(false);\n                toast.info('Đã hủy các thay đổi');\n              }}\n            >\n              Không lưu\n            </Button>\n            <Button\n              className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n              size=\"sm\"\n              onClick={async () => {\n                await savePlan();\n                setIsEditing(false);\n                setShowUnsavedChangesDialog(false);\n\n                if (isClosing) {\n                  onClose();\n                }\n\n                setIsClosing(false);\n                toast.success('Đã lưu thay đổi');\n              }}\n            >\n              Lưu thay đổi\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n\n      {/* Dialog chỉnh sửa hoạt động */}\n      <Dialog\n        open={!!editingActivity || !!isAddingActivity}\n        onOpenChange={(open) => {\n          if (!open) {\n            setEditingActivity(null);\n            setIsAddingActivity(null);\n          }\n        }}\n      >\n        <DialogContent className=\"max-w-md\">\n          <DialogHeader>\n            <DialogTitle className=\"text-base\">\n              {editingActivity ? 'Chỉnh sửa hoạt động' : 'Thêm hoạt động mới'}\n            </DialogTitle>\n          </DialogHeader>\n\n          <div className=\"grid gap-4 py-2\">\n            <div className=\"grid gap-2\">\n              <Label htmlFor=\"activity-time\">Thời gian</Label>\n              <Input\n                id=\"activity-time\"\n                type=\"time\"\n                value={newActivity.time}\n                onChange={(e) => setNewActivity({ ...newActivity, time: e.target.value })}\n              />\n            </div>\n            <div className=\"grid gap-2\">\n              <Label htmlFor=\"activity-title\">Tên hoạt động</Label>\n              <Input\n                id=\"activity-title\"\n                value={newActivity.title}\n                onChange={(e) => setNewActivity({ ...newActivity, title: e.target.value })}\n              />\n            </div>\n            <div className=\"grid gap-2\">\n              <Label htmlFor=\"activity-location\">Địa điểm</Label>\n              <Input\n                id=\"activity-location\"\n                value={newActivity.location}\n                onChange={(e) => setNewActivity({ ...newActivity, location: e.target.value })}\n              />\n            </div>\n            <div className=\"grid gap-2\">\n              <Label htmlFor=\"activity-description\">Mô tả (tùy chọn)</Label>\n              <Textarea\n                id=\"activity-description\"\n                value={newActivity.description}\n                onChange={(e) => setNewActivity({ ...newActivity, description: e.target.value })}\n                className=\"min-h-[80px]\"\n              />\n            </div>\n          </div>\n\n          <DialogFooter className=\"gap-2\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => {\n                setEditingActivity(null);\n                setIsAddingActivity(null);\n              }}\n            >\n              Hủy\n            </Button>\n            <Button\n              className=\"bg-purple-600 hover:bg-purple-700 text-white\"\n              size=\"sm\"\n              onClick={handleSaveActivity}\n            >\n              Lưu\n            </Button>\n          </DialogFooter>\n        </DialogContent>\n      </Dialog>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;AAhBA;;;;;;;;;;;;;;;;AAkBA,MAAM,2BAA2B,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IAEnC,KAAK;;AAUM,SAAS,eAAe,EACrC,MAAM,WAAW,EACjB,MAAM,EACN,OAAO,EACP,MAAM,EACc;IACpB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gBAAgB;IAClB,GAAG;QAAC;KAAO;IAEX,oBAAoB;IACpB,MAAM,oBAAoB;QACxB;IACF;IAEA,qBACE,8OAAC,iJAAA,CAAA,SAAM;QACL,MAAM;QACN,cAAc,CAAC;YACb,IAAI,SAAS,OAAO;gBAClB,sEAAsE;gBACtE;YACF;YACA,gBAAgB;QAClB;kBAEA,cAAA,8OAAC,iJAAA,CAAA,gBAAa;YAAC,WAAU;sBACvB,cAAA,8OAAC,uJAAA,CAAA,mBAAgB;gBAAC,aAAa;gBAAa,QAAQ;0BAClD,cAAA,8OAAC;oBAAsB,SAAS;;;;;;;;;;;;;;;;;;;;;AAK1C;AAEA,SAAS,sBAAsB,EAAE,OAAO,EAA2B;IACjE,MAAM,EACJ,IAAI,EACJ,SAAS,EACT,YAAY,EACZ,SAAS,EACT,SAAS,EACT,SAAS,EACT,UAAU,EACV,gBAAgB,EAChB,QAAQ,EACR,aAAa,EACb,iBAAiB,EAClB,GAAG,CAAA,GAAA,uJAAA,CAAA,cAAW,AAAD;IAEd,MAAM,CAAC,0BAA0B,4BAA4B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,oBAAoB;IACpB,MAAM,cAAc;QAClB,IAAI,aAAa,mBAAmB;YAClC,aAAa;YACb,4BAA4B;QAC9B,OAAO;YACL;QACF;IACF;IAEA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmD;IACxG,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IACtF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;QAChE,MAAM;QACN,OAAO;QACP,aAAa;QACb,UAAU;IACZ;IAEA,0BAA0B;IAC1B,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,IAAI,mBAAmB;YACrB,EAAE,cAAc;YAChB,MAAM,UAAU;YAChB,EAAE,WAAW,GAAG;YAChB,OAAO;QACT;IACF,GAAG;QAAC;KAAkB;IAEtB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO,gBAAgB,CAAC,gBAAgB;QACxC,OAAO;YACL,OAAO,mBAAmB,CAAC,gBAAgB;QAC7C;IACF,GAAG;QAAC;KAAmB;IAEvB,oBAAoB;IACpB,MAAM,qBAAqB;QACzB,aAAa;IACf;IAEA,wDAAwD;IACxD,MAAM,qBAAqB;QACzB,IAAI,mBAAmB;YACrB,4BAA4B;QAC9B,OAAO;YACL,aAAa;QACf;IACF;IAEA,6DAA6D;IAC7D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB,CAAC;YACvB,IAAI,EAAE,GAAG,KAAK,YAAY,aAAa,mBAAmB;gBACxD,EAAE,cAAc;gBAChB,aAAa;gBACb,4BAA4B;YAC9B;QACF;QAEA,OAAO,gBAAgB,CAAC,WAAW;QACnC,OAAO;YACL,OAAO,mBAAmB,CAAC,WAAW;QACxC;IACF,GAAG;QAAC;QAAW;KAAkB;IAEjC,gBAAgB;IAChB,MAAM,sBAAsB;QAC1B,IAAI,mBAAmB;YACrB,4BAA4B;QAC9B,OAAO;YACL;YACA,aAAa;QACf;IACF;IAEA,eAAe;IACf,MAAM,oBAAoB;QACxB,MAAM;QACN,aAAa;QACb,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,4BAA4B;IAC5B,MAAM,wBAAwB,CAAC,OAAuB;QACpD,WAAW;YAAE,CAAC,MAAM,EAAE;QAAM;IAC9B;IAEA,gCAAgC;IAChC,MAAM,qBAAqB,CAAC,UAAoB;QAC9C,mBAAmB;YAAE;YAAU;QAAS;QACxC,eAAe;YACb,MAAM,SAAS,IAAI;YACnB,OAAO,SAAS,KAAK;YACrB,aAAa,SAAS,WAAW;YACjC,UAAU,SAAS,QAAQ;QAC7B;IACF;IAEA,qBAAqB;IACrB,MAAM,oBAAoB,CAAC;QACzB,oBAAoB;YAAE;QAAS;QAC/B,eAAe;YACb,MAAM;YACN,OAAO;YACP,aAAa;YACb,UAAU;QACZ;IACF;IAEA,gBAAgB;IAChB,MAAM,qBAAqB;QACzB,IAAI,CAAC,MAAM;QAEX,WAAW;QACX,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,QAAQ,EAAE;YACpE,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ;QACF;QAEA,MAAM,cAAc;eAAI,KAAK,IAAI;SAAC;QAElC,IAAI,iBAAiB;YACnB,2BAA2B;YAC3B,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG;YAC/B,MAAM,gBAAgB,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,SAAS,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,SAAS,EAAE;YAE1F,IAAI,kBAAkB,CAAC,GAAG;gBACxB,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,cAAc,GAAG;oBAChD,GAAG,QAAQ;oBACX,OAAO,YAAY,KAAK;oBACxB,MAAM,YAAY,IAAI;oBACtB,aAAa,YAAY,WAAW,IAAI;oBACxC,UAAU,YAAY,QAAQ;oBAC9B,cAAc,YAAY,QAAQ;gBACpC;YACF;QACF,OAAO,IAAI,kBAAkB;YAC3B,mBAAmB;YACnB,MAAM,iBAA2B;gBAC/B,IAAI,CAAC,SAAS,EAAE,KAAK,GAAG,IAAI;gBAC5B,OAAO,YAAY,KAAK;gBACxB,MAAM,YAAY,IAAI;gBACtB,aAAa,YAAY,WAAW,IAAI;gBACxC,UAAU,YAAY,QAAQ;gBAC9B,cAAc,YAAY,QAAQ;YACpC;YAEA,WAAW,CAAC,iBAAiB,QAAQ,CAAC,CAAC,UAAU,CAAC,IAAI,CAAC;QACzD;QAEA,oCAAoC;QACpC,WAAW;YACT,MAAM;QACR;QAEA,mBAAmB;QACnB,oBAAoB;QAEpB,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC,kBAAkB,0BAA0B;IAC5D;IAEA,gBAAgB;IAChB,MAAM,uBAAuB,CAAC,YAAoB;QAChD,IAAI,CAAC,MAAM;QAEX,MAAM,cAAc;eAAI,KAAK,IAAI;SAAC;QAClC,WAAW,CAAC,SAAS,CAAC,UAAU,GAAG,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,MAAM,CACxE,CAAC,WAAuB,SAAS,EAAE,KAAK;QAG1C,oCAAoC;QACpC,WAAW;YACT,MAAM;QACR;QAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;IAChB;IAEA,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyB,KAAK,IAAI;;;;;;0CAChD,8OAAC;gCAAE,WAAU;0CAAiC,KAAK,WAAW;;;;;;;;;;;;kCAGhE,8OAAC;wBAAI,WAAU;;4BACZ,2BACC,8OAAC,8IAAA,CAAA,oBAAiB;gCAChB,OAAO;gCACP,WAAW;gCACX,OAAO;gCACP,SAAS;;;;;;4BAIZ,0BACC;;kDACE,8OAAC,iJAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS;wCACT,MAAK;kDACN;;;;;;kDAGD,8OAAC,iJAAA,CAAA,SAAM;wCACL,WAAU;wCACV,SAAS;wCACT,MAAK;wCACL,UAAU,cAAc;kDAEvB,cAAc,yBACb;;8DACE,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAoC;;yEAIvD;;8DACE,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAuB;;;;;;;;;6DAO/C,8OAAC,iJAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,SAAS;gCAAoB,MAAK;;kDAC1D,8OAAC,2MAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAuB;;;;;;;;;;;;;;;;;;;0BAQ/C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,+IAAA,CAAA,OAAI;oBAAC,OAAO;oBAAW,eAAe;;sCACrC,8OAAC,+IAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,+IAAA,CAAA,cAAW;oCAAC,OAAM;oCAAO,WAAU;8CAAe;;;;;;8CACnD,8OAAC,+IAAA,CAAA,cAAW;oCAAC,OAAM;oCAAW,WAAU;8CAAe;;;;;;8CACvD,8OAAC,+IAAA,CAAA,cAAW;oCAAC,OAAM;oCAAQ,WAAU;8CAAe;;;;;;;;;;;;sCAItD,8OAAC,+IAAA,CAAA,cAAW;4BAAC,OAAM;4BAAO,WAAU;;8CAClC,8OAAC,+IAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,+IAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,8OAAC,+IAAA,CAAA,YAAS;gDAAC,WAAU;0DAAY;;;;;;;;;;;sDAEnC,8OAAC,+IAAA,CAAA,cAAW;4CAAC,WAAU;sDACpB,0BACC,8OAAC,mJAAA,CAAA,WAAQ;gDACP,OAAO,KAAK,WAAW;gDACvB,UAAU,CAAC,IAAM,sBAAsB,eAAe,EAAE,MAAM,CAAC,KAAK;gDACpE,WAAU;;;;;qEAGZ,8OAAC;gDAAE,WAAU;0DAAiC,KAAK,WAAW;;;;;;;;;;;;;;;;;8CAKpE,8OAAC,+IAAA,CAAA,OAAI;oCAAC,WAAU;;sDACd,8OAAC,+IAAA,CAAA,aAAU;4CAAC,WAAU;sDACpB,cAAA,8OAAC,+IAAA,CAAA,YAAS;gDAAC,WAAU;0DAAY;;;;;;;;;;;sDAEnC,8OAAC,+IAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC;gDAAI,WAAU;;oDACZ,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,oBACd,8OAAC,gJAAA,CAAA,QAAK;4DAAW,SAAQ;4DAAU,WAAU;;gEAAkL;gEAC3N;;2DADQ;;;;;oDAIb,2BACC,8OAAC,gJAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;;0EACjC,8OAAC,kMAAA,CAAA,OAAI;gEAAC,WAAU;;;;;;4DAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASjD,8OAAC,+IAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,8OAAC,+IAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,+IAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,+IAAA,CAAA,YAAS;gDAAC,WAAU;0DAAY;;;;;;4CAChC,2BACC,8OAAC,iJAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,kBAAkB;;kEAEjC,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;kDAKvC,8OAAC,+IAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC,+IAAA,CAAA,OAAI;4CAAC,cAAc,CAAC,KAAK,CAAC;4CAAE,WAAU;;8DACrC,8OAAC,+IAAA,CAAA,WAAQ;oDAAC,WAAU;8DACjB,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAU,sBACxB,8OAAC,+IAAA,CAAA,cAAW;4DAAc,OAAO,CAAC,IAAI,EAAE,OAAO;4DAAE,WAAU;;gEAAe;gEAClE,QAAQ;;2DADE,IAAI,EAAE;;;;;;;;;;gDAM3B,KAAK,IAAI,CAAC,GAAG,CAAC,CAAC,KAAU,yBACxB,8OAAC,+IAAA,CAAA,cAAW;wDAAc,OAAO,CAAC,IAAI,EAAE,UAAU;wDAAE,WAAU;kEAC5D,cAAA,8OAAC;4DAAI,WAAU;sEACZ,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,yBACnB,8OAAC;oEAEC,WAAU;;sFAEV,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAK,WAAU;sGACb,SAAS,IAAI;;;;;;sGAEhB,8OAAC;4FAAK,WAAU;sGAAuB,SAAS,KAAK;;;;;;;;;;;;gFAEtD,2BACC,8OAAC;oFAAI,WAAU;;sGACb,8OAAC,iJAAA,CAAA,SAAM;4FACL,SAAQ;4FACR,MAAK;4FACL,WAAU;4FACV,SAAS,IAAM,mBAAmB,UAAU;sGAE5C,cAAA,8OAAC,2MAAA,CAAA,OAAI;gGAAC,WAAU;;;;;;;;;;;sGAElB,8OAAC,iJAAA,CAAA,SAAM;4FACL,SAAQ;4FACR,MAAK;4FACL,WAAU;4FACV,SAAS,IAAM,qBAAqB,SAAS,EAAE,EAAE;sGAEjD,cAAA,8OAAC,0MAAA,CAAA,SAAM;gGAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sFAK1B,8OAAC;4EAAI,WAAU;;8FACb,8OAAC,0MAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;8FAClB,8OAAC;8FAAM,SAAS,QAAQ;;;;;;;;;;;;wEAEzB,SAAS,WAAW,kBACnB,8OAAC;4EAAE,WAAU;sFAAiC,SAAS,WAAW;;;;;;;mEApC/D,SAAS,EAAE;;;;;;;;;;uDAJN,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAqDlC,8OAAC,+IAAA,CAAA,cAAW;4BAAC,OAAM;4BAAQ,WAAU;sCACnC,cAAA,8OAAC,+IAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,+IAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,8OAAC,+IAAA,CAAA,YAAS;4CAAC,WAAU;sDAAY;;;;;;;;;;;kDAEnC,8OAAC,+IAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAM,KAAK,IAAI;oDACf,WAAW;oDACX,oBAAoB,CAAC,UAAU;wDAC7B,iBAAiB,UAAU;oDAC7B;;;;;;gDAGD,2BACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,8IAAA,CAAA,oBAAiB;wDAChB,OAAO;wDACP,WAAW;wDACX,OAAO;wDACP,SAAS;wDACT,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAY5B,8OAAC,iJAAA,CAAA,SAAM;gBACL,MAAM;gBACN,cAAc,CAAC;oBACb,IAAI,CAAC,MAAM;wBACT,4BAA4B;oBAC9B;gBACF;0BAEA,cAAA,8OAAC,iJAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,iJAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,iJAAA,CAAA,cAAW;gCAAC,WAAU;0CAAY;;;;;;;;;;;sCAGrC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;sCAK/C,8OAAC,iJAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,iJAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;wCACP;wCACA,aAAa;wCACb,4BAA4B;wCAE5B,IAAI,WAAW;4CACb;wCACF;wCAEA,aAAa;wCACb,wIAAA,CAAA,QAAK,CAAC,IAAI,CAAC;oCACb;8CACD;;;;;;8CAGD,8OAAC,iJAAA,CAAA,SAAM;oCACL,WAAU;oCACV,MAAK;oCACL,SAAS;wCACP,MAAM;wCACN,aAAa;wCACb,4BAA4B;wCAE5B,IAAI,WAAW;4CACb;wCACF;wCAEA,aAAa;wCACb,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oCAChB;8CACD;;;;;;;;;;;;;;;;;;;;;;;0BAQP,8OAAC,iJAAA,CAAA,SAAM;gBACL,MAAM,CAAC,CAAC,mBAAmB,CAAC,CAAC;gBAC7B,cAAc,CAAC;oBACb,IAAI,CAAC,MAAM;wBACT,mBAAmB;wBACnB,oBAAoB;oBACtB;gBACF;0BAEA,cAAA,8OAAC,iJAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,iJAAA,CAAA,eAAY;sCACX,cAAA,8OAAC,iJAAA,CAAA,cAAW;gCAAC,WAAU;0CACpB,kBAAkB,wBAAwB;;;;;;;;;;;sCAI/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gJAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAgB;;;;;;sDAC/B,8OAAC,gJAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,MAAK;4CACL,OAAO,YAAY,IAAI;4CACvB,UAAU,CAAC,IAAM,eAAe;oDAAE,GAAG,WAAW;oDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;gDAAC;;;;;;;;;;;;8CAG3E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gJAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAiB;;;;;;sDAChC,8OAAC,gJAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,YAAY,KAAK;4CACxB,UAAU,CAAC,IAAM,eAAe;oDAAE,GAAG,WAAW;oDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;gDAAC;;;;;;;;;;;;8CAG5E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gJAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAoB;;;;;;sDACnC,8OAAC,gJAAA,CAAA,QAAK;4CACJ,IAAG;4CACH,OAAO,YAAY,QAAQ;4CAC3B,UAAU,CAAC,IAAM,eAAe;oDAAE,GAAG,WAAW;oDAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gDAAC;;;;;;;;;;;;8CAG/E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gJAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAuB;;;;;;sDACtC,8OAAC,mJAAA,CAAA,WAAQ;4CACP,IAAG;4CACH,OAAO,YAAY,WAAW;4CAC9B,UAAU,CAAC,IAAM,eAAe;oDAAE,GAAG,WAAW;oDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;gDAAC;4CAC9E,WAAU;;;;;;;;;;;;;;;;;;sCAKhB,8OAAC,iJAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,iJAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;wCACP,mBAAmB;wCACnB,oBAAoB;oCACtB;8CACD;;;;;;8CAGD,8OAAC,iJAAA,CAAA,SAAM;oCACL,WAAU;oCACV,MAAK;oCACL,SAAS;8CACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 5622, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/trips/mock-trip-plans.ts"], "sourcesContent": ["// Mock data cho các kế hoạch chuyến đi\n\nimport { TripPlan } from './types';\n\n// Mock trip plans data\nexport const MOCK_TRIP_PLANS: Record<string, TripPlan> = {\n  'plan-dalat': {\n    id: 'plan-dalat',\n    name: 'Khám phá Đà Lạt',\n    destination: 'Đà Lạt, Lâm Đồng',\n    region: 'Miền Trung',\n    description: 'Kế hoạch 3 ngày khám phá thành phố ngàn hoa Đà Lạt với các điểm tham quan nổi tiếng và ẩm thực đặc sắc.',\n    duration: 3,\n    image: 'https://images.pexels.com/photos/5746250/pexels-photo-5746250.jpeg?auto=compress&cs=tinysrgb&w=600',\n    tags: ['DaLat', 'ThanhPhoNganHoa', 'MienNui'],\n    days: [\n      {\n        id: 'day-1',\n        date: null,\n        activities: [\n          {\n            id: 'activity-1',\n            time: '08:00',\n            title: 'Ăn sáng tại chợ Đà Lạt',\n            description: 'Thưởng thức bánh căn, bánh mì xíu mại và cà phê Đà Lạt tại chợ trung tâm.',\n            location: 'Chợ Đà Lạt',\n            mainLocation: 'Chợ Đà Lạt',\n            type: 'Ăn sáng'\n          },\n          {\n            id: 'activity-2',\n            time: '09:30',\n            title: 'Tham quan Nhà thờ Con Gà',\n            description: 'Tham quan nhà thờ mang kiến trúc Pháp nổi tiếng với chiếc chuông hình con gà.',\n            location: 'Nhà thờ Con Gà',\n            mainLocation: 'Nhà thờ Con Gà',\n            type: 'Tham quan'\n          },\n          {\n            id: 'activity-3',\n            time: '11:30',\n            title: 'Ăn trưa tại quán Bún Bò Huế',\n            description: 'Thưởng thức bún bò Huế nổi tiếng tại Đà Lạt.',\n            location: 'Quán Bún Bò Huế',\n            mainLocation: 'Quán Bún Bò Huế',\n            type: 'Ăn trưa'\n          },\n          {\n            id: 'activity-4',\n            time: '13:00',\n            title: 'Tham quan Hồ Xuân Hương',\n            description: 'Dạo quanh hồ Xuân Hương, ngắm cảnh đẹp và thuê xe đạp đôi.',\n            location: 'Hồ Xuân Hương',\n            mainLocation: 'Hồ Xuân Hương',\n            type: 'Tham quan'\n          },\n          {\n            id: 'activity-5',\n            time: '16:00',\n            title: 'Cà phê tại Cà Phê Túi',\n            description: 'Thưởng thức cà phê trong không gian độc đáo của quán cà phê túi.',\n            location: 'Cà Phê Túi',\n            mainLocation: 'Cà Phê Túi',\n            type: 'Cà phê'\n          },\n          {\n            id: 'activity-6',\n            time: '18:30',\n            title: 'Ăn tối tại chợ đêm Đà Lạt',\n            description: 'Khám phá ẩm thực đường phố tại chợ đêm Đà Lạt.',\n            location: 'Chợ đêm Đà Lạt',\n            mainLocation: 'Chợ đêm Đà Lạt',\n            type: 'Ăn tối'\n          }\n        ]\n      },\n      {\n        id: 'day-2',\n        date: null,\n        activities: [\n          {\n            id: 'activity-7',\n            time: '07:30',\n            title: 'Ăn sáng tại khách sạn',\n            description: 'Thưởng thức bữa sáng tại khách sạn.',\n            location: 'Khách sạn',\n            mainLocation: 'Khách sạn',\n            type: 'Ăn sáng'\n          },\n          {\n            id: 'activity-8',\n            time: '09:00',\n            title: 'Tham quan Thung lũng Tình Yêu',\n            description: 'Khám phá cảnh đẹp của Thung lũng Tình Yêu.',\n            location: 'Thung lũng Tình Yêu',\n            mainLocation: 'Thung lũng Tình Yêu',\n            type: 'Tham quan'\n          },\n          {\n            id: 'activity-9',\n            time: '12:00',\n            title: 'Ăn trưa tại nhà hàng Leguda',\n            description: 'Thưởng thức ẩm thực Đà Lạt tại nhà hàng nổi tiếng.',\n            location: 'Nhà hàng Leguda',\n            mainLocation: 'Nhà hàng Leguda',\n            type: 'Ăn trưa'\n          },\n          {\n            id: 'activity-10',\n            time: '14:00',\n            title: 'Tham quan Làng Cù Lần',\n            description: 'Khám phá làng Cù Lần với kiến trúc độc đáo và hoạt động thú vị.',\n            location: 'Làng Cù Lần',\n            mainLocation: 'Làng Cù Lần',\n            type: 'Tham quan'\n          },\n          {\n            id: 'activity-11',\n            time: '18:00',\n            title: 'Ăn tối tại nhà hàng Ấn Độ',\n            description: 'Thưởng thức ẩm thực Ấn Độ tại Đà Lạt.',\n            location: 'Nhà hàng Ấn Độ',\n            mainLocation: 'Nhà hàng Ấn Độ',\n            type: 'Ăn tối'\n          }\n        ]\n      },\n      {\n        id: 'day-3',\n        date: null,\n        activities: [\n          {\n            id: 'activity-12',\n            time: '07:00',\n            title: 'Ăn sáng tại khách sạn',\n            description: 'Thưởng thức bữa sáng tại khách sạn.',\n            location: 'Khách sạn',\n            mainLocation: 'Khách sạn',\n            type: 'Ăn sáng'\n          },\n          {\n            id: 'activity-13',\n            time: '08:30',\n            title: 'Tham quan Đồi Robin',\n            description: 'Khám phá cảnh đẹp của Đồi Robin.',\n            location: 'Đồi Robin',\n            mainLocation: 'Đồi Robin',\n            type: 'Tham quan'\n          },\n          {\n            id: 'activity-14',\n            time: '11:30',\n            title: 'Ăn trưa tại quán Bánh Căn',\n            description: 'Thưởng thức bánh căn Đà Lạt truyền thống.',\n            location: 'Quán Bánh Căn',\n            mainLocation: 'Quán Bánh Căn',\n            type: 'Ăn trưa'\n          },\n          {\n            id: 'activity-15',\n            time: '13:30',\n            title: 'Mua sắm đặc sản',\n            description: 'Mua sắm đặc sản Đà Lạt làm quà.',\n            location: 'Chợ Đà Lạt',\n            mainLocation: 'Chợ Đà Lạt',\n            type: 'Mua sắm'\n          },\n          {\n            id: 'activity-16',\n            time: '16:00',\n            title: 'Cà phê tại Cà phê Mê Linh',\n            description: 'Thưởng thức cà phê chồn tại đồi Mê Linh.',\n            location: 'Cà phê Mê Linh',\n            mainLocation: 'Cà phê Mê Linh',\n            type: 'Cà phê'\n          }\n        ]\n      }\n    ],\n    authorId: 'user-1',\n    authorName: 'Nguyễn Văn A',\n    isPublic: true,\n    groupId: '1', // ID of the group this plan belongs to\n    createdAt: new Date('2023-05-15'),\n    updatedAt: new Date('2023-05-20')\n  },\n  'plan-sapa': {\n    id: 'plan-sapa',\n    name: 'Khám phá Sapa',\n    destination: 'Sapa, Lào Cai',\n    region: 'Miền Bắc',\n    description: 'Kế hoạch 3 ngày khám phá Sapa với những ruộng bậc thang tuyệt đẹp, văn hóa dân tộc đặc sắc và chinh phục đỉnh Fansipan.',\n    duration: 3,\n    image: 'https://images.pexels.com/photos/4350383/pexels-photo-4350383.jpeg?auto=compress&cs=tinysrgb&w=600',\n    tags: ['Sapa', 'RuongBacThang', 'Fansipan'],\n    days: [\n      {\n        id: 'day-1',\n        date: null,\n        activities: [\n          {\n            id: 'activity-1',\n            time: '08:00',\n            title: 'Ăn sáng',\n            description: 'Thưởng thức bữa sáng tại khách sạn với các món đặc sản vùng núi.',\n            location: 'Khách sạn',\n            mainLocation: 'Khách sạn',\n            type: 'Ăn sáng'\n          },\n          {\n            id: 'activity-2',\n            time: '09:30',\n            title: 'Tham quan Bản Cát Cát',\n            description: 'Khám phá văn hóa dân tộc H\\'Mông tại bản Cát Cát.',\n            location: 'Bản Cát Cát',\n            mainLocation: 'Bản Cát Cát',\n            type: 'Tham quan'\n          },\n          {\n            id: 'activity-3',\n            time: '12:30',\n            title: 'Ăn trưa tại nhà hàng địa phương',\n            description: 'Thưởng thức các món ăn đặc sản của người dân tộc.',\n            location: 'Nhà hàng địa phương',\n            mainLocation: 'Nhà hàng địa phương',\n            type: 'Ăn trưa'\n          }\n        ]\n      },\n      {\n        id: 'day-2',\n        date: null,\n        activities: [\n          {\n            id: 'activity-4',\n            time: '07:00',\n            title: 'Ăn sáng',\n            description: 'Thưởng thức bữa sáng tại khách sạn.',\n            location: 'Khách sạn',\n            mainLocation: 'Khách sạn',\n            type: 'Ăn sáng'\n          },\n          {\n            id: 'activity-5',\n            time: '08:30',\n            title: 'Chinh phục đỉnh Fansipan',\n            description: 'Đi cáp treo lên đỉnh Fansipan - \"Nóc nhà Đông Dương\".',\n            location: 'Fansipan',\n            mainLocation: 'Fansipan',\n            type: 'Tham quan'\n          }\n        ]\n      },\n      {\n        id: 'day-3',\n        date: null,\n        activities: [\n          {\n            id: 'activity-6',\n            time: '08:00',\n            title: 'Ăn sáng',\n            description: 'Thưởng thức bữa sáng tại khách sạn.',\n            location: 'Khách sạn',\n            mainLocation: 'Khách sạn',\n            type: 'Ăn sáng'\n          },\n          {\n            id: 'activity-7',\n            time: '09:30',\n            title: 'Tham quan ruộng bậc thang',\n            description: 'Ngắm nhìn ruộng bậc thang tuyệt đẹp tại Sapa.',\n            location: 'Ruộng bậc thang',\n            mainLocation: 'Ruộng bậc thang',\n            type: 'Tham quan'\n          }\n        ]\n      }\n    ],\n    authorId: 'user-2',\n    authorName: 'Trần Thị B',\n    isPublic: true,\n    groupId: '2',\n    createdAt: new Date('2023-06-10'),\n    updatedAt: new Date('2023-06-15')\n  },\n  'plan-halong': {\n    id: 'plan-halong',\n    name: 'Khám phá Vịnh Hạ Long',\n    destination: 'Hạ Long, Quảng Ninh',\n    region: 'Miền Bắc',\n    description: 'Kế hoạch 3 ngày khám phá kỳ quan thiên nhiên thế giới Vịnh Hạ Long với tour du thuyền, khám phá hang động và các hoạt động thú vị trên biển.',\n    duration: 3,\n    image: 'https://images.pexels.com/photos/2132180/pexels-photo-2132180.jpeg?auto=compress&cs=tinysrgb&w=600',\n    tags: ['HaLong', 'DiSanThienNhien', 'KyQuan'],\n    days: [\n      {\n        id: 'day-1',\n        date: null,\n        activities: [\n          {\n            id: 'activity-1',\n            time: '08:00',\n            title: 'Khởi hành từ Hà Nội',\n            description: 'Di chuyển từ Hà Nội đến Hạ Long bằng xe limousine.',\n            location: 'Hà Nội',\n            mainLocation: 'Hà Nội',\n            type: 'Di chuyển'\n          },\n          {\n            id: 'activity-2',\n            time: '12:00',\n            title: 'Ăn trưa trên du thuyền',\n            description: 'Thưởng thức bữa trưa hải sản trên du thuyền.',\n            location: 'Du thuyền',\n            mainLocation: 'Du thuyền',\n            type: 'Ăn trưa'\n          }\n        ]\n      },\n      {\n        id: 'day-2',\n        date: null,\n        activities: [\n          {\n            id: 'activity-3',\n            time: '06:00',\n            title: 'Ngắm bình minh trên vịnh',\n            description: 'Dậy sớm ngắm cảnh bình minh tuyệt đẹp trên vịnh Hạ Long.',\n            location: 'Vịnh Hạ Long',\n            mainLocation: 'Vịnh Hạ Long',\n            type: 'Tham quan'\n          },\n          {\n            id: 'activity-4',\n            time: '08:00',\n            title: 'Ăn sáng trên du thuyền',\n            description: 'Thưởng thức bữa sáng trên du thuyền.',\n            location: 'Du thuyền',\n            mainLocation: 'Du thuyền',\n            type: 'Ăn sáng'\n          }\n        ]\n      },\n      {\n        id: 'day-3',\n        date: null,\n        activities: [\n          {\n            id: 'activity-5',\n            time: '07:30',\n            title: 'Ăn sáng',\n            description: 'Thưởng thức bữa sáng trên du thuyền.',\n            location: 'Du thuyền',\n            mainLocation: 'Du thuyền',\n            type: 'Ăn sáng'\n          },\n          {\n            id: 'activity-6',\n            time: '09:00',\n            title: 'Tham quan hang Sửng Sốt',\n            description: 'Khám phá hang động lớn nhất và đẹp nhất vịnh Hạ Long.',\n            location: 'Hang Sửng Sốt',\n            mainLocation: 'Hang Sửng Sốt',\n            type: 'Tham quan'\n          }\n        ]\n      }\n    ],\n    authorId: 'user-3',\n    authorName: 'Lê Văn C',\n    isPublic: true,\n    groupId: '3',\n    createdAt: new Date('2023-07-05'),\n    updatedAt: new Date('2023-07-10')\n  }\n};\n\n// Hàm lấy kế hoạch chuyến đi theo ID\nexport const getTripPlanById = (id: string): TripPlan | undefined => {\n  return MOCK_TRIP_PLANS[id];\n};\n\n// Hàm lấy kế hoạch chuyến đi theo ID nhóm\nexport const getTripPlanByGroupId = (groupId: string): TripPlan | undefined => {\n  return Object.values(MOCK_TRIP_PLANS).find(plan => plan.groupId === groupId);\n};\n\n// Hàm cập nhật kế hoạch chuyến đi\nexport const updateTripPlan = (plan: TripPlan): TripPlan => {\n  MOCK_TRIP_PLANS[plan.id] = {\n    ...plan,\n    updatedAt: new Date()\n  };\n  return MOCK_TRIP_PLANS[plan.id];\n};\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;;;;AAKhC,MAAM,kBAA4C;IACvD,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,aAAa;QACb,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAS;YAAmB;SAAU;QAC7C,MAAM;YACJ;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;iBACD;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;iBACD;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;iBACD;YACH;SACD;QACD,UAAU;QACV,YAAY;QACZ,UAAU;QACV,SAAS;QACT,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA,aAAa;QACX,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,aAAa;QACb,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAQ;YAAiB;SAAW;QAC3C,MAAM;YACJ;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;iBACD;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;iBACD;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;iBACD;YACH;SACD;QACD,UAAU;QACV,YAAY;QACZ,UAAU;QACV,SAAS;QACT,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;IACA,eAAe;QACb,IAAI;QACJ,MAAM;QACN,aAAa;QACb,QAAQ;QACR,aAAa;QACb,UAAU;QACV,OAAO;QACP,MAAM;YAAC;YAAU;YAAmB;SAAS;QAC7C,MAAM;YACJ;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;iBACD;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;iBACD;YACH;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,YAAY;oBACV;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,OAAO;wBACP,aAAa;wBACb,UAAU;wBACV,cAAc;wBACd,MAAM;oBACR;iBACD;YACH;SACD;QACD,UAAU;QACV,YAAY;QACZ,UAAU;QACV,SAAS;QACT,WAAW,IAAI,KAAK;QACpB,WAAW,IAAI,KAAK;IACtB;AACF;AAGO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,eAAe,CAAC,GAAG;AAC5B;AAGO,MAAM,uBAAuB,CAAC;IACnC,OAAO,OAAO,MAAM,CAAC,iBAAiB,IAAI,CAAC,CAAA,OAAQ,KAAK,OAAO,KAAK;AACtE;AAGO,MAAM,iBAAiB,CAAC;IAC7B,eAAe,CAAC,KAAK,EAAE,CAAC,GAAG;QACzB,GAAG,IAAI;QACP,WAAW,IAAI;IACjB;IACA,OAAO,eAAe,CAAC,KAAK,EAAE,CAAC;AACjC", "debugId": null}}, {"offset": {"line": 6031, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/trips/GroupChatDetails.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { TripGroup, TripMember } from './mock-trip-groups';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/radix-ui/avatar';\nimport { Button } from '@/components/ui/radix-ui/button';\nimport { ScrollArea } from '@/components/ui/radix-ui/scroll-area';\nimport { Badge } from '@/components/ui/radix-ui/badge';\nimport { Calendar, MapPin, Users, Clock, Globe, Lock, UserPlus, FileText, Pencil, Trash2, Plus, ChevronRight } from 'lucide-react';\nimport { InviteMembersDialog } from './invite-members-dialog';\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/radix-ui/dialog';\nimport { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/radix-ui/tabs';\nimport { TRAVEL_PLAN_TEMPLATES } from '../planning/mock-data';\nimport { MemberListDialog } from './member-list-dialog';\nimport TripPlanEditor from './TripPlanEditor';\nimport { TripPlan } from './types';\nimport { getTripPlanByGroupId, updateTripPlan } from './mock-trip-plans';\n\ntype GroupChatDetailsProps = {\n  group: TripGroup;\n  isCollapsed?: boolean;\n};\n\nexport function GroupChatDetails({ group, isCollapsed = false }: GroupChatDetailsProps) {\n  const [members, setMembers] = useState<TripMember[]>(group.members.list);\n  const [memberCount, setMemberCount] = useState(group.members.count);\n  const [showPlanDetails, setShowPlanDetails] = useState(false);\n  const [showMemberList, setShowMemberList] = useState(false);\n  const [showInviteDialog, setShowInviteDialog] = useState(false);\n  const [showTripPlanEditor, setShowTripPlanEditor] = useState(false);\n\n  // Get trip plan from mock data\n  const [tripPlan, setTripPlan] = useState<TripPlan | undefined>(\n    getTripPlanByGroupId(group.id)\n  );\n\n  // Find a matching template for this group (in a real app, this would come from the database)\n  const matchingTemplate = TRAVEL_PLAN_TEMPLATES.find(\n    template => template.destination.includes(group.location.split(',')[0])\n  );\n\n  const handleInviteMembers = (newMembers: TripMember[]) => {\n    setMembers([...members, ...newMembers]);\n    setMemberCount(memberCount + newMembers.length);\n  };\n\n  const handleOpenInviteDialog = () => {\n    setShowInviteDialog(true);\n    setShowMemberList(false);\n  };\n\n  // Xử lý khi lưu kế hoạch chuyến đi\n  const handleSaveTripPlan = async (updatedPlan: TripPlan) => {\n    try {\n      // Trong thực tế, đây là nơi bạn sẽ gọi API để lưu kế hoạch\n      const savedPlan = updateTripPlan(updatedPlan);\n      setTripPlan(savedPlan);\n      return Promise.resolve();\n    } catch (error) {\n      console.error('Lỗi khi lưu kế hoạch:', error);\n      return Promise.reject(error);\n    }\n  };\n\n  return (\n    <div className=\"flex flex-col h-full\">\n      {/* Group info header */}\n      <div className=\"p-2 border-b border-purple-100 dark:border-purple-900 bg-teal-50/50 dark:bg-teal-900/10\">\n        <div className=\"flex items-center gap-3 mb-3\">\n          <Avatar className=\"h-12 w-12 border border-teal-100 dark:border-teal-800 shadow-xs\">\n            <AvatarImage src={group.image} alt={group.title} />\n            <AvatarFallback>{group.title[0]}</AvatarFallback>\n          </Avatar>\n          <div className=\"flex-1 min-w-0\">\n            <h2 className=\"font-semibold text-base truncate\">{group.title}</h2>\n            <div className=\"flex items-center gap-1.5 text-xs mt-1\">\n              {group.isPrivate ? (\n                <Badge variant=\"secondary\" className=\"flex items-center gap-1 text-xs h-5\">\n                  <Lock className=\"h-3 w-3\" />\n                  {!isCollapsed && \"Riêng tư\"}\n                </Badge>\n              ) : (\n                <Badge className=\"bg-green-500 flex items-center gap-1 text-xs h-5\">\n                  <Globe className=\"h-3 w-3\" />\n                  {!isCollapsed && \"Công khai\"}\n                </Badge>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {!isCollapsed && <p className=\"text-sm text-muted-foreground mb-3 line-clamp-3\">{group.description}</p>}\n\n        <div className={`${isCollapsed ? 'grid grid-cols-2' : 'grid grid-cols-2'} gap-2 text-sm`}>\n          <div className=\"flex items-center gap-1.5 text-muted-foreground\">\n            <MapPin className=\"h-4 w-4 text-teal-500\" />\n            <span className=\"truncate\">{group.location.split(',')[0]}</span>\n          </div>\n          {!isCollapsed && (\n            <>\n              <div className=\"flex items-center gap-1.5 text-muted-foreground\">\n                <Calendar className=\"h-4 w-4 text-teal-500\" />\n                <span>{group.date}</span>\n              </div>\n              <div className=\"flex items-center gap-1.5 text-muted-foreground\">\n                <Clock className=\"h-4 w-4 text-teal-500\" />\n                <span>{group.duration}</span>\n              </div>\n            </>\n          )}\n          <div className=\"flex items-center gap-1.5 text-muted-foreground\">\n            <Users className=\"h-4 w-4 text-teal-500\" />\n            <span>{memberCount}/{group.members.max}</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Members section - Simplified for vertical layout */}\n      {isCollapsed ? (\n        <div className=\"p-1 border-b border-teal-100 dark:border-teal-900\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-1\">\n              <h3 className=\"font-medium text-xs\">Thành viên</h3>\n              <Badge variant=\"outline\" className=\"text-[10px] h-4\">{memberCount}/{group.members.max}</Badge>\n            </div>\n            <div className=\"flex items-center gap-1\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-6 p-0 w-6 hover:bg-teal-100 dark:hover:bg-teal-900/20\"\n                onClick={() => setShowMemberList(true)}\n              >\n                <Users className=\"h-3 w-3 text-teal-500\" />\n              </Button>\n\n              {memberCount < group.members.max && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className=\"h-6 p-0 w-6 hover:bg-teal-100 dark:hover:bg-teal-900/20\"\n                  onClick={() => setShowInviteDialog(true)}\n                >\n                  <UserPlus className=\"h-3 w-3 text-teal-500\" />\n                </Button>\n              )}\n            </div>\n          </div>\n\n          {/* Chế độ thu gọn: chỉ hiển thị avatar */}\n          <div className=\"flex flex-wrap gap-1 mt-1\">\n            {members.slice(0, 8).map((member) => (\n              <Avatar key={member.id} className=\"h-6 w-6 border border-teal-100 dark:border-teal-800\" title={member.name}>\n                <AvatarImage src={member.avatar} alt={member.name} />\n                <AvatarFallback>{member.name[0]}</AvatarFallback>\n              </Avatar>\n            ))}\n            {members.length > 8 && (\n              <div\n                className=\"h-6 w-6 rounded-full bg-teal-100 dark:bg-teal-900/30 flex items-center justify-center text-[10px] text-teal-700 dark:text-teal-300 cursor-pointer\"\n                onClick={() => setShowMemberList(true)}\n              >\n                +{members.length - 8}\n              </div>\n            )}\n          </div>\n        </div>\n      ) : (\n        <div className=\"p-3 border-b border-teal-100 dark:border-teal-900\">\n          <div className=\"flex items-center justify-between mb-2\">\n            <div className=\"flex items-center gap-1.5\">\n              <h3 className=\"font-medium text-sm\">Thành viên</h3>\n              <Badge variant=\"outline\" className=\"text-xs h-5\">{memberCount}/{group.members.max}</Badge>\n            </div>\n            <div className=\"flex items-center gap-1.5\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                className=\"h-7 px-2 text-xs gap-1.5 hover:bg-teal-100 dark:hover:bg-teal-900/20\"\n                onClick={() => setShowMemberList(true)}\n              >\n                <Users className=\"h-3.5 w-3.5 text-teal-500\" />\n                <span>Xem tất cả</span>\n                <ChevronRight className=\"h-3.5 w-3.5 ml-0.5 text-muted-foreground\" />\n              </Button>\n\n              {memberCount < group.members.max && (\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  className=\"h-7 px-2 text-xs gap-1.5 hover:bg-teal-100 dark:hover:bg-teal-900/20\"\n                  onClick={() => setShowInviteDialog(true)}\n                >\n                  <UserPlus className=\"h-3.5 w-3.5 text-teal-500\" />\n                  <span>Mời</span>\n                </Button>\n              )}\n            </div>\n          </div>\n\n          {/* Chế độ đầy đủ */}\n          <div className=\"space-y-1\">\n            {members.slice(0, 5).map((member) => (\n              <div key={member.id} className=\"flex items-center gap-2 p-1.5 rounded-md hover:bg-secondary/50\">\n                <Avatar className=\"h-7 w-7\">\n                  <AvatarImage src={member.avatar} alt={member.name} />\n                  <AvatarFallback>{member.name[0]}</AvatarFallback>\n                </Avatar>\n                <div className=\"flex items-center justify-between w-full\">\n                  <span className=\"text-sm\">{member.name}</span>\n                  {member.role === 'admin' && (\n                    <Badge variant=\"outline\" className=\"text-xs h-5 bg-teal-50 text-teal-700 border-teal-200 dark:bg-teal-900/20 dark:text-teal-400 dark:border-teal-800\">Admin</Badge>\n                  )}\n                </div>\n              </div>\n            ))}\n\n            {/* Hiển thị số thành viên còn lại nếu có nhiều hơn 5 */}\n            {members.length > 5 && (\n              <Button\n                variant=\"ghost\"\n                className=\"w-full h-8 text-sm text-muted-foreground hover:bg-secondary/50\"\n                onClick={() => setShowMemberList(true)}\n              >\n                Xem thêm {members.length - 5} thành viên khác\n              </Button>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Dialog hiển thị đầy đủ danh sách thành viên */}\n      <MemberListDialog\n        members={members}\n        maxMembers={group.members.max}\n        isOpen={showMemberList}\n        onClose={() => setShowMemberList(false)}\n        onInvite={handleOpenInviteDialog}\n      />\n\n      {/* Dialog mời thành viên */}\n      <InviteMembersDialog\n        tripId={group.id}\n        currentMembers={members}\n        maxMembers={group.members.max}\n        onInvite={handleInviteMembers}\n        open={showInviteDialog}\n        onOpenChange={setShowInviteDialog}\n      />\n\n      {/* Plan section - Simplified for vertical layout */}\n      {(tripPlan || matchingTemplate) && isCollapsed ? (\n        <div className=\"p-1 flex-shrink-0 overflow-hidden flex flex-col\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center gap-1\">\n              <h3 className=\"font-medium text-xs\">Kế hoạch</h3>\n            </div>\n            {tripPlan && (\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                className=\"h-6 p-0 w-6 hover:bg-teal-100 dark:hover:bg-teal-900/20\"\n                onClick={() => setShowTripPlanEditor(true)}\n              >\n                <Pencil className=\"h-3 w-3 text-teal-500\" />\n              </Button>\n            )}\n          </div>\n\n          {/* Chế độ thu gọn */}\n          <div className=\"flex items-center gap-2 mt-1\">\n            <Button\n              variant=\"outline\"\n              className=\"flex items-center justify-center p-1 hover:bg-teal-50 dark:hover:bg-teal-900/20 group rounded-lg\"\n              onClick={() => tripPlan ? setShowTripPlanEditor(true) : setShowPlanDetails(true)}\n            >\n              <div className=\"h-8 w-8 rounded-md overflow-hidden shrink-0 border border-teal-100 dark:border-teal-800 shadow-xs\">\n                {/* eslint-disable-next-line */}\n                <img\n                  src={(tripPlan || matchingTemplate)?.image}\n                  alt={(tripPlan || matchingTemplate)?.name}\n                  className=\"h-full w-full object-cover transition-transform group-hover:scale-105\"\n                />\n              </div>\n            </Button>\n            <div className=\"text-left flex-1 min-w-0\">\n              <div className=\"font-medium text-xs truncate\">{(tripPlan || matchingTemplate)?.name}</div>\n              <div className=\"text-xs text-muted-foreground flex items-center gap-1\">\n                <Clock className=\"h-2.5 w-2.5 text-teal-500 flex-shrink-0\" />\n                <span className=\"truncate\">{(tripPlan || matchingTemplate)?.duration} ngày</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      ) : (tripPlan || matchingTemplate) && (\n        <div className=\"p-3 flex-1 overflow-hidden flex flex-col\">\n          <div className=\"flex items-center justify-between mb-2\">\n            <div className=\"flex items-center gap-1.5\">\n              <h3 className=\"font-medium text-sm\">Kế hoạch</h3>\n              <Badge variant=\"outline\" className=\"text-xs h-5 bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800\">Đã áp dụng</Badge>\n            </div>\n            {tripPlan && (\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                className=\"h-7 px-2 text-xs gap-1.5 hover:bg-teal-100 dark:hover:bg-teal-900/20\"\n                onClick={() => setShowTripPlanEditor(true)}\n              >\n                <Pencil className=\"h-3.5 w-3.5 text-teal-500\" />\n                <span>Chỉnh sửa</span>\n              </Button>\n            )}\n          </div>\n\n          {/* Chế độ đầy đủ */}\n          <Button\n            variant=\"outline\"\n            className=\"w-full flex items-center gap-3 h-auto p-3 justify-start hover:bg-teal-50 dark:hover:bg-teal-900/20 group rounded-lg\"\n            onClick={() => tripPlan ? setShowTripPlanEditor(true) : setShowPlanDetails(true)}\n          >\n            <div className=\"h-16 w-16 rounded-md overflow-hidden shrink-0 border border-teal-100 dark:border-teal-800 shadow-xs\">\n              {/* eslint-disable-next-line */}\n              <img\n                src={(tripPlan || matchingTemplate)?.image}\n                alt={(tripPlan || matchingTemplate)?.name}\n                className=\"h-full w-full object-cover transition-transform group-hover:scale-105\"\n              />\n            </div>\n            <div className=\"text-left\">\n              <div className=\"font-medium text-sm\">{(tripPlan || matchingTemplate)?.name}</div>\n              <div className=\"text-sm text-muted-foreground mt-1 flex items-center gap-1.5\">\n                <Clock className=\"h-3.5 w-3.5 text-teal-500\" />\n                <span>{(tripPlan || matchingTemplate)?.duration} ngày</span>\n                <span className=\"mx-1\">•</span>\n                <MapPin className=\"h-3.5 w-3.5 text-teal-500\" />\n                <span>{(tripPlan || matchingTemplate)?.destination.split(',')[0]}</span>\n              </div>\n              <div className=\"text-xs text-muted-foreground mt-1\">\n                Nhấn để {tripPlan ? 'chỉnh sửa' : 'xem chi tiết'} lịch trình\n              </div>\n            </div>\n          </Button>\n        </div>\n      )}\n\n      {/* Plan details dialog */}\n      {matchingTemplate && (\n        <Dialog open={showPlanDetails} onOpenChange={setShowPlanDetails}>\n          <DialogContent className=\"max-w-3xl max-h-[85vh] overflow-y-auto\">\n            <DialogHeader>\n              <DialogTitle className=\"text-xl flex items-center gap-2\">\n                <span>{matchingTemplate.name}</span>\n                <Badge variant=\"outline\" className=\"ml-2 bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800\">\n                  Đã áp dụng\n                </Badge>\n              </DialogTitle>\n              <DialogDescription>\n                <div className=\"flex items-center gap-2 mt-1\">\n                  <MapPin className=\"h-4 w-4 text-purple-500\" />\n                  <span>{matchingTemplate.destination}</span>\n                  <span className=\"mx-2\">•</span>\n                  <Clock className=\"h-4 w-4 text-purple-500\" />\n                  <span>{matchingTemplate.duration} ngày</span>\n                </div>\n              </DialogDescription>\n            </DialogHeader>\n\n            <div className=\"mt-4 space-y-6\">\n              <div className=\"aspect-video rounded-lg overflow-hidden border border-purple-100 dark:border-purple-800\">\n                {/* eslint-disable-next-line */}\n                <img\n                  src={matchingTemplate.image}\n                  alt={matchingTemplate.name}\n                  className=\"w-full h-full object-cover\"\n                />\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-medium mb-2\">Mô tả</h3>\n                <p className=\"text-muted-foreground\">{matchingTemplate.description}</p>\n              </div>\n\n              <div>\n                <h3 className=\"text-lg font-medium mb-4\">Lịch trình chi tiết</h3>\n                <Tabs defaultValue=\"schedule\" className=\"w-full\">\n                  <TabsList className=\"mb-4\">\n                    <TabsTrigger value=\"schedule\">Lịch trình chi tiết</TabsTrigger>\n                    <TabsTrigger value=\"chart\">Biểu đồ</TabsTrigger>\n                  </TabsList>\n\n                  <TabsContent value=\"schedule\" className=\"space-y-4\">\n                    <Tabs defaultValue={matchingTemplate.days[0].id} className=\"w-full\">\n                      <TabsList className=\"mb-4 flex flex-wrap\">\n                        {matchingTemplate.days.map((day, index) => (\n                          <TabsTrigger key={day.id} value={day.id}>\n                            Ngày {index + 1}\n                          </TabsTrigger>\n                        ))}\n                      </TabsList>\n\n                      {matchingTemplate.days.map((day, dayIndex) => (\n                        <TabsContent key={day.id} value={day.id} className=\"space-y-4\">\n                          <div className=\"space-y-4\">\n                            {day.activities.map((activity) => (\n                              <div\n                                key={activity.id}\n                                className=\"border border-purple-100 dark:border-purple-900 rounded-lg p-4 space-y-2 hover:bg-purple-50/50 dark:hover:bg-purple-900/10 transition-colors group\"\n                              >\n                                <div className=\"flex items-center justify-between\">\n                                  <div className=\"flex items-center gap-2\">\n                                    <span className=\"font-medium text-purple-600 dark:text-purple-400 w-16\">\n                                      {activity.time}\n                                    </span>\n                                    <span className=\"font-medium\">{activity.title}</span>\n                                  </div>\n                                  <div className=\"flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity\">\n                                    <Button\n                                      variant=\"ghost\"\n                                      size=\"icon\"\n                                      className=\"h-7 w-7 rounded-full hover:bg-purple-100 dark:hover:bg-purple-900/20\"\n                                      title=\"Chỉnh sửa hoạt động\"\n                                    >\n                                      <Pencil className=\"h-3.5 w-3.5 text-purple-600\" />\n                                    </Button>\n                                    <Button\n                                      variant=\"ghost\"\n                                      size=\"icon\"\n                                      className=\"h-7 w-7 rounded-full hover:bg-red-100 dark:hover:bg-red-900/20\"\n                                      title=\"Xóa hoạt động\"\n                                    >\n                                      <Trash2 className=\"h-3.5 w-3.5 text-red-600\" />\n                                    </Button>\n                                  </div>\n                                </div>\n                                <div className=\"text-sm text-muted-foreground\">\n                                  {activity.description}\n                                </div>\n                                <div className=\"flex items-center gap-2 text-sm text-muted-foreground\">\n                                  <MapPin className=\"h-4 w-4 text-blue-500\" />\n                                  <span>{activity.location}</span>\n                                </div>\n                              </div>\n                            ))}\n\n                            {/* Nút thêm hoạt động mới */}\n                            <Button\n                              variant=\"outline\"\n                              className=\"w-full py-6 border-dashed border-purple-200 dark:border-purple-800 hover:border-purple-400 dark:hover:border-purple-600 hover:bg-purple-50/50 dark:hover:bg-purple-900/10\"\n                            >\n                              <div className=\"flex flex-col items-center justify-center gap-1\">\n                                <div className=\"h-8 w-8 rounded-full bg-purple-100 dark:bg-purple-900/30 flex items-center justify-center\">\n                                  <Plus className=\"h-4 w-4 text-purple-600 dark:text-purple-400\" />\n                                </div>\n                                <span className=\"text-sm font-medium text-purple-600 dark:text-purple-400\">Thêm hoạt động mới</span>\n                                <span className=\"text-xs text-muted-foreground\">Nhấn để thêm hoạt động vào lịch trình</span>\n                              </div>\n                            </Button>\n                          </div>\n                        </TabsContent>\n                      ))}\n                    </Tabs>\n                  </TabsContent>\n\n                  <TabsContent value=\"chart\">\n                    <div className=\"border rounded-lg p-4\">\n                      {/* Chart description */}\n                      <div className=\"mb-4 text-sm text-muted-foreground\">\n                        <p>Biểu đồ lịch trình hiển thị các hoạt động theo thời gian từ 6:00 đến 00:00.</p>\n                      </div>\n\n                      {/* Activity type legend */}\n                      <div className=\"flex flex-wrap gap-2 mb-4\">\n                        <div className=\"flex items-center\">\n                          <div className=\"w-3 h-3 rounded-sm mr-1 bg-red-200 dark:bg-red-900/50\"></div>\n                          <span className=\"text-xs text-red-800 dark:text-red-300\">Ăn uống</span>\n                        </div>\n                        <div className=\"flex items-center\">\n                          <div className=\"w-3 h-3 rounded-sm mr-1 bg-blue-200 dark:bg-blue-900/50\"></div>\n                          <span className=\"text-xs text-blue-800 dark:text-blue-300\">Tham quan</span>\n                        </div>\n                        <div className=\"flex items-center\">\n                          <div className=\"w-3 h-3 rounded-sm mr-1 bg-amber-200 dark:bg-amber-900/50\"></div>\n                          <span className=\"text-xs text-amber-800 dark:text-amber-300\">Cà phê</span>\n                        </div>\n                        <div className=\"flex items-center\">\n                          <div className=\"w-3 h-3 rounded-sm mr-1 bg-purple-200 dark:bg-purple-900/50\"></div>\n                          <span className=\"text-xs text-purple-800 dark:text-purple-300\">Mua sắm</span>\n                        </div>\n                        <div className=\"flex items-center\">\n                          <div className=\"w-3 h-3 rounded-sm mr-1 bg-green-200 dark:bg-green-900/50\"></div>\n                          <span className=\"text-xs text-green-800 dark:text-green-300\">Di chuyển</span>\n                        </div>\n                        <div className=\"flex items-center\">\n                          <div className=\"w-3 h-3 rounded-sm mr-1 bg-gray-200 dark:bg-gray-800\"></div>\n                          <span className=\"text-xs text-gray-800 dark:text-gray-300\">Khác</span>\n                        </div>\n                      </div>\n\n                      {/* Chart container */}\n                      <div className=\"grid grid-cols-[200px_1fr] border rounded-md overflow-hidden\">\n                        {/* Header row */}\n                        <div className=\"bg-muted/30 border-b p-2 font-medium text-sm\">Địa điểm</div>\n                        <div className=\"flex border-b bg-muted/30 overflow-hidden\">\n                          <div className=\"flex min-w-[1440px]\"> {/* 18 hours * 80px */}\n                            {Array.from({ length: 19 }).map((_, i) => (\n                              <div key={i} className=\"w-[80px] shrink-0 text-center text-xs text-muted-foreground py-2 border-r last:border-r-0\">\n                                {(i + 6) % 24}:00\n                              </div>\n                            ))}\n                          </div>\n                        </div>\n\n                        {/* Scrollable content */}\n                        <div className=\"max-h-[500px] overflow-auto\">\n                          {/* Location column */}\n                          {matchingTemplate.days[0].activities.map((activity) => {\n                            // Extract main location (before first comma)\n                            const mainLocation = activity.location.split(',')[0].trim();\n                            return (\n                              <div key={activity.id} className=\"h-12 flex items-center text-sm font-medium border-b last:border-b-0 px-3 truncate\">\n                                {mainLocation}\n                              </div>\n                            );\n                          })}\n                        </div>\n\n                        <div className=\"relative overflow-x-auto max-h-[500px]\">\n                          <div className=\"min-w-[1440px]\"> {/* 18 hours * 80px */}\n                            {/* Timeline grid background */}\n                            <div className=\"absolute inset-0 grid grid-cols-18 w-full h-full pointer-events-none\">\n                              {Array.from({ length: 18 }).map((_, i) => (\n                                <div key={i} className=\"border-r h-full last:border-r-0\"></div>\n                              ))}\n                            </div>\n\n                            {/* Timeline rows */}\n                            {matchingTemplate.days[0].activities.map((activity, index) => {\n                              // Determine activity type for styling\n                              let bgColor = \"bg-gray-200 dark:bg-gray-800\";\n                              let textColor = \"text-gray-800 dark:text-gray-300\";\n\n                              if (activity.title.includes(\"Ăn\") || activity.title.includes(\"ăn\")) {\n                                bgColor = \"bg-red-200 dark:bg-red-900/50\";\n                                textColor = \"text-red-800 dark:text-red-300\";\n                              } else if (activity.title.includes(\"Tham quan\") || activity.title.includes(\"thăm\")) {\n                                bgColor = \"bg-blue-200 dark:bg-blue-900/50\";\n                                textColor = \"text-blue-800 dark:text-blue-300\";\n                              } else if (activity.title.includes(\"Cà phê\") || activity.title.includes(\"cà phê\")) {\n                                bgColor = \"bg-amber-200 dark:bg-amber-900/50\";\n                                textColor = \"text-amber-800 dark:text-amber-300\";\n                              } else if (activity.title.includes(\"Mua\") || activity.title.includes(\"mua\")) {\n                                bgColor = \"bg-purple-200 dark:bg-purple-900/50\";\n                                textColor = \"text-purple-800 dark:text-purple-300\";\n                              } else if (activity.title.includes(\"Di chuyển\") || activity.title.includes(\"đi\")) {\n                                bgColor = \"bg-green-200 dark:bg-green-900/50\";\n                                textColor = \"text-green-800 dark:text-green-300\";\n                              }\n\n                              // Calculate position and width\n                              const [hours, minutes] = activity.time.split(':').map(Number);\n                              const startPosition = (hours - 6) * 80 + (minutes / 60) * 80;\n\n                              // Calculate duration (default to 1.5 hours if it's the last activity)\n                              let duration = 1.5;\n                              if (index < matchingTemplate.days[0].activities.length - 1) {\n                                const nextActivity = matchingTemplate.days[0].activities[index + 1];\n                                const [nextHours, nextMinutes] = nextActivity.time.split(':').map(Number);\n                                const nextTimeInHours = nextHours + nextMinutes / 60;\n                                const currentTimeInHours = hours + minutes / 60;\n                                duration = nextTimeInHours - currentTimeInHours;\n                              }\n\n                              const width = duration * 80;\n\n                              return (\n                                <div key={activity.id} className=\"h-12 border-b last:border-b-0 relative\">\n                                  <div\n                                    className={`absolute h-8 top-2 rounded-md border ${bgColor} ${textColor} flex items-center px-2 text-xs shadow-xs overflow-hidden group cursor-pointer hover:ring-2 hover:ring-purple-400 dark:hover:ring-purple-600 hover:z-10`}\n                                    style={{\n                                      left: `${startPosition}px`,\n                                      width: `${width}px`,\n                                    }}\n                                    title={`${activity.time} - ${activity.title}`}\n                                  >\n                                    <div className=\"truncate flex-1\">\n                                      {activity.time} - {activity.title}\n                                    </div>\n                                    <div className=\"flex items-center gap-0.5 ml-1 opacity-0 group-hover:opacity-100 transition-opacity\">\n                                      <Button\n                                        variant=\"ghost\"\n                                        size=\"icon\"\n                                        className=\"h-5 w-5 p-0 rounded-full hover:bg-white/20 dark:hover:bg-black/20\"\n                                        title=\"Chỉnh sửa\"\n                                      >\n                                        <Pencil className=\"h-2.5 w-2.5\" />\n                                      </Button>\n                                    </div>\n                                  </div>\n                                </div>\n                              );\n                            })}\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n                  </TabsContent>\n                </Tabs>\n              </div>\n            </div>\n          </DialogContent>\n        </Dialog>\n      )}\n\n      {/* TripPlanEditor */}\n      {tripPlan && (\n        <TripPlanEditor\n          plan={tripPlan}\n          isOpen={showTripPlanEditor}\n          onClose={() => setShowTripPlanEditor(false)}\n          onSave={handleSaveTripPlan}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAhBA;;;;;;;;;;;;;;AAuBO,SAAS,iBAAiB,EAAE,KAAK,EAAE,cAAc,KAAK,EAAyB;IACpF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,MAAM,OAAO,CAAC,IAAI;IACvE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,OAAO,CAAC,KAAK;IAClE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,+BAA+B;IAC/B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EACrC,CAAA,GAAA,iJAAA,CAAA,uBAAoB,AAAD,EAAE,MAAM,EAAE;IAG/B,6FAA6F;IAC7F,MAAM,mBAAmB,2IAAA,CAAA,wBAAqB,CAAC,IAAI,CACjD,CAAA,WAAY,SAAS,WAAW,CAAC,QAAQ,CAAC,MAAM,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;IAGxE,MAAM,sBAAsB,CAAC;QAC3B,WAAW;eAAI;eAAY;SAAW;QACtC,eAAe,cAAc,WAAW,MAAM;IAChD;IAEA,MAAM,yBAAyB;QAC7B,oBAAoB;QACpB,kBAAkB;IACpB;IAEA,mCAAmC;IACnC,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,2DAA2D;YAC3D,MAAM,YAAY,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE;YACjC,YAAY;YACZ,OAAO,QAAQ,OAAO;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,OAAO,QAAQ,MAAM,CAAC;QACxB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iJAAA,CAAA,SAAM;gCAAC,WAAU;;kDAChB,8OAAC,iJAAA,CAAA,cAAW;wCAAC,KAAK,MAAM,KAAK;wCAAE,KAAK,MAAM,KAAK;;;;;;kDAC/C,8OAAC,iJAAA,CAAA,iBAAc;kDAAE,MAAM,KAAK,CAAC,EAAE;;;;;;;;;;;;0CAEjC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAoC,MAAM,KAAK;;;;;;kDAC7D,8OAAC;wCAAI,WAAU;kDACZ,MAAM,SAAS,iBACd,8OAAC,gJAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAY,WAAU;;8DACnC,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,CAAC,eAAe;;;;;;iEAGnB,8OAAC,gJAAA,CAAA,QAAK;4CAAC,WAAU;;8DACf,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAChB,CAAC,eAAe;;;;;;;;;;;;;;;;;;;;;;;;oBAO1B,CAAC,6BAAe,8OAAC;wBAAE,WAAU;kCAAmD,MAAM,WAAW;;;;;;kCAElG,8OAAC;wBAAI,WAAW,GAAG,cAAc,qBAAqB,mBAAmB,cAAc,CAAC;;0CACtF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAAY,MAAM,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;;;;;;;4BAEzD,CAAC,6BACA;;kDACE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAM,MAAM,IAAI;;;;;;;;;;;;kDAEnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;0DAAM,MAAM,QAAQ;;;;;;;;;;;;;;0CAI3B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;kDACjB,8OAAC;;4CAAM;4CAAY;4CAAE,MAAM,OAAO,CAAC,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;YAM3C,4BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsB;;;;;;kDACpC,8OAAC,gJAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;;4CAAmB;4CAAY;4CAAE,MAAM,OAAO,CAAC,GAAG;;;;;;;;;;;;;0CAEvF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iJAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;kDAEjC,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;oCAGlB,cAAc,MAAM,OAAO,CAAC,GAAG,kBAC9B,8OAAC,iJAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,oBAAoB;kDAEnC,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kCAO5B,8OAAC;wBAAI,WAAU;;4BACZ,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,uBACxB,8OAAC,iJAAA,CAAA,SAAM;oCAAiB,WAAU;oCAAsD,OAAO,OAAO,IAAI;;sDACxG,8OAAC,iJAAA,CAAA,cAAW;4CAAC,KAAK,OAAO,MAAM;4CAAE,KAAK,OAAO,IAAI;;;;;;sDACjD,8OAAC,iJAAA,CAAA,iBAAc;sDAAE,OAAO,IAAI,CAAC,EAAE;;;;;;;mCAFpB,OAAO,EAAE;;;;;4BAKvB,QAAQ,MAAM,GAAG,mBAChB,8OAAC;gCACC,WAAU;gCACV,SAAS,IAAM,kBAAkB;;oCAClC;oCACG,QAAQ,MAAM,GAAG;;;;;;;;;;;;;;;;;;qCAM3B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsB;;;;;;kDACpC,8OAAC,gJAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;;4CAAe;4CAAY;4CAAE,MAAM,OAAO,CAAC,GAAG;;;;;;;;;;;;;0CAEnF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iJAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,kBAAkB;;0DAEjC,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;0DAAK;;;;;;0DACN,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;oCAGzB,cAAc,MAAM,OAAO,CAAC,GAAG,kBAC9B,8OAAC,iJAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,oBAAoB;;0DAEnC,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;kCAOd,8OAAC;wBAAI,WAAU;;4BACZ,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,uBACxB,8OAAC;oCAAoB,WAAU;;sDAC7B,8OAAC,iJAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,iJAAA,CAAA,cAAW;oDAAC,KAAK,OAAO,MAAM;oDAAE,KAAK,OAAO,IAAI;;;;;;8DACjD,8OAAC,iJAAA,CAAA,iBAAc;8DAAE,OAAO,IAAI,CAAC,EAAE;;;;;;;;;;;;sDAEjC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAW,OAAO,IAAI;;;;;;gDACrC,OAAO,IAAI,KAAK,yBACf,8OAAC,gJAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAAmH;;;;;;;;;;;;;mCARlJ,OAAO,EAAE;;;;;4BAepB,QAAQ,MAAM,GAAG,mBAChB,8OAAC,iJAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,kBAAkB;;oCAClC;oCACW,QAAQ,MAAM,GAAG;oCAAE;;;;;;;;;;;;;;;;;;;0BAQvC,8OAAC,qJAAA,CAAA,mBAAgB;gBACf,SAAS;gBACT,YAAY,MAAM,OAAO,CAAC,GAAG;gBAC7B,QAAQ;gBACR,SAAS,IAAM,kBAAkB;gBACjC,UAAU;;;;;;0BAIZ,8OAAC,wJAAA,CAAA,sBAAmB;gBAClB,QAAQ,MAAM,EAAE;gBAChB,gBAAgB;gBAChB,YAAY,MAAM,OAAO,CAAC,GAAG;gBAC7B,UAAU;gBACV,MAAM;gBACN,cAAc;;;;;;YAIf,CAAC,YAAY,gBAAgB,KAAK,4BACjC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAsB;;;;;;;;;;;4BAErC,0BACC,8OAAC,iJAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,sBAAsB;0CAErC,cAAA,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAMxB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iJAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,WAAW,sBAAsB,QAAQ,mBAAmB;0CAE3E,cAAA,8OAAC;oCAAI,WAAU;8CAEb,cAAA,8OAAC;wCACC,KAAK,CAAC,YAAY,gBAAgB,GAAG;wCACrC,KAAK,CAAC,YAAY,gBAAgB,GAAG;wCACrC,WAAU;;;;;;;;;;;;;;;;0CAIhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgC,CAAC,YAAY,gBAAgB,GAAG;;;;;;kDAC/E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;gDAAK,WAAU;;oDAAY,CAAC,YAAY,gBAAgB,GAAG;oDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAK3E,CAAC,YAAY,gBAAgB,mBAC/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAsB;;;;;;kDACpC,8OAAC,gJAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAU,WAAU;kDAAyH;;;;;;;;;;;;4BAE7J,0BACC,8OAAC,iJAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,sBAAsB;;kDAErC,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;kCAMZ,8OAAC,iJAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAU;wBACV,SAAS,IAAM,WAAW,sBAAsB,QAAQ,mBAAmB;;0CAE3E,8OAAC;gCAAI,WAAU;0CAEb,cAAA,8OAAC;oCACC,KAAK,CAAC,YAAY,gBAAgB,GAAG;oCACrC,KAAK,CAAC,YAAY,gBAAgB,GAAG;oCACrC,WAAU;;;;;;;;;;;0CAGd,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAuB,CAAC,YAAY,gBAAgB,GAAG;;;;;;kDACtE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;;oDAAM,CAAC,YAAY,gBAAgB,GAAG;oDAAS;;;;;;;0DAChD,8OAAC;gDAAK,WAAU;0DAAO;;;;;;0DACvB,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAM,CAAC,YAAY,gBAAgB,GAAG,YAAY,MAAM,IAAI,CAAC,EAAE;;;;;;;;;;;;kDAElE,8OAAC;wCAAI,WAAU;;4CAAqC;4CACzC,WAAW,cAAc;4CAAe;;;;;;;;;;;;;;;;;;;;;;;;;YAQ1D,kCACC,8OAAC,iJAAA,CAAA,SAAM;gBAAC,MAAM;gBAAiB,cAAc;0BAC3C,cAAA,8OAAC,iJAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,iJAAA,CAAA,eAAY;;8CACX,8OAAC,iJAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;sDAAM,iBAAiB,IAAI;;;;;;sDAC5B,8OAAC,gJAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAkH;;;;;;;;;;;;8CAIvJ,8OAAC,iJAAA,CAAA,oBAAiB;8CAChB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAM,iBAAiB,WAAW;;;;;;0DACnC,8OAAC;gDAAK,WAAU;0DAAO;;;;;;0DACvB,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;;oDAAM,iBAAiB,QAAQ;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;sCAKvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAEb,cAAA,8OAAC;wCACC,KAAK,iBAAiB,KAAK;wCAC3B,KAAK,iBAAiB,IAAI;wCAC1B,WAAU;;;;;;;;;;;8CAId,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,8OAAC;4CAAE,WAAU;sDAAyB,iBAAiB,WAAW;;;;;;;;;;;;8CAGpE,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA2B;;;;;;sDACzC,8OAAC,+IAAA,CAAA,OAAI;4CAAC,cAAa;4CAAW,WAAU;;8DACtC,8OAAC,+IAAA,CAAA,WAAQ;oDAAC,WAAU;;sEAClB,8OAAC,+IAAA,CAAA,cAAW;4DAAC,OAAM;sEAAW;;;;;;sEAC9B,8OAAC,+IAAA,CAAA,cAAW;4DAAC,OAAM;sEAAQ;;;;;;;;;;;;8DAG7B,8OAAC,+IAAA,CAAA,cAAW;oDAAC,OAAM;oDAAW,WAAU;8DACtC,cAAA,8OAAC,+IAAA,CAAA,OAAI;wDAAC,cAAc,iBAAiB,IAAI,CAAC,EAAE,CAAC,EAAE;wDAAE,WAAU;;0EACzD,8OAAC,+IAAA,CAAA,WAAQ;gEAAC,WAAU;0EACjB,iBAAiB,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC/B,8OAAC,+IAAA,CAAA,cAAW;wEAAc,OAAO,IAAI,EAAE;;4EAAE;4EACjC,QAAQ;;uEADE,IAAI,EAAE;;;;;;;;;;4DAM3B,iBAAiB,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,yBAC/B,8OAAC,+IAAA,CAAA,cAAW;oEAAc,OAAO,IAAI,EAAE;oEAAE,WAAU;8EACjD,cAAA,8OAAC;wEAAI,WAAU;;4EACZ,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC,yBACnB,8OAAC;oFAEC,WAAU;;sGAEV,8OAAC;4FAAI,WAAU;;8GACb,8OAAC;oGAAI,WAAU;;sHACb,8OAAC;4GAAK,WAAU;sHACb,SAAS,IAAI;;;;;;sHAEhB,8OAAC;4GAAK,WAAU;sHAAe,SAAS,KAAK;;;;;;;;;;;;8GAE/C,8OAAC;oGAAI,WAAU;;sHACb,8OAAC,iJAAA,CAAA,SAAM;4GACL,SAAQ;4GACR,MAAK;4GACL,WAAU;4GACV,OAAM;sHAEN,cAAA,8OAAC,sMAAA,CAAA,SAAM;gHAAC,WAAU;;;;;;;;;;;sHAEpB,8OAAC,iJAAA,CAAA,SAAM;4GACL,SAAQ;4GACR,MAAK;4GACL,WAAU;4GACV,OAAM;sHAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;gHAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sGAIxB,8OAAC;4FAAI,WAAU;sGACZ,SAAS,WAAW;;;;;;sGAEvB,8OAAC;4FAAI,WAAU;;8GACb,8OAAC,0MAAA,CAAA,SAAM;oGAAC,WAAU;;;;;;8GAClB,8OAAC;8GAAM,SAAS,QAAQ;;;;;;;;;;;;;mFAlCrB,SAAS,EAAE;;;;;0FAwCpB,8OAAC,iJAAA,CAAA,SAAM;gFACL,SAAQ;gFACR,WAAU;0FAEV,cAAA,8OAAC;oFAAI,WAAU;;sGACb,8OAAC;4FAAI,WAAU;sGACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gGAAC,WAAU;;;;;;;;;;;sGAElB,8OAAC;4FAAK,WAAU;sGAA2D;;;;;;sGAC3E,8OAAC;4FAAK,WAAU;sGAAgC;;;;;;;;;;;;;;;;;;;;;;;mEArDtC,IAAI,EAAE;;;;;;;;;;;;;;;;8DA8D9B,8OAAC,+IAAA,CAAA,cAAW;oDAAC,OAAM;8DACjB,cAAA,8OAAC;wDAAI,WAAU;;0EAEb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;8EAAE;;;;;;;;;;;0EAIL,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;;;;;0FACf,8OAAC;gFAAK,WAAU;0FAAyC;;;;;;;;;;;;kFAE3D,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;;;;;0FACf,8OAAC;gFAAK,WAAU;0FAA2C;;;;;;;;;;;;kFAE7D,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;;;;;0FACf,8OAAC;gFAAK,WAAU;0FAA6C;;;;;;;;;;;;kFAE/D,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;;;;;0FACf,8OAAC;gFAAK,WAAU;0FAA+C;;;;;;;;;;;;kFAEjE,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;;;;;0FACf,8OAAC;gFAAK,WAAU;0FAA6C;;;;;;;;;;;;kFAE/D,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;;;;;;0FACf,8OAAC;gFAAK,WAAU;0FAA2C;;;;;;;;;;;;;;;;;;0EAK/D,8OAAC;gEAAI,WAAU;;kFAEb,8OAAC;wEAAI,WAAU;kFAA+C;;;;;;kFAC9D,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAU;;gFAAsB;gFAClC,MAAM,IAAI,CAAC;oFAAE,QAAQ;gFAAG,GAAG,GAAG,CAAC,CAAC,GAAG,kBAClC,8OAAC;wFAAY,WAAU;;4FACpB,CAAC,IAAI,CAAC,IAAI;4FAAG;;uFADN;;;;;;;;;;;;;;;;kFAQhB,8OAAC;wEAAI,WAAU;kFAEZ,iBAAiB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;4EACxC,6CAA6C;4EAC7C,MAAM,eAAe,SAAS,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;4EACzD,qBACE,8OAAC;gFAAsB,WAAU;0FAC9B;+EADO,SAAS,EAAE;;;;;wEAIzB;;;;;;kFAGF,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAI,WAAU;;gFAAiB;8FAE9B,8OAAC;oFAAI,WAAU;8FACZ,MAAM,IAAI,CAAC;wFAAE,QAAQ;oFAAG,GAAG,GAAG,CAAC,CAAC,GAAG,kBAClC,8OAAC;4FAAY,WAAU;2FAAb;;;;;;;;;;gFAKb,iBAAiB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,UAAU;oFAClD,sCAAsC;oFACtC,IAAI,UAAU;oFACd,IAAI,YAAY;oFAEhB,IAAI,SAAS,KAAK,CAAC,QAAQ,CAAC,SAAS,SAAS,KAAK,CAAC,QAAQ,CAAC,OAAO;wFAClE,UAAU;wFACV,YAAY;oFACd,OAAO,IAAI,SAAS,KAAK,CAAC,QAAQ,CAAC,gBAAgB,SAAS,KAAK,CAAC,QAAQ,CAAC,SAAS;wFAClF,UAAU;wFACV,YAAY;oFACd,OAAO,IAAI,SAAS,KAAK,CAAC,QAAQ,CAAC,aAAa,SAAS,KAAK,CAAC,QAAQ,CAAC,WAAW;wFACjF,UAAU;wFACV,YAAY;oFACd,OAAO,IAAI,SAAS,KAAK,CAAC,QAAQ,CAAC,UAAU,SAAS,KAAK,CAAC,QAAQ,CAAC,QAAQ;wFAC3E,UAAU;wFACV,YAAY;oFACd,OAAO,IAAI,SAAS,KAAK,CAAC,QAAQ,CAAC,gBAAgB,SAAS,KAAK,CAAC,QAAQ,CAAC,OAAO;wFAChF,UAAU;wFACV,YAAY;oFACd;oFAEA,+BAA+B;oFAC/B,MAAM,CAAC,OAAO,QAAQ,GAAG,SAAS,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;oFACtD,MAAM,gBAAgB,CAAC,QAAQ,CAAC,IAAI,KAAK,AAAC,UAAU,KAAM;oFAE1D,sEAAsE;oFACtE,IAAI,WAAW;oFACf,IAAI,QAAQ,iBAAiB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,GAAG,GAAG;wFAC1D,MAAM,eAAe,iBAAiB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,EAAE;wFACnE,MAAM,CAAC,WAAW,YAAY,GAAG,aAAa,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC;wFAClE,MAAM,kBAAkB,YAAY,cAAc;wFAClD,MAAM,qBAAqB,QAAQ,UAAU;wFAC7C,WAAW,kBAAkB;oFAC/B;oFAEA,MAAM,QAAQ,WAAW;oFAEzB,qBACE,8OAAC;wFAAsB,WAAU;kGAC/B,cAAA,8OAAC;4FACC,WAAW,CAAC,qCAAqC,EAAE,QAAQ,CAAC,EAAE,UAAU,uJAAuJ,CAAC;4FAChO,OAAO;gGACL,MAAM,GAAG,cAAc,EAAE,CAAC;gGAC1B,OAAO,GAAG,MAAM,EAAE,CAAC;4FACrB;4FACA,OAAO,GAAG,SAAS,IAAI,CAAC,GAAG,EAAE,SAAS,KAAK,EAAE;;8GAE7C,8OAAC;oGAAI,WAAU;;wGACZ,SAAS,IAAI;wGAAC;wGAAI,SAAS,KAAK;;;;;;;8GAEnC,8OAAC;oGAAI,WAAU;8GACb,cAAA,8OAAC,iJAAA,CAAA,SAAM;wGACL,SAAQ;wGACR,MAAK;wGACL,WAAU;wGACV,OAAM;kHAEN,cAAA,8OAAC,sMAAA,CAAA,SAAM;4GAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;uFAnBhB,SAAS,EAAE;;;;;gFAyBzB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAcrB,0BACC,8OAAC,2IAAA,CAAA,UAAc;gBACb,MAAM;gBACN,QAAQ;gBACR,SAAS,IAAM,sBAAsB;gBACrC,QAAQ;;;;;;;;;;;;AAKlB", "debugId": null}}, {"offset": {"line": 7801, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/trips/TripTabMenu.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { Home, MessageSquare, Calendar, View } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\ntype TabItem = {\n  id: string;\n  label: string;\n  icon: React.ReactNode;\n  href?: string;\n  onClick?: () => void;\n};\n\ntype TripTabMenuProps = {\n  tripId: string;\n  onTabChange?: (tabId: string) => void;\n};\n\nexport function TripTabMenu({ tripId, onTabChange }: TripTabMenuProps) {\n  const pathname = usePathname();\n  const [activeTab, setActiveTab] = useState('chat');\n\n  const handleTabClick = (tabId: string) => {\n    setActiveTab(tabId);\n    if (onTabChange) {\n      onTabChange(tabId);\n    }\n  };\n\n  const tabs: TabItem[] = [\n    {\n      id: 'forum',\n      label: '<PERSON><PERSON><PERSON> đ<PERSON>n',\n      icon: <Home className=\"h-6 w-6\" />,\n      href: '/',\n    },\n    {\n      id: 'chat',\n      label: 'Chuyến đi',\n      icon: <MessageSquare className=\"h-6 w-6\" />,\n      onClick: () => handleTabClick('chat'),\n    },\n    {\n      id: 'planning',\n      label: 'Lập kế hoạch',\n      icon: <Calendar className=\"h-6 w-6\" />,\n      href: '/planning',\n    },\n    {\n      id: 'view360',\n      label: 'View 360°',\n      icon: <View className=\"h-6 w-6\" />,\n      href: '/view360',\n    },\n  ];\n\n  return (\n    <div className=\"hidden md:block lg:hidden sticky top-0 z-10 bg-white dark:bg-gray-950 border-b border-purple-100 dark:border-purple-900\">\n      <div className=\"flex justify-between\">\n        {tabs.map((tab) => (\n          tab.href ? (\n            <Link\n              key={tab.id}\n              href={tab.href}\n              className={cn(\n                \"flex flex-1 flex-col items-center justify-center py-2 text-xs font-medium transition-colors\",\n                tab.href === pathname\n                  ? \"text-teal-600 border-b-2 border-teal-600 dark:text-teal-400 dark:border-teal-400\"\n                  : \"text-gray-500 hover:text-teal-600 dark:text-gray-400 dark:hover:text-teal-400\"\n              )}\n            >\n              <div className={cn(\n                \"p-1 rounded-full\",\n                tab.href === pathname\n                  ? \"text-teal-600 dark:text-teal-400\"\n                  : \"text-gray-500 dark:text-gray-400\"\n              )}>\n                {tab.icon}\n              </div>\n              <span className=\"mt-0.5\">{tab.label}</span>\n            </Link>\n          ) : (\n            <button\n              key={tab.id}\n              onClick={tab.onClick}\n              className={cn(\n                \"flex flex-1 flex-col items-center justify-center py-2 text-xs font-medium transition-colors\",\n                activeTab === tab.id\n                  ? \"text-teal-600 border-b-2 border-teal-600 dark:text-teal-400 dark:border-teal-400\"\n                  : \"text-gray-500 hover:text-teal-600 dark:text-gray-400 dark:hover:text-teal-400\"\n              )}\n            >\n              <div className={cn(\n                \"p-1 rounded-full\",\n                activeTab === tab.id\n                  ? \"text-teal-600 dark:text-teal-400\"\n                  : \"text-gray-500 dark:text-gray-400\"\n              )}>\n                {tab.icon}\n              </div>\n              <span className=\"mt-0.5\">{tab.label}</span>\n            </button>\n          )\n        ))}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;;AAqBO,SAAS,YAAY,EAAE,MAAM,EAAE,WAAW,EAAoB;IACnE,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,iBAAiB,CAAC;QACtB,aAAa;QACb,IAAI,aAAa;YACf,YAAY;QACd;IACF;IAEA,MAAM,OAAkB;QACtB;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,8OAAC,mMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;YAC/B,SAAS,IAAM,eAAe;QAChC;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,oBAAM,8OAAC,kMAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACtB,MAAM;QACR;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACZ,KAAK,GAAG,CAAC,CAAC,MACT,IAAI,IAAI,iBACN,8OAAC,4JAAA,CAAA,UAAI;oBAEH,MAAM,IAAI,IAAI;oBACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+FACA,IAAI,IAAI,KAAK,WACT,qFACA;;sCAGN,8OAAC;4BAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,oBACA,IAAI,IAAI,KAAK,WACT,qCACA;sCAEH,IAAI,IAAI;;;;;;sCAEX,8OAAC;4BAAK,WAAU;sCAAU,IAAI,KAAK;;;;;;;mBAjB9B,IAAI,EAAE;;;;yCAoBb,8OAAC;oBAEC,SAAS,IAAI,OAAO;oBACpB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+FACA,cAAc,IAAI,EAAE,GAChB,qFACA;;sCAGN,8OAAC;4BAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,oBACA,cAAc,IAAI,EAAE,GAChB,qCACA;sCAEH,IAAI,IAAI;;;;;;sCAEX,8OAAC;4BAAK,WAAU;sCAAU,IAAI,KAAK;;;;;;;mBAjB9B,IAAI,EAAE;;;;;;;;;;;;;;;AAwBzB", "debugId": null}}, {"offset": {"line": 7951, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/trips/mock-trip-groups.ts"], "sourcesContent": ["// Mock data cho các nhóm du lịch\n\nexport type TripMember = {\n  id: string;\n  name: string;\n  avatar: string;\n  role?: 'admin' | 'member'; // Role của thành viên trong nhóm\n};\n\nexport type TripGroup = {\n  id: string;\n  title: string;\n  image: string;\n  description: string;\n  location: string;\n  date: string;\n  duration: string;\n  members: {\n    count: number;\n    max: number;\n    list: TripMember[];\n  };\n  hashtags: string[];\n  isPrivate: boolean;\n  hasPlan: boolean; // Indicates if the group already has a travel plan\n  planDays?: number; // Number of days in the current plan, if any\n};\n\n// Mock trip groups data từ hình ảnh\nexport const MOCK_TRIP_GROUPS: TripGroup[] = [\n  {\n    id: '1',\n    title: 'Khám phá Đà Lạt',\n    image: 'https://images.pexels.com/photos/5746250/pexels-photo-5746250.jpeg?auto=compress&cs=tinysrgb&w=600',\n    description: '<PERSON><PERSON><PERSON> nhau khám phá thành phố sương mù với những địa điểm nổi tiếng và ẩm thực đặc sắc.',\n    location: 'Đà Lạt, Lâm Đồng',\n    date: '15/06/2025 - 18/06/2025',\n    duration: '4 ngày 3 đêm',\n    members: {\n      count: 5,\n      max: 10,\n      list: [\n        { id: '1', name: 'Nguyễn Minh', avatar: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1', role: 'admin' },\n        { id: '2', name: 'Trần Hà', avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1', role: 'member' },\n        { id: '3', name: 'Lê Hoàng', avatar: 'https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1', role: 'member' },\n      ],\n    },\n    hashtags: ['DaLat', 'DuLich', 'NhomDuLich'],\n    isPrivate: false,\n    hasPlan: true,\n    planDays: 4\n  },\n  {\n    id: '2',\n    title: 'Biển Nha Trang',\n    image: 'https://images.pexels.com/photos/4428272/pexels-photo-4428272.jpeg?auto=compress&cs=tinysrgb&w=600',\n    description: 'Chuyến đi biển Nha Trang cùng các hoạt động lặn biển, tham quan đảo và nghỉ dưỡng.',\n    location: 'Nha Trang, Khánh Hòa',\n    date: '22/07/2025 - 26/07/2025',\n    duration: '5 ngày 4 đêm',\n    members: {\n      count: 8,\n      max: 12,\n      list: [\n        { id: '1', name: 'Nguyễn Minh', avatar: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1', role: 'member' },\n        { id: '4', name: 'Ngọc Mai', avatar: 'https://images.pexels.com/photos/733872/pexels-photo-733872.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1', role: 'admin' },\n        { id: '2', name: 'Trần Hà', avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1', role: 'member' },\n      ],\n    },\n    hashtags: ['NhaTrang', 'Bien', 'DuLich'],\n    isPrivate: false,\n    hasPlan: true\n  },\n  {\n    id: '3',\n    title: 'Sapa mùa đông',\n    image: 'https://images.pexels.com/photos/4350383/pexels-photo-4350383.jpeg?auto=compress&cs=tinysrgb&w=600',\n    description: 'Chinh phục đỉnh Fansipan và khám phá các bản làng dân tộc thiểu số ở Sapa trong mùa đông.',\n    location: 'Sapa, Lào Cai',\n    date: '20/12/2025 - 24/12/2025',\n    duration: '5 ngày 4 đêm',\n    members: {\n      count: 6,\n      max: 15,\n      list: [\n        { id: '3', name: 'Lê Hoàng', avatar: 'https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1', role: 'admin' },\n        { id: '4', name: 'Ngọc Mai', avatar: 'https://images.pexels.com/photos/733872/pexels-photo-733872.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1', role: 'member' },\n      ],\n    },\n    hashtags: ['Sapa', 'MuaDong', 'Fansipan'],\n    isPrivate: false,\n    hasPlan: true\n  },\n  {\n    id: '4',\n    title: 'Khám phá Hội An',\n    image: 'https://images.pexels.com/photos/5191371/pexels-photo-5191371.jpeg?auto=compress&cs=tinysrgb&w=600',\n    description: 'Tham quan phố cổ Hội An, trải nghiệm văn hóa và ẩm thực đặc sắc của miền Trung.',\n    location: 'Hội An, Quảng Nam',\n    date: '10/08/2025 - 13/08/2025',\n    duration: '4 ngày 3 đêm',\n    members: {\n      count: 4,\n      max: 8,\n      list: [\n        { id: '1', name: 'Nguyễn Minh', avatar: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1', role: 'member' },\n        { id: '5', name: 'Phạm Tuấn', avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1', role: 'admin' },\n      ],\n    },\n    hashtags: ['HoiAn', 'PhoCoHoiAn', 'DuLich'],\n    isPrivate: true,\n    hasPlan: false\n  },\n  {\n    id: '5',\n    title: 'Vịnh Hạ Long',\n    image: 'https://images.pexels.com/photos/2132180/pexels-photo-2132180.jpeg?auto=compress&cs=tinysrgb&w=600',\n    description: 'Khám phá kỳ quan thiên nhiên thế giới với tour du thuyền ngắm cảnh và khám phá các hang động.',\n    location: 'Hạ Long, Quảng Ninh',\n    date: '05/09/2025 - 07/09/2025',\n    duration: '3 ngày 2 đêm',\n    members: {\n      count: 10,\n      max: 20,\n      list: [\n        { id: '2', name: 'Trần Hà', avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1', role: 'admin' },\n        { id: '3', name: 'Lê Hoàng', avatar: 'https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1', role: 'member' },\n        { id: '4', name: 'Ngọc Mai', avatar: 'https://images.pexels.com/photos/733872/pexels-photo-733872.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1', role: 'member' },\n      ],\n    },\n    hashtags: ['HaLong', 'VinhHaLong', 'DiSan'],\n    isPrivate: false,\n    hasPlan: true,\n    planDays: 3\n  }\n];\n\n// Current user ID (for demo purposes)\nexport const CURRENT_USER_ID = '1';\n\n// Function to get groups that the current user is a member of\nexport const getUserGroups = () => {\n  return MOCK_TRIP_GROUPS.filter(group =>\n    group.members.list.some(member => member.id === CURRENT_USER_ID)\n  );\n};\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;AA6B1B,MAAM,mBAAgC;IAC3C;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;QACN,UAAU;QACV,SAAS;YACP,OAAO;YACP,KAAK;YACL,MAAM;gBACJ;oBAAE,IAAI;oBAAK,MAAM;oBAAe,QAAQ;oBAAgH,MAAM;gBAAQ;gBACtK;oBAAE,IAAI;oBAAK,MAAM;oBAAW,QAAQ;oBAAgH,MAAM;gBAAS;gBACnK;oBAAE,IAAI;oBAAK,MAAM;oBAAY,QAAQ;oBAAkH,MAAM;gBAAS;aACvK;QACH;QACA,UAAU;YAAC;YAAS;YAAU;SAAa;QAC3C,WAAW;QACX,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;QACN,UAAU;QACV,SAAS;YACP,OAAO;YACP,KAAK;YACL,MAAM;gBACJ;oBAAE,IAAI;oBAAK,MAAM;oBAAe,QAAQ;oBAAgH,MAAM;gBAAS;gBACvK;oBAAE,IAAI;oBAAK,MAAM;oBAAY,QAAQ;oBAAgH,MAAM;gBAAQ;gBACnK;oBAAE,IAAI;oBAAK,MAAM;oBAAW,QAAQ;oBAAgH,MAAM;gBAAS;aACpK;QACH;QACA,UAAU;YAAC;YAAY;YAAQ;SAAS;QACxC,WAAW;QACX,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;QACN,UAAU;QACV,SAAS;YACP,OAAO;YACP,KAAK;YACL,MAAM;gBACJ;oBAAE,IAAI;oBAAK,MAAM;oBAAY,QAAQ;oBAAkH,MAAM;gBAAQ;gBACrK;oBAAE,IAAI;oBAAK,MAAM;oBAAY,QAAQ;oBAAgH,MAAM;gBAAS;aACrK;QACH;QACA,UAAU;YAAC;YAAQ;YAAW;SAAW;QACzC,WAAW;QACX,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;QACN,UAAU;QACV,SAAS;YACP,OAAO;YACP,KAAK;YACL,MAAM;gBACJ;oBAAE,IAAI;oBAAK,MAAM;oBAAe,QAAQ;oBAAgH,MAAM;gBAAS;gBACvK;oBAAE,IAAI;oBAAK,MAAM;oBAAa,QAAQ;oBAAgH,MAAM;gBAAQ;aACrK;QACH;QACA,UAAU;YAAC;YAAS;YAAc;SAAS;QAC3C,WAAW;QACX,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;QACN,UAAU;QACV,SAAS;YACP,OAAO;YACP,KAAK;YACL,MAAM;gBACJ;oBAAE,IAAI;oBAAK,MAAM;oBAAW,QAAQ;oBAAgH,MAAM;gBAAQ;gBAClK;oBAAE,IAAI;oBAAK,MAAM;oBAAY,QAAQ;oBAAkH,MAAM;gBAAS;gBACtK;oBAAE,IAAI;oBAAK,MAAM;oBAAY,QAAQ;oBAAgH,MAAM;gBAAS;aACrK;QACH;QACA,UAAU;YAAC;YAAU;YAAc;SAAQ;QAC3C,WAAW;QACX,SAAS;QACT,UAAU;IACZ;CACD;AAGM,MAAM,kBAAkB;AAGxB,MAAM,gBAAgB;IAC3B,OAAO,iBAAiB,MAAM,CAAC,CAAA,QAC7B,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;AAEpD", "debugId": null}}, {"offset": {"line": 8159, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/features/trips/TripChatLayout.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useMemo, useEffect } from 'react';\nimport { GroupChatList } from './GroupChatList';\nimport { TripChat } from './trip-chat';\nimport { GroupChatDetails } from './GroupChatDetails';\nimport { TripTabMenu } from './TripTabMenu';\nimport { MOCK_TRIP_GROUPS, TripGroup } from './mock-trip-groups';\nimport { ChevronLeft, ChevronRight } from 'lucide-react';\nimport { Button } from '@/components/ui/radix-ui/button';\n\ntype TripChatLayoutProps = {\n  initialTripId: string;\n};\n\nexport function TripChatLayout({ initialTripId }: TripChatLayoutProps) {\n  const [selectedGroup, setSelectedGroup] = useState<TripGroup | null>(\n    MOCK_TRIP_GROUPS.find(group => group.id === initialTripId) || null\n  );\n  const [showDetails, setShowDetails] = useState(true);\n  const [isTablet, setIsTablet] = useState(false);\n  const [activeTab, setActiveTab] = useState('chat');\n\n  // <PERSON> dõi kích thước màn hình\n  useEffect(() => {\n    const checkScreenSize = () => {\n      const isTabletSize = window.innerWidth >= 768 && window.innerWidth < 1024;\n      setIsTablet(isTabletSize);\n\n      // Khi ở chế độ tablet, luôn hiển thị layout dọc (showDetails = true)\n      if (isTabletSize) {\n        setShowDetails(true);\n      }\n    };\n\n    // Kiểm tra kích thước ban đầu\n    checkScreenSize();\n\n    // Thêm event listener để theo dõi thay đổi kích thước\n    window.addEventListener('resize', checkScreenSize);\n\n    // Cleanup\n    return () => window.removeEventListener('resize', checkScreenSize);\n  }, []);\n\n  // Hiển thị tất cả các nhóm có trong hệ thống, không lọc theo người dùng\n  const allGroups = useMemo(() => {\n    // Trả về tất cả các nhóm từ mock data\n    return MOCK_TRIP_GROUPS;\n  }, []);\n\n  const handleSelectGroup = (group: TripGroup) => {\n    setSelectedGroup(group);\n    // Khi chọn nhóm mới, hiển thị chi tiết nếu đang ở chế độ ẩn và không phải ở chế độ tablet\n    if (!showDetails && !isTablet) {\n      setShowDetails(true);\n    }\n  };\n\n  // Không cho phép toggle chi tiết nhóm trong responsive design Tablet-768px\n  const toggleDetails = () => {\n    // Chỉ cho phép toggle chi tiết nhóm khi không phải ở responsive design Tablet-768px\n    if (!isTablet) {\n      setShowDetails(!showDetails);\n    }\n  };\n\n  // Xử lý khi tab thay đổi\n  const handleTabChange = (tabId: string) => {\n    setActiveTab(tabId);\n  };\n\n  return (\n    <div className=\"flex flex-col h-full overflow-hidden w-full max-w-full\">\n      {/* Tab Menu for Tablet - Only visible on md screens */}\n      {selectedGroup && (\n        <TripTabMenu\n          tripId={selectedGroup.id}\n          onTabChange={handleTabChange}\n        />\n      )}\n\n      <div className=\"flex flex-1 overflow-hidden\">\n        {/* Left column - Group list */}\n        <div className=\"w-[280px] md:w-[80px] lg:w-[280px] min-w-[80px] lg:min-w-[220px] flex-shrink-0 rounded-lg border border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 shadow-md mr-3 md:mr-2 lg:mr-3 overflow-hidden\">\n          <GroupChatList\n            groups={allGroups}\n            selectedGroupId={selectedGroup?.id || ''}\n            onSelectGroup={handleSelectGroup}\n          />\n        </div>\n\n        {/* Middle and Right columns container */}\n        <div className=\"flex flex-1 rounded-lg border border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs shadow-md overflow-hidden\">\n        {/* Middle column - Chat area */}\n        <div className={`flex-1 min-w-0 overflow-hidden relative ${selectedGroup ? 'flex flex-col' : ''}`}>\n          {selectedGroup ? (\n            <>\n              {/* Tablet view with tab menu */}\n              {isTablet ? (\n                <div className=\"flex flex-col h-full\">\n                  {/* Content based on active tab */}\n                  {activeTab === 'chat' && (\n                    <>\n                      {/* Fixed group info at top */}\n                      <div className=\"flex-shrink-0 border-b border-purple-100 dark:border-purple-900\">\n                        <GroupChatDetails group={selectedGroup} isCollapsed={isTablet} />\n                      </div>\n\n                      {/* Scrollable chat area */}\n                      <div className=\"flex-1 overflow-hidden\">\n                        <TripChat\n                          tripId={selectedGroup.id}\n                          members={selectedGroup.members.list}\n                          isTablet={isTablet}\n                          isVerticalLayout={true}\n                        />\n                      </div>\n                    </>\n                  )}\n\n                  {/* Tab Planning đã được chuyển thành link đến trang /planning */}\n                </div>\n              ) : (\n                <>\n                  {/* Desktop/Mobile layout */}\n                  <TripChat\n                    tripId={selectedGroup.id}\n                    members={selectedGroup.members.list}\n                    isTablet={isTablet}\n                  />\n                </>\n              )}\n\n              {/* Nút điều hướng đã bị xóa theo yêu cầu */}\n            </>\n          ) : (\n            <div className=\"flex h-full items-center justify-center text-muted-foreground\">\n              <div className=\"text-center space-y-2\">\n                <div className=\"text-4xl mb-4\">👋</div>\n                <h3 className=\"text-lg font-medium\">Chọn một nhóm để bắt đầu trò chuyện</h3>\n                <p className=\"text-sm text-muted-foreground\">Hoặc tạo một nhóm mới từ trang Chuyến đi</p>\n              </div>\n            </div>\n          )}\n        </div>\n\n        {/* Right column - Group details (only for desktop and mobile, or tablet with hidden vertical layout) */}\n        <div\n          className={`border-l border-purple-100 dark:border-purple-900 flex-shrink-0 transition-all duration-300\n            ${showDetails ? 'w-[400px] lg:min-w-[350px]' : 'w-0 min-w-0'}\n            md:absolute md:right-0 md:top-0 md:bottom-0 md:z-20 md:bg-white/95 md:dark:bg-gray-950/95 md:backdrop-blur-sm md:shadow-lg\n            ${showDetails ? 'md:translate-x-0 md:w-[280px]' : 'md:translate-x-full'}\n            ${isTablet && showDetails ? 'md:hidden' : ''}\n            lg:static lg:translate-x-0 lg:shadow-none lg:bg-transparent lg:dark:bg-transparent`}\n        >\n          {selectedGroup && showDetails && !isTablet && (\n            <GroupChatDetails group={selectedGroup} isCollapsed={isTablet} />\n          )}\n        </div>\n      </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAeO,SAAS,eAAe,EAAE,aAAa,EAAuB;IACnE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC/C,kJAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,kBAAkB;IAEhE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,MAAM,eAAe,OAAO,UAAU,IAAI,OAAO,OAAO,UAAU,GAAG;YACrE,YAAY;YAEZ,qEAAqE;YACrE,IAAI,cAAc;gBAChB,eAAe;YACjB;QACF;QAEA,8BAA8B;QAC9B;QAEA,sDAAsD;QACtD,OAAO,gBAAgB,CAAC,UAAU;QAElC,UAAU;QACV,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,wEAAwE;IACxE,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE;QACxB,sCAAsC;QACtC,OAAO,kJAAA,CAAA,mBAAgB;IACzB,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC;QACzB,iBAAiB;QACjB,0FAA0F;QAC1F,IAAI,CAAC,eAAe,CAAC,UAAU;YAC7B,eAAe;QACjB;IACF;IAEA,2EAA2E;IAC3E,MAAM,gBAAgB;QACpB,oFAAoF;QACpF,IAAI,CAAC,UAAU;YACb,eAAe,CAAC;QAClB;IACF;IAEA,yBAAyB;IACzB,MAAM,kBAAkB,CAAC;QACvB,aAAa;IACf;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,+BACC,8OAAC,wIAAA,CAAA,cAAW;gBACV,QAAQ,cAAc,EAAE;gBACxB,aAAa;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0IAAA,CAAA,gBAAa;4BACZ,QAAQ;4BACR,iBAAiB,eAAe,MAAM;4BACtC,eAAe;;;;;;;;;;;kCAKnB,8OAAC;wBAAI,WAAU;;0CAEf,8OAAC;gCAAI,WAAW,CAAC,wCAAwC,EAAE,gBAAgB,kBAAkB,IAAI;0CAC9F,8BACC;8CAEG,yBACC,8OAAC;wCAAI,WAAU;kDAEZ,cAAc,wBACb;;8DAEE,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,6IAAA,CAAA,mBAAgB;wDAAC,OAAO;wDAAe,aAAa;;;;;;;;;;;8DAIvD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,yIAAA,CAAA,WAAQ;wDACP,QAAQ,cAAc,EAAE;wDACxB,SAAS,cAAc,OAAO,CAAC,IAAI;wDACnC,UAAU;wDACV,kBAAkB;;;;;;;;;;;;;;;;;6DAS5B;kDAEE,cAAA,8OAAC,yIAAA,CAAA,WAAQ;4CACP,QAAQ,cAAc,EAAE;4CACxB,SAAS,cAAc,OAAO,CAAC,IAAI;4CACnC,UAAU;;;;;;;kEAQlB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,8OAAC;gDAAG,WAAU;0DAAsB;;;;;;0DACpC,8OAAC;gDAAE,WAAU;0DAAgC;;;;;;;;;;;;;;;;;;;;;;0CAOrD,8OAAC;gCACC,WAAW,CAAC;YACV,EAAE,cAAc,+BAA+B,cAAc;;YAE7D,EAAE,cAAc,kCAAkC,sBAAsB;YACxE,EAAE,YAAY,cAAc,cAAc,GAAG;8FACqC,CAAC;0CAEpF,iBAAiB,eAAe,CAAC,0BAChC,8OAAC,6IAAA,CAAA,mBAAgB;oCAAC,OAAO;oCAAe,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjE", "debugId": null}}, {"offset": {"line": 8401, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/social-travel-trip/social-travel-trip-frontend/src/app/%28social-travel-trip%29/trips/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { TripChatLayout } from '@/features/trips/TripChatLayout';\nimport { useParams } from 'next/navigation';\n\nexport default function TripPage() {\n\n  const params = useParams();\n\n  const [trip] = useState({\n    id: params.id as string,\n    title: '<PERSON>hám phá <PERSON>',\n    description: 'Cùng nhau khám phá thành phố sương mù với những địa điểm nổi tiếng và ẩm thực đặc sắc.',\n    location: 'Đà Lạt, Lâm Đồng',\n    date: '15/06/2025 - 18/06/2025',\n    duration: '4 ngày 3 đêm',\n    image: 'https://images.pexels.com/photos/5746250/pexels-photo-5746250.jpeg?auto=compress&cs=tinysrgb&w=600',\n    members: {\n      count: 5,\n      max: 10,\n      list: [\n        { id: '1', name: '<PERSON><PERSON><PERSON><PERSON>', avatar: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1' },\n        { id: '2', name: 'Trần <PERSON>à', avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1' },\n        { id: '3', name: 'Lê Hoàng', avatar: 'https://images.pexels.com/photos/1681010/pexels-photo-1681010.jpeg?auto=compress&cs=tinysrgb&w=120&h=120&dpr=1' },\n      ],\n    },\n    hashtags: ['DaLat', 'DuLich', 'NhomDuLich'],\n    isPrivate: false,\n  });\n\n  return (\n    <div className=\"w-full overflow-hidden\">\n      <div className=\"h-[calc(100vh-7rem)]\">\n        <TripChatLayout initialTripId={trip.id} />\n      </div>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IAEtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,KAAK,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACtB,IAAI,OAAO,EAAE;QACb,OAAO;QACP,aAAa;QACb,UAAU;QACV,MAAM;QACN,UAAU;QACV,OAAO;QACP,SAAS;YACP,OAAO;YACP,KAAK;YACL,MAAM;gBACJ;oBAAE,IAAI;oBAAK,MAAM;oBAAe,QAAQ;gBAA+G;gBACvJ;oBAAE,IAAI;oBAAK,MAAM;oBAAW,QAAQ;gBAA+G;gBACnJ;oBAAE,IAAI;oBAAK,MAAM;oBAAY,QAAQ;gBAAiH;aACvJ;QACH;QACA,UAAU;YAAC;YAAS;YAAU;SAAa;QAC3C,WAAW;IACb;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,2IAAA,CAAA,iBAAc;gBAAC,eAAe,KAAK,EAAE;;;;;;;;;;;;;;;;AAI9C", "debugId": null}}]}