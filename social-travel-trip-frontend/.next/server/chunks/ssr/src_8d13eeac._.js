module.exports = {

"[project]/src/components/ui/page-header.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "PageHeader": (()=>PageHeader)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
;
function PageHeader({ title, description }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-1",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                className: "text-2xl md:text-3xl font-bold text-purple-800 dark:text-purple-400",
                children: title
            }, void 0, false, {
                fileName: "[project]/src/components/ui/page-header.tsx",
                lineNumber: 9,
                columnNumber: 7
            }, this),
            description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-muted-foreground",
                children: description
            }, void 0, false, {
                fileName: "[project]/src/components/ui/page-header.tsx",
                lineNumber: 11,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/ui/page-header.tsx",
        lineNumber: 8,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/components/ui/card.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Card": (()=>Card),
    "CardContent": (()=>CardContent),
    "CardDescription": (()=>CardDescription),
    "CardFooter": (()=>CardFooter),
    "CardHeader": (()=>CardHeader),
    "CardTitle": (()=>CardTitle)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
;
;
;
const Card = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("rounded-xl border bg-card text-card-foreground shadow-sm", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 9,
        columnNumber: 3
    }, this));
Card.displayName = "Card";
const CardHeader = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex flex-col space-y-1.5 p-6", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 24,
        columnNumber: 3
    }, this));
CardHeader.displayName = "CardHeader";
const CardTitle = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("font-semibold leading-none tracking-tight", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 36,
        columnNumber: 3
    }, this));
CardTitle.displayName = "CardTitle";
const CardDescription = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("text-sm text-muted-foreground", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 48,
        columnNumber: 3
    }, this));
CardDescription.displayName = "CardDescription";
const CardContent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("p-6 pt-0", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 60,
        columnNumber: 3
    }, this));
CardContent.displayName = "CardContent";
const CardFooter = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])("flex items-center p-6 pt-0", className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/card.tsx",
        lineNumber: 68,
        columnNumber: 3
    }, this));
CardFooter.displayName = "CardFooter";
;
}}),
"[project]/src/features/view360/view360.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "View360": (()=>View360),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$sphere$2d$viewer$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-photo-sphere-viewer/dist/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$markers$2d$plugin$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@photo-sphere-viewer/markers-plugin/index.module.js [app-ssr] (ecmascript)");
// Bỏ import CSS vì nó không tồn tại trong package
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$maximize$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Maximize$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/maximize.js [app-ssr] (ecmascript) <export default as Maximize>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$minimize$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Minimize$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/minimize.js [app-ssr] (ecmascript) <export default as Minimize>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rotate$2d$ccw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RotateCcw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/rotate-ccw.js [app-ssr] (ecmascript) <export default as RotateCcw>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronLeft$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-left.js [app-ssr] (ecmascript) <export default as ChevronLeft>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/chevron-right.js [app-ssr] (ecmascript) <export default as ChevronRight>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MapPin$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/map-pin.js [app-ssr] (ecmascript) <export default as MapPin>");
'use client';
;
;
;
;
;
;
;
const View360 = ({ scenes, initialSceneId, className = '', height = '400px', width = '100%', fullscreenButton = true, resetButton = true, showSceneSelector = true })=>{
    const [isFullscreen, setIsFullscreen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [currentSceneIndex, setCurrentSceneIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(initialSceneId ? scenes.findIndex((scene)=>scene.id === initialSceneId) : 0);
    const viewerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const containerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Xử lý khi component unmount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        return ()=>{
            if (viewerRef.current) {
                try {
                    viewerRef.current.destroy();
                } catch (error) {
                    console.error('Error destroying viewer:', error);
                }
            }
        };
    }, []);
    // Xử lý khi nhấn nút fullscreen
    const toggleFullscreen = ()=>{
        if (!containerRef.current) return;
        if (!isFullscreen) {
            if (containerRef.current.requestFullscreen) {
                containerRef.current.requestFullscreen();
            }
            setIsFullscreen(true);
        } else {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            }
            setIsFullscreen(false);
        }
    };
    // Xử lý khi nhấn nút reset
    const handleReset = ()=>{
        if (viewerRef.current) {
            viewerRef.current.animate({
                yaw: 0,
                pitch: 0,
                zoom: 50,
                speed: '10rpm'
            });
        }
    };
    // Xử lý sự kiện fullscreen change
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleFullscreenChange = ()=>{
            setIsFullscreen(!!document.fullscreenElement);
        };
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        return ()=>{
            document.removeEventListener('fullscreenchange', handleFullscreenChange);
        };
    }, []);
    // Chuyển đến scene trước đó
    const goToPreviousScene = ()=>{
        if (currentSceneIndex > 0) {
            setCurrentSceneIndex(currentSceneIndex - 1);
        } else {
            setCurrentSceneIndex(scenes.length - 1);
        }
    };
    // Chuyển đến scene tiếp theo
    const goToNextScene = ()=>{
        if (currentSceneIndex < scenes.length - 1) {
            setCurrentSceneIndex(currentSceneIndex + 1);
        } else {
            setCurrentSceneIndex(0);
        }
    };
    // Chuyển đến scene cụ thể theo index
    const goToScene = (index)=>{
        if (index >= 0 && index < scenes.length) {
            setCurrentSceneIndex(index);
        }
    };
    // Chuyển đến scene cụ thể theo ID
    const goToSceneById = (sceneId)=>{
        const sceneIndex = scenes.findIndex((scene)=>scene.id === sceneId);
        if (sceneIndex !== -1) {
            setCurrentSceneIndex(sceneIndex);
        }
    };
    // Xử lý khi nhấp vào hotspot
    const handleHotspotClick = (hotspotId)=>{
        // Chuyển đến scene khác trong cùng địa điểm
        goToSceneById(hotspotId);
    };
    // Lấy scene hiện tại
    const currentScene = scenes[currentSceneIndex];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
        className: `overflow-hidden border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs ${className}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
            className: "p-0",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                ref: containerRef,
                className: "relative",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            height,
                            width
                        },
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$photo$2d$sphere$2d$viewer$2f$dist$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ReactPhotoSphereViewer"], {
                            ref: viewerRef,
                            src: currentScene.image,
                            height: isFullscreen ? '100vh' : height,
                            width: width,
                            littlePlanet: false,
                            container: containerRef.current || undefined,
                            plugins: [
                                [
                                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$markers$2d$plugin$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MarkersPlugin"],
                                    {
                                        markers: currentScene.hotspots?.map((hotspot)=>({
                                                id: hotspot.id,
                                                position: hotspot.position,
                                                tooltip: {
                                                    content: hotspot.tooltip || hotspot.name,
                                                    position: 'bottom'
                                                },
                                                html: `
                      <div style="
                        display: flex;
                        align-items: center;
                        background-color: rgba(255, 255, 255, 0.8);
                        padding: 5px 10px;
                        border-radius: 20px;
                        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
                        cursor: pointer;
                        transition: all 0.2s ease;
                      ">
                        <div style="
                          margin-right: 5px;
                          color: rgb(147, 51, 234);
                        ">
                          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M5 12h14"></path>
                            <path d="m12 5 7 7-7 7"></path>
                          </svg>
                        </div>
                        <div style="
                          font-weight: 500;
                          font-size: 14px;
                        ">${hotspot.name}</div>
                      </div>
                    `,
                                                data: {
                                                    sceneId: hotspot.id
                                                }
                                            })) || []
                                    }
                                ]
                            ],
                            onReady: (instance)=>{
                                // Xử lý sự kiện click vào marker
                                if (currentScene.hotspots && currentScene.hotspots.length > 0) {
                                    const markersPlugin = instance.getPlugin(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$photo$2d$sphere$2d$viewer$2f$markers$2d$plugin$2f$index$2e$module$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MarkersPlugin"]);
                                    if (markersPlugin) {
                                        markersPlugin.addEventListener('select-marker', (e)=>{
                                            if (e.marker && e.marker.data && e.marker.data.sceneId) {
                                                handleHotspotClick(e.marker.data.sceneId);
                                            }
                                        });
                                    }
                                }
                            }
                        }, currentScene.id, false, {
                            fileName: "[project]/src/features/view360/view360.tsx",
                            lineNumber: 160,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/view360/view360.tsx",
                        lineNumber: 159,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute top-4 left-4 right-4 flex justify-between items-center",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm px-3 py-1.5 rounded-md flex items-center",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MapPin$3e$__["MapPin"], {
                                        className: "h-4 w-4 mr-2 text-purple-600"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/view360/view360.tsx",
                                        lineNumber: 228,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "font-medium text-sm",
                                        children: currentScene.name
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/view360/view360.tsx",
                                        lineNumber: 229,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/view360/view360.tsx",
                                lineNumber: 227,
                                columnNumber: 13
                            }, this),
                            currentScene.description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm px-3 py-1.5 rounded-md",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-sm",
                                    children: currentScene.description
                                }, void 0, false, {
                                    fileName: "[project]/src/features/view360/view360.tsx",
                                    lineNumber: 234,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/view360/view360.tsx",
                                lineNumber: 233,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/view360/view360.tsx",
                        lineNumber: 226,
                        columnNumber: 11
                    }, this),
                    scenes.length > 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute top-1/2 left-4 right-4 -translate-y-1/2 flex justify-between",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                size: "icon",
                                className: "bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm",
                                onClick: goToPreviousScene,
                                title: "Cảnh trước đó",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$left$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronLeft$3e$__["ChevronLeft"], {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/view360/view360.tsx",
                                    lineNumber: 249,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/view360/view360.tsx",
                                lineNumber: 242,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                size: "icon",
                                className: "bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm",
                                onClick: goToNextScene,
                                title: "Cảnh tiếp theo",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$chevron$2d$right$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__ChevronRight$3e$__["ChevronRight"], {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/view360/view360.tsx",
                                    lineNumber: 259,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/view360/view360.tsx",
                                lineNumber: 252,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/view360/view360.tsx",
                        lineNumber: 241,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute bottom-4 right-4 flex gap-2",
                        children: [
                            resetButton && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                size: "icon",
                                className: "bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm",
                                onClick: handleReset,
                                title: "Đặt lại góc nhìn",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rotate$2d$ccw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RotateCcw$3e$__["RotateCcw"], {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/view360/view360.tsx",
                                    lineNumber: 274,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/view360/view360.tsx",
                                lineNumber: 267,
                                columnNumber: 15
                            }, this),
                            fullscreenButton && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                variant: "outline",
                                size: "icon",
                                className: "bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm",
                                onClick: toggleFullscreen,
                                title: isFullscreen ? "Thoát toàn màn hình" : "Toàn màn hình",
                                children: isFullscreen ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$minimize$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Minimize$3e$__["Minimize"], {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/view360/view360.tsx",
                                    lineNumber: 287,
                                    columnNumber: 19
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$maximize$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Maximize$3e$__["Maximize"], {
                                    className: "h-4 w-4"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/view360/view360.tsx",
                                    lineNumber: 289,
                                    columnNumber: 19
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/features/view360/view360.tsx",
                                lineNumber: 279,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/features/view360/view360.tsx",
                        lineNumber: 265,
                        columnNumber: 11
                    }, this),
                    showSceneSelector && scenes.length > 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute bottom-4 left-1/2 -translate-x-1/2 flex gap-2",
                        children: scenes.map((scene, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                variant: index === currentSceneIndex ? "default" : "outline",
                                size: "sm",
                                className: `bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm ${index === currentSceneIndex ? "bg-purple-600 text-white" : ""}`,
                                onClick: ()=>goToScene(index),
                                children: scene.name
                            }, scene.id, false, {
                                fileName: "[project]/src/features/view360/view360.tsx",
                                lineNumber: 299,
                                columnNumber: 17
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/features/view360/view360.tsx",
                        lineNumber: 297,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/features/view360/view360.tsx",
                lineNumber: 158,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/features/view360/view360.tsx",
            lineNumber: 157,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/features/view360/view360.tsx",
        lineNumber: 156,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = View360;
}}),
"[project]/src/features/view360/google-maps-view.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GoogleMapsView": (()=>GoogleMapsView),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MapPin$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/map-pin.js [app-ssr] (ecmascript) <export default as MapPin>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rotate$2d$ccw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RotateCcw$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/rotate-ccw.js [app-ssr] (ecmascript) <export default as RotateCcw>");
'use client';
;
;
;
;
;
const GoogleMapsView = ({ mapUrl, className = '', height = '400px', width = '100%', fullscreenButton = true, reloadButton = true, title, showInfoCard = true })=>{
    const [isFullscreen, setIsFullscreen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const containerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    const iframeRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Xử lý URL Google Maps để lấy embed URL và ẩn UI
    const getEmbedUrl = (url)=>{
        // Kiểm tra nếu URL đã là embed URL
        if (url.includes('maps.google.com/maps/embed') || url.includes('www.google.com/maps/embed')) {
            // Thêm tham số để ẩn UI
            if (!url.includes('!3m2!1sen!2s!4v')) {
                // Thêm tham số để ẩn UI
                const modifiedUrl = url.replace(/!4v\d+!/, '!4v1!3m2!1sen!2s!4v!');
                // Thêm tham số để ẩn nút zoom và cho phép truy cập từ localhost
                return modifiedUrl + '&disableDefaultUI=1&scrollwheel=0&zoomControl=0&origin=localhost:4200';
            }
            // Thêm tham số để ẩn nút zoom và cho phép truy cập từ localhost
            return url + '&disableDefaultUI=1&scrollwheel=0&zoomControl=0&origin=localhost:4200';
        }
        // Xử lý URL dạng https://maps.app.goo.gl/XXX hoặc https://goo.gl/maps/XXX
        let embedUrl = '';
        try {
            // Nếu URL chứa tham số pb (đây là URL embed đã được định dạng)
            if (url.includes('pb=')) {
                // Thêm tham số để ẩn UI
                if (!url.includes('!3m2!1sen!2s!4v')) {
                    const modifiedUrl = url.replace(/!4v\d+!/, '!4v1!3m2!1sen!2s!4v!');
                    // Thêm tham số để ẩn nút zoom và cho phép truy cập từ localhost
                    return modifiedUrl + '&disableDefaultUI=1&scrollwheel=0&zoomControl=0&origin=localhost:4200';
                }
                // Thêm tham số để ẩn nút zoom và cho phép truy cập từ localhost
                return url + '&disableDefaultUI=1&scrollwheel=0&zoomControl=0&origin=localhost:4200';
            }
            // Nếu là URL rút gọn, thử trích xuất tham số
            if (url.includes('maps.app.goo.gl') || url.includes('goo.gl/maps')) {
                // Đối với URL rút gọn, chúng ta sẽ sử dụng mode=streetview và thêm tham số để ẩn UI
                embedUrl = `https://www.google.com/maps/embed?pb=!4v1!1m3!1m2!1s${encodeURIComponent(url)}!2s!3m2!1sen!2s!4v!5e0!3m2!1svi!2s!4v1&disableDefaultUI=1&scrollwheel=0&zoomControl=0&origin=localhost:4200`;
            } else if (url.includes('google.com/maps')) {
                // Trích xuất tham số từ URL
                const urlObj = new URL(url);
                const params = new URLSearchParams(urlObj.search);
                // Nếu có tham số ll (latitude,longitude)
                if (params.has('ll')) {
                    const ll = params.get('ll');
                    embedUrl = `https://www.google.com/maps/embed?pb=!4v1!1m3!1m2!1s!2s${ll}!3m2!1sen!2s!4v!5e0!3m2!1svi!2s!4v1&disableDefaultUI=1&scrollwheel=0&zoomControl=0&origin=localhost:4200`;
                } else if (params.has('q')) {
                    const q = params.get('q');
                    embedUrl = `https://www.google.com/maps/embed?pb=!4v1!1m3!1m2!1s!2s${q}!3m2!1sen!2s!4v!5e0!3m2!1svi!2s!4v1&disableDefaultUI=1&scrollwheel=0&zoomControl=0&origin=localhost:4200`;
                } else {
                    embedUrl = `https://www.google.com/maps/embed/v1/streetview?key=YOUR_API_KEY&location=${encodeURIComponent(url)}&showui=false&disableDefaultUI=1&scrollwheel=0&zoomControl=0&origin=localhost:4200`;
                }
            } else {
                console.error('URL không hợp lệ:', url);
                embedUrl = url; // Sử dụng URL gốc
            }
        } catch (error) {
            console.error('Lỗi khi xử lý URL:', error);
            embedUrl = url; // Sử dụng URL gốc nếu có lỗi
        }
        return embedUrl;
    };
    // Xử lý khi nhấn nút fullscreen
    const toggleFullscreen = ()=>{
        if (!containerRef.current) return;
        if (!isFullscreen) {
            if (containerRef.current.requestFullscreen) {
                containerRef.current.requestFullscreen();
            }
            setIsFullscreen(true);
        } else {
            if (document.exitFullscreen) {
                document.exitFullscreen();
            }
            setIsFullscreen(false);
        }
    };
    // Xử lý khi nhấn nút reload
    const handleReload = ()=>{
        if (iframeRef.current) {
            // Lưu lại URL hiện tại
            const currentSrc = iframeRef.current.src;
            // Đặt src thành empty string để buộc iframe reload
            iframeRef.current.src = '';
            // Đặt lại src sau một khoảng thời gian ngắn
            setTimeout(()=>{
                if (iframeRef.current) {
                    iframeRef.current.src = currentSrc;
                }
            }, 100);
        }
    };
    // Theo dõi sự kiện fullscreenchange
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const handleFullscreenChange = ()=>{
            setIsFullscreen(!!document.fullscreenElement);
        };
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        return ()=>{
            document.removeEventListener('fullscreenchange', handleFullscreenChange);
        };
    }, []);
    // Không cố gắng truy cập vào contentDocument của iframe để tránh lỗi CORS
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (iframeRef.current) {
            try {
                // Chỉ đặt sự kiện onload để biết khi iframe đã tải xong
                iframeRef.current.onload = ()=>{
                    console.log('Iframe đã tải xong');
                // Không cố gắng truy cập vào contentDocument hoặc contentWindow
                };
            } catch (error) {
                console.error('Lỗi khi xử lý iframe:', error);
            }
        }
    }, [
        iframeRef.current
    ]);
    const embedUrl = getEmbedUrl(mapUrl);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
        className: `overflow-hidden border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs ${className}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
            className: "p-0",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                ref: containerRef,
                className: "relative",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        style: {
                            height,
                            width
                        },
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "google-maps-iframe-container",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("iframe", {
                                ref: iframeRef,
                                src: embedUrl,
                                width: "100%",
                                height: "100%",
                                style: {
                                    border: 0
                                },
                                allowFullScreen: true,
                                loading: "lazy",
                                referrerPolicy: "no-referrer-when-downgrade",
                                className: "google-maps-iframe",
                                allow: "fullscreen",
                                sandbox: "allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox allow-presentation allow-forms"
                            }, void 0, false, {
                                fileName: "[project]/src/features/view360/google-maps-view.tsx",
                                lineNumber: 185,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/features/view360/google-maps-view.tsx",
                            lineNumber: 184,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/view360/google-maps-view.tsx",
                        lineNumber: 183,
                        columnNumber: 11
                    }, this),
                    title && showInfoCard && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute top-4 left-4 z-10",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm px-3 py-1.5 rounded-md flex items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MapPin$3e$__["MapPin"], {
                                    className: "h-4 w-4 mr-2 text-purple-600"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/view360/google-maps-view.tsx",
                                    lineNumber: 205,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "font-medium text-sm",
                                    children: title
                                }, void 0, false, {
                                    fileName: "[project]/src/features/view360/google-maps-view.tsx",
                                    lineNumber: 206,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/features/view360/google-maps-view.tsx",
                            lineNumber: 204,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/view360/google-maps-view.tsx",
                        lineNumber: 203,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute bottom-6 right-16 flex gap-2",
                        children: reloadButton && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                            variant: "outline",
                            size: "icon",
                            className: "bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm",
                            onClick: handleReload,
                            title: "Tải lại ảnh",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$rotate$2d$ccw$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__RotateCcw$3e$__["RotateCcw"], {
                                className: "h-4 w-4"
                            }, void 0, false, {
                                fileName: "[project]/src/features/view360/google-maps-view.tsx",
                                lineNumber: 221,
                                columnNumber: 17
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/features/view360/google-maps-view.tsx",
                            lineNumber: 214,
                            columnNumber: 15
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/features/view360/google-maps-view.tsx",
                        lineNumber: 212,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/features/view360/google-maps-view.tsx",
                lineNumber: 182,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/features/view360/google-maps-view.tsx",
            lineNumber: 181,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/features/view360/google-maps-view.tsx",
        lineNumber: 180,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = GoogleMapsView;
}}),
"[project]/src/features/view360/universal-view360.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "UniversalView360": (()=>UniversalView360),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$view360$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/view360/view360.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$google$2d$maps$2d$view$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/view360/google-maps-view.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
const UniversalView360 = ({ source, className = '', height = '400px', width = '100%', fullscreenButton = true, reloadButton = true, showSceneSelector = true, showInfoCard = true })=>{
    // Hiển thị component tương ứng dựa trên loại nguồn dữ liệu
    if (source.type === 'panorama') {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$view360$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["View360"], {
            scenes: source.scenes,
            initialSceneId: source.initialSceneId,
            className: className,
            height: height,
            width: width,
            fullscreenButton: fullscreenButton,
            resetButton: true,
            showSceneSelector: showSceneSelector
        }, void 0, false, {
            fileName: "[project]/src/features/view360/universal-view360.tsx",
            lineNumber: 48,
            columnNumber: 7
        }, this);
    } else if (source.type === 'google-maps') {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$google$2d$maps$2d$view$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GoogleMapsView"], {
            mapUrl: source.url,
            className: className,
            height: height,
            width: width,
            fullscreenButton: fullscreenButton,
            reloadButton: reloadButton,
            title: source.title,
            showInfoCard: showInfoCard
        }, void 0, false, {
            fileName: "[project]/src/features/view360/universal-view360.tsx",
            lineNumber: 61,
            columnNumber: 7
        }, this);
    }
    // Trường hợp không có nguồn dữ liệu hợp lệ
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `flex items-center justify-center ${className}`,
        style: {
            height,
            width
        },
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
            className: "text-muted-foreground",
            children: "Không có dữ liệu 360° hợp lệ"
        }, void 0, false, {
            fileName: "[project]/src/features/view360/universal-view360.tsx",
            lineNumber: 77,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/features/view360/universal-view360.tsx",
        lineNumber: 76,
        columnNumber: 5
    }, this);
};
const __TURBOPACK__default__export__ = UniversalView360;
}}),
"[project]/src/components/ui/radix-ui/tabs.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Tabs": (()=>Tabs),
    "TabsContent": (()=>TabsContent),
    "TabsList": (()=>TabsList),
    "TabsTrigger": (()=>TabsTrigger)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@radix-ui/react-tabs/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
const Tabs = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Root"];
const TabsList = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["List"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground', className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/radix-ui/tabs.tsx",
        lineNumber: 14,
        columnNumber: 3
    }, this));
TabsList.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["List"].displayName;
const TabsTrigger = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Trigger"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-xs', className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/radix-ui/tabs.tsx",
        lineNumber: 29,
        columnNumber: 3
    }, this));
TabsTrigger.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Trigger"].displayName;
const TabsContent = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["forwardRef"])(({ className, ...props }, ref)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Content"], {
        ref: ref,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])('mt-2 ring-offset-background focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2', className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/radix-ui/tabs.tsx",
        lineNumber: 44,
        columnNumber: 3
    }, this));
TabsContent.displayName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$radix$2d$ui$2f$react$2d$tabs$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Content"].displayName;
;
}}),
"[project]/src/components/ui/radix-ui/badge.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Badge": (()=>Badge),
    "badgeVariants": (()=>badgeVariants)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/class-variance-authority/dist/index.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/utils.ts [app-ssr] (ecmascript)");
;
;
;
const badgeVariants = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$class$2d$variance$2d$authority$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cva"])('inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-hidden focus:ring-2 focus:ring-ring focus:ring-offset-2', {
    variants: {
        variant: {
            default: 'border-transparent bg-primary text-primary-foreground hover:bg-primary/80',
            secondary: 'border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80',
            destructive: 'border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80',
            outline: 'text-foreground'
        }
    },
    defaultVariants: {
        variant: 'default'
    }
});
function Badge({ className, variant, ...props }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$utils$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cn"])(badgeVariants({
            variant
        }), className),
        ...props
    }, void 0, false, {
        fileName: "[project]/src/components/ui/radix-ui/badge.tsx",
        lineNumber: 32,
        columnNumber: 5
    }, this);
}
;
}}),
"[project]/src/features/view360/view360-locations.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/**
 * Predefined locations for the 360-degree view feature
 */ __turbopack_context__.s({
    "VIEW_360_LOCATIONS": (()=>VIEW_360_LOCATIONS),
    "getAllCities": (()=>getAllCities),
    "getAllRegions": (()=>getAllRegions),
    "getLocationsByCity": (()=>getLocationsByCity),
    "getLocationsByRegion": (()=>getLocationsByRegion),
    "searchLocations": (()=>searchLocations)
});
const VIEW_360_LOCATIONS = [
    // Miền Nam
    {
        id: 'bai-sao',
        name: 'Bãi Sao',
        city: 'Kiên Giang',
        region: 'Miền Nam',
        description: '',
        coordinates: {
            lat: 10.05725757562915,
            lng: 104.0363948436442
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747505297576!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQzRyZV9SN2dF!2m2!1d10.0552462121511!2d104.0366325129505!3f41.32156850984014!4f-22.57191954269578!5f0.7820865974627469'
    },
    {
        id: 'hon-thom',
        name: 'Hòn Thơm',
        city: 'Phú Quốc',
        region: 'Miền Nam',
        description: '',
        coordinates: {
            lat: 9.954605838430725,
            lng: 104.0178143976055
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747332749752!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQzR3NERDSUE.!2m2!1d9.954605838430725!2d104.0178143976055!3f352.99579097798187!4f-11.542141392533921!5f0.7820865974627469'
    },
    {
        id: 'vinpearl',
        name: 'Vinpearl Resort',
        city: 'Phú Quốc',
        region: 'Miền Nam',
        description: '',
        coordinates: {
            lat: 10.33683427532572,
            lng: 103.8555491298273
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747332930528!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQzRyZV9zUlE.!2m2!1d10.33683427532572!2d103.8555491298273!3f9.87975441837457!4f-61.96086477266688!5f0.7820865974627469'
    },
    {
        id: 'landmark',
        name: 'Landmark 81',
        city: 'Hồ Chí Minh',
        region: 'Miền Nam',
        description: '',
        coordinates: {
            lat: 10.79354656053439,
            lng: 106.7240047363216
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747500249620!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJRGtqWXFtSmc.!2m2!1d10.79354656053439!2d106.7240047363216!3f65.9868573871558!4f-4.652932567714487!5f0.7820865974627469'
    },
    {
        id: 'rach-vem',
        name: 'Rạch Vẹm',
        city: 'Phú Quốc',
        region: 'Miền Nam',
        description: '',
        coordinates: {
            lat: 10.37304277793628,
            lng: 103.9377705339461
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747333532742!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQzRqZGo5S0E.!2m2!1d10.37304277793628!2d103.9377705339461!3f216.24777645854576!4f-0.38721998348161435!5f0.7820865974627469'
    },
    {
        id: 'pho-di-bo-nguyen-hue',
        name: 'Phố đi bộ Nguyễn Huệ',
        city: 'Hồ Chí Minh',
        region: 'Miền Nam',
        description: '',
        coordinates: {
            lat: 10.37304277793628,
            lng: 103.9377705339461
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747500618497!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQzQ0OHlWendF!2m2!1d10.77261925857123!2d106.7052078134514!3f98.16935090250226!4f12.161081380563019!5f0.7820865974627469'
    },
    {
        id: 'ben-ninh-kieu',
        name: 'Bến Ninh Kiều',
        city: 'Cần Thơ',
        region: 'Miền Nam',
        description: '',
        coordinates: {
            lat: 10.0333,
            lng: 105.7833
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747333532742!6m8!1m7!1sCAoSLEFGMVFpcE5XVnRXVXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXQ!2m2!1d10.0333!2d105.7833!3f0!4f0!5f0.7820865974627469'
    },
    {
        id: 'cho-noi-cai-rang',
        name: 'Chợ nổi Cái Răng',
        city: 'Cần Thơ',
        region: 'Miền Nam',
        description: '',
        coordinates: {
            lat: 10.0361,
            lng: 105.7908
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747333532742!6m8!1m7!1sCAoSLEFGMVFpcE5XVnRXVXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXRXQ!2m2!1d10.0361!2d105.7908!3f0!4f0!5f0.7820865974627469'
    },
    {
        id: 'mui-ne',
        name: 'Mũi Né',
        city: 'Phan Thiết',
        region: 'Miền Nam',
        description: '',
        coordinates: {
            lat: 10.92277134063956,
            lng: 108.2827949618694
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747413870859!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJRHFxOG5zRGc.!2m2!1d10.92277134063956!2d108.2827949618694!3f250.67714063698654!4f-21.37455813653939!5f0.7820865974627469'
    },
    {
        id: 'nui-den',
        name: 'Núi Đèn',
        city: 'Hà Tiên',
        region: 'Miền Nam',
        description: '',
        coordinates: {
            lat: 9.954605838430725,
            lng: 104.0178143976055
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747499560225!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJRHFyZWkyY3c.!2m2!1d10.37289226596782!2d104.4451584259442!3f211.81944677726216!4f0!5f0.7820865974627469'
    },
    {
        id: 'rung-tram-tra-su',
        name: 'Rừng tràm Trà Sư',
        city: 'An Giang',
        region: 'Miền Nam',
        description: '',
        coordinates: {
            lat: 10.5833,
            lng: 105.0833
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747495047834!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJRHEtZXZDZmc.!2m2!1d10.58140358825379!2d105.0589104983493!3f22.155458669411246!4f4.898122194557175!5f0.4000000000000002'
    },
    {
        id: 'nui-sam',
        name: 'Núi Sam và Miếu Bà Chúa Xứ',
        city: 'An Giang',
        region: 'Miền Nam',
        description: '',
        coordinates: {
            lat: 10.6751202698838,
            lng: 105.0782791127975
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747495235097!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQ09zNFQ3X2dF!2m2!1d10.68210573732136!2d105.0802910017875!3f261.63629762709695!4f10.182778710084548!5f0.4000000000000002'
    },
    {
        id: 'bai-sau',
        name: 'Bãi Sau Vũng Tàu',
        city: 'Vũng Tàu',
        region: 'Miền Nam',
        description: '',
        coordinates: {
            lat: 10.34907186654009,
            lng: 107.0971676648209
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747414304414!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJRDJzSlRnZWc.!2m2!1d10.34907186654009!2d107.0971676648209!3f38.71530157927323!4f-82.21191574849168!5f0.7820865974627469'
    },
    {
        id: 'nha-cong-tu-bac-lieu',
        name: 'Nhà Công tử Bạc Liêu',
        city: 'Bạc Liêu',
        region: 'Miền Nam',
        description: '',
        coordinates: {
            lat: 9.2833,
            lng: 105.7167
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747494902258!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQ3BoZGJZZFE.!2m2!1d9.28408452732009!2d105.7238055313035!3f7.423296444444819!4f-26.258200588604304!5f0.4000000000000002'
    },
    {
        id: 'quan-dao-nam-du',
        name: 'Quần đảo Nam Du',
        city: 'Kiên Giang',
        region: 'Miền Nam',
        description: '',
        coordinates: {
            lat: 9.66389502040506,
            lng: 104.3496417202332
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747414488523!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQ1VuTlMzQ2c.!2m2!1d9.66389502040506!2d104.3496417202332!3f99.81168081790992!4f0.7850056876728928!5f0.4000000000000002'
    },
    {
        id: 'khu-du-lich-dai-nam',
        name: 'Khu du lịch Đại Nam',
        city: 'Bình Dương',
        region: 'Miền Nam',
        description: '',
        coordinates: {
            lat: 11.0522,
            lng: 106.6527
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747561603966!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQ2t1UEsyV2c.!2m2!1d11.04273297863585!2d106.6231230307857!3f348.80734111153146!4f-6.92320452177762!5f0.4000000000000002'
    },
    {
        id: 'dao-con-dao',
        name: 'Đảo Côn Đảo',
        city: 'Bà Rịa - Vũng Tàu',
        region: 'Miền Nam',
        description: '',
        coordinates: {
            lat: 8.6833,
            lng: 106.6333
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747561710036!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJRHVrTWlrVHc.!2m2!1d8.680223308555638!2d106.6077933101246!3f275.5838632836326!4f-15.889832735152922!5f0.4000000000000002'
    },
    {
        id: 'khu-du-lich-tram-chim',
        name: 'Vườn quốc gia Tràm Chim',
        city: 'Đồng Tháp',
        region: 'Miền Nam',
        description: '',
        coordinates: {
            lat: 10.7125,
            lng: 105.5833
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747561823119!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQ0VqX0stZHc.!2m2!1d10.71352680128817!2d105.4976494560806!3f189.63468163564727!4f3.176300071053035!5f0.4000000000000002'
    },
    {
        id: 'khu-du-lich-suoi-tien',
        name: 'Khu du lịch Suối Tiên',
        city: 'Hồ Chí Minh',
        region: 'Miền Nam',
        description: '',
        coordinates: {
            lat: 10.8667,
            lng: 106.8000
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747562106701!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJREV5Ym03Rmc.!2m2!1d10.86375003824863!2d106.8021699904355!3f139.20049558043593!4f3.460598320150865!5f0.4000000000000002'
    },
    // Miền Trung
    {
        id: 'pho-co-hoi-an',
        name: 'Phố cổ Hội An',
        city: 'Hội An',
        region: 'Miền Trung',
        description: '',
        coordinates: {
            lat: 15.87498002820639,
            lng: 108.3359990034829
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747414738724!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQzR5ckRWRnc.!2m2!1d15.87498002820639!2d108.3359990034829!3f176.96713275830936!4f2.585110776640718!5f0.7820865974627469'
    },
    {
        id: 'bien-my-khe',
        name: 'Biển Mỹ Khê',
        city: 'Đà Nẵng',
        region: 'Miền Trung',
        description: '',
        coordinates: {
            lat: 16.03155926286188,
            lng: 108.2569290591596
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747414942488!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQ2s4dVdmY1E.!2m2!1d16.03155926286188!2d108.2569290591596!3f70.15133672476054!4f16.882785257899485!5f0.7820865974627469'
    },
    {
        id: 'cau-rong',
        name: 'Cầu Rồng',
        city: 'Đà Nẵng',
        region: 'Miền Trung',
        description: '',
        coordinates: {
            lat: 16.06128313959818,
            lng: 108.2296736742121
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747416784145!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJRGFvLXFHV0E.!2m2!1d16.06128313959818!2d108.2296736742121!3f250.46428959931467!4f10.691060139093182!5f0.7820865974627469'
    },
    {
        id: 'co-do-hue',
        name: 'Cố đô Huế',
        city: 'Huế',
        region: 'Miền Trung',
        description: '',
        coordinates: {
            lat: 16.46576501032935,
            lng: 107.5876617319682
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747416638814!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJRFd2cERiaVFF!2m2!1d16.46576501032935!2d107.5876617319682!3f194.21115801890443!4f-13.360151105557819!5f0.7820865974627469'
    },
    {
        id: 'phong-nha-ke-bang',
        name: 'Phong Nha - Kẻ Bàng',
        city: 'Quảng Bình',
        region: 'Miền Trung',
        description: '',
        coordinates: {
            lat: 17.47767903045252,
            lng: 106.1340395116056
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747416553678!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQ0RwT2FoUGc.!2m2!1d17.47767903045252!2d106.1340395116056!3f160.51280089450208!4f-5.969980438522754!5f0.7820865974627469'
    },
    {
        id: 'ba-na-hills',
        name: 'Bà Nà Hills',
        city: 'Đà Nẵng',
        region: 'Miền Trung',
        description: '',
        coordinates: {
            lat: 15.99480629711804,
            lng: 107.996643130635
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747415798333!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQ0U2c0dOSUE.!2m2!1d15.99480629711804!2d107.996643130635!3f19.657977674331732!4f-2.1407677760772827!5f0.4000000000000002'
    },
    {
        id: 'lang-co-beach',
        name: 'Biển Lăng Cô',
        city: 'Huế',
        region: 'Miền Trung',
        description: '',
        coordinates: {
            lat: 16.27289566516503,
            lng: 108.0599606277014
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747416038681!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQ2NuZFQ3RlE.!2m2!1d16.27289566516503!2d108.0599606277014!3f303.7113294016193!4f0.9003761491211009!5f0.7820865974627469'
    },
    {
        id: 'thien-mu-pagoda',
        name: 'Chùa Thiên Mụ',
        city: 'Huế',
        region: 'Miền Trung',
        description: '',
        coordinates: {
            lat: 16.45225832914808,
            lng: 107.5450653153509
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747416075819!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQ2MtdnpqWnc.!2m2!1d16.45225832914808!2d107.5450653153509!3f49.94292753934999!4f-5.757623044544431!5f0.7820865974627469'
    },
    {
        id: 'son-tra-peninsula',
        name: 'Bán đảo Sơn Trà',
        city: 'Đà Nẵng',
        region: 'Miền Trung',
        description: '',
        coordinates: {
            lat: 16.08574793591492,
            lng: 108.2259978348675
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747416201895!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQ090b3FubkFF!2m2!1d16.08574793591492!2d108.2259978348675!3f211.3137785310005!4f-30.892596490263706!5f0.7820865974627469'
    },
    {
        id: 'ly-son-island',
        name: 'Đảo Lý Sơn',
        city: 'Quảng Ngãi',
        region: 'Miền Trung',
        description: '',
        coordinates: {
            lat: 15.37973169526243,
            lng: 109.0956007385369
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747416264668!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJRHF2ZDJiNWdF!2m2!1d15.37973169526243!2d109.0956007385369!3f328.7627233443913!4f0.7540181699922215!5f0.7820865974627469'
    },
    {
        id: 'marble-mountains',
        name: 'Ngũ Hành Sơn',
        city: 'Đà Nẵng',
        region: 'Miền Trung',
        description: '',
        coordinates: {
            lat: 16.0406273796354,
            lng: 108.2502678178907
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747495746304!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQ1dqS2Z1RkE.!2m2!1d16.00357997322952!2d108.2643904557801!3f73.7676291487919!4f16.219845205115803!5f0.4000000000000002'
    },
    {
        id: 'tam-giang-lagoon',
        name: 'Đầm phá Tam Giang',
        city: 'Huế',
        region: 'Miền Trung',
        description: '',
        coordinates: {
            lat: 16.61206061804068,
            lng: 107.5533934158846
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747416423335!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQ2xxWkx3bUFF!2m2!1d16.61206061804068!2d107.5533934158846!3f181.57897343718923!4f-33.32387855244893!5f0.7820865974627469'
    },
    {
        id: 'my-son-sanctuary',
        name: 'Thánh địa Mỹ Sơn',
        city: 'Quảng Nam',
        region: 'Miền Trung',
        description: '',
        coordinates: {
            lat: 15.76300706641276,
            lng: 108.123989442643
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747416506358!6m8!1m7!1sCAoSHENJQUJJaEFEeWRkbXFSS0JfbWV1OU13QUNKaGs.!2m2!1d15.76300706641276!2d108.123989442643!3f344.50527686499316!4f-14.758897808970943!5f0.7820865974627469'
    },
    {
        id: 'bien-nha-trang',
        name: 'Biển Nha Trang',
        city: 'Nha Trang',
        region: 'Miền Trung',
        description: '',
        coordinates: {
            lat: 12.2388,
            lng: 109.1967
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747562600027!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJRFU5cy1NaEFF!2m2!1d12.23576672365317!2d109.199113311098!3f295.9503787412098!4f-8.56763189466777!5f0.4000000000000002'
    },
    {
        id: 'vinpearl-nha-trang',
        name: 'Vinpearl Nha Trang',
        city: 'Nha Trang',
        region: 'Miền Trung',
        description: '',
        coordinates: {
            lat: 12.2086,
            lng: 109.1233
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747563078452!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJRC0ydERPeFFF!2m2!1d12.21603828855668!2d109.2419716610762!3f281.6114334037844!4f-31.027932938717477!5f0.7820865974627469'
    },
    {
        id: 'bien-quy-nhon',
        name: 'Biển Quy Nhơn',
        city: 'Quy Nhơn',
        region: 'Miền Trung',
        description: '',
        coordinates: {
            lat: 13.7829,
            lng: 109.2196
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747563245340!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQ0VfdlBJS2c.!2m2!1d13.76751709804615!2d109.2307239886753!3f341.57406360660553!4f8.001507463638163!5f0.4000000000000002'
    },
    {
        id: 'thanh-dia-la-vang',
        name: 'Thánh địa La Vang',
        city: 'Quảng Trị',
        region: 'Miền Trung',
        description: '',
        coordinates: {
            lat: 16.5433,
            lng: 107.3333
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747563401134!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJREVwYXFPN2dF!2m2!1d16.70625162542189!2d107.1954857184009!3f66.60204422834953!4f7.61032793814627!5f0.7820865974627469'
    },
    // Miền Bắc
    {
        id: 'vinh-ha-long',
        name: 'Vịnh Hạ Long',
        city: 'Hạ Long',
        region: 'Miền Bắc',
        description: '',
        coordinates: {
            lat: 20.9100512289355,
            lng: 107.1839024224061
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747415018081!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJRGZqT2VkdlFF!2m2!1d20.9100512289355!2d107.1839024224061!3f87.5373364913488!4f6.563715102588802!5f0.4000000000000002'
    },
    {
        id: 'ho-hoan-kiem',
        name: 'Hồ Hoàn Kiếm',
        city: 'Hà Nội',
        region: 'Miền Bắc',
        description: '',
        coordinates: {
            lat: 21.0286301307459,
            lng: 105.8524792676769
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747415060722!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJRDRfOWFlWFE.!2m2!1d21.0286301307459!2d105.8524792676769!3f83.13261344682465!4f-21.383569861287327!5f0.7820865974627469'
    },
    {
        id: 'pho-co-ha-noi',
        name: 'Phố cổ Hà Nội',
        city: 'Hà Nội',
        region: 'Miền Bắc',
        description: '',
        coordinates: {
            lat: 21.03491298285746,
            lng: 105.8501825722273
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747417954095!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQ0I4cG15OUFF!2m2!1d21.03491298285746!2d105.8501825722273!3f261.0068602876837!4f19.8481637385228!5f0.7820865974627469'
    },
    {
        id: 'sapa',
        name: 'Sa Pa',
        city: 'Lào Cai',
        region: 'Miền Bắc',
        description: '',
        coordinates: {
            lat: 22.3351392005417,
            lng: 103.8414929631456
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747418046410!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQzJsNkN0bHdF!2m2!1d22.3351392005417!2d103.8414929631456!3f66.51452436282926!4f-21.456194225199894!5f0.7820865974627469'
    },
    {
        id: 'moc-chau',
        name: 'Mộc Châu',
        city: 'Sơn La',
        region: 'Miền Bắc',
        description: '',
        coordinates: {
            lat: 20.872343214208,
            lng: 104.5861787075125
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747418108902!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJREIxYzdubUFF!2m2!1d20.872343214208!2d104.5861787075125!3f143.9887647994639!4f-4.121293306540068!5f0.7820865974627469'
    },
    {
        id: 'dong-van',
        name: 'Cao nguyên đá Đồng Văn',
        city: 'Hà Giang',
        region: 'Miền Bắc',
        description: '',
        coordinates: {
            lat: 23.26028780776931,
            lng: 105.2575134451518
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747418153307!6m8!1m7!1sCAoSLEFGMVFpcE9qSVlCckNaUFFQNzljQ2o0Rkw0OTBlMmtSaEJ4bTlsVmtMUTl3!2m2!1d23.26028780776931!2d105.2575134451518!3f113.12560539499034!4f9.127558760790421!5f0.4000000000000002'
    },
    {
        id: 'tam-coc-bich-dong',
        name: 'Tam Cốc - Bích Động',
        city: 'Ninh Bình',
        region: 'Miền Bắc',
        description: '',
        coordinates: {
            lat: 20.21836211713047,
            lng: 105.9163782869523
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747421765637!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQ2NncTZRTVE.!2m2!1d20.21836211713047!2d105.9163782869523!3f278.4057461623623!4f8.630222287469934!5f0.4000000000000002'
    },
    {
        id: 'trang-an',
        name: 'Tràng An',
        city: 'Ninh Bình',
        region: 'Miền Bắc',
        description: '',
        coordinates: {
            lat: 20.26097517872346,
            lng: 105.9488758025064
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747418243175!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJRGk0dVdBTEE.!2m2!1d20.26097517872346!2d105.9488758025064!3f245.81152627846677!4f2.946906027989897!5f0.4000000000000002'
    },
    {
        id: 'ba-be',
        name: 'Hồ Ba Bể',
        city: 'Bắc Kạn',
        region: 'Miền Bắc',
        description: '',
        coordinates: {
            lat: 22.40503503635209,
            lng: 105.618247999901
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747418301894!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJRHFqZEdqRnc.!2m2!1d22.40503503635209!2d105.618247999901!3f161.81056016349112!4f15.740176686016312!5f0.4000000000000002'
    },
    {
        id: 'ban-gioc',
        name: 'Thác Bản Giốc',
        city: 'Cao Bằng',
        region: 'Miền Bắc',
        description: '',
        coordinates: {
            lat: 22.85571692846102,
            lng: 106.723523327683
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747418347303!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJREVzSmJCLVFF!2m2!1d22.85571692846102!2d106.723523327683!3f265.75594752276453!4f-2.06832889283983!5f0.4000000000000002'
    },
    {
        id: 'cat-ba',
        name: 'Đảo Cát Bà',
        city: 'Hải Phòng',
        region: 'Miền Bắc',
        description: '',
        coordinates: {
            lat: 20.7176737657048,
            lng: 107.0475840797433
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747418414959!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJRDR3LVRQNVFF!2m2!1d20.7176737657048!2d107.0475840797433!3f292.59219783413215!4f8.61224911887335!5f0.4000000000000002'
    },
    {
        id: 'chua-huong',
        name: 'Chùa Hương',
        city: 'Hà Nội',
        region: 'Miền Bắc',
        description: '',
        coordinates: {
            lat: 20.61814211249567,
            lng: 105.7465708887379
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747418476805!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQ0VoZnJscEFF!2m2!1d20.61814211249567!2d105.7465708887379!3f147.52896567946613!4f1.6896686787253543!5f0.4000000000000002'
    },
    {
        id: 'chua-bai-dinh',
        name: 'Chùa Bái Đính',
        city: 'Ninh Bình',
        region: 'Miền Bắc',
        description: '',
        coordinates: {
            lat: 20.27640809408571,
            lng: 105.8647083883197
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747418525319!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQzR2OVRsSEE.!2m2!1d20.27640809408571!2d105.8647083883197!3f13.312925768905245!4f-5.935714180298291!5f0.4000000000000002'
    },
    {
        id: 'lang-bac-ho',
        name: 'Lăng Chủ tịch Hồ Chí Minh',
        city: 'Hà Nội',
        region: 'Miền Bắc',
        description: '',
        coordinates: {
            lat: 21.03577328129474,
            lng: 105.8348450571805
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747418643649!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJRDR4TWpBSEE.!2m2!1d21.03577328129474!2d105.8348450571805!3f340.95211465312445!4f5.437508876767197!5f0.4000000000000002'
    },
    {
        id: 'mai-chau',
        name: 'Mai Châu',
        city: 'Hòa Bình',
        region: 'Miền Bắc',
        description: '',
        coordinates: {
            lat: 20.65792083742968,
            lng: 105.0651627041354
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747418672165!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQ01zTXF2bmdF!2m2!1d20.65792083742968!2d105.0651627041354!3f11.58085652629353!4f-10.144319783809792!5f0.4000000000000002'
    },
    {
        id: 'vuon-quoc-gia-ba-vi',
        name: 'Vườn quốc gia Ba Vì',
        city: 'Hà Nội',
        region: 'Miền Bắc',
        description: '',
        coordinates: {
            lat: 21.0868,
            lng: 105.3725
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747502185809!6m8!1m7!1sCAoSFkNJSE0wb2dLRUlDQWdJQ3BpSlQ0QWc.!2m2!1d21.09601891537984!2d105.4021723888768!3f10.588603722511014!4f10.5295985479449!5f0.4000000000000002'
    },
    {
        id: 'den-hung',
        name: 'Đền Hùng',
        city: 'Phú Thọ',
        region: 'Miền Bắc',
        description: '',
        coordinates: {
            lat: 21.3869,
            lng: 105.3683
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747501724744!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQzA1dDMxd1FF!2m2!1d21.36869176390695!2d105.3245776794423!3f148.0523867968432!4f-0.4643377189296558!5f0.4000000000000002'
    },
    {
        id: 'yen-tu',
        name: 'Yên Tử',
        city: 'Quảng Ninh',
        region: 'Miền Bắc',
        description: '',
        coordinates: {
            lat: 21.0243,
            lng: 106.7258
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747501808479!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQzZsY3ZqdmdF!2m2!1d21.10999434178431!2d106.7303056171739!3f206.85988696603425!4f-7.526339644999354!5f0.4000000000000002'
    },
    {
        id: 'thung-lung-bac-son',
        name: 'Thung lũng Bắc Sơn',
        city: 'Lạng Sơn',
        region: 'Miền Bắc',
        description: '',
        coordinates: {
            lat: 21.9167,
            lng: 106.3333
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747501888874!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQ0UwdWpfc1FF!2m2!1d21.82327158626864!2d106.3645267382869!3f9.221122842440405!4f11.198685276810096!5f0.4000000000000002'
    },
    {
        id: 'dao-co-to',
        name: 'Đảo Cô Tô',
        city: 'Quảng Ninh',
        region: 'Miền Bắc',
        description: '',
        coordinates: {
            lat: 20.9833,
            lng: 107.7667
        },
        googleMapsUrl: 'https://www.google.com/maps/embed?pb=!4v1747502015504!6m8!1m7!1sCAoSF0NJSE0wb2dLRUlDQWdJQ014cktXb3dF!2m2!1d21.0091022647372!2d107.751909999064!3f132.33947247101014!4f-41.172825724925644!5f0.4000000000000002'
    }
];
function searchLocations(query) {
    if (!query || query.trim() === '') {
        return VIEW_360_LOCATIONS;
    }
    const normalizedQuery = query.toLowerCase().trim();
    return VIEW_360_LOCATIONS.filter((location)=>location.name.toLowerCase().includes(normalizedQuery) || location.city.toLowerCase().includes(normalizedQuery) || location.region.toLowerCase().includes(normalizedQuery) || location.description.toLowerCase().includes(normalizedQuery));
}
function getLocationsByRegion(region) {
    if (!region || region === 'all') {
        return VIEW_360_LOCATIONS;
    }
    return VIEW_360_LOCATIONS.filter((location)=>location.region.toLowerCase() === region.toLowerCase());
}
function getLocationsByCity(city) {
    if (!city || city === 'all') {
        return VIEW_360_LOCATIONS;
    }
    return VIEW_360_LOCATIONS.filter((location)=>location.city.toLowerCase() === city.toLowerCase());
}
function getAllRegions() {
    const regions = VIEW_360_LOCATIONS.map((location)=>location.region);
    return [
        ...new Set(regions)
    ].sort();
}
function getAllCities() {
    const cities = VIEW_360_LOCATIONS.map((location)=>location.city);
    return [
        ...new Set(cities)
    ].sort();
}
}}),
"[project]/src/features/view360/view360-search.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "View360Search": (()=>View360Search),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/search.js [app-ssr] (ecmascript) <export default as Search>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MapPin$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/map-pin.js [app-ssr] (ecmascript) <export default as MapPin>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/x.js [app-ssr] (ecmascript) <export default as X>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$globe$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Globe$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/globe.js [app-ssr] (ecmascript) <export default as Globe>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/input.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/button.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radix$2d$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/radix-ui/badge.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$view360$2d$locations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/view360/view360-locations.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
;
function View360Search({ onSelectLocation, className = '' }) {
    const [searchQuery, setSearchQuery] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('');
    const [searchResults, setSearchResults] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [showResults, setShowResults] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [regions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$view360$2d$locations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAllRegions"])());
    const [selectedRegion, setSelectedRegion] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('all');
    const searchContainerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useRef"])(null);
    // Perform search when query changes or region changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Filter by region first
        let filteredByRegion = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$view360$2d$locations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VIEW_360_LOCATIONS"];
        if (selectedRegion !== 'all') {
            filteredByRegion = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$view360$2d$locations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VIEW_360_LOCATIONS"].filter((location)=>location.region.toLowerCase() === selectedRegion.toLowerCase());
        }
        // Then filter by search query
        if (searchQuery.trim() === '') {
            setSearchResults(filteredByRegion);
        } else {
            const normalizedQuery = searchQuery.toLowerCase().trim();
            const results = filteredByRegion.filter((location)=>location.name.toLowerCase().includes(normalizedQuery) || location.city.toLowerCase().includes(normalizedQuery) || location.description.toLowerCase().includes(normalizedQuery));
            setSearchResults(results);
        }
    }, [
        searchQuery,
        selectedRegion
    ]);
    // Xử lý sự kiện click bên ngoài để đóng danh sách kết quả tìm kiếm
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        function handleClickOutside(event) {
            if (searchContainerRef.current && !searchContainerRef.current.contains(event.target)) {
                setShowResults(false);
            }
        }
        document.addEventListener('mousedown', handleClickOutside);
        return ()=>{
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);
    // Handle search input change
    const handleSearchChange = (e)=>{
        setSearchQuery(e.target.value);
        setShowResults(true);
    };
    // Handle location selection
    const handleSelectLocation = (location)=>{
        // Gọi callback để thông báo cho component cha về địa điểm được chọn
        onSelectLocation(location);
        // Reset thanh tìm kiếm và đóng kết quả
        setSearchQuery('');
        setShowResults(false);
    // Nếu bạn muốn giữ lại region filter, hãy giữ dòng dưới đây
    // Nếu bạn muốn reset cả region filter, hãy uncomment dòng tiếp theo
    // setSelectedRegion('all');
    };
    // Clear search
    const handleClearSearch = ()=>{
        setSearchQuery('');
        setShowResults(false);
    };
    // Filter by region
    const handleRegionFilter = (region)=>{
        setSelectedRegion(region);
        setShowResults(true);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `${className}`,
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
            className: "border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs p-4",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center gap-3 mb-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$globe$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Globe$3e$__["Globe"], {
                            className: "h-5 w-5 text-purple-600 dark:text-purple-400"
                        }, void 0, false, {
                            fileName: "[project]/src/features/view360/view360-search.tsx",
                            lineNumber: 104,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "font-medium",
                            children: "Tìm kiếm địa điểm 360°"
                        }, void 0, false, {
                            fileName: "[project]/src/features/view360/view360-search.tsx",
                            lineNumber: 105,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/features/view360/view360-search.tsx",
                    lineNumber: 103,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                            children: "Vùng miền:"
                        }, void 0, false, {
                            fileName: "[project]/src/features/view360/view360-search.tsx",
                            lineNumber: 110,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-wrap gap-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radix$2d$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Badge"], {
                                    variant: selectedRegion === 'all' ? 'default' : 'outline',
                                    className: "cursor-pointer hover:bg-purple-100 dark:hover:bg-purple-900 transition-colors",
                                    onClick: ()=>handleRegionFilter('all'),
                                    children: "Tất cả"
                                }, "all", false, {
                                    fileName: "[project]/src/features/view360/view360-search.tsx",
                                    lineNumber: 112,
                                    columnNumber: 13
                                }, this),
                                regions.map((region)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radix$2d$ui$2f$badge$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Badge"], {
                                        variant: selectedRegion === region ? 'default' : 'outline',
                                        className: "cursor-pointer hover:bg-purple-100 dark:hover:bg-purple-900 transition-colors",
                                        onClick: ()=>handleRegionFilter(region),
                                        children: region
                                    }, region, false, {
                                        fileName: "[project]/src/features/view360/view360-search.tsx",
                                        lineNumber: 121,
                                        columnNumber: 15
                                    }, this))
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/features/view360/view360-search.tsx",
                            lineNumber: 111,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/features/view360/view360-search.tsx",
                    lineNumber: 109,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative",
                    ref: searchContainerRef,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "relative",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$input$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Input"], {
                                    type: "text",
                                    placeholder: "Nhập tên địa điểm hoặc thành phố...",
                                    value: searchQuery,
                                    onChange: handleSearchChange,
                                    className: "pl-10 pr-10 bg-white dark:bg-gray-950 border-purple-200 dark:border-purple-800 focus:ring-purple-500",
                                    onFocus: ()=>setShowResults(true)
                                }, void 0, false, {
                                    fileName: "[project]/src/features/view360/view360-search.tsx",
                                    lineNumber: 136,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$search$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Search$3e$__["Search"], {
                                    className: "absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-purple-500"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/view360/view360-search.tsx",
                                    lineNumber: 144,
                                    columnNumber: 13
                                }, this),
                                searchQuery && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$button$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Button"], {
                                    variant: "ghost",
                                    size: "icon",
                                    className: "absolute right-1 top-1/2 -translate-y-1/2 h-7 w-7 hover:bg-purple-100 dark:hover:bg-purple-900",
                                    onClick: handleClearSearch,
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                        className: "h-4 w-4"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/view360/view360-search.tsx",
                                        lineNumber: 152,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/features/view360/view360-search.tsx",
                                    lineNumber: 146,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/features/view360/view360-search.tsx",
                            lineNumber: 135,
                            columnNumber: 11
                        }, this),
                        showResults && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-2 rounded-md border border-purple-100 dark:border-purple-800 bg-white dark:bg-gray-950 overflow-hidden max-h-[300px] overflow-y-auto",
                            children: searchResults.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "divide-y divide-purple-100 dark:divide-purple-800",
                                children: searchResults.map((location)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "p-2 hover:bg-purple-50 dark:hover:bg-purple-900/30 cursor-pointer transition-colors",
                                        onClick: ()=>handleSelectLocation(location),
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center gap-2",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MapPin$3e$__["MapPin"], {
                                                    className: "h-4 w-4 text-purple-600 dark:text-purple-400 flex-shrink-0"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/features/view360/view360-search.tsx",
                                                    lineNumber: 169,
                                                    columnNumber: 25
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex-1 overflow-hidden",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "font-medium text-purple-700 dark:text-purple-300 text-sm",
                                                            children: location.name
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/features/view360/view360-search.tsx",
                                                            lineNumber: 171,
                                                            columnNumber: 27
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "text-xs text-muted-foreground",
                                                            children: [
                                                                location.city,
                                                                ", ",
                                                                location.region
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/features/view360/view360-search.tsx",
                                                            lineNumber: 172,
                                                            columnNumber: 27
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/features/view360/view360-search.tsx",
                                                    lineNumber: 170,
                                                    columnNumber: 25
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/features/view360/view360-search.tsx",
                                            lineNumber: 168,
                                            columnNumber: 23
                                        }, this)
                                    }, location.id, false, {
                                        fileName: "[project]/src/features/view360/view360-search.tsx",
                                        lineNumber: 163,
                                        columnNumber: 21
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/features/view360/view360-search.tsx",
                                lineNumber: 161,
                                columnNumber: 17
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "p-3 text-center text-muted-foreground",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$x$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__X$3e$__["X"], {
                                        className: "h-5 w-5 text-gray-400 mx-auto mb-1"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/view360/view360-search.tsx",
                                        lineNumber: 180,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm",
                                        children: "Không tìm thấy địa điểm nào phù hợp"
                                    }, void 0, false, {
                                        fileName: "[project]/src/features/view360/view360-search.tsx",
                                        lineNumber: 181,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/features/view360/view360-search.tsx",
                                lineNumber: 179,
                                columnNumber: 17
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/features/view360/view360-search.tsx",
                            lineNumber: 159,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/features/view360/view360-search.tsx",
                    lineNumber: 134,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/features/view360/view360-search.tsx",
            lineNumber: 102,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/features/view360/view360-search.tsx",
        lineNumber: 101,
        columnNumber: 5
    }, this);
}
const __TURBOPACK__default__export__ = View360Search;
}}),
"[project]/src/features/view360/view360-location-tabs.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "View360LocationTabs": (()=>View360LocationTabs),
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radix$2d$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/radix-ui/tabs.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MapPin$3e$__ = __turbopack_context__.i("[project]/node_modules/lucide-react/dist/esm/icons/map-pin.js [app-ssr] (ecmascript) <export default as MapPin>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$google$2d$maps$2d$view$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/view360/google-maps-view.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$view360$2d$search$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/view360/view360-search.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$view360$2d$locations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/view360/view360-locations.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
;
function View360LocationTabs({ defaultLocation = 'bai-sao', className = '', showInfoCard = true }) {
    const [selectedLocation, setSelectedLocation] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [activeTab, setActiveTab] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(defaultLocation);
    // Initialize with default location
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const defaultLoc = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$view360$2d$locations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VIEW_360_LOCATIONS"].find((loc)=>loc.id === defaultLocation);
        if (defaultLoc) {
            setSelectedLocation(defaultLoc);
        }
    }, [
        defaultLocation
    ]);
    // Handle location selection from search
    const handleSelectLocation = (location)=>{
        setSelectedLocation(location);
        setActiveTab(location.id);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `${className} space-y-6`,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white rounded-xl shadow-md border border-purple-200",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$view360$2d$search$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["View360Search"], {
                    onSelectLocation: handleSelectLocation,
                    className: "p-3 sm:p-4"
                }, void 0, false, {
                    fileName: "[project]/src/features/view360/view360-location-tabs.tsx",
                    lineNumber: 39,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/view360/view360-location-tabs.tsx",
                lineNumber: 38,
                columnNumber: 7
            }, this),
            selectedLocation && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white rounded-xl shadow-md border border-purple-200 p-3 sm:p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center space-x-2 sm:space-x-3",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "bg-purple-100 p-1.5 sm:p-2 rounded-full",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$lucide$2d$react$2f$dist$2f$esm$2f$icons$2f$map$2d$pin$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__MapPin$3e$__["MapPin"], {
                                className: "h-4 w-4 sm:h-5 sm:w-5 text-purple-600"
                            }, void 0, false, {
                                fileName: "[project]/src/features/view360/view360-location-tabs.tsx",
                                lineNumber: 50,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/features/view360/view360-location-tabs.tsx",
                            lineNumber: 49,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-base sm:text-lg font-semibold text-purple-800",
                                    children: selectedLocation.name
                                }, void 0, false, {
                                    fileName: "[project]/src/features/view360/view360-location-tabs.tsx",
                                    lineNumber: 53,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-xs sm:text-sm text-gray-600",
                                    children: [
                                        selectedLocation.city,
                                        ", ",
                                        selectedLocation.region
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/features/view360/view360-location-tabs.tsx",
                                    lineNumber: 54,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/features/view360/view360-location-tabs.tsx",
                            lineNumber: 52,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/features/view360/view360-location-tabs.tsx",
                    lineNumber: 48,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/view360/view360-location-tabs.tsx",
                lineNumber: 47,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white rounded-xl shadow-md border border-purple-200 overflow-hidden",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radix$2d$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Tabs"], {
                    value: activeTab,
                    onValueChange: setActiveTab,
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radix$2d$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TabsList"], {
                            className: "hidden",
                            children: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$view360$2d$locations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VIEW_360_LOCATIONS"].map((location)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radix$2d$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TabsTrigger"], {
                                    value: location.id,
                                    children: location.name
                                }, location.id, false, {
                                    fileName: "[project]/src/features/view360/view360-location-tabs.tsx",
                                    lineNumber: 67,
                                    columnNumber: 15
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/features/view360/view360-location-tabs.tsx",
                            lineNumber: 65,
                            columnNumber: 11
                        }, this),
                        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$view360$2d$locations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["VIEW_360_LOCATIONS"].map((location)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$radix$2d$ui$2f$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["TabsContent"], {
                                value: location.id,
                                className: "p-0",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$google$2d$maps$2d$view$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["GoogleMapsView"], {
                                    mapUrl: location.googleMapsUrl,
                                    height: "400px",
                                    title: location.name,
                                    showInfoCard: showInfoCard,
                                    reloadButton: true,
                                    className: "rounded-none border-0 h-[400px] sm:h-[500px] md:h-[600px]"
                                }, void 0, false, {
                                    fileName: "[project]/src/features/view360/view360-location-tabs.tsx",
                                    lineNumber: 75,
                                    columnNumber: 15
                                }, this)
                            }, location.id, false, {
                                fileName: "[project]/src/features/view360/view360-location-tabs.tsx",
                                lineNumber: 74,
                                columnNumber: 13
                            }, this))
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/features/view360/view360-location-tabs.tsx",
                    lineNumber: 64,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/features/view360/view360-location-tabs.tsx",
                lineNumber: 63,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/features/view360/view360-location-tabs.tsx",
        lineNumber: 36,
        columnNumber: 5
    }, this);
}
const __TURBOPACK__default__export__ = View360LocationTabs;
}}),
"[project]/src/features/view360/index.ts [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$view360$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/view360/view360.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$google$2d$maps$2d$view$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/view360/google-maps-view.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$universal$2d$view360$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/view360/universal-view360.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$view360$2d$location$2d$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/view360/view360-location-tabs.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$view360$2d$search$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/view360/view360-search.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$view360$2d$locations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/view360/view360-locations.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
}}),
"[project]/src/features/view360/index.ts [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$view360$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/view360/view360.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$google$2d$maps$2d$view$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/view360/google-maps-view.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$universal$2d$view360$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/view360/universal-view360.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$view360$2d$location$2d$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/view360/view360-location-tabs.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$view360$2d$search$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/view360/view360-search.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$view360$2d$locations$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/view360/view360-locations.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/features/view360/index.ts [app-ssr] (ecmascript) <locals>");
}}),
"[project]/src/app/(social-travel-trip)/view360/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>View360Page)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$page$2d$header$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/page-header.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ui/card.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$index$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/features/view360/index.ts [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$view360$2d$location$2d$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/features/view360/view360-location-tabs.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
function View360Page() {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full space-y-4 sm:space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$page$2d$header$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["PageHeader"], {
                title: "View 360°",
                description: "Khám phá các địa điểm du lịch với góc nhìn 360 độ từ Google Maps Street View"
            }, void 0, false, {
                fileName: "[project]/src/app/(social-travel-trip)/view360/page.tsx",
                lineNumber: 11,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Card"], {
                className: "border-purple-100 dark:border-purple-900 bg-white/90 dark:bg-gray-950/90 backdrop-blur-xs",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ui$2f$card$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["CardContent"], {
                    className: "p-3 sm:p-6",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$features$2f$view360$2f$view360$2d$location$2d$tabs$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["View360LocationTabs"], {
                        showInfoCard: false
                    }, void 0, false, {
                        fileName: "[project]/src/app/(social-travel-trip)/view360/page.tsx",
                        lineNumber: 18,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/(social-travel-trip)/view360/page.tsx",
                    lineNumber: 17,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/(social-travel-trip)/view360/page.tsx",
                lineNumber: 16,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/(social-travel-trip)/view360/page.tsx",
        lineNumber: 10,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=src_8d13eeac._.js.map