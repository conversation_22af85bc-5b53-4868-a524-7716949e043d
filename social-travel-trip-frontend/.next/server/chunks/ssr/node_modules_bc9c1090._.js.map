{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40radix-ui/react-tabs/src/tabs.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { Presence } from '@radix-ui/react-presence';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { useDirection } from '@radix-ui/react-direction';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useId } from '@radix-ui/react-id';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Tabs\n * -----------------------------------------------------------------------------------------------*/\n\nconst TABS_NAME = 'Tabs';\n\ntype ScopedProps<P> = P & { __scopeTabs?: Scope };\nconst [createTabsContext, createTabsScope] = createContextScope(TABS_NAME, [\n  createRovingFocusGroupScope,\n]);\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype TabsContextValue = {\n  baseId: string;\n  value: string;\n  onValueChange: (value: string) => void;\n  orientation?: TabsProps['orientation'];\n  dir?: TabsProps['dir'];\n  activationMode?: TabsProps['activationMode'];\n};\n\nconst [TabsProvider, useTabsContext] = createTabsContext<TabsContextValue>(TABS_NAME);\n\ntype TabsElement = React.ElementRef<typeof Primitive.div>;\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface TabsProps extends PrimitiveDivProps {\n  /** The value for the selected tab, if controlled */\n  value?: string;\n  /** The value of the tab to select by default, if uncontrolled */\n  defaultValue?: string;\n  /** A function called when a new tab is selected */\n  onValueChange?: (value: string) => void;\n  /**\n   * The orientation the tabs are layed out.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   * @defaultValue horizontal\n   */\n  orientation?: RovingFocusGroupProps['orientation'];\n  /**\n   * The direction of navigation between toolbar items.\n   */\n  dir?: RovingFocusGroupProps['dir'];\n  /**\n   * Whether a tab is activated automatically or manually.\n   * @defaultValue automatic\n   * */\n  activationMode?: 'automatic' | 'manual';\n}\n\nconst Tabs = React.forwardRef<TabsElement, TabsProps>(\n  (props: ScopedProps<TabsProps>, forwardedRef) => {\n    const {\n      __scopeTabs,\n      value: valueProp,\n      onValueChange,\n      defaultValue,\n      orientation = 'horizontal',\n      dir,\n      activationMode = 'automatic',\n      ...tabsProps\n    } = props;\n    const direction = useDirection(dir);\n    const [value, setValue] = useControllableState({\n      prop: valueProp,\n      onChange: onValueChange,\n      defaultProp: defaultValue ?? '',\n      caller: TABS_NAME,\n    });\n\n    return (\n      <TabsProvider\n        scope={__scopeTabs}\n        baseId={useId()}\n        value={value}\n        onValueChange={setValue}\n        orientation={orientation}\n        dir={direction}\n        activationMode={activationMode}\n      >\n        <Primitive.div\n          dir={direction}\n          data-orientation={orientation}\n          {...tabsProps}\n          ref={forwardedRef}\n        />\n      </TabsProvider>\n    );\n  }\n);\n\nTabs.displayName = TABS_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsList\n * -----------------------------------------------------------------------------------------------*/\n\nconst TAB_LIST_NAME = 'TabsList';\n\ntype TabsListElement = React.ElementRef<typeof Primitive.div>;\ninterface TabsListProps extends PrimitiveDivProps {\n  loop?: RovingFocusGroupProps['loop'];\n}\n\nconst TabsList = React.forwardRef<TabsListElement, TabsListProps>(\n  (props: ScopedProps<TabsListProps>, forwardedRef) => {\n    const { __scopeTabs, loop = true, ...listProps } = props;\n    const context = useTabsContext(TAB_LIST_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    return (\n      <RovingFocusGroup.Root\n        asChild\n        {...rovingFocusGroupScope}\n        orientation={context.orientation}\n        dir={context.dir}\n        loop={loop}\n      >\n        <Primitive.div\n          role=\"tablist\"\n          aria-orientation={context.orientation}\n          {...listProps}\n          ref={forwardedRef}\n        />\n      </RovingFocusGroup.Root>\n    );\n  }\n);\n\nTabsList.displayName = TAB_LIST_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsTrigger\n * -----------------------------------------------------------------------------------------------*/\n\nconst TRIGGER_NAME = 'TabsTrigger';\n\ntype TabsTriggerElement = React.ElementRef<typeof Primitive.button>;\ntype PrimitiveButtonProps = React.ComponentPropsWithoutRef<typeof Primitive.button>;\ninterface TabsTriggerProps extends PrimitiveButtonProps {\n  value: string;\n}\n\nconst TabsTrigger = React.forwardRef<TabsTriggerElement, TabsTriggerProps>(\n  (props: ScopedProps<TabsTriggerProps>, forwardedRef) => {\n    const { __scopeTabs, value, disabled = false, ...triggerProps } = props;\n    const context = useTabsContext(TRIGGER_NAME, __scopeTabs);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    return (\n      <RovingFocusGroup.Item\n        asChild\n        {...rovingFocusGroupScope}\n        focusable={!disabled}\n        active={isSelected}\n      >\n        <Primitive.button\n          type=\"button\"\n          role=\"tab\"\n          aria-selected={isSelected}\n          aria-controls={contentId}\n          data-state={isSelected ? 'active' : 'inactive'}\n          data-disabled={disabled ? '' : undefined}\n          disabled={disabled}\n          id={triggerId}\n          {...triggerProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // only call handler if it's the left button (mousedown gets triggered by all mouse buttons)\n            // but not when the control key is pressed (avoiding MacOS right click)\n            if (!disabled && event.button === 0 && event.ctrlKey === false) {\n              context.onValueChange(value);\n            } else {\n              // prevent focus to avoid accidental activation\n              event.preventDefault();\n            }\n          })}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if ([' ', 'Enter'].includes(event.key)) context.onValueChange(value);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => {\n            // handle \"automatic\" activation if necessary\n            // ie. activate tab following focus\n            const isAutomaticActivation = context.activationMode !== 'manual';\n            if (!isSelected && !disabled && isAutomaticActivation) {\n              context.onValueChange(value);\n            }\n          })}\n        />\n      </RovingFocusGroup.Item>\n    );\n  }\n);\n\nTabsTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * TabsContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'TabsContent';\n\ntype TabsContentElement = React.ElementRef<typeof Primitive.div>;\ninterface TabsContentProps extends PrimitiveDivProps {\n  value: string;\n\n  /**\n   * Used to force mounting when more control is needed. Useful when\n   * controlling animation with React animation libraries.\n   */\n  forceMount?: true;\n}\n\nconst TabsContent = React.forwardRef<TabsContentElement, TabsContentProps>(\n  (props: ScopedProps<TabsContentProps>, forwardedRef) => {\n    const { __scopeTabs, value, forceMount, children, ...contentProps } = props;\n    const context = useTabsContext(CONTENT_NAME, __scopeTabs);\n    const triggerId = makeTriggerId(context.baseId, value);\n    const contentId = makeContentId(context.baseId, value);\n    const isSelected = value === context.value;\n    const isMountAnimationPreventedRef = React.useRef(isSelected);\n\n    React.useEffect(() => {\n      const rAF = requestAnimationFrame(() => (isMountAnimationPreventedRef.current = false));\n      return () => cancelAnimationFrame(rAF);\n    }, []);\n\n    return (\n      <Presence present={forceMount || isSelected}>\n        {({ present }) => (\n          <Primitive.div\n            data-state={isSelected ? 'active' : 'inactive'}\n            data-orientation={context.orientation}\n            role=\"tabpanel\"\n            aria-labelledby={triggerId}\n            hidden={!present}\n            id={contentId}\n            tabIndex={0}\n            {...contentProps}\n            ref={forwardedRef}\n            style={{\n              ...props.style,\n              animationDuration: isMountAnimationPreventedRef.current ? '0s' : undefined,\n            }}\n          >\n            {present && children}\n          </Primitive.div>\n        )}\n      </Presence>\n    );\n  }\n);\n\nTabsContent.displayName = CONTENT_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\nfunction makeTriggerId(baseId: string, value: string) {\n  return `${baseId}-trigger-${value}`;\n}\n\nfunction makeContentId(baseId: string, value: string) {\n  return `${baseId}-content-${value}`;\n}\n\nconst Root = Tabs;\nconst List = TabsList;\nconst Trigger = TabsTrigger;\nconst Content = TabsContent;\n\nexport {\n  createTabsScope,\n  //\n  Tabs,\n  TabsList,\n  TabsTrigger,\n  TabsContent,\n  //\n  Root,\n  List,\n  Trigger,\n  Content,\n};\nexport type { TabsProps, TabsListProps, TabsTriggerProps, TabsContentProps };\n"], "names": ["Root"], "mappings": ";;;;;;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,4BAA4B;AACrC,SAAS,0BAA0B;AACnC,SAAS,mCAAmC;AAC5C,SAAS,gBAAgB;AACzB,SAAS,iBAAiB;AAE1B,SAAS,oBAAoB;AAC7B,SAAS,4BAA4B;AACrC,SAAS,aAAa;AAoFd;;;;;;;;;;;;;AA5ER,IAAM,YAAY;AAGlB,IAAM,CAAC,mBAAmB,eAAe,CAAA,2KAAI,qBAAA,EAAmB,WAAW;gLACzE,8BAAA;CACD;AACD,IAAM,2MAA2B,8BAAA,CAA4B;AAW7D,IAAM,CAAC,cAAc,cAAc,CAAA,GAAI,kBAAoC,SAAS;AA6BpF,IAAM,iNAAa,aAAA,EACjB,CAAC,OAA+B,iBAAiB;IAC/C,MAAM,EACJ,WAAA,EACA,OAAO,SAAA,EACP,aAAA,EACA,YAAA,EACA,cAAc,YAAA,EACd,GAAA,EACA,iBAAiB,WAAA,EACjB,GAAG,WACL,GAAI;IACJ,MAAM,sLAAY,eAAA,EAAa,GAAG;IAClC,MAAM,CAAC,OAAO,QAAQ,CAAA,gMAAI,uBAAA,EAAqB;QAC7C,MAAM;QACN,UAAU;QACV,aAAa,gBAAgB;QAC7B,QAAQ;IACV,CAAC;IAED,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,cAAA;QACC,OAAO;QACP,2KAAQ,QAAA,CAAM;QACd;QACA,eAAe;QACf;QACA,KAAK;QACL;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;YACC,KAAK;YACL,oBAAkB;YACjB,GAAG,SAAA;YACJ,KAAK;QAAA;IACP;AAGN;AAGF,KAAK,WAAA,GAAc;AAMnB,IAAM,gBAAgB;AAOtB,IAAM,qNAAiB,aAAA,EACrB,CAAC,OAAmC,iBAAiB;IACnD,MAAM,EAAE,WAAA,EAAa,OAAO,IAAA,EAAM,GAAG,UAAU,CAAA,GAAI;IACnD,MAAM,UAAU,eAAe,eAAe,WAAW;IACzD,MAAM,wBAAwB,yBAAyB,WAAW;IAClE,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAkB,mLAAA,EAAjB;QACC,SAAO;QACN,GAAG,qBAAA;QACJ,aAAa,QAAQ,WAAA;QACrB,KAAK,QAAQ,GAAA;QACb;QAEA,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;YACC,MAAK;YACL,oBAAkB,QAAQ,WAAA;YACzB,GAAG,SAAA;YACJ,KAAK;QAAA;IACP;AAGN;AAGF,SAAS,WAAA,GAAc;AAMvB,IAAM,eAAe;AAQrB,IAAM,wNAAoB,aAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,WAAA,EAAa,KAAA,EAAO,WAAW,KAAA,EAAO,GAAG,aAAa,CAAA,GAAI;IAClE,MAAM,UAAU,eAAe,cAAc,WAAW;IACxD,MAAM,wBAAwB,yBAAyB,WAAW;IAClE,MAAM,YAAY,cAAc,QAAQ,MAAA,EAAQ,KAAK;IACrD,MAAM,YAAY,cAAc,QAAQ,MAAA,EAAQ,KAAK;IACrD,MAAM,aAAa,UAAU,QAAQ,KAAA;IACrC,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,6KAAkB,QAAA,EAAjB;QACC,SAAO;QACN,GAAG,qBAAA;QACJ,WAAW,CAAC;QACZ,QAAQ;QAER,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,MAAA,EAAV;YACC,MAAK;YACL,MAAK;YACL,iBAAe;YACf,iBAAe;YACf,cAAY,aAAa,WAAW;YACpC,iBAAe,WAAW,KAAK,KAAA;YAC/B;YACA,IAAI;YACH,GAAG,YAAA;YACJ,KAAK;YACL,cAAa,uLAAA,EAAqB,MAAM,WAAA,EAAa,CAAC,UAAU;gBAG9D,IAAI,CAAC,YAAY,MAAM,MAAA,KAAW,KAAK,MAAM,OAAA,KAAY,OAAO;oBAC9D,QAAQ,aAAA,CAAc,KAAK;gBAC7B,OAAO;oBAEL,MAAM,cAAA,CAAe;gBACvB;YACF,CAAC;YACD,4KAAW,uBAAA,EAAqB,MAAM,SAAA,EAAW,CAAC,UAAU;gBAC1D,IAAI;oBAAC;oBAAK,OAAO;iBAAA,CAAE,QAAA,CAAS,MAAM,GAAG,EAAG,CAAA,QAAQ,aAAA,CAAc,KAAK;YACrE,CAAC;YACD,0KAAS,uBAAA,EAAqB,MAAM,OAAA,EAAS,MAAM;gBAGjD,MAAM,wBAAwB,QAAQ,cAAA,KAAmB;gBACzD,IAAI,CAAC,cAAc,CAAC,YAAY,uBAAuB;oBACrD,QAAQ,aAAA,CAAc,KAAK;gBAC7B;YACF,CAAC;QAAA;IACH;AAGN;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,eAAe;AAarB,IAAM,wNAAoB,aAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,WAAA,EAAa,KAAA,EAAO,UAAA,EAAY,QAAA,EAAU,GAAG,aAAa,CAAA,GAAI;IACtE,MAAM,UAAU,eAAe,cAAc,WAAW;IACxD,MAAM,YAAY,cAAc,QAAQ,MAAA,EAAQ,KAAK;IACrD,MAAM,YAAY,cAAc,QAAQ,MAAA,EAAQ,KAAK;IACrD,MAAM,aAAa,UAAU,QAAQ,KAAA;IACrC,MAAM,yOAAqC,SAAA,EAAO,UAAU;8MAEtD,YAAA,EAAU,MAAM;QACpB,MAAM,MAAM,sBAAsB,IAAO,6BAA6B,OAAA,GAAU,KAAM;QACtF,OAAO,IAAM,qBAAqB,GAAG;IACvC,GAAG,CAAC,CAAC;IAEL,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,sKAAC,YAAA,EAAA;QAAS,SAAS,cAAc;QAC9B,UAAA,CAAC,EAAE,OAAA,CAAQ,CAAA,GACV,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,kLAAA,CAAU,GAAA,EAAV;gBACC,cAAY,aAAa,WAAW;gBACpC,oBAAkB,QAAQ,WAAA;gBAC1B,MAAK;gBACL,mBAAiB;gBACjB,QAAQ,CAAC;gBACT,IAAI;gBACJ,UAAU;gBACT,GAAG,YAAA;gBACJ,KAAK;gBACL,OAAO;oBACL,GAAG,MAAM,KAAA;oBACT,mBAAmB,6BAA6B,OAAA,GAAU,OAAO,KAAA;gBACnE;gBAEC,UAAA,WAAW;YAAA;IACd,CAEJ;AAEJ;AAGF,YAAY,WAAA,GAAc;AAI1B,SAAS,cAAc,MAAA,EAAgB,KAAA,EAAe;IACpD,OAAO,GAAG,MAAM,CAAA,SAAA,EAAY,KAAK,EAAA;AACnC;AAEA,SAAS,cAAc,MAAA,EAAgB,KAAA,EAAe;IACpD,OAAO,GAAG,MAAM,CAAA,SAAA,EAAY,KAAK,EAAA;AACnC;AAEA,IAAMA,QAAO;AACb,IAAM,OAAO;AACb,IAAM,UAAU;AAChB,IAAM,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40radix-ui/react-use-is-hydrated/src/use-is-hydrated.tsx"], "sourcesContent": ["import { useSyncExternalStore } from 'use-sync-external-store/shim';\n\n/**\n * Determines whether or not the component tree has been hydrated.\n */\nexport function useIsHydrated() {\n  return useSyncExternalStore(\n    subscribe,\n    () => true,\n    () => false\n  );\n}\n\nfunction subscribe() {\n  return () => {};\n}\n"], "names": [], "mappings": ";;;;AAAA,SAAS,4BAA4B;;AAK9B,SAAS,gBAAgB;IAC9B,6KAAO,uBAAA,EACL,WACA,IAAM,MACN,IAAM;AAEV;AAEA,SAAS,YAAY;IACnB,OAAO,KAAO,CAAD;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 211, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40radix-ui/react-avatar/src/avatar.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useIsHydrated } from '@radix-ui/react-use-is-hydrated';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * Avatar\n * -----------------------------------------------------------------------------------------------*/\n\nconst AVATAR_NAME = 'Avatar';\n\ntype ScopedProps<P> = P & { __scopeAvatar?: Scope };\nconst [createAvatarContext, createAvatarScope] = createContextScope(AVATAR_NAME);\n\ntype ImageLoadingStatus = 'idle' | 'loading' | 'loaded' | 'error';\n\ntype AvatarContextValue = {\n  imageLoadingStatus: ImageLoadingStatus;\n  onImageLoadingStatusChange(status: ImageLoadingStatus): void;\n};\n\nconst [AvatarProvider, useAvatarContext] = createAvatarContext<AvatarContextValue>(AVATAR_NAME);\n\ntype AvatarElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface AvatarProps extends PrimitiveSpanProps {}\n\nconst Avatar = React.forwardRef<AvatarElement, AvatarProps>(\n  (props: ScopedProps<AvatarProps>, forwardedRef) => {\n    const { __scopeAvatar, ...avatarProps } = props;\n    const [imageLoadingStatus, setImageLoadingStatus] = React.useState<ImageLoadingStatus>('idle');\n    return (\n      <AvatarProvider\n        scope={__scopeAvatar}\n        imageLoadingStatus={imageLoadingStatus}\n        onImageLoadingStatusChange={setImageLoadingStatus}\n      >\n        <Primitive.span {...avatarProps} ref={forwardedRef} />\n      </AvatarProvider>\n    );\n  }\n);\n\nAvatar.displayName = AVATAR_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AvatarImage\n * -----------------------------------------------------------------------------------------------*/\n\nconst IMAGE_NAME = 'AvatarImage';\n\ntype AvatarImageElement = React.ElementRef<typeof Primitive.img>;\ntype PrimitiveImageProps = React.ComponentPropsWithoutRef<typeof Primitive.img>;\ninterface AvatarImageProps extends PrimitiveImageProps {\n  onLoadingStatusChange?: (status: ImageLoadingStatus) => void;\n}\n\nconst AvatarImage = React.forwardRef<AvatarImageElement, AvatarImageProps>(\n  (props: ScopedProps<AvatarImageProps>, forwardedRef) => {\n    const { __scopeAvatar, src, onLoadingStatusChange = () => {}, ...imageProps } = props;\n    const context = useAvatarContext(IMAGE_NAME, __scopeAvatar);\n    const imageLoadingStatus = useImageLoadingStatus(src, imageProps);\n    const handleLoadingStatusChange = useCallbackRef((status: ImageLoadingStatus) => {\n      onLoadingStatusChange(status);\n      context.onImageLoadingStatusChange(status);\n    });\n\n    useLayoutEffect(() => {\n      if (imageLoadingStatus !== 'idle') {\n        handleLoadingStatusChange(imageLoadingStatus);\n      }\n    }, [imageLoadingStatus, handleLoadingStatusChange]);\n\n    return imageLoadingStatus === 'loaded' ? (\n      <Primitive.img {...imageProps} ref={forwardedRef} src={src} />\n    ) : null;\n  }\n);\n\nAvatarImage.displayName = IMAGE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AvatarFallback\n * -----------------------------------------------------------------------------------------------*/\n\nconst FALLBACK_NAME = 'AvatarFallback';\n\ntype AvatarFallbackElement = React.ElementRef<typeof Primitive.span>;\ninterface AvatarFallbackProps extends PrimitiveSpanProps {\n  delayMs?: number;\n}\n\nconst AvatarFallback = React.forwardRef<AvatarFallbackElement, AvatarFallbackProps>(\n  (props: ScopedProps<AvatarFallbackProps>, forwardedRef) => {\n    const { __scopeAvatar, delayMs, ...fallbackProps } = props;\n    const context = useAvatarContext(FALLBACK_NAME, __scopeAvatar);\n    const [canRender, setCanRender] = React.useState(delayMs === undefined);\n\n    React.useEffect(() => {\n      if (delayMs !== undefined) {\n        const timerId = window.setTimeout(() => setCanRender(true), delayMs);\n        return () => window.clearTimeout(timerId);\n      }\n    }, [delayMs]);\n\n    return canRender && context.imageLoadingStatus !== 'loaded' ? (\n      <Primitive.span {...fallbackProps} ref={forwardedRef} />\n    ) : null;\n  }\n);\n\nAvatarFallback.displayName = FALLBACK_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction resolveLoadingStatus(image: HTMLImageElement | null, src?: string): ImageLoadingStatus {\n  if (!image) {\n    return 'idle';\n  }\n  if (!src) {\n    return 'error';\n  }\n  if (image.src !== src) {\n    image.src = src;\n  }\n  return image.complete && image.naturalWidth > 0 ? 'loaded' : 'loading';\n}\n\nfunction useImageLoadingStatus(\n  src: string | undefined,\n  { referrerPolicy, crossOrigin }: AvatarImageProps\n) {\n  const isHydrated = useIsHydrated();\n  const imageRef = React.useRef<HTMLImageElement | null>(null);\n  const image = (() => {\n    if (!isHydrated) return null;\n    if (!imageRef.current) {\n      imageRef.current = new window.Image();\n    }\n    return imageRef.current;\n  })();\n\n  const [loadingStatus, setLoadingStatus] = React.useState<ImageLoadingStatus>(() =>\n    resolveLoadingStatus(image, src)\n  );\n\n  useLayoutEffect(() => {\n    setLoadingStatus(resolveLoadingStatus(image, src));\n  }, [image, src]);\n\n  useLayoutEffect(() => {\n    const updateStatus = (status: ImageLoadingStatus) => () => {\n      setLoadingStatus(status);\n    };\n\n    if (!image) return;\n\n    const handleLoad = updateStatus('loaded');\n    const handleError = updateStatus('error');\n    image.addEventListener('load', handleLoad);\n    image.addEventListener('error', handleError);\n    if (referrerPolicy) {\n      image.referrerPolicy = referrerPolicy;\n    }\n    if (typeof crossOrigin === 'string') {\n      image.crossOrigin = crossOrigin;\n    }\n\n    return () => {\n      image.removeEventListener('load', handleLoad);\n      image.removeEventListener('error', handleError);\n    };\n  }, [image, crossOrigin, referrerPolicy]);\n\n  return loadingStatus;\n}\n\nconst Root = Avatar;\nconst Image = AvatarImage;\nconst Fallback = AvatarFallback;\n\nexport {\n  createAvatarScope,\n  //\n  Avatar,\n  AvatarImage,\n  AvatarFallback,\n  //\n  Root,\n  Image,\n  Fallback,\n};\nexport type { AvatarProps, AvatarImageProps, AvatarFallbackProps };\n"], "names": [], "mappings": ";;;;;;;;;;AAAA,YAAY,WAAW;AACvB,SAAS,0BAA0B;AACnC,SAAS,sBAAsB;AAC/B,SAAS,uBAAuB;AAChC,SAAS,iBAAiB;AAC1B,SAAS,qBAAqB;AAoCtB;;;;;;;;;AA5BR,IAAM,cAAc;AAGpB,IAAM,CAAC,qBAAqB,iBAAiB,CAAA,2KAAI,qBAAA,EAAmB,WAAW;AAS/E,IAAM,CAAC,gBAAgB,gBAAgB,CAAA,GAAI,oBAAwC,WAAW;AAM9F,IAAM,mNAAe,aAAA,EACnB,CAAC,OAAiC,iBAAiB;IACjD,MAAM,EAAE,aAAA,EAAe,GAAG,YAAY,CAAA,GAAI;IAC1C,MAAM,CAAC,oBAAoB,qBAAqB,CAAA,6MAAU,WAAA,EAA6B,MAAM;IAC7F,OACE,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,gBAAA;QACC,OAAO;QACP;QACA,4BAA4B;QAE5B,UAAA,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,IAAA,EAAV;YAAgB,GAAG,WAAA;YAAa,KAAK;QAAA,CAAc;IAAA;AAG1D;AAGF,OAAO,WAAA,GAAc;AAMrB,IAAM,aAAa;AAQnB,IAAM,uNAAoB,cAAA,EACxB,CAAC,OAAsC,iBAAiB;IACtD,MAAM,EAAE,aAAA,EAAe,GAAA,EAAK,wBAAwB,KAAO,CAAD,AAAC,EAAG,GAAG,WAAW,CAAA,GAAI;IAChF,MAAM,UAAU,iBAAiB,YAAY,aAAa;IAC1D,MAAM,qBAAqB,sBAAsB,KAAK,UAAU;IAChE,MAAM,mNAA4B,iBAAA,EAAe,CAAC,WAA+B;QAC/E,sBAAsB,MAAM;QAC5B,QAAQ,0BAAA,CAA2B,MAAM;IAC3C,CAAC;IAED,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,IAAI,uBAAuB,QAAQ;YACjC,0BAA0B,kBAAkB;QAC9C;IACF,GAAG;QAAC;QAAoB,yBAAyB;KAAC;IAElD,OAAO,uBAAuB,WAC5B,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,wKAAC,YAAA,CAAU,GAAA,EAAV;QAAe,GAAG,UAAA;QAAY,KAAK;QAAc;IAAA,CAAU,IAC1D;AACN;AAGF,YAAY,WAAA,GAAc;AAM1B,IAAM,gBAAgB;AAOtB,IAAM,2NAAuB,aAAA,EAC3B,CAAC,OAAyC,iBAAiB;IACzD,MAAM,EAAE,aAAA,EAAe,OAAA,EAAS,GAAG,cAAc,CAAA,GAAI;IACrD,MAAM,UAAU,iBAAiB,eAAe,aAAa;IAC7D,MAAM,CAAC,WAAW,YAAY,CAAA,6MAAU,WAAA,EAAS,YAAY,KAAA,CAAS;QAEhE,kNAAA,EAAU,MAAM;QACpB,IAAI,YAAY,KAAA,GAAW;YACzB,MAAM,UAAU,OAAO,UAAA,CAAW,IAAM,aAAa,IAAI,GAAG,OAAO;YACnE,OAAO,IAAM,OAAO,YAAA,CAAa,OAAO;QAC1C;IACF,GAAG;QAAC,OAAO;KAAC;IAEZ,OAAO,aAAa,QAAQ,kBAAA,KAAuB,WACjD,aAAA,GAAA,CAAA,GAAA,uNAAA,CAAA,MAAA,EAAC,kLAAA,CAAU,IAAA,EAAV;QAAgB,GAAG,aAAA;QAAe,KAAK;IAAA,CAAc,IACpD;AACN;AAGF,eAAe,WAAA,GAAc;AAI7B,SAAS,qBAAqB,KAAA,EAAgC,GAAA,EAAkC;IAC9F,IAAI,CAAC,OAAO;QACV,OAAO;IACT;IACA,IAAI,CAAC,KAAK;QACR,OAAO;IACT;IACA,IAAI,MAAM,GAAA,KAAQ,KAAK;QACrB,MAAM,GAAA,GAAM;IACd;IACA,OAAO,MAAM,QAAA,IAAY,MAAM,YAAA,GAAe,IAAI,WAAW;AAC/D;AAEA,SAAS,sBACP,GAAA,EACA,EAAE,cAAA,EAAgB,WAAA,CAAY,CAAA,EAC9B;IACA,MAAM,cAAa,qMAAA,CAAc;IACjC,MAAM,qNAAiB,SAAA,EAAgC,IAAI;IAC3D,MAAM,QAAA,CAAS,MAAM;QACnB,IAAI,CAAC,WAAY,CAAA,OAAO;QACxB,IAAI,CAAC,SAAS,OAAA,EAAS;YACrB,SAAS,OAAA,GAAU,IAAI,OAAO,KAAA,CAAM;QACtC;QACA,OAAO,SAAS,OAAA;IAClB,CAAA,EAAG;IAEH,MAAM,CAAC,eAAe,gBAAgB,CAAA,6MAAU,WAAA,EAA6B,IAC3E,qBAAqB,OAAO,GAAG;IAGjC,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,iBAAiB,qBAAqB,OAAO,GAAG,CAAC;IACnD,GAAG;QAAC;QAAO,GAAG;KAAC;IAEf,CAAA,GAAA,mLAAA,CAAA,kBAAA,EAAgB,MAAM;QACpB,MAAM,eAAe,CAAC,SAA+B,MAAM;gBACzD,iBAAiB,MAAM;YACzB;QAEA,IAAI,CAAC,MAAO,CAAA;QAEZ,MAAM,aAAa,aAAa,QAAQ;QACxC,MAAM,cAAc,aAAa,OAAO;QACxC,MAAM,gBAAA,CAAiB,QAAQ,UAAU;QACzC,MAAM,gBAAA,CAAiB,SAAS,WAAW;QAC3C,IAAI,gBAAgB;YAClB,MAAM,cAAA,GAAiB;QACzB;QACA,IAAI,OAAO,gBAAgB,UAAU;YACnC,MAAM,WAAA,GAAc;QACtB;QAEA,OAAO,MAAM;YACX,MAAM,mBAAA,CAAoB,QAAQ,UAAU;YAC5C,MAAM,mBAAA,CAAoB,SAAS,WAAW;QAChD;IACF,GAAG;QAAC;QAAO;QAAa,cAAc;KAAC;IAEvC,OAAO;AACT;AAEA,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,WAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 362, "column": 0}, "map": {"version": 3, "file": "map-pin.js", "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/lucide-react/src/icons/map-pin.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0',\n      key: '1r0f0z',\n    },\n  ],\n  ['circle', { cx: '12', cy: '10', r: '3', key: 'ilqhr7' }],\n];\n\n/**\n * @component @name MapPin\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgMTBjMCA0Ljk5My01LjUzOSAxMC4xOTMtNy4zOTkgMTEuNzk5YTEgMSAwIDAgMS0xLjIwMiAwQzkuNTM5IDIwLjE5MyA0IDE0Ljk5MyA0IDEwYTggOCAwIDAgMSAxNiAwIiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTAiIHI9IjMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/map-pin\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst MapPin = createLucideIcon('map-pin', __iconNode);\n\nexport default MapPin;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;IACA;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "file": "users.js", "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/lucide-react/src/icons/users.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2', key: '1yyitq' }],\n  ['path', { d: 'M16 3.128a4 4 0 0 1 0 7.744', key: '16gr8j' }],\n  ['path', { d: 'M22 21v-2a4 4 0 0 0-3-3.87', key: 'kshegd' }],\n  ['circle', { cx: '9', cy: '7', r: '4', key: 'nufk8' }],\n];\n\n/**\n * @component @name Users\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgMjF2LTJhNCA0IDAgMCAwLTQtNEg2YTQgNCAwIDAgMC00IDR2MiIgLz4KICA8cGF0aCBkPSJNMTYgMy4xMjhhNCA0IDAgMCAxIDAgNy43NDQiIC8+CiAgPHBhdGggZD0iTTIyIDIxdi0yYTQgNCAwIDAgMC0zLTMuODciIC8+CiAgPGNpcmNsZSBjeD0iOSIgY3k9IjciIHI9IjQiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/users\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Users = createLucideIcon('users', __iconNode);\n\nexport default Users;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC5D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3D;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAS;KAAA;CACvD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 472, "column": 0}, "map": {"version": 3, "file": "star.js", "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/lucide-react/src/icons/star.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z',\n      key: 'r04s7s',\n    },\n  ],\n];\n\n/**\n * @component @name Star\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEuNTI1IDIuMjk1YS41My41MyAwIDAgMSAuOTUgMGwyLjMxIDQuNjc5YTIuMTIzIDIuMTIzIDAgMCAwIDEuNTk1IDEuMTZsNS4xNjYuNzU2YS41My41MyAwIDAgMSAuMjk0LjkwNGwtMy43MzYgMy42MzhhMi4xMjMgMi4xMjMgMCAwIDAtLjYxMSAxLjg3OGwuODgyIDUuMTRhLjUzLjUzIDAgMCAxLS43NzEuNTZsLTQuNjE4LTIuNDI4YTIuMTIyIDIuMTIyIDAgMCAwLTEuOTczIDBMNi4zOTYgMjEuMDFhLjUzLjUzIDAgMCAxLS43Ny0uNTZsLjg4MS01LjEzOWEyLjEyMiAyLjEyMiAwIDAgMC0uNjExLTEuODc5TDIuMTYgOS43OTVhLjUzLjUzIDAgMCAxIC4yOTQtLjkwNmw1LjE2NS0uNzU1YTIuMTIyIDIuMTIyIDAgMCAwIDEuNTk3LTEuMTZ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/star\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Star = createLucideIcon('star', __iconNode);\n\nexport default Star;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;CACF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 511, "column": 0}, "map": {"version": 3, "file": "share-2.js", "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/lucide-react/src/icons/share-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '18', cy: '5', r: '3', key: 'gq8acd' }],\n  ['circle', { cx: '6', cy: '12', r: '3', key: 'w7nqdw' }],\n  ['circle', { cx: '18', cy: '19', r: '3', key: '1xt0gg' }],\n  ['line', { x1: '8.59', x2: '15.42', y1: '13.51', y2: '17.49', key: '47mynk' }],\n  ['line', { x1: '15.41', x2: '8.59', y1: '6.51', y2: '10.49', key: '1n3mei' }],\n];\n\n/**\n * @component @name Share2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxOCIgY3k9IjUiIHI9IjMiIC8+CiAgPGNpcmNsZSBjeD0iNiIgY3k9IjEyIiByPSIzIiAvPgogIDxjaXJjbGUgY3g9IjE4IiBjeT0iMTkiIHI9IjMiIC8+CiAgPGxpbmUgeDE9IjguNTkiIHgyPSIxNS40MiIgeTE9IjEzLjUxIiB5Mj0iMTcuNDkiIC8+CiAgPGxpbmUgeDE9IjE1LjQxIiB4Mj0iOC41OSIgeTE9IjYuNTEiIHkyPSIxMC40OSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/share-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Share2 = createLucideIcon('share-2', __iconNode);\n\nexport default Share2;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,GAAA,CAAK;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACvD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACvD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC7E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC9E;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 590, "column": 0}, "map": {"version": 3, "file": "bookmark.js", "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/lucide-react/src/icons/bookmark.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z', key: '1fy3hk' }],\n];\n\n/**\n * @component @name Bookmark\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTkgMjEtNy00LTcgNFY1YTIgMiAwIDAgMSAyLTJoMTBhMiAyIDAgMCAxIDIgMnYxNnoiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/bookmark\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Bookmark = createLucideIcon('bookmark', __iconNode);\n\nexport default Bookmark;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAuD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CACtF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 629, "column": 0}, "map": {"version": 3, "file": "camera.js", "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/lucide-react/src/icons/camera.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z',\n      key: '1tc9qg',\n    },\n  ],\n  ['circle', { cx: '12', cy: '13', r: '3', key: '1vg3eu' }],\n];\n\n/**\n * @component @name Camera\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTQuNSA0aC01TDcgN0g0YTIgMiAwIDAgMC0yIDJ2OWEyIDIgMCAwIDAgMiAyaDE2YTIgMiAwIDAgMCAyLTJWOWEyIDIgMCAwIDAtMi0yaC0zbC0yLjUtM3oiIC8+CiAgPGNpcmNsZSBjeD0iMTIiIGN5PSIxMyIgcj0iMyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/camera\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Camera = createLucideIcon('camera', __iconNode);\n\nexport default Camera;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,CAAA,CAAA,CAAK,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACP;KACF;IACA;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 677, "column": 0}, "map": {"version": 3, "file": "maximize.js", "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/lucide-react/src/icons/maximize.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M8 3H5a2 2 0 0 0-2 2v3', key: '1dcmit' }],\n  ['path', { d: 'M21 8V5a2 2 0 0 0-2-2h-3', key: '1e4gt3' }],\n  ['path', { d: 'M3 16v3a2 2 0 0 0 2 2h3', key: 'wsl5sc' }],\n  ['path', { d: 'M16 21h3a2 2 0 0 0 2-2v-3', key: '18trek' }],\n];\n\n/**\n * @component @name Maximize\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAzSDVhMiAyIDAgMCAwLTIgMnYzIiAvPgogIDxwYXRoIGQ9Ik0yMSA4VjVhMiAyIDAgMCAwLTItMmgtMyIgLz4KICA8cGF0aCBkPSJNMyAxNnYzYTIgMiAwIDAgMCAyIDJoMyIgLz4KICA8cGF0aCBkPSJNMTYgMjFoM2EyIDIgMCAwIDAgMi0ydi0zIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/maximize\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Maximize = createLucideIcon('maximize', __iconNode);\n\nexport default Maximize;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA0B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACvD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA4B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA2B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC5D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 737, "column": 0}, "map": {"version": 3, "file": "minimize.js", "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/lucide-react/src/icons/minimize.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M8 3v3a2 2 0 0 1-2 2H3', key: 'hohbtr' }],\n  ['path', { d: 'M21 8h-3a2 2 0 0 1-2-2V3', key: '5jw1f3' }],\n  ['path', { d: 'M3 16h3a2 2 0 0 1 2 2v3', key: '198tvr' }],\n  ['path', { d: 'M16 21v-3a2 2 0 0 1 2-2h3', key: 'ph8mxp' }],\n];\n\n/**\n * @component @name Minimize\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAzdjNhMiAyIDAgMCAxLTIgMkgzIiAvPgogIDxwYXRoIGQ9Ik0yMSA4aC0zYTIgMiAwIDAgMS0yLTJWMyIgLz4KICA8cGF0aCBkPSJNMyAxNmgzYTIgMiAwIDAgMSAyIDJ2MyIgLz4KICA8cGF0aCBkPSJNMTYgMjF2LTNhMiAyIDAgMCAxIDItMmgzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/minimize\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Minimize = createLucideIcon('minimize', __iconNode);\n\nexport default Minimize;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA0B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACvD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA4B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA2B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA6B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC5D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,QAAA,CAAW,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAY,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 797, "column": 0}, "map": {"version": 3, "file": "rotate-ccw.js", "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/lucide-react/src/icons/rotate-ccw.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8', key: '1357e3' }],\n  ['path', { d: 'M3 3v5h5', key: '1xhq8a' }],\n];\n\n/**\n * @component @name RotateCcw\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyAxMmE5IDkgMCAxIDAgOS05IDkuNzUgOS43NSAwIDAgMC02Ljc0IDIuNzRMMyA4IiAvPgogIDxwYXRoIGQ9Ik0zIDN2NWg1IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/rotate-ccw\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst RotateCcw = createLucideIcon('rotate-ccw', __iconNode);\n\nexport default RotateCcw;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAqD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 843, "column": 0}, "map": {"version": 3, "file": "chevron-left.js", "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/lucide-react/src/icons/chevron-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm15 18-6-6 6-6', key: '1wnfg3' }]];\n\n/**\n * @component @name ChevronLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTUgMTgtNi02IDYtNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chevron-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronLeft = createLucideIcon('chevron-left', __iconNode);\n\nexport default ChevronLeft;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAuB,CAAA,CAAA;IAAC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAC;KAAC;CAAA;AAa/E,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAiB,AAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 892, "column": 0}, "map": {"version": 3, "file": "globe.js", "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/lucide-react/src/icons/globe.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['path', { d: 'M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20', key: '13o1zl' }],\n  ['path', { d: 'M2 12h20', key: '9i4pu4' }],\n];\n\n/**\n * @component @name Globe\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8cGF0aCBkPSJNMTIgMmExNC41IDE0LjUgMCAwIDAgMCAyMCAxNC41IDE0LjUgMCAwIDAgMC0yMCIgLz4KICA8cGF0aCBkPSJNMiAxMmgyMCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/globe\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Globe = createLucideIcon('globe', __iconNode);\n\nexport default Globe;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAmD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAChF;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAQ,CAAA,wKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 947, "column": 0}, "map": {"version": 3, "file": "_rollupPluginBabelHelpers.js", "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1061, "column": 0}, "map": {"version": 3, "file": "_commonjsHelpers.js", "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1075, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1089, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/react-photo-sphere-viewer/src/index.tsx"], "sourcesContent": ["import React, { useState, useEffect, useImperativeHandle, forwardRef, useRef, useCallback, useMemo } from \"react\"\nimport {\n    Viewer,\n    ViewerConfig,\n    AnimateOptions,\n    CssSize,\n    ExtendedPosition,\n    UpdatableViewerConfig,\n    events,\n    PluginConstructor,\n    NavbarCustomButton,\n    TooltipConfig,\n    Tooltip,\n    Position,\n    Size,\n    PanoramaOptions,\n    utils,\n    AbstractPlugin\n    /* @ts-ignore next line */\n} from \"@photo-sphere-viewer/core\" // Peer dependency\nimport \"./styles.css\"\nimport \"@photo-sphere-viewer/core/index.css\"\n\nimport EventEmitter from \"eventemitter3\"\n\n\nconst eventEmitter = new EventEmitter()\nconst Emitter = {\n    on: (event: string, fn: (...args: unknown[]) => void) => eventEmitter.on(event, fn),\n    once: (event: string, fn: (...args: unknown[]) => void) => eventEmitter.once(event, fn),\n    off: (event: string, fn: (...args: unknown[]) => void) => eventEmitter.off(event, fn),\n    emit: (event: string, payload: unknown) => eventEmitter.emit(event, payload),\n}\n\nObject.freeze(Emitter)\n\nconst omittedProps = [\n    \"src\",\n    \"height\",\n    \"width\",\n    \"hideNavbarButton\",\n    \"containerClass\",\n    \"littlePlanet\",\n    \"onPositionChange\",\n    \"onZoomChange\",\n    \"onClick\",\n    \"onDblclick\",\n    \"onReady\",\n]\ntype MakeOptional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;\n\nexport interface CubeMapSrc {\n    left: string;\n    front: string;\n    right: string;\n    back: string;\n    top: string;\n    bottom: string;\n}\n\nexport interface TilesAdapterSrc {\n    width: number;\n    cols: number;\n    rows: number;\n    baseUrl: string;\n    tileUrl: (col: number, row: number) => string;\n}\n\ntype PluginEntry =\n    | PluginConstructor\n    | [PluginConstructor, Record<string, unknown>];\n\nexport type PluginConfig = PluginEntry;\n\n/**\n * Props interface for the Viewer component.\n * \n * @interface\n * @property {string} src - The source of the image to be viewed.\n * @property {boolean | string | Array<string | NavbarCustomButton>} [navbar] - Configuration for the navbar. Can be a boolean, string, or an array of strings or NavbarCustomButton.\n * @property {string} height - The height of the viewer.\n * @property {string} [width] - The width of the viewer.\n * @property {string} [containerClass] - The CSS class for the viewer container.\n * @property {boolean} [littlePlanet] - Enable or disable the little planet effect.\n * @property {boolean | number} [fishEye] - Enable or disable the fisheye effect, or set the fisheye level.\n * @property {boolean} [hideNavbarButton] - Show/hide the button that hides the navbar.\n * @property {Object} [lang] - Language configuration for the viewer. Each property is a string that represents the text for a specific action.\n * @property {Function} [onPositionChange] - Event handler for when the position changes. Receives the latitude, longitude, and the Viewer instance.\n * @property {Function} [onZoomChange] - Event handler for when the zoom level changes. Receives the ZoomUpdatedEvent and the Viewer instance.\n * @property {Function} [onClick] - Event handler for when the viewer is clicked. Receives the ClickEvent and the Viewer instance.\n * @property {Function} [onDblclick] - Event handler for when the viewer is double clicked. Receives the ClickEvent and the Viewer instance.\n * @property {Function} [onReady] - Event handler for when the viewer is ready. Receives the Viewer instance.\n */\nexport interface Props extends MakeOptional<ViewerConfig, \"container\"> {\n    src: string | CubeMapSrc | TilesAdapterSrc;\n    navbar?: boolean | string | Array<string | NavbarCustomButton>;\n    height: string;\n    width?: string;\n    containerClass?: string;\n    littlePlanet?: boolean;\n    fishEye?: boolean | number;\n    hideNavbarButton?: boolean;\n    lang?: Record<string, string>;\n    plugins?: PluginEntry[];\n    // Events\n    onPositionChange?(lat: number, lng: number, instance: Viewer): void;\n    onZoomChange?(data: events.ZoomUpdatedEvent & { type: \"zoom-updated\"; }, instance: Viewer): void;\n    onClick?(data: events.ClickEvent & { type: \"click\"; }, instance: Viewer): void;\n    onDblclick?(data: events.ClickEvent & { type: \"dblclick\"; }, instance: Viewer): void;\n    onReady?(instance: Viewer): void;\n}\n\nconst defaultNavbar = [\n    \"zoom\",\n    \"fullscreen\"\n]\n\nfunction adaptOptions(options: Props): ViewerConfig {\n    const adaptedOptions = { ...options }\n    for (const key in adaptedOptions) {\n        if (omittedProps.includes(key)) {\n            delete (adaptedOptions as Record<string, unknown>)[key]\n        }\n    }\n    return adaptedOptions as ViewerConfig\n}\n\nfunction map(_in: number, inMin: number, inMax: number, outMin: number, outMax: number): number {\n    return (_in - inMin) * (outMax - outMin) / (inMax - inMin) + outMin\n}\n\nfunction filterNavbar(navbar?: boolean | string | Array<string | NavbarCustomButton>): false | Array<string | NavbarCustomButton> {\n    if (navbar == null) return defaultNavbar\n    if (!Array.isArray(navbar)) {\n        if (typeof navbar === \"string\") {\n            return navbar === \"\" ? false : [navbar]\n        }\n        return navbar ? defaultNavbar : false\n    }\n    return navbar\n}\n\nfunction useDomElement(): [HTMLDivElement | undefined, (r: HTMLDivElement) => void] {\n    const [element, setElement] = useState<HTMLDivElement>()\n    const ref = useCallback(\n        (r: HTMLDivElement) => {\n            if (r && r !== element) {\n                setElement(r)\n            }\n        },\n        [element]\n    )\n    return [element, ref]\n}\n\n/**\n * Interface for the Viewer API.\n * \n * @interface\n * @property {Function} animate - Starts an animation. Receives an object of AnimateOptions.\n * @property {Function} destroy - Destroys the viewer.\n * @property {Function} createTooltip - Creates a tooltip. Receives a TooltipConfig object.\n * @property {Function} needsContinuousUpdate - Enables or disables continuous updates. Receives a boolean.\n * @property {Function} observeObjects - Starts observing objects. Receives a string key.\n * @property {Function} unobserveObjects - Stops observing objects. Receives a string key.\n * @property {Function} setCursor - Sets the cursor. Receives a string.\n * @property {Function} stopAnimation - Stops the current animation. Returns a Promise.\n * @property {Function} rotate - Rotates the viewer. Receives an ExtendedPosition object.\n * @property {Function} setOption - Sets a single option. Receives an option key and a value.\n * @property {Function} setOptions - Sets multiple options. Receives an object of options.\n * @property {Function} getCurrentNavbar - Returns the current navbar.\n * @property {Function} zoom - Sets the zoom level. Receives a number.\n * @property {Function} zoomIn - Increases the zoom level. Receives a number.\n * @property {Function} zoomOut - Decreases the zoom level. Receives a number.\n * @property {Function} resize - Resizes the viewer. Receives a CssSize object.\n * @property {Function} enterFullscreen - Enters fullscreen mode.\n * @property {Function} exitFullscreen - Exits fullscreen mode.\n * @property {Function} toggleFullscreen - Toggles fullscreen mode.\n * @property {Function} isFullscreenEnabled - Returns whether fullscreen is enabled.\n * @property {Function} getPlugin - Returns a plugin. Receives a plugin ID or a PluginConstructor.\n * @property {Function} getPosition - Returns the current position.\n * @property {Function} getZoomLevel - Returns the current zoom level.\n * @property {Function} getSize - Returns the current size.\n * @property {Function} needsUpdate - Updates the viewer.\n * @property {Function} autoSize - Sets the size to auto.\n * @property {Function} setPanorama - Sets the panorama. Receives a path and an optional PanoramaOptions object. Returns a Promise.\n * @property {Function} showError - Shows an error message. Receives a string.\n * @property {Function} hideError - Hides the error message.\n * @property {Function} startKeyboardControl - Starts keyboard control.\n * @property {Function} stopKeyboardControl - Stops keyboard control.\n */\nexport interface ViewerAPI {\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    animate(options: AnimateOptions): utils.Animation<any>;\n    destroy(): void;\n    createTooltip(config: TooltipConfig): Tooltip;\n    needsContinuousUpdate(enabled: boolean): void;\n    observeObjects(userDataKey: string): void;\n    unobserveObjects(userDataKey: string): void;\n    setCursor(cursor: string): void;\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    stopAnimation(): PromiseLike<any>;\n    rotate(position: ExtendedPosition): void;\n    setOption<T extends keyof UpdatableViewerConfig>(option: T, value: UpdatableViewerConfig[T]): void;\n    setOptions(options: Partial<UpdatableViewerConfig>): void;\n    getCurrentNavbar(): (string | object)[] | void;\n    zoom(value: number): void;\n    zoomIn(step: number): void;\n    zoomOut(step: number): void;\n    resize(size: CssSize): void;\n    enterFullscreen(): void;\n    exitFullscreen(): void;\n    toggleFullscreen(): void;\n    isFullscreenEnabled(): boolean | void;\n    /**\n     * Returns the instance of a plugin if it exists\n     * @example By plugin identifier\n     * ```js\n     * viewer.getPlugin('markers')\n     * ```\n     * @example By plugin class with TypeScript support\n     * ```ts\n     * viewer.getPlugin<MarkersPlugin>(MarkersPlugin)\n     * ```\n     */\n    getPlugin<T extends AbstractPlugin<never>>(pluginId: string | PluginConstructor): T;\n    getPosition(): Position; // Specify the return type\n    getZoomLevel(): number; // Specify the return type\n    getSize(): Size; // Specify the return type\n    needsUpdate(): void;\n    autoSize(): void;\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    setPanorama(path: any, options?: PanoramaOptions): Promise<boolean>;\n    showError(message: string): void;\n    hideError(): void;\n    startKeyboardControl(): void;\n    stopKeyboardControl(): void;\n}\n\nconst ReactPhotoSphereViewer = forwardRef<ViewerAPI, Props>((props, ref): React.ReactElement => {\n    const [sphereElement, setRef] = useDomElement()\n    const options = useMemo(\n        () => props,\n        [\n            // recreate options when individual props change\n            props.panorama,\n            props.src,\n            props.size,\n            props.canvasBackground,\n            props.navbar,\n            props.height,\n            props.width,\n            props.containerClass,\n            props.hideNavbarButton || true,\n            props.littlePlanet,\n            props.fishEye,\n            props.lang,\n            props.onPositionChange,\n            props.onZoomChange,\n            props.onClick,\n            props.onDblclick,\n            props.onReady,\n            props.moveSpeed,\n            props.zoomSpeed,\n            props.moveInertia,\n            props.mousewheel,\n            props.mousemove,\n            props.mousewheelCtrlKey,\n            props.touchmoveTwoFingers,\n            props.panoData,\n            props.requestHeaders,\n            props.withCredentials,\n            props.keyboard,\n            props.keyboardActions,\n            props.plugins,\n            props.adapter,\n            props.sphereCorrection,\n            props.minFov,\n            props.maxFov,\n            props.defaultZoomLvl,\n            props.defaultYaw,\n            props.defaultPitch,\n            props.caption,\n            props.description,\n            props.downloadUrl,\n            props.downloadName,\n            props.loadingImg,\n            props.loadingTxt,\n            props.rendererParameters,\n            props.defaultTransition,\n        ]\n    )\n\n\n    const spherePlayerInstance = useRef<Viewer | null>(null)\n    let LITTLEPLANET_MAX_ZOOM = 130\n    const [LITTLEPLANET_DEF_LAT] = useState(-90)\n    const [LITTLEPLANET_FISHEYE] = useState(2)\n    const [LITTLEPLANET_DEF_ZOOM] = useState(0)\n    const [currentNavbar, setCurrentNavbar] = useState<(string | object)[]>(defaultNavbar)\n    const littlePlanetEnabledRef = useRef(true)\n\n    useEffect(() => {\n        function handleResize() {\n            const aspectRatio = window.innerWidth / window.innerHeight\n            //console.log(aspectRatio)\n            LITTLEPLANET_MAX_ZOOM = Math.floor(map(aspectRatio, 0.5, 1.8, 140, 115))\n        }\n        // Add event listener\n        window.addEventListener(\"resize\", handleResize)\n\n        handleResize()\n        return () => window.removeEventListener(\"resize\", handleResize)\n    }, [])\n\n    useEffect(() => {\n        if (sphereElement && !spherePlayerInstance.current) {\n            const _c = new Viewer({\n                ...adaptOptions(options),\n                container: sphereElement,\n                panorama: options.panorama || options.src,\n                size: {\n                    height: options.height,\n                    width: options.width || \"100px\"\n                },\n                fisheye: options.littlePlanet ? LITTLEPLANET_FISHEYE : options.fisheye || false,\n                minFov: options.minFov ?? 30,\n                maxFov: options.littlePlanet ? LITTLEPLANET_MAX_ZOOM : options.maxFov ?? 90,\n                defaultZoomLvl: options.littlePlanet ? LITTLEPLANET_DEF_ZOOM : options.defaultZoomLvl ?? 50,\n                defaultYaw: options.defaultYaw ?? 0,\n                defaultPitch: options.littlePlanet ? LITTLEPLANET_DEF_LAT : options.defaultPitch ?? 0,\n                sphereCorrection: options.sphereCorrection || { pan: 0, tilt: 0, roll: 0 },\n                moveSpeed: options.moveSpeed || 1,\n                zoomSpeed: options.zoomSpeed || 1,\n                // when it undefined, = true, then use input value.\n                // The input value maybe false, value || true => false => true\n                moveInertia: options.moveInertia ?? true,\n                mousewheel: options.littlePlanet ? false : options.mousewheel ?? true,\n                mousemove: options.mousemove ?? true,\n                mousewheelCtrlKey: options.mousewheelCtrlKey || false,\n                touchmoveTwoFingers: options.touchmoveTwoFingers || false,\n                panoData: options.panoData || undefined,\n                requestHeaders: options.requestHeaders || undefined,\n                withCredentials: options.withCredentials || false,\n                navbar: filterNavbar(options.navbar),\n                lang: options.lang || {} as keyof Props[\"lang\"],\n                keyboard: options.keyboard || \"fullscreen\",\n                plugins: options.plugins ?? [],\n            })\n            _c.addEventListener(\"ready\", () => {\n                if (options.onReady) {\n                    options.onReady(_c)\n                }\n            }, { once: true })\n            _c.addEventListener(\"click\", (data: events.ClickEvent & { type: \"click\"; }) => {\n                if (options.onClick) {\n                    options.onClick(data, _c)\n                }\n                if (options.littlePlanet && littlePlanetEnabledRef.current) {\n                    littlePlanetEnabledRef.current = false\n                    // fly inside the sphere\n                    _c.animate({\n                        yaw: 0,\n                        pitch: LITTLEPLANET_DEF_LAT,\n                        zoom: 75,\n                        speed: \"3rpm\",\n                    }).then(() => {\n                        // watch on the sky\n                        _c.animate({\n                            yaw: 0,\n                            pitch: 0,\n                            zoom: 90,\n                            speed: \"10rpm\",\n                        }).then(() => {\n                            // Disable Little Planet.\n                            _c.setOption(\"maxFov\", options.maxFov || 70)\n                            _c.setOption(\"mousewheel\", options.mousewheel ?? true)\n                        })\n                    })\n                }\n            })\n            _c.addEventListener(\"dblclick\", (data: events.ClickEvent & { type: \"dblclick\"; }) => {\n                if (options.onDblclick) {\n                    options.onDblclick(data, _c)\n                }\n            })\n            _c.addEventListener(\"zoom-updated\", (zoom: events.ZoomUpdatedEvent & { type: \"zoom-updated\" }) => {\n                if (options.onZoomChange) {\n                    options.onZoomChange(zoom, _c)\n                }\n            })\n            _c.addEventListener(\"position-updated\", (position: events.PositionUpdatedEvent & { type: \"position-updated\"; }) => {\n                if (options.onPositionChange) {\n                    options.onPositionChange(position.position.pitch, position.position.yaw, _c)\n                }\n            })\n\n            const _currentNavbar = filterNavbar(options.navbar)\n            if (options.littlePlanet) {\n                const littlePlanetIcon = `<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24px\" height=\"24px\" viewBox=\"0 0 24 24\" fill=\"none\">\n                <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 20C16.4183 20 20 16.4183 20 12C20 11.8805 19.9974 11.7615 19.9922 11.6433C20.2479 11.4141 20.4882 11.1864 20.7118 10.9611C21.0037 10.6672 21.002 10.1923 20.708 9.90049C20.4336 9.628 20.0014 9.61143 19.7077 9.84972C19.4023 8.75248 18.8688 7.75024 18.1616 6.89725C18.4607 6.84611 18.7436 6.8084 19.0087 6.784C19.4212 6.74604 19.7247 6.38089 19.6868 5.96842C19.6488 5.55595 19.2837 5.25235 18.8712 5.29032C18.4474 5.32932 17.9972 5.39638 17.5262 5.48921C17.3267 5.52851 17.1614 5.64353 17.0543 5.79852C15.6765 4.67424 13.917 4 12 4C7.58172 4 4 7.58172 4 12C4 12.2776 4.01414 12.552 4.04175 12.8223C3.78987 12.7532 3.50899 12.8177 3.31137 13.0159C2.97651 13.3517 2.67596 13.6846 2.415 14.0113C2.15647 14.3349 2.20924 14.8069 2.53287 15.0654C2.8565 15.3239 3.32843 15.2711 3.58696 14.9475C3.78866 14.695 4.02466 14.4302 4.2938 14.1557C4.60754 15.2796 5.16056 16.3037 5.8945 17.1697C5.66824 17.3368 5.54578 17.6248 5.60398 17.919C5.68437 18.3253 6.07894 18.5896 6.48528 18.5092C6.7024 18.4662 6.92455 18.4177 7.15125 18.3637C8.49656 19.3903 10.1771 20 12 20ZM7.15125 18.3637C6.69042 18.012 6.26891 17.6114 5.8945 17.1697C5.98073 17.106 6.08204 17.0599 6.19417 17.0377C7.19089 16.8405 8.33112 16.5084 9.55581 16.0486C9.94359 15.903 10.376 16.0994 10.5216 16.4872C10.6671 16.8749 10.4708 17.3073 10.083 17.4529C9.05325 17.8395 8.0653 18.1459 7.15125 18.3637ZM19.7077 9.84972C19.6869 9.86663 19.6667 9.88483 19.6474 9.90431C18.9609 10.5957 18.0797 11.3337 17.0388 12.0753C16.7014 12.3157 16.6228 12.784 16.8631 13.1213C17.1035 13.4587 17.5718 13.5373 17.9091 13.297C18.6809 12.7471 19.3806 12.1912 19.9922 11.6433C19.965 11.0246 19.8676 10.4241 19.7077 9.84972ZM20.9366 5.37924C20.5336 5.28378 20.1294 5.53313 20.034 5.93619C19.9385 6.33925 20.1879 6.74339 20.5909 6.83886C20.985 6.93219 21.1368 7.07125 21.1932 7.16142C21.2565 7.26269 21.3262 7.52732 21.0363 8.10938C20.8516 8.48014 21.0025 8.93042 21.3732 9.1151C21.744 9.29979 22.1943 9.14894 22.379 8.77818C22.7566 8.02003 22.9422 7.12886 22.4648 6.36582C22.1206 5.81574 21.5416 5.52252 20.9366 5.37924ZM2.81481 16.2501C2.94057 15.8555 2.72259 15.4336 2.32793 15.3078C1.93327 15.1821 1.51138 15.4 1.38562 15.7947C1.19392 16.3963 1.17354 17.0573 1.53488 17.6349C1.98775 18.3587 2.84153 18.6413 3.68907 18.7224C4.1014 18.7619 4.46765 18.4596 4.50712 18.0473C4.54658 17.635 4.24432 17.2687 3.83199 17.2293C3.13763 17.1628 2.88355 16.9624 2.80651 16.8393C2.75679 16.7598 2.70479 16.5954 2.81481 16.2501ZM15.7504 14.704C16.106 14.4915 16.2218 14.0309 16.0093 13.6754C15.7967 13.3199 15.3362 13.204 14.9807 13.4166C14.4991 13.7045 13.9974 13.9881 13.4781 14.2648C12.9445 14.5491 12.4132 14.8149 11.8883 15.0615C11.5134 15.2376 11.3522 15.6843 11.5283 16.0592C11.7044 16.4341 12.1511 16.5953 12.526 16.4192C13.0739 16.1618 13.6277 15.8847 14.1834 15.5887C14.7242 15.3005 15.2474 15.0048 15.7504 14.704Z\" fill=\"rgba(255,255,255,.7)\"/>\n                </svg>`\n                const resetLittlePlanetButton = {\n                    id: \"resetLittlePlanetButton\",\n                    content: props.lang?.littlePlanetIcon || littlePlanetIcon,\n                    title: props.lang?.littlePlanetButton || \"Reset Little Planet\",\n                    className: \"resetLittlePlanetButton\",\n                    onClick: () => {\n                        littlePlanetEnabledRef.current = true\n                        _c.setOption(\"maxFov\", LITTLEPLANET_MAX_ZOOM)\n                        //_c.setOption(\"fisheye\", LITTLEPLANET_FISHEYE) // @ts-ignore ts(2345)\n                        _c.setOption(\"mousewheel\", false)\n                        _c.animate({\n                            yaw: 0,\n                            pitch: LITTLEPLANET_DEF_LAT,\n                            zoom: LITTLEPLANET_DEF_ZOOM,\n                            speed: \"10rpm\",\n                        })\n                    },\n                }\n                if (_currentNavbar !== false && !_currentNavbar.find((item) => typeof item === \"object\" && item?.id === \"resetLittlePlanetButton\")) {\n                    _currentNavbar.splice(1, 0, resetLittlePlanetButton)\n                }\n            }\n\n            if (options.hideNavbarButton) {\n                // add toggle navbar visibility button\n                const hideNavbarButton = {\n                    id: \"hideNavbarButton\",\n                    content: `<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24px\" height=\"24px\" viewBox=\"0 0 24 24\" fill=\"none\">\n                            <g clip-path=\"url(#clip0_429_11083)\">\n                            <path d=\"M7 7.00006L17 17.0001M7 17.0001L17 7.00006\" stroke=\"rgba(255,255,255,.7)\" stroke-width=\"2.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n                            </g>\n                            <defs>\n                            <clipPath id=\"clip0_429_11083\">\n                            <rect width=\"24\" height=\"24\" fill=\"white\"/>\n                            </clipPath>\n                            </defs>\n                            </svg>`,\n                    title: \"Hide Navbar\",\n                    className: \"hideNavbarButton\",\n                    onClick: () => {\n                        _c.navbar.hide()\n                        // add a show navbar button that is always hidden until mouseover\n                        const btn = document.createElement(\"a\")\n                        btn.className = \"showNavbarButton\"\n                        // add svg icon\n                        btn.innerHTML = `<svg version=\"1.1\" id=\"Layer_1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\" y=\"0px\" viewBox=\"0 0 26 26\" style=\"enable-background:new 0 0 26 26;\" xml:space=\"preserve\" class=\"icon icon-back-to-top\">\n                                        <g>\n                                        <path d=\"M13.8,1.3L21.6,9c0.1,0.1,0.1,0.3,0.2,0.4c0.1,0.1,0.1,0.3,0.1,0.4s0,0.3-0.1,0.4c-0.1,0.1-0.1,0.3-0.3,0.4\n                                            c-0.1,0.1-0.2,0.2-0.4,0.3c-0.2,0.1-0.3,0.1-0.4,0.1c-0.1,0-0.3,0-0.4-0.1c-0.2-0.1-0.3-0.2-0.4-0.3L14.2,5l0,19.1\n                                            c0,0.2-0.1,0.3-0.1,0.5c0,0.1-0.1,0.3-0.3,0.4c-0.1,0.1-0.2,0.2-0.4,0.3c-0.1,0.1-0.3,0.1-0.5,0.1c-0.1,0-0.3,0-0.4-0.1\n                                            c-0.1-0.1-0.3-0.1-0.4-0.3c-0.1-0.1-0.2-0.2-0.3-0.4c-0.1-0.1-0.1-0.3-0.1-0.5l0-19.1l-5.7,5.7C6,10.8,5.8,10.9,5.7,11\n                                            c-0.1,0.1-0.3,0.1-0.4,0.1c-0.2,0-0.3,0-0.4-0.1c-0.1-0.1-0.3-0.2-0.4-0.3c-0.1-0.1-0.1-0.2-0.2-0.4C4.1,10.2,4,10.1,4.1,9.9\n                                            c0-0.1,0-0.3,0.1-0.4c0-0.1,0.1-0.3,0.3-0.4l7.7-7.8c0.1,0,0.2-0.1,0.2-0.1c0,0,0.1-0.1,0.2-0.1c0.1,0,0.2,0,0.2-0.1\n                                            c0.1,0,0.1,0,0.2,0c0,0,0.1,0,0.2,0c0.1,0,0.2,0,0.2,0.1c0.1,0,0.1,0.1,0.2,0.1C13.7,1.2,13.8,1.2,13.8,1.3z\"></path>\n                                        </g>\n                                        </svg>`\n                        btn.title = \"Show Navbar\"\n                        btn.onclick = (e) => {\n                            e.preventDefault()\n                            _c.navbar.show()\n                            btn.remove()\n                        }\n\n                        // add the button to the viewer container\n                        document.body.appendChild(btn)\n                    },\n                }\n\n                if (_currentNavbar !== false && !_currentNavbar.find((item) => typeof item === \"object\" && item?.id === \"hideNavbarButton\")) {\n                    _currentNavbar.push(hideNavbarButton)\n                }\n            }\n\n            if (_currentNavbar !== false) {\n                _c.setOption(\"navbar\", _currentNavbar)\n                setCurrentNavbar(_currentNavbar as (string | object)[])\n            } else {\n                _c.navbar.hide()\n            }\n\n            /* @ts-ignore next line */\n            Emitter.on(\"animate\", (options: AnimateOptions) => {\n                _c.animate(options)\n            }).on(\"stop-animation\", () => {\n                _c.stopAnimation()\n            }).on(\"destroy\", () => {\n                _c.destroy()\n            }).on(\"rotate\", (options: ExtendedPosition) => {\n                _c.rotate(options)\n            }).on(\"setOption\", (pair: { option: keyof UpdatableViewerConfig, value: UpdatableViewerConfig[keyof UpdatableViewerConfig] }) => {\n                const { option, value } = pair\n                _c.setOption(option, value)\n            }).on(\"setOptions\", (options: Partial<UpdatableViewerConfig>) => {\n                _c.setOptions(options)\n            }).on(\"zoom\", (zoom: number) => {\n                _c.zoom(zoom)\n            }).on(\"zoomIn\", (step: number) => {\n                _c.zoomIn(step)\n            }).on(\"zoomOut\", (step: number) => {\n                _c.zoomOut(step)\n            }).on(\"resize\", (size: CssSize) => {\n                _c.resize(size)\n            }).on(\"enterFullscreen\", () => {\n                _c.enterFullscreen()\n            }).on(\"exitFullscreen\", () => {\n                _c.exitFullscreen()\n            }).on(\"toggleFullscreen\", () => {\n                _c.toggleFullscreen()\n            }).on(\"needsContinuousUpdate\", (enabled: boolean) => {\n                _c.needsContinuousUpdate(enabled)\n            }).on(\"observeObjects\", (userDataKey: string) => {\n                _c.observeObjects(userDataKey)\n            }).on(\"unobserveObjects\", (userDataKey: string) => {\n                _c.unobserveObjects(userDataKey)\n            }).on(\"setCursor\", (cursor: string) => {\n                _c.setCursor(cursor)\n            }).on(\"setPanorama\", (payload: { path: unknown, options?: PanoramaOptions }) => {\n                _c.setPanorama(payload.path, payload.options)\n            }).on(\"showError\", (message: string) => {\n                _c.showError(message)\n            }).on(\"hideError\", () => {\n                _c.hideError()\n            }).on(\"startKeyboardControl\", () => {\n                _c.startKeyboardControl()\n            }).on(\"stopKeyboardControl\", () => {\n                _c.stopKeyboardControl()\n            })\n\n            spherePlayerInstance.current = _c\n        }\n    }, [sphereElement, options])\n\n    useEffect(() => {\n        const viewer: Viewer | null = spherePlayerInstance.current\n        if (viewer && viewer.container && viewer.container.parentNode) {\n            if (viewer && viewer.container && viewer.container.parentNode) {\n                (viewer.renderer as unknown as { renderer?: { dispose: () => void } })?.renderer?.dispose()\n                ; (viewer.renderer as unknown as { renderer?: { forceContextLoss: () => void } })?.renderer?.forceContextLoss()\n                viewer.destroy()\n            }\n        }\n    }, [spherePlayerInstance])\n\n    useEffect(() => {\n        const panorama = options.panorama ?? options.src\n        if (spherePlayerInstance.current && panorama) {\n            spherePlayerInstance.current.setPanorama(panorama, {})\n        }\n    }, [options.src, options.panorama])\n\n    const _imperativeHandle = () => ({\n        animate(options: AnimateOptions) {\n            Emitter.emit(\"animate\", options)\n        },\n        destroy() {\n            Emitter.emit(\"destroy\", {})\n        },\n        createTooltip(config: TooltipConfig): Tooltip {\n            return spherePlayerInstance.current?.createTooltip(config) as Tooltip\n        },\n        needsContinuousUpdate(enabled: boolean) {\n            Emitter.emit(\"needsContinuousUpdate\", enabled)\n        },\n        observeObjects(userDataKey: string) {\n            Emitter.emit(\"observeObjects\", userDataKey)\n        },\n        unobserveObjects(userDataKey: string) {\n            Emitter.emit(\"unobserveObjects\", userDataKey)\n        },\n        setCursor(cursor: string) {\n            Emitter.emit(\"setCursor\", cursor)\n        },\n        stopAnimation() {\n            Emitter.emit(\"stop-animation\", {})\n        },\n        rotate(position: ExtendedPosition) {\n            Emitter.emit(\"rotate\", position)\n        },\n        setOption<T extends keyof UpdatableViewerConfig>(option: T, value: UpdatableViewerConfig[T]) {\n            Emitter.emit(\"setOption\", { option, value })\n        },\n        setOptions(options: Partial<UpdatableViewerConfig>): void {\n            return spherePlayerInstance.current?.setOptions(options)\n        },\n        getCurrentNavbar(): (string | object)[] {\n            return currentNavbar\n        },\n        zoom(value: number) {\n            Emitter.emit(\"zoom\", value)\n        },\n        zoomIn(step: number) {\n            Emitter.emit(\"zoomIn\", { step })\n        },\n        zoomOut(step: number) {\n            Emitter.emit(\"zoomOut\", { step })\n        },\n        resize(size: CssSize) {\n            return spherePlayerInstance.current?.resize(size)\n        },\n        enterFullscreen() {\n            return spherePlayerInstance.current?.enterFullscreen()\n        },\n        exitFullscreen() {\n            return spherePlayerInstance.current?.exitFullscreen()\n        },\n        toggleFullscreen() {\n            return spherePlayerInstance.current?.toggleFullscreen()\n        },\n        isFullscreenEnabled() {\n            return spherePlayerInstance.current?.isFullscreenEnabled()\n        },\n        getPlugin<T extends AbstractPlugin<never>>(pluginId: string | PluginConstructor): T {\n            return spherePlayerInstance.current?.getPlugin(pluginId) as T\n        },\n        getPosition(): Position {\n            return spherePlayerInstance.current?.getPosition() as Position\n        },\n        getZoomLevel(): number {\n            return spherePlayerInstance.current?.getZoomLevel() as number\n        },\n        getSize(): Size {\n            return spherePlayerInstance.current?.getSize() as Size\n        },\n        needsUpdate() {\n            return spherePlayerInstance.current?.needsUpdate()\n        },\n        autoSize() {\n            return spherePlayerInstance.current?.autoSize()\n        },\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n        setPanorama(path: any, options?: PanoramaOptions): Promise<boolean> {\n            return spherePlayerInstance.current?.setPanorama(path, options) as Promise<boolean>\n        },\n        showError(message: string) {\n            return spherePlayerInstance.current?.showError(message)\n        },\n        hideError() {\n            return spherePlayerInstance.current?.hideError()\n        },\n        startKeyboardControl() {\n            return spherePlayerInstance.current?.startKeyboardControl()\n        },\n        stopKeyboardControl() {\n            return spherePlayerInstance.current?.stopKeyboardControl()\n        },\n    }) as ViewerAPI\n    // Methods\n    useImperativeHandle(ref, _imperativeHandle, [\n        spherePlayerInstance.current,\n        sphereElement,\n        options,\n        ref,\n    ])\n\n    return <div className={options.containerClass || \"view-container\"} ref={setRef} />\n})\n\nReactPhotoSphereViewer.displayName = \"ReactPhotoSphereViewer\"\n\nexport {\n    ReactPhotoSphereViewer\n}\n"], "names": ["eventEmitter", "EventEmitter", "Emitter", "on", "event", "fn", "once", "off", "emit", "payload", "Object", "freeze", "omittedProps", "defaultNavbar", "adaptOptions", "options", "adaptedOptions", "_objectSpread", "key", "includes", "map", "_in", "inMin", "inMax", "outMin", "outMax", "filterNavbar", "navbar", "Array", "isArray", "useDomElement", "_useState", "useState", "_useState2", "_slicedToArray", "element", "setElement", "ref", "useCallback", "r", "ReactPhotoSphereViewer", "forwardRef", "props", "_useDomElement", "_useDomElement2", "sphereElement", "setRef", "useMemo", "panorama", "src", "size", "canvasBackground", "height", "width", "containerClass", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "little<PERSON><PERSON><PERSON>", "fishEye", "lang", "onPositionChange", "onZoomChange", "onClick", "onDblclick", "onReady", "moveSpeed", "zoomSpeed", "moveInertia", "mousewheel", "mousemove", "mousewheelCtrlKey", "touchmoveTwoFingers", "panoData", "requestHeaders", "withCredentials", "keyboard", "keyboardActions", "plugins", "adapter", "sphereCorrection", "<PERSON><PERSON><PERSON>", "max<PERSON>ov", "defaultZoomLvl", "defaultYaw", "defaultPitch", "caption", "description", "downloadUrl", "downloadName", "loadingImg", "loadingTxt", "rendererParameters", "defaultTransition", "spherePlayerInstance", "useRef", "LITTLEPLANET_MAX_ZOOM", "_useState3", "_useState4", "LITTLEPLANET_DEF_LAT", "_useState5", "_useState6", "LITTLEPLANET_FISHEYE", "_useState7", "_useState8", "LITTLEPLANET_DEF_ZOOM", "_useState9", "_useState10", "currentNavbar", "setCurrentNavbar", "littlePlanetEnabledRef", "useEffect", "handleResize", "aspectRatio", "window", "innerWidth", "innerHeight", "Math", "floor", "addEventListener", "removeEventListener", "current", "_options$minFov", "_options$maxFov", "_options$defaultZoomL", "_options$defaultYaw", "_options$defaultPitch", "_options$moveInertia", "_options$mousewheel", "_options$mousemove", "_options$plugins", "_c", "Viewer", "container", "fisheye", "pan", "tilt", "roll", "undefined", "data", "animate", "yaw", "pitch", "zoom", "speed", "then", "_options$mousewheel2", "setOption", "position", "_currentNavbar", "_props$lang", "_props$lang2", "littlePlanetIcon", "resetLittlePlanetButton", "id", "content", "title", "littlePlanetButton", "className", "find", "item", "_typeof", "splice", "hide", "btn", "document", "createElement", "innerHTML", "onclick", "e", "preventDefault", "show", "remove", "body", "append<PERSON><PERSON><PERSON>", "push", "stopAnimation", "destroy", "rotate", "pair", "option", "value", "setOptions", "step", "zoomIn", "zoomOut", "resize", "enterFullscreen", "exitFullscreen", "toggleFullscreen", "enabled", "needsContinuousUpdate", "userDataKey", "observeObjects", "unobserveObjects", "cursor", "setCursor", "setPanorama", "path", "message", "showError", "hideError", "startKeyboardControl", "stopKeyboardControl", "viewer", "parentNode", "_viewer$renderer", "_viewer$renderer$rend", "_viewer$renderer2", "_viewer$renderer2$ren", "renderer", "dispose", "forceContextLoss", "_options$panorama", "_imperativeHandle", "createTooltip", "config", "_spherePlayerInstance", "_spherePlayerInstance2", "getCurrentNavbar", "_spherePlayerInstance3", "_spherePlayerInstance4", "_spherePlayerInstance5", "_spherePlayerInstance6", "isFullscreenEnabled", "_spherePlayerInstance7", "getPlugin", "pluginId", "_spherePlayerInstance8", "getPosition", "_spherePlayerInstance9", "getZoomLevel", "_spherePlayerInstance10", "getSize", "_spherePlayerInstance11", "needsUpdate", "_spherePlayerInstance12", "autoSize", "_spherePlayerInstance13", "_spherePlayerInstance14", "_spherePlayerInstance15", "_spherePlayerInstance16", "_spherePlayerInstance17", "_spherePlayerInstance18", "useImperativeHandle", "React", "displayName"], "mappings": ";;;;;;;;;;;;AA0BA,IAAMA,YAAY,GAAG,IAAIC,+MAAY,EAAE;AACvC,IAAMC,OAAO,GAAG;IACZC,EAAE,EAAE,SAAAA,EAACC,CAAAA,KAAa,EAAEC,EAAgC,EAAA;QAAA,OAAKL,YAAY,CAACG,EAAE,CAACC,KAAK,EAAEC,EAAE,CAAC;IAAA,CAAA;IACnFC,IAAI,EAAE,SAAAA,IAACF,CAAAA,KAAa,EAAEC,EAAgC,EAAA;QAAA,OAAKL,YAAY,CAACM,IAAI,CAACF,KAAK,EAAEC,EAAE,CAAC;IAAA,CAAA;IACvFE,GAAG,EAAE,SAAAA,GAACH,CAAAA,KAAa,EAAEC,EAAgC,EAAA;QAAA,OAAKL,YAAY,CAACO,GAAG,CAACH,KAAK,EAAEC,EAAE,CAAC;IAAA,CAAA;IACrFG,IAAI,EAAE,SAAAA,IAACJ,CAAAA,KAAa,EAAEK,OAAgB,EAAA;QAAA,OAAKT,YAAY,CAACQ,IAAI,CAACJ,KAAK,EAAEK,OAAO,CAAC;IAAA;AAChF,CAAC;AAEDC,MAAM,CAACC,MAAM,CAACT,OAAO,CAAC;AAEtB,IAAMU,YAAY,GAAG;IACjB,KAAK;IACL,QAAQ;IACR,OAAO;IACP,kBAAkB;IAClB,gBAAgB;IAChB,cAAc;IACd,kBAAkB;IAClB,cAAc;IACd,SAAS;IACT,YAAY;IACZ,SAAS;CACZ;AA0BD;;;;;;;;;;;;;;;;;;CAkBA,GAoBA,IAAMC,aAAa,GAAG;IAClB,MAAM;IACN,YAAY;CACf;AAED,SAASC,YAAYA,CAACC,OAAc,EAAgB;IAChD,IAAMC,cAAc,2MAAAC,gBAAAA,AAAA,EAAA,CAAA,CAAA,EAAQF,OAAO,CAAE;IACrC,IAAK,IAAMG,GAAG,IAAIF,cAAc,CAAE;QAC9B,IAAIJ,YAAY,CAACO,QAAQ,CAACD,GAAG,CAAC,EAAE;YAC5B,OAAQF,cAAc,CAA6BE,GAAG,CAAC;QAC3D;IACJ;IACA,OAAOF,cAAc;AACzB;AAEA,SAASI,GAAGA,CAACC,GAAW,EAAEC,KAAa,EAAEC,KAAa,EAAEC,MAAc,EAAEC,MAAc,EAAU;IAC5F,OAAO,CAACJ,GAAG,GAAGC,KAAK,IAAA,CAAKG,MAAM,GAAGD,MAAM,CAAC,GAAA,CAAID,KAAK,GAAGD,KAAK,CAAC,GAAGE,MAAM;AACvE;AAEA,SAASE,YAAYA,CAACC,MAA8D,EAA8C;IAC9H,IAAIA,MAAM,IAAI,IAAI,EAAE,OAAOd,aAAa;IACxC,IAAI,CAACe,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;QACxB,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;YAC5B,OAAOA,MAAM,KAAK,EAAE,GAAG,KAAK,GAAG;gBAACA,MAAM;aAAC;QAC3C;QACA,OAAOA,MAAM,GAAGd,aAAa,GAAG,KAAK;IACzC;IACA,OAAOc,MAAM;AACjB;AAEA,SAASG,aAAaA,GAA8D;IAChF,IAAAC,SAAA,6MAA8BC,WAAAA,AAAQ,EAAkB,GAAAC,UAAA,2MAAAC,gBAAAA,AAAA,EAAAH,SAAA,EAAA,CAAA,CAAA,EAAjDI,OAAO,GAAAF,UAAA,CAAA,CAAA,CAAA,EAAEG,UAAU,GAAAH,UAAA,CAAA,CAAA,CAAA;IAC1B,IAAMI,GAAG,6MAAGC,cAAAA,AAAW,EACnB,SAACC,CAAiB,EAAK;QACnB,IAAIA,CAAC,IAAIA,CAAC,KAAKJ,OAAO,EAAE;YACpBC,UAAU,CAACG,CAAC,CAAC;QACjB;IACJ,CAAC,EACD;QAACJ,OAAO;KACZ,CAAC;IACD,OAAO;QAACA,OAAO;QAAEE,GAAG;KAAC;AACzB;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAmCA,GAiDMG,IAAAA,sBAAsB,GAAA,WAAA,6MAAGC,aAAAA,AAAU,EAAmB,SAACC,KAAK,EAAEL,GAAG,EAAyB;IAC5F,IAAAM,cAAA,GAAgCb,aAAa,EAAE,EAAAc,eAAA,2MAAAV,gBAAAA,AAAA,EAAAS,cAAA,EAAA,CAAA,CAAA,EAAxCE,aAAa,GAAAD,eAAA,CAAA,CAAA,CAAA,EAAEE,MAAM,GAAAF,eAAA,CAAA,CAAA,CAAA;IAC5B,IAAM7B,OAAO,6MAAGgC,UAAAA,AAAO,EACnB,YAAA;QAAA,OAAML,KAAK;KACX,EAAA;QACI,gDAAA;QACAA,KAAK,CAACM,QAAQ;QACdN,KAAK,CAACO,GAAG;QACTP,KAAK,CAACQ,IAAI;QACVR,KAAK,CAACS,gBAAgB;QACtBT,KAAK,CAACf,MAAM;QACZe,KAAK,CAACU,MAAM;QACZV,KAAK,CAACW,KAAK;QACXX,KAAK,CAACY,cAAc;QACpBZ,KAAK,CAACa,gBAAgB,IAAI,IAAI;QAC9Bb,KAAK,CAACc,YAAY;QAClBd,KAAK,CAACe,OAAO;QACbf,KAAK,CAACgB,IAAI;QACVhB,KAAK,CAACiB,gBAAgB;QACtBjB,KAAK,CAACkB,YAAY;QAClBlB,KAAK,CAACmB,OAAO;QACbnB,KAAK,CAACoB,UAAU;QAChBpB,KAAK,CAACqB,OAAO;QACbrB,KAAK,CAACsB,SAAS;QACftB,KAAK,CAACuB,SAAS;QACfvB,KAAK,CAACwB,WAAW;QACjBxB,KAAK,CAACyB,UAAU;QAChBzB,KAAK,CAAC0B,SAAS;QACf1B,KAAK,CAAC2B,iBAAiB;QACvB3B,KAAK,CAAC4B,mBAAmB;QACzB5B,KAAK,CAAC6B,QAAQ;QACd7B,KAAK,CAAC8B,cAAc;QACpB9B,KAAK,CAAC+B,eAAe;QACrB/B,KAAK,CAACgC,QAAQ;QACdhC,KAAK,CAACiC,eAAe;QACrBjC,KAAK,CAACkC,OAAO;QACblC,KAAK,CAACmC,OAAO;QACbnC,KAAK,CAACoC,gBAAgB;QACtBpC,KAAK,CAACqC,MAAM;QACZrC,KAAK,CAACsC,MAAM;QACZtC,KAAK,CAACuC,cAAc;QACpBvC,KAAK,CAACwC,UAAU;QAChBxC,KAAK,CAACyC,YAAY;QAClBzC,KAAK,CAAC0C,OAAO;QACb1C,KAAK,CAAC2C,WAAW;QACjB3C,KAAK,CAAC4C,WAAW;QACjB5C,KAAK,CAAC6C,YAAY;QAClB7C,KAAK,CAAC8C,UAAU;QAChB9C,KAAK,CAAC+C,UAAU;QAChB/C,KAAK,CAACgD,kBAAkB;QACxBhD,KAAK,CAACiD,iBAAiB;KAE/B,CAAC;IAGD,IAAMC,oBAAoB,6MAAGC,SAAAA,AAAM,EAAgB,IAAI,CAAC;IACxD,IAAIC,qBAAqB,GAAG,GAAG;IAC/B,IAAAC,UAAA,6MAA+B/D,WAAAA,AAAQ,EAAC,CAAA,EAAG,CAAC,EAAAgE,UAAA,2MAAA9D,gBAAAA,AAAA,EAAA6D,UAAA,EAAA,CAAA,CAAA,EAArCE,oBAAoB,GAAAD,UAAA,CAAA,CAAA,CAAA;IAC3B,IAAAE,UAAA,6MAA+BlE,WAAAA,AAAQ,EAAC,CAAC,CAAC,EAAAmE,UAAA,GAAAjE,wNAAAA,AAAA,EAAAgE,UAAA,EAAA,CAAA,CAAA,EAAnCE,oBAAoB,GAAAD,UAAA,CAAA,CAAA,CAAA;IAC3B,IAAAE,UAAA,6MAAgCrE,WAAAA,AAAQ,EAAC,CAAC,CAAC,EAAAsE,UAAA,0MAAApE,iBAAAA,AAAA,EAAAmE,UAAA,EAAA,CAAA,CAAA,EAApCE,qBAAqB,GAAAD,UAAA,CAAA,CAAA,CAAA;IAC5B,IAAAE,UAAA,6MAA0CxE,WAAAA,AAAQ,EAAsBnB,aAAa,CAAC,EAAA4F,WAAA,2MAAAvE,gBAAAA,AAAA,EAAAsE,UAAA,EAAA,CAAA,CAAA,EAA/EE,aAAa,GAAAD,WAAA,CAAA,CAAA,CAAA,EAAEE,gBAAgB,GAAAF,WAAA,CAAA,CAAA,CAAA;IACtC,IAAMG,sBAAsB,4MAAGf,UAAAA,AAAM,EAAC,IAAI,CAAC;8MAE3CgB,YAAAA,AAAS,EAAC,YAAM;QACZ,SAASC,YAAYA,GAAG;YACpB,IAAMC,WAAW,GAAGC,MAAM,CAACC,UAAU,GAAGD,MAAM,CAACE,WAAW;YAC1D,0BAAA;YACApB,qBAAqB,GAAGqB,IAAI,CAACC,KAAK,CAAChG,GAAG,CAAC2F,WAAW,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAC5E;QACA,qBAAA;QACAC,MAAM,CAACK,gBAAgB,CAAC,QAAQ,EAAEP,YAAY,CAAC;QAE/CA,YAAY,EAAE;QACd,OAAO,YAAA;YAAA,OAAME,MAAM,CAACM,mBAAmB,CAAC,QAAQ,EAAER,YAAY,CAAC;QAAA,CAAA;KAClE,EAAE,EAAE,CAAC;IAEND,sNAAAA,AAAS,EAAC,YAAM;QACZ,IAAIhE,aAAa,IAAI,CAAC+C,oBAAoB,CAAC2B,OAAO,EAAE;YAAA,IAAAC,eAAA,EAAAC,eAAA,EAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,mBAAA,EAAAC,kBAAA,EAAAC,gBAAA;YAChD,IAAMC,EAAE,GAAG,2KAAIC,SAAM,yMAAAjH,gBAAAA,AAAA,yMAAAA,iBAAAA,AAAA,EACdH,CAAAA,CAAAA,EAAAA,YAAY,CAACC,OAAO,CAAC,CAAA,EAAA,CAAA,CAAA,EAAA;gBACxBoH,SAAS,EAAEtF,aAAa;gBACxBG,QAAQ,EAAEjC,OAAO,CAACiC,QAAQ,IAAIjC,OAAO,CAACkC,GAAG;gBACzCC,IAAI,EAAE;oBACFE,MAAM,EAAErC,OAAO,CAACqC,MAAM;oBACtBC,KAAK,EAAEtC,OAAO,CAACsC,KAAK,IAAI;iBAC3B;gBACD+E,OAAO,EAAErH,OAAO,CAACyC,YAAY,GAAG4C,oBAAoB,GAAGrF,OAAO,CAACqH,OAAO,IAAI,KAAK;gBAC/ErD,MAAM,EAAA,CAAAyC,eAAA,GAAEzG,OAAO,CAACgE,MAAM,MAAA,IAAA,IAAAyC,eAAA,KAAA,KAAA,CAAA,GAAAA,eAAA,GAAI,EAAE;gBAC5BxC,MAAM,EAAEjE,OAAO,CAACyC,YAAY,GAAGsC,qBAAqB,GAAA2B,CAAAA,eAAA,GAAG1G,OAAO,CAACiE,MAAM,MAAA,IAAA,IAAAyC,eAAA,KAAAA,KAAAA,CAAAA,GAAAA,eAAA,GAAI,EAAE;gBAC3ExC,cAAc,EAAElE,OAAO,CAACyC,YAAY,GAAG+C,qBAAqB,GAAAmB,CAAAA,qBAAA,GAAG3G,OAAO,CAACkE,cAAc,MAAA,IAAA,IAAAyC,qBAAA,KAAAA,KAAAA,CAAAA,GAAAA,qBAAA,GAAI,EAAE;gBAC3FxC,UAAU,EAAA,CAAAyC,mBAAA,GAAE5G,OAAO,CAACmE,UAAU,MAAA,IAAA,IAAAyC,mBAAA,KAAA,KAAA,CAAA,GAAAA,mBAAA,GAAI,CAAC;gBACnCxC,YAAY,EAAEpE,OAAO,CAACyC,YAAY,GAAGyC,oBAAoB,GAAA2B,CAAAA,qBAAA,GAAG7G,OAAO,CAACoE,YAAY,MAAA,IAAA,IAAAyC,qBAAA,KAAAA,KAAAA,CAAAA,GAAAA,qBAAA,GAAI,CAAC;gBACrF9C,gBAAgB,EAAE/D,OAAO,CAAC+D,gBAAgB,IAAI;oBAAEuD,GAAG,EAAE,CAAC;oBAAEC,IAAI,EAAE,CAAC;oBAAEC,IAAI,EAAE;iBAAG;gBAC1EvE,SAAS,EAAEjD,OAAO,CAACiD,SAAS,IAAI,CAAC;gBACjCC,SAAS,EAAElD,OAAO,CAACkD,SAAS,IAAI,CAAC;gBACjC,mDAAA;gBACA,8DAAA;gBACAC,WAAW,EAAA,CAAA2D,oBAAA,GAAE9G,OAAO,CAACmD,WAAW,MAAA,IAAA,IAAA2D,oBAAA,KAAA,KAAA,CAAA,GAAAA,oBAAA,GAAI,IAAI;gBACxC1D,UAAU,EAAEpD,OAAO,CAACyC,YAAY,GAAG,KAAK,GAAAsE,CAAAA,mBAAA,GAAG/G,OAAO,CAACoD,UAAU,MAAA,IAAA,IAAA2D,mBAAA,KAAAA,KAAAA,CAAAA,GAAAA,mBAAA,GAAI,IAAI;gBACrE1D,SAAS,EAAA,CAAA2D,kBAAA,GAAEhH,OAAO,CAACqD,SAAS,MAAA,IAAA,IAAA2D,kBAAA,KAAA,KAAA,CAAA,GAAAA,kBAAA,GAAI,IAAI;gBACpC1D,iBAAiB,EAAEtD,OAAO,CAACsD,iBAAiB,IAAI,KAAK;gBACrDC,mBAAmB,EAAEvD,OAAO,CAACuD,mBAAmB,IAAI,KAAK;gBACzDC,QAAQ,EAAExD,OAAO,CAACwD,QAAQ,IAAIiE,SAAS;gBACvChE,cAAc,EAAEzD,OAAO,CAACyD,cAAc,IAAIgE,SAAS;gBACnD/D,eAAe,EAAE1D,OAAO,CAAC0D,eAAe,IAAI,KAAK;gBACjD9C,MAAM,EAAED,YAAY,CAACX,OAAO,CAACY,MAAM,CAAC;gBACpC+B,IAAI,EAAE3C,OAAO,CAAC2C,IAAI,IAAI,CAAA,CAAyB;gBAC/CgB,QAAQ,EAAE3D,OAAO,CAAC2D,QAAQ,IAAI,YAAY;gBAC1CE,OAAO,EAAA,CAAAoD,gBAAA,GAAEjH,OAAO,CAAC6D,OAAO,MAAAoD,IAAAA,IAAAA,gBAAA,KAAAA,KAAAA,CAAAA,GAAAA,gBAAA,GAAI,EAAA;YAAE,CAAA,CACjC,CAAC;YACFC,EAAE,CAACZ,gBAAgB,CAAC,OAAO,EAAE,YAAM;gBAC/B,IAAItG,OAAO,CAACgD,OAAO,EAAE;oBACjBhD,OAAO,CAACgD,OAAO,CAACkE,EAAE,CAAC;gBACvB;YACJ,CAAC,EAAE;gBAAE3H,IAAI,EAAE;YAAK,CAAC,CAAC;YAClB2H,EAAE,CAACZ,gBAAgB,CAAC,OAAO,EAAE,SAACoB,IAA4C,EAAK;gBAC3E,IAAI1H,OAAO,CAAC8C,OAAO,EAAE;oBACjB9C,OAAO,CAAC8C,OAAO,CAAC4E,IAAI,EAAER,EAAE,CAAC;gBAC7B;gBACA,IAAIlH,OAAO,CAACyC,YAAY,IAAIoD,sBAAsB,CAACW,OAAO,EAAE;oBACxDX,sBAAsB,CAACW,OAAO,GAAG,KAAK;oBACtC,wBAAA;oBACAU,EAAE,CAACS,OAAO,CAAC;wBACPC,GAAG,EAAE,CAAC;wBACNC,KAAK,EAAE3C,oBAAoB;wBAC3B4C,IAAI,EAAE,EAAE;wBACRC,KAAK,EAAE;oBACX,CAAC,CAAC,CAACC,IAAI,CAAC,YAAM;wBACV,mBAAA;wBACAd,EAAE,CAACS,OAAO,CAAC;4BACPC,GAAG,EAAE,CAAC;4BACNC,KAAK,EAAE,CAAC;4BACRC,IAAI,EAAE,EAAE;4BACRC,KAAK,EAAE;wBACX,CAAC,CAAC,CAACC,IAAI,CAAC,YAAM;4BAAA,IAAAC,oBAAA;4BACV,yBAAA;4BACAf,EAAE,CAACgB,SAAS,CAAC,QAAQ,EAAElI,OAAO,CAACiE,MAAM,IAAI,EAAE,CAAC;4BAC5CiD,EAAE,CAACgB,SAAS,CAAC,YAAY,EAAA,CAAAD,oBAAA,GAAEjI,OAAO,CAACoD,UAAU,MAAA,QAAA6E,oBAAA,KAAA,KAAA,CAAA,GAAAA,oBAAA,GAAI,IAAI,CAAC;wBAC1D,CAAC,CAAC;oBACN,CAAC,CAAC;gBACN;YACJ,CAAC,CAAC;YACFf,EAAE,CAACZ,gBAAgB,CAAC,UAAU,EAAE,SAACoB,IAA+C,EAAK;gBACjF,IAAI1H,OAAO,CAAC+C,UAAU,EAAE;oBACpB/C,OAAO,CAAC+C,UAAU,CAAC2E,IAAI,EAAER,EAAE,CAAC;gBAChC;YACJ,CAAC,CAAC;YACFA,EAAE,CAACZ,gBAAgB,CAAC,cAAc,EAAE,SAACwB,IAAwD,EAAK;gBAC9F,IAAI9H,OAAO,CAAC6C,YAAY,EAAE;oBACtB7C,OAAO,CAAC6C,YAAY,CAACiF,IAAI,EAAEZ,EAAE,CAAC;gBAClC;YACJ,CAAC,CAAC;YACFA,EAAE,CAACZ,gBAAgB,CAAC,kBAAkB,EAAE,SAAC6B,QAAqE,EAAK;gBAC/G,IAAInI,OAAO,CAAC4C,gBAAgB,EAAE;oBAC1B5C,OAAO,CAAC4C,gBAAgB,CAACuF,QAAQ,CAACA,QAAQ,CAACN,KAAK,EAAEM,QAAQ,CAACA,QAAQ,CAACP,GAAG,EAAEV,EAAE,CAAC;gBAChF;YACJ,CAAC,CAAC;YAEF,IAAMkB,cAAc,GAAGzH,YAAY,CAACX,OAAO,CAACY,MAAM,CAAC;YACnD,IAAIZ,OAAO,CAACyC,YAAY,EAAE;gBAAA,IAAA4F,WAAA,EAAAC,YAAA;gBACtB,IAAMC,gBAAgB,GAEf,6gGAAA;gBACP,IAAMC,uBAAuB,GAAG;oBAC5BC,EAAE,EAAE,yBAAyB;oBAC7BC,OAAO,EAAE,CAAA,CAAAL,WAAA,GAAA1G,KAAK,CAACgB,IAAI,MAAA0F,IAAAA,IAAAA,WAAA,KAAVA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,WAAA,CAAYE,gBAAgB,KAAIA,gBAAgB;oBACzDI,KAAK,EAAE,CAAA,CAAAL,YAAA,GAAA3G,KAAK,CAACgB,IAAI,MAAA2F,IAAAA,IAAAA,YAAA,KAAVA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,YAAA,CAAYM,kBAAkB,KAAI,qBAAqB;oBAC9DC,SAAS,EAAE,yBAAyB;oBACpC/F,OAAO,EAAE,SAAAA,OAAAA,GAAM;wBACX+C,sBAAsB,CAACW,OAAO,GAAG,IAAI;wBACrCU,EAAE,CAACgB,SAAS,CAAC,QAAQ,EAAEnD,qBAAqB,CAAC;wBAC7C,sEAAA;wBACAmC,EAAE,CAACgB,SAAS,CAAC,YAAY,EAAE,KAAK,CAAC;wBACjChB,EAAE,CAACS,OAAO,CAAC;4BACPC,GAAG,EAAE,CAAC;4BACNC,KAAK,EAAE3C,oBAAoB;4BAC3B4C,IAAI,EAAEtC,qBAAqB;4BAC3BuC,KAAK,EAAE;wBACX,CAAC,CAAC;oBACN;iBACH;gBACD,IAAIK,cAAc,KAAK,KAAK,IAAI,CAACA,cAAc,CAACU,IAAI,CAAC,SAACC,IAAI,EAAA;oBAAA,WAAKC,6MAAAA,AAAA,EAAOD,IAAI,CAAA,KAAK,QAAQ,IAAI,CAAAA,IAAI,KAAA,IAAA,IAAJA,IAAI,KAAJA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEN,EAAE,MAAK,yBAAyB;gBAAA,CAAA,CAAC,EAAE;oBAChIL,cAAc,CAACa,MAAM,CAAC,CAAC,EAAE,CAAC,EAAET,uBAAuB,CAAC;gBACxD;YACJ;YAEA,IAAIxI,OAAO,CAACwC,gBAAgB,EAAE;gBAC1B,sCAAA;gBACA,IAAMA,gBAAgB,GAAG;oBACrBiG,EAAE,EAAE,kBAAkB;oBACtBC,OAAO,EASQ,wrBAAA;oBACfC,KAAK,EAAE,aAAa;oBACpBE,SAAS,EAAE,kBAAkB;oBAC7B/F,OAAO,EAAE,SAAAA,OAAAA,GAAM;wBACXoE,EAAE,CAACtG,MAAM,CAACsI,IAAI,EAAE;wBAChB,iEAAA;wBACA,IAAMC,GAAG,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;wBACvCF,GAAG,CAACN,SAAS,GAAG,kBAAkB;wBAClC,eAAA;wBACAM,GAAG,CAACG,SAAS,GAUU,2+CAAA;wBACvBH,GAAG,CAACR,KAAK,GAAG,aAAa;wBACzBQ,GAAG,CAACI,OAAO,GAAG,SAACC,CAAC,EAAK;4BACjBA,CAAC,CAACC,cAAc,EAAE;4BAClBvC,EAAE,CAACtG,MAAM,CAAC8I,IAAI,EAAE;4BAChBP,GAAG,CAACQ,MAAM,EAAE;yBACf;wBAED,yCAAA;wBACAP,QAAQ,CAACQ,IAAI,CAACC,WAAW,CAACV,GAAG,CAAC;oBAClC;iBACH;gBAED,IAAIf,cAAc,KAAK,KAAK,IAAI,CAACA,cAAc,CAACU,IAAI,CAAC,SAACC,IAAI,EAAA;oBAAA,+MAAKC,SAAAA,AAAA,EAAOD,IAAI,CAAA,KAAK,QAAQ,IAAI,CAAAA,IAAI,KAAA,IAAA,IAAJA,IAAI,KAAJA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,IAAI,CAAEN,EAAE,MAAK,kBAAkB;gBAAA,CAAA,CAAC,EAAE;oBACzHL,cAAc,CAAC0B,IAAI,CAACtH,gBAAgB,CAAC;gBACzC;YACJ;YAEA,IAAI4F,cAAc,KAAK,KAAK,EAAE;gBAC1BlB,EAAE,CAACgB,SAAS,CAAC,QAAQ,EAAEE,cAAc,CAAC;gBACtCxC,gBAAgB,CAACwC,cAAqC,CAAC;YAC3D,CAAC,MAAM;gBACHlB,EAAE,CAACtG,MAAM,CAACsI,IAAI,EAAE;YACpB;YAEA,wBAAA,GACA/J,OAAO,CAACC,EAAE,CAAC,SAAS,EAAE,SAACY,OAAuB,EAAK;gBAC/CkH,EAAE,CAACS,OAAO,CAAC3H,OAAO,CAAC;YACvB,CAAC,CAAC,CAACZ,EAAE,CAAC,gBAAgB,EAAE,YAAM;gBAC1B8H,EAAE,CAAC6C,aAAa,EAAE;YACtB,CAAC,CAAC,CAAC3K,EAAE,CAAC,SAAS,EAAE,YAAM;gBACnB8H,EAAE,CAAC8C,OAAO,EAAE;aACf,CAAC,CAAC5K,EAAE,CAAC,QAAQ,EAAE,SAACY,OAAyB,EAAK;gBAC3CkH,EAAE,CAAC+C,MAAM,CAACjK,OAAO,CAAC;aACrB,CAAC,CAACZ,EAAE,CAAC,WAAW,EAAE,SAAC8K,IAAwG,EAAK;gBAC7H,IAAQC,MAAM,GAAYD,IAAI,CAAtBC,MAAM,EAAEC,KAAK,GAAKF,IAAI,CAAdE,KAAK;gBACrBlD,EAAE,CAACgB,SAAS,CAACiC,MAAM,EAAEC,KAAK,CAAC;aAC9B,CAAC,CAAChL,EAAE,CAAC,YAAY,EAAE,SAACY,OAAuC,EAAK;gBAC7DkH,EAAE,CAACmD,UAAU,CAACrK,OAAO,CAAC;aACzB,CAAC,CAACZ,EAAE,CAAC,MAAM,EAAE,SAAC0I,IAAY,EAAK;gBAC5BZ,EAAE,CAACY,IAAI,CAACA,IAAI,CAAC;aAChB,CAAC,CAAC1I,EAAE,CAAC,QAAQ,EAAE,SAACkL,IAAY,EAAK;gBAC9BpD,EAAE,CAACqD,MAAM,CAACD,IAAI,CAAC;aAClB,CAAC,CAAClL,EAAE,CAAC,SAAS,EAAE,SAACkL,IAAY,EAAK;gBAC/BpD,EAAE,CAACsD,OAAO,CAACF,IAAI,CAAC;aACnB,CAAC,CAAClL,EAAE,CAAC,QAAQ,EAAE,SAAC+C,IAAa,EAAK;gBAC/B+E,EAAE,CAACuD,MAAM,CAACtI,IAAI,CAAC;YACnB,CAAC,CAAC,CAAC/C,EAAE,CAAC,iBAAiB,EAAE,YAAM;gBAC3B8H,EAAE,CAACwD,eAAe,EAAE;YACxB,CAAC,CAAC,CAACtL,EAAE,CAAC,gBAAgB,EAAE,YAAM;gBAC1B8H,EAAE,CAACyD,cAAc,EAAE;YACvB,CAAC,CAAC,CAACvL,EAAE,CAAC,kBAAkB,EAAE,YAAM;gBAC5B8H,EAAE,CAAC0D,gBAAgB,EAAE;aACxB,CAAC,CAACxL,EAAE,CAAC,uBAAuB,EAAE,SAACyL,OAAgB,EAAK;gBACjD3D,EAAE,CAAC4D,qBAAqB,CAACD,OAAO,CAAC;aACpC,CAAC,CAACzL,EAAE,CAAC,gBAAgB,EAAE,SAAC2L,WAAmB,EAAK;gBAC7C7D,EAAE,CAAC8D,cAAc,CAACD,WAAW,CAAC;aACjC,CAAC,CAAC3L,EAAE,CAAC,kBAAkB,EAAE,SAAC2L,WAAmB,EAAK;gBAC/C7D,EAAE,CAAC+D,gBAAgB,CAACF,WAAW,CAAC;aACnC,CAAC,CAAC3L,EAAE,CAAC,WAAW,EAAE,SAAC8L,MAAc,EAAK;gBACnChE,EAAE,CAACiE,SAAS,CAACD,MAAM,CAAC;aACvB,CAAC,CAAC9L,EAAE,CAAC,aAAa,EAAE,SAACM,OAAqD,EAAK;gBAC5EwH,EAAE,CAACkE,WAAW,CAAC1L,OAAO,CAAC2L,IAAI,EAAE3L,OAAO,CAACM,OAAO,CAAC;aAChD,CAAC,CAACZ,EAAE,CAAC,WAAW,EAAE,SAACkM,OAAe,EAAK;gBACpCpE,EAAE,CAACqE,SAAS,CAACD,OAAO,CAAC;YACzB,CAAC,CAAC,CAAClM,EAAE,CAAC,WAAW,EAAE,YAAM;gBACrB8H,EAAE,CAACsE,SAAS,EAAE;YAClB,CAAC,CAAC,CAACpM,EAAE,CAAC,sBAAsB,EAAE,YAAM;gBAChC8H,EAAE,CAACuE,oBAAoB,EAAE;YAC7B,CAAC,CAAC,CAACrM,EAAE,CAAC,qBAAqB,EAAE,YAAM;gBAC/B8H,EAAE,CAACwE,mBAAmB,EAAE;YAC5B,CAAC,CAAC;YAEF7G,oBAAoB,CAAC2B,OAAO,GAAGU,EAAE;QACrC;IACJ,CAAC,EAAE;QAACpF,aAAa;QAAE9B,OAAO;KAAC,CAAC;8MAE5B8F,YAAAA,AAAS,EAAC,YAAM;QACZ,IAAM6F,MAAqB,GAAG9G,oBAAoB,CAAC2B,OAAO;QAC1D,IAAImF,MAAM,IAAIA,MAAM,CAACvE,SAAS,IAAIuE,MAAM,CAACvE,SAAS,CAACwE,UAAU,EAAE;YAC3D,IAAID,MAAM,IAAIA,MAAM,CAACvE,SAAS,IAAIuE,MAAM,CAACvE,SAAS,CAACwE,UAAU,EAAE;gBAAA,IAAAC,gBAAA,EAAAC,qBAAA,EAAAC,iBAAA,EAAAC,qBAAA;gBAC3D,CAAAH,gBAAA,GAACF,MAAM,CAACM,QAAQ,MAAAJ,IAAAA,IAAAA,gBAAA,KAAA,KAAA,IAAA,KAAA,IAAA,CAAAC,qBAAA,GAAhBD,gBAAA,CAAwEI,QAAQ,MAAA,QAAAH,qBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAhFA,qBAAA,CAAkFI,OAAO,EAAE;gBACzF,CAAAH,iBAAA,GAACJ,MAAM,CAACM,QAAQ,MAAAF,IAAAA,IAAAA,iBAAA,KAAA,KAAA,IAAA,KAAA,IAAA,CAAAC,qBAAA,GAAhBD,iBAAA,CAAiFE,QAAQ,MAAA,QAAAD,qBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAAzFA,qBAAA,CAA2FG,gBAAgB,EAAE;gBAC/GR,MAAM,CAAC3B,OAAO,EAAE;YACpB;QACJ;IACJ,CAAC,EAAE;QAACnF,oBAAoB;KAAC,CAAC;8MAE1BiB,YAAAA,AAAS,EAAC,YAAM;QAAA,IAAAsG,iBAAA;QACZ,IAAMnK,QAAQ,GAAA,CAAAmK,iBAAA,GAAGpM,OAAO,CAACiC,QAAQ,MAAAmK,IAAAA,IAAAA,iBAAA,KAAAA,KAAAA,CAAAA,GAAAA,iBAAA,GAAIpM,OAAO,CAACkC,GAAG;QAChD,IAAI2C,oBAAoB,CAAC2B,OAAO,IAAIvE,QAAQ,EAAE;YAC1C4C,oBAAoB,CAAC2B,OAAO,CAAC4E,WAAW,CAACnJ,QAAQ,EAAE,CAAA,CAAE,CAAC;QAC1D;KACH,EAAE;QAACjC,OAAO,CAACkC,GAAG;QAAElC,OAAO,CAACiC,QAAQ;KAAC,CAAC;IAEnC,IAAMoK,iBAAiB,GAAG,SAApBA,iBAAiBA,GAAA;QAAA,OAAU;YAC7B1E,OAAO,EAAA,SAAAA,OAAC3H,CAAAA,OAAuB,EAAE;gBAC7Bb,OAAO,CAACM,IAAI,CAAC,SAAS,EAAEO,OAAO,CAAC;aACnC;YACDgK,OAAO,EAAA,SAAAA,UAAG;gBACN7K,OAAO,CAACM,IAAI,CAAC,SAAS,EAAE,CAAA,CAAE,CAAC;aAC9B;YACD6M,aAAa,EAAA,SAAAA,aAACC,CAAAA,MAAqB,EAAW;gBAAA,IAAAC,qBAAA;gBAC1C,OAAA,CAAAA,qBAAA,GAAO3H,oBAAoB,CAAC2B,OAAO,MAAA,IAAA,IAAAgG,qBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAA5BA,qBAAA,CAA8BF,aAAa,CAACC,MAAM,CAAC;aAC7D;YACDzB,qBAAqB,EAAA,SAAAA,qBAACD,CAAAA,OAAgB,EAAE;gBACpC1L,OAAO,CAACM,IAAI,CAAC,uBAAuB,EAAEoL,OAAO,CAAC;aACjD;YACDG,cAAc,EAAA,SAAAA,cAACD,CAAAA,WAAmB,EAAE;gBAChC5L,OAAO,CAACM,IAAI,CAAC,gBAAgB,EAAEsL,WAAW,CAAC;aAC9C;YACDE,gBAAgB,EAAA,SAAAA,gBAACF,CAAAA,WAAmB,EAAE;gBAClC5L,OAAO,CAACM,IAAI,CAAC,kBAAkB,EAAEsL,WAAW,CAAC;aAChD;YACDI,SAAS,EAAA,SAAAA,SAACD,CAAAA,MAAc,EAAE;gBACtB/L,OAAO,CAACM,IAAI,CAAC,WAAW,EAAEyL,MAAM,CAAC;aACpC;YACDnB,aAAa,EAAA,SAAAA,gBAAG;gBACZ5K,OAAO,CAACM,IAAI,CAAC,gBAAgB,EAAE,CAAA,CAAE,CAAC;aACrC;YACDwK,MAAM,EAAA,SAAAA,MAAC9B,CAAAA,QAA0B,EAAE;gBAC/BhJ,OAAO,CAACM,IAAI,CAAC,QAAQ,EAAE0I,QAAQ,CAAC;aACnC;YACDD,SAAS,EAAAA,SAAAA,SAAAA,CAAwCiC,MAAS,EAAEC,KAA+B,EAAE;gBACzFjL,OAAO,CAACM,IAAI,CAAC,WAAW,EAAE;oBAAE0K,MAAM,EAANA,MAAM;oBAAEC,KAAK,EAALA;gBAAM,CAAC,CAAC;aAC/C;YACDC,UAAU,EAAA,SAAAA,UAACrK,CAAAA,OAAuC,EAAQ;gBAAA,IAAAyM,sBAAA;gBACtD,OAAA,CAAAA,sBAAA,GAAO5H,oBAAoB,CAAC2B,OAAO,MAAA,IAAA,IAAAiG,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAA5BA,sBAAA,CAA8BpC,UAAU,CAACrK,OAAO,CAAC;aAC3D;YACD0M,gBAAgB,EAAA,SAAAA,mBAAwB;gBACpC,OAAO/G,aAAa;aACvB;YACDmC,IAAI,EAAA,SAAAA,IAACsC,CAAAA,KAAa,EAAE;gBAChBjL,OAAO,CAACM,IAAI,CAAC,MAAM,EAAE2K,KAAK,CAAC;aAC9B;YACDG,MAAM,EAAA,SAAAA,MAACD,CAAAA,IAAY,EAAE;gBACjBnL,OAAO,CAACM,IAAI,CAAC,QAAQ,EAAE;oBAAE6K,IAAI,EAAJA;gBAAK,CAAC,CAAC;aACnC;YACDE,OAAO,EAAA,SAAAA,OAACF,CAAAA,IAAY,EAAE;gBAClBnL,OAAO,CAACM,IAAI,CAAC,SAAS,EAAE;oBAAE6K,IAAI,EAAJA;gBAAK,CAAC,CAAC;aACpC;YACDG,MAAM,EAAA,SAAAA,MAACtI,CAAAA,IAAa,EAAE;gBAAA,IAAAwK,sBAAA;gBAClB,OAAA,CAAAA,sBAAA,GAAO9H,oBAAoB,CAAC2B,OAAO,MAAA,IAAA,IAAAmG,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAA5BA,sBAAA,CAA8BlC,MAAM,CAACtI,IAAI,CAAC;aACpD;YACDuI,eAAe,EAAA,SAAAA,kBAAG;gBAAA,IAAAkC,sBAAA;gBACd,OAAA,CAAAA,sBAAA,GAAO/H,oBAAoB,CAAC2B,OAAO,MAAA,IAAA,IAAAoG,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAA5BA,sBAAA,CAA8BlC,eAAe,EAAE;aACzD;YACDC,cAAc,EAAA,SAAAA,iBAAG;gBAAA,IAAAkC,sBAAA;gBACb,OAAA,CAAAA,sBAAA,GAAOhI,oBAAoB,CAAC2B,OAAO,MAAA,IAAA,IAAAqG,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAA5BA,sBAAA,CAA8BlC,cAAc,EAAE;aACxD;YACDC,gBAAgB,EAAA,SAAAA,mBAAG;gBAAA,IAAAkC,sBAAA;gBACf,OAAA,CAAAA,sBAAA,GAAOjI,oBAAoB,CAAC2B,OAAO,MAAA,IAAA,IAAAsG,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAA5BA,sBAAA,CAA8BlC,gBAAgB,EAAE;aAC1D;YACDmC,mBAAmB,EAAA,SAAAA,sBAAG;gBAAA,IAAAC,sBAAA;gBAClB,OAAA,CAAAA,sBAAA,GAAOnI,oBAAoB,CAAC2B,OAAO,MAAA,IAAA,IAAAwG,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAA5BA,sBAAA,CAA8BD,mBAAmB,EAAE;aAC7D;YACDE,SAAS,EAAA,SAAAA,SAAkCC,CAAAA,QAAoC,EAAK;gBAAA,IAAAC,sBAAA;gBAChF,OAAA,CAAAA,sBAAA,GAAOtI,oBAAoB,CAAC2B,OAAO,MAAA,IAAA,IAAA2G,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAA5BA,sBAAA,CAA8BF,SAAS,CAACC,QAAQ,CAAC;aAC3D;YACDE,WAAW,EAAA,SAAAA,cAAa;gBAAA,IAAAC,sBAAA;gBACpB,OAAA,CAAAA,sBAAA,GAAOxI,oBAAoB,CAAC2B,OAAO,MAAA,IAAA,IAAA6G,sBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAA5BA,sBAAA,CAA8BD,WAAW,EAAE;aACrD;YACDE,YAAY,EAAA,SAAAA,eAAW;gBAAA,IAAAC,uBAAA;gBACnB,OAAA,CAAAA,uBAAA,GAAO1I,oBAAoB,CAAC2B,OAAO,MAAA,IAAA,IAAA+G,uBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAA5BA,uBAAA,CAA8BD,YAAY,EAAE;aACtD;YACDE,OAAO,EAAA,SAAAA,UAAS;gBAAA,IAAAC,uBAAA;gBACZ,OAAA,CAAAA,uBAAA,GAAO5I,oBAAoB,CAAC2B,OAAO,MAAA,IAAA,IAAAiH,uBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAA5BA,uBAAA,CAA8BD,OAAO,EAAE;aACjD;YACDE,WAAW,EAAA,SAAAA,cAAG;gBAAA,IAAAC,uBAAA;gBACV,OAAA,CAAAA,uBAAA,GAAO9I,oBAAoB,CAAC2B,OAAO,MAAA,IAAA,IAAAmH,uBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAA5BA,uBAAA,CAA8BD,WAAW,EAAE;aACrD;YACDE,QAAQ,EAAA,SAAAA,WAAG;gBAAA,IAAAC,uBAAA;gBACP,OAAA,CAAAA,uBAAA,GAAOhJ,oBAAoB,CAAC2B,OAAO,MAAA,IAAA,IAAAqH,uBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAA5BA,uBAAA,CAA8BD,QAAQ,EAAE;aAClD;YACD,8DAAA;YACAxC,WAAW,EAAAA,SAAAA,WAAAA,CAACC,IAAS,EAAErL,OAAyB,EAAoB;gBAAA,IAAA8N,uBAAA;gBAChE,OAAA,CAAAA,uBAAA,GAAOjJ,oBAAoB,CAAC2B,OAAO,MAAAsH,IAAAA,IAAAA,uBAAA,KAA5BA,KAAAA,CAAAA,GAAAA,KAAAA,CAAAA,GAAAA,uBAAA,CAA8B1C,WAAW,CAACC,IAAI,EAAErL,OAAO,CAAC;aAClE;YACDuL,SAAS,EAAA,SAAAA,SAACD,CAAAA,OAAe,EAAE;gBAAA,IAAAyC,uBAAA;gBACvB,OAAA,CAAAA,uBAAA,GAAOlJ,oBAAoB,CAAC2B,OAAO,MAAA,IAAA,IAAAuH,uBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAA5BA,uBAAA,CAA8BxC,SAAS,CAACD,OAAO,CAAC;aAC1D;YACDE,SAAS,EAAA,SAAAA,YAAG;gBAAA,IAAAwC,uBAAA;gBACR,OAAA,CAAAA,uBAAA,GAAOnJ,oBAAoB,CAAC2B,OAAO,MAAA,IAAA,IAAAwH,uBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAA5BA,uBAAA,CAA8BxC,SAAS,EAAE;aACnD;YACDC,oBAAoB,EAAA,SAAAA,uBAAG;gBAAA,IAAAwC,uBAAA;gBACnB,OAAA,CAAAA,uBAAA,GAAOpJ,oBAAoB,CAAC2B,OAAO,MAAA,IAAA,IAAAyH,uBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAA5BA,uBAAA,CAA8BxC,oBAAoB,EAAE;aAC9D;YACDC,mBAAmB,EAAA,SAAAA,sBAAG;gBAAA,IAAAwC,uBAAA;gBAClB,OAAA,CAAAA,uBAAA,GAAOrJ,oBAAoB,CAAC2B,OAAO,MAAA,IAAA,IAAA0H,uBAAA,KAAA,KAAA,CAAA,GAAA,KAAA,CAAA,GAA5BA,uBAAA,CAA8BxC,mBAAmB,EAAE;YAC9D;SACH;KAAc;IACf,UAAA;KACAyC,+NAAAA,AAAmB,EAAC7M,GAAG,EAAE+K,iBAAiB,EAAE;QACxCxH,oBAAoB,CAAC2B,OAAO;QAC5B1E,aAAa;QACb9B,OAAO;QACPsB,GAAG;KACN,CAAC;IAEF,OAAA,WAAA,yMAAO8M,UAAA,CAAA/E,aAAA,CAAA,KAAA,EAAA;QAAKR,SAAS,EAAE7I,OAAO,CAACuC,cAAc,IAAI,gBAAiB;QAACjB,GAAG,EAAES;IAAO,CAAE,CAAC;AACtF,CAAC;AAEDN,sBAAsB,CAAC4M,WAAW,GAAG,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1668, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/react-photo-sphere-viewer/node_modules/eventemitter3/index.js"], "sourcesContent": ["'use strict';\n\nvar has = Object.prototype.hasOwnProperty\n  , prefix = '~';\n\n/**\n * Constructor to create a storage for our `EE` objects.\n * An `Events` instance is a plain object whose properties are event names.\n *\n * @constructor\n * @private\n */\nfunction Events() {}\n\n//\n// We try to not inherit from `Object.prototype`. In some engines creating an\n// instance in this way is faster than calling `Object.create(null)` directly.\n// If `Object.create(null)` is not supported we prefix the event names with a\n// character to make sure that the built-in object properties are not\n// overridden or used as an attack vector.\n//\nif (Object.create) {\n  Events.prototype = Object.create(null);\n\n  //\n  // This hack is needed because the `__proto__` property is still inherited in\n  // some old browsers like Android 4, iPhone 5.1, Opera 11 and Safari 5.\n  //\n  if (!new Events().__proto__) prefix = false;\n}\n\n/**\n * Representation of a single event listener.\n *\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} [once=false] Specify if the listener is a one-time listener.\n * @constructor\n * @private\n */\nfunction EE(fn, context, once) {\n  this.fn = fn;\n  this.context = context;\n  this.once = once || false;\n}\n\n/**\n * Add a listener for a given event.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} context The context to invoke the listener with.\n * @param {Boolean} once Specify if the listener is a one-time listener.\n * @returns {EventEmitter}\n * @private\n */\nfunction addListener(emitter, event, fn, context, once) {\n  if (typeof fn !== 'function') {\n    throw new TypeError('The listener must be a function');\n  }\n\n  var listener = new EE(fn, context || emitter, once)\n    , evt = prefix ? prefix + event : event;\n\n  if (!emitter._events[evt]) emitter._events[evt] = listener, emitter._eventsCount++;\n  else if (!emitter._events[evt].fn) emitter._events[evt].push(listener);\n  else emitter._events[evt] = [emitter._events[evt], listener];\n\n  return emitter;\n}\n\n/**\n * Clear event by name.\n *\n * @param {EventEmitter} emitter Reference to the `EventEmitter` instance.\n * @param {(String|Symbol)} evt The Event name.\n * @private\n */\nfunction clearEvent(emitter, evt) {\n  if (--emitter._eventsCount === 0) emitter._events = new Events();\n  else delete emitter._events[evt];\n}\n\n/**\n * Minimal `EventEmitter` interface that is molded against the Node.js\n * `EventEmitter` interface.\n *\n * @constructor\n * @public\n */\nfunction EventEmitter() {\n  this._events = new Events();\n  this._eventsCount = 0;\n}\n\n/**\n * Return an array listing the events for which the emitter has registered\n * listeners.\n *\n * @returns {Array}\n * @public\n */\nEventEmitter.prototype.eventNames = function eventNames() {\n  var names = []\n    , events\n    , name;\n\n  if (this._eventsCount === 0) return names;\n\n  for (name in (events = this._events)) {\n    if (has.call(events, name)) names.push(prefix ? name.slice(1) : name);\n  }\n\n  if (Object.getOwnPropertySymbols) {\n    return names.concat(Object.getOwnPropertySymbols(events));\n  }\n\n  return names;\n};\n\n/**\n * Return the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Array} The registered listeners.\n * @public\n */\nEventEmitter.prototype.listeners = function listeners(event) {\n  var evt = prefix ? prefix + event : event\n    , handlers = this._events[evt];\n\n  if (!handlers) return [];\n  if (handlers.fn) return [handlers.fn];\n\n  for (var i = 0, l = handlers.length, ee = new Array(l); i < l; i++) {\n    ee[i] = handlers[i].fn;\n  }\n\n  return ee;\n};\n\n/**\n * Return the number of listeners listening to a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Number} The number of listeners.\n * @public\n */\nEventEmitter.prototype.listenerCount = function listenerCount(event) {\n  var evt = prefix ? prefix + event : event\n    , listeners = this._events[evt];\n\n  if (!listeners) return 0;\n  if (listeners.fn) return 1;\n  return listeners.length;\n};\n\n/**\n * Calls each of the listeners registered for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @returns {Boolean} `true` if the event had listeners, else `false`.\n * @public\n */\nEventEmitter.prototype.emit = function emit(event, a1, a2, a3, a4, a5) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return false;\n\n  var listeners = this._events[evt]\n    , len = arguments.length\n    , args\n    , i;\n\n  if (listeners.fn) {\n    if (listeners.once) this.removeListener(event, listeners.fn, undefined, true);\n\n    switch (len) {\n      case 1: return listeners.fn.call(listeners.context), true;\n      case 2: return listeners.fn.call(listeners.context, a1), true;\n      case 3: return listeners.fn.call(listeners.context, a1, a2), true;\n      case 4: return listeners.fn.call(listeners.context, a1, a2, a3), true;\n      case 5: return listeners.fn.call(listeners.context, a1, a2, a3, a4), true;\n      case 6: return listeners.fn.call(listeners.context, a1, a2, a3, a4, a5), true;\n    }\n\n    for (i = 1, args = new Array(len -1); i < len; i++) {\n      args[i - 1] = arguments[i];\n    }\n\n    listeners.fn.apply(listeners.context, args);\n  } else {\n    var length = listeners.length\n      , j;\n\n    for (i = 0; i < length; i++) {\n      if (listeners[i].once) this.removeListener(event, listeners[i].fn, undefined, true);\n\n      switch (len) {\n        case 1: listeners[i].fn.call(listeners[i].context); break;\n        case 2: listeners[i].fn.call(listeners[i].context, a1); break;\n        case 3: listeners[i].fn.call(listeners[i].context, a1, a2); break;\n        case 4: listeners[i].fn.call(listeners[i].context, a1, a2, a3); break;\n        default:\n          if (!args) for (j = 1, args = new Array(len -1); j < len; j++) {\n            args[j - 1] = arguments[j];\n          }\n\n          listeners[i].fn.apply(listeners[i].context, args);\n      }\n    }\n  }\n\n  return true;\n};\n\n/**\n * Add a listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.on = function on(event, fn, context) {\n  return addListener(this, event, fn, context, false);\n};\n\n/**\n * Add a one-time listener for a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn The listener function.\n * @param {*} [context=this] The context to invoke the listener with.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.once = function once(event, fn, context) {\n  return addListener(this, event, fn, context, true);\n};\n\n/**\n * Remove the listeners of a given event.\n *\n * @param {(String|Symbol)} event The event name.\n * @param {Function} fn Only remove the listeners that match this function.\n * @param {*} context Only remove the listeners that have this context.\n * @param {Boolean} once Only remove one-time listeners.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeListener = function removeListener(event, fn, context, once) {\n  var evt = prefix ? prefix + event : event;\n\n  if (!this._events[evt]) return this;\n  if (!fn) {\n    clearEvent(this, evt);\n    return this;\n  }\n\n  var listeners = this._events[evt];\n\n  if (listeners.fn) {\n    if (\n      listeners.fn === fn &&\n      (!once || listeners.once) &&\n      (!context || listeners.context === context)\n    ) {\n      clearEvent(this, evt);\n    }\n  } else {\n    for (var i = 0, events = [], length = listeners.length; i < length; i++) {\n      if (\n        listeners[i].fn !== fn ||\n        (once && !listeners[i].once) ||\n        (context && listeners[i].context !== context)\n      ) {\n        events.push(listeners[i]);\n      }\n    }\n\n    //\n    // Reset the array, or remove it completely if we have no more listeners.\n    //\n    if (events.length) this._events[evt] = events.length === 1 ? events[0] : events;\n    else clearEvent(this, evt);\n  }\n\n  return this;\n};\n\n/**\n * Remove all listeners, or those of the specified event.\n *\n * @param {(String|Symbol)} [event] The event name.\n * @returns {EventEmitter} `this`.\n * @public\n */\nEventEmitter.prototype.removeAllListeners = function removeAllListeners(event) {\n  var evt;\n\n  if (event) {\n    evt = prefix ? prefix + event : event;\n    if (this._events[evt]) clearEvent(this, evt);\n  } else {\n    this._events = new Events();\n    this._eventsCount = 0;\n  }\n\n  return this;\n};\n\n//\n// Alias methods names because people roll like that.\n//\nEventEmitter.prototype.off = EventEmitter.prototype.removeListener;\nEventEmitter.prototype.addListener = EventEmitter.prototype.on;\n\n//\n// Expose the prefix.\n//\nEventEmitter.prefixed = prefix;\n\n//\n// Allow `EventEmitter` to be imported as module namespace.\n//\nEventEmitter.EventEmitter = EventEmitter;\n\n//\n// Expose the module.\n//\nif ('undefined' !== typeof module) {\n  module.exports = EventEmitter;\n}\n"], "names": [], "mappings": ";;;;;;;;IAEA,IAAI,GAAG,GAAG,MAAM,CAAC,SAAS,CAAC,cAAA,EACvB,MAAM,GAAG,GAAG;IAEhB;;;;;;EAMA,GACA,SAAS,MAAM,GAAG,CAAA;IAElB,EAAA;IACA,6EAAA;IACA,8EAAA;IACA,6EAAA;IACA,qEAAA;IACA,0CAAA;IACA,EAAA;IACA,IAAI,MAAM,CAAC,MAAM,EAAE;QACjB,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;QAExC,EAAA;QACA,6EAAA;QACA,uEAAA;QACA,EAAA;QACE,IAAI,CAAC,IAAI,MAAM,EAAE,CAAC,SAAS,EAAE,MAAM,GAAG,KAAK;;IAG7C;;;;;;;;EAQA,GACA,SAAS,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;QAC7B,IAAI,CAAC,EAAE,GAAG,EAAE;QACZ,IAAI,CAAC,OAAO,GAAG,OAAO;QACtB,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,KAAK;;IAG3B;;;;;;;;;;EAUA,GACA,SAAS,WAAW,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;QACtD,IAAI,OAAO,EAAE,KAAK,UAAU,EAAE;YAC5B,MAAM,IAAI,SAAS,CAAC,iCAAiC,CAAC;;QAGxD,IAAI,QAAQ,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,OAAO,IAAI,OAAO,EAAE,IAAI,GAC9C,GAAG,GAAG,MAAM,GAAG,MAAM,GAAG,KAAK,GAAG,KAAK;QAEzC,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,QAAQ,EAAE,OAAO,CAAC,YAAY,EAAE;aAC7E,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC;aACjE,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG;YAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC;YAAE,QAAQ;SAAC;QAE5D,OAAO,OAAO;;IAGhB;;;;;;EAMA,GACA,SAAS,UAAU,CAAC,OAAO,EAAE,GAAG,EAAE;QAChC,IAAI,EAAE,OAAO,CAAC,YAAY,KAAK,CAAC,EAAE,OAAO,CAAC,OAAO,GAAG,IAAI,MAAM,EAAE;aAC3D,OAAO,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC;;IAGlC;;;;;;EAMA,GACA,SAAS,YAAY,GAAG;QACtB,IAAI,CAAC,OAAO,GAAG,IAAI,MAAM,EAAE;QAC3B,IAAI,CAAC,YAAY,GAAG,CAAC;;IAGvB;;;;;;EAMA,GACA,YAAY,CAAC,SAAS,CAAC,UAAU,GAAG,SAAS,UAAU,GAAG;QACxD,IAAI,KAAK,GAAG,EAAA,EACR,QACA,IAAI;QAER,IAAI,IAAI,CAAC,YAAY,KAAK,CAAC,EAAE,OAAO,KAAK;QAEzC,IAAK,IAAI,IAAK,MAAM,GAAG,IAAI,CAAC,OAAO,CAAG;YACpC,IAAI,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;;QAGvE,IAAI,MAAM,CAAC,qBAAqB,EAAE;YAChC,OAAO,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;;QAG3D,OAAO,KAAK;IACd,CAAC;IAED;;;;;;EAMA,GACA,YAAY,CAAC,SAAS,CAAC,SAAS,GAAG,SAAS,SAAS,CAAC,KAAK,EAAE;QAC3D,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM,GAAG,KAAK,GAAG,OAChC,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAEhC,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE;QACxB,IAAI,QAAQ,CAAC,EAAE,EAAE,OAAO;YAAC,QAAQ,CAAC,EAAE;SAAC;QAErC,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,EAAE,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,CAAE;YAClE,EAAE,CAAC,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE;;QAGxB,OAAO,EAAE;IACX,CAAC;IAED;;;;;;EAMA,GACA,YAAY,CAAC,SAAS,CAAC,aAAa,GAAG,SAAS,aAAa,CAAC,KAAK,EAAE;QACnE,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM,GAAG,KAAK,GAAG,OAChC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAEjC,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC;QACxB,IAAI,SAAS,CAAC,EAAE,EAAE,OAAO,CAAC;QAC1B,OAAO,SAAS,CAAC,MAAM;IACzB,CAAC;IAED;;;;;;EAMA,GACA,YAAY,CAAC,SAAS,CAAC,IAAI,GAAG,SAAS,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QACrE,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM,GAAG,KAAK,GAAG,KAAK;QAEzC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,OAAO,KAAK;QAEpC,IAAI,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAA,EAC5B,GAAG,GAAG,SAAS,CAAC,MAAA,EAChB,MACA,CAAC;QAEL,IAAI,SAAS,CAAC,EAAE,EAAE;YAChB,IAAI,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC;YAE7E,OAAQ,GAAG;gBACT,KAAK,CAAC;oBAAE,OAAO,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,EAAE,IAAI;gBACzD,KAAK,CAAC;oBAAE,OAAO,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,CAAC,EAAE,IAAI;gBAC7D,KAAK,CAAC;oBAAE,OAAO,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI;gBACjE,KAAK,CAAC;oBAAE,OAAO,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI;gBACrE,KAAK,CAAC;oBAAE,OAAO,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI;gBACzE,KAAK,CAAC;oBAAE,OAAO,SAAS,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,IAAI;;YAG/E,IAAK,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,KAAK,CAAC,GAAG,GAAE,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAE;gBAClD,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;;YAG5B,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE,IAAI,CAAC;QAC/C,CAAG,MAAM;YACL,IAAI,MAAM,GAAG,SAAS,CAAC,MAAA,EACnB,CAAC;YAEL,IAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAE;gBAC3B,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC;gBAEnF,OAAQ,GAAG;oBACT,KAAK,CAAC;wBAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC;wBAAC;oBACpD,KAAK,CAAC;wBAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC;wBAAC;oBACxD,KAAK,CAAC;wBAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;wBAAC;oBAC5D,KAAK,CAAC;wBAAE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAC;wBAAC;oBAChE;wBACE,IAAI,CAAC,IAAI,EAAE,IAAK,CAAC,GAAG,CAAC,EAAE,IAAI,GAAG,IAAI,KAAK,CAAC,GAAG,GAAE,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,CAAE;4BAC7D,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC;;wBAG5B,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,IAAI,CAAC;;;;QAKzD,OAAO,IAAI;IACb,CAAC;IAED;;;;;;;;EAQA,GACA,YAAY,CAAC,SAAS,CAAC,EAAE,GAAG,SAAS,EAAE,CAAC,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE;QAC1D,OAAO,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,KAAK,CAAC;IACrD,CAAC;IAED;;;;;;;;EAQA,GACA,YAAY,CAAC,SAAS,CAAC,IAAI,GAAG,SAAS,IAAI,CAAC,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE;QAC9D,OAAO,WAAW,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,CAAC;IACpD,CAAC;IAED;;;;;;;;;EASA,GACA,YAAY,CAAC,SAAS,CAAC,cAAc,GAAG,SAAS,cAAc,CAAC,KAAK,EAAE,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE;QACxF,IAAI,GAAG,GAAG,MAAM,GAAG,MAAM,GAAG,KAAK,GAAG,KAAK;QAEzC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,OAAO,IAAI;QACnC,IAAI,CAAC,EAAE,EAAE;YACP,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC;YACrB,OAAO,IAAI;;QAGb,IAAI,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QAEjC,IAAI,SAAS,CAAC,EAAE,EAAE;YAChB,IACE,SAAS,CAAC,EAAE,KAAK,EAAE,IACzB,CAAO,CAAC,IAAI,IAAI,SAAS,CAAC,IAAI,CAAC,KACxB,CAAC,OAAO,IAAI,SAAS,CAAC,OAAO,KAAK,OAAO,GAC1C;gBACA,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC;;QAE3B,CAAG,MAAM;YACL,IAAK,IAAI,CAAC,GAAG,CAAC,EAAE,MAAM,GAAG,EAAE,EAAE,MAAM,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,CAAE;gBACvE,IACE,SAAS,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,IACrB,IAAI,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAC3B,OAAO,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,KAAK,OAAO,EAC5C;oBACA,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;;;YAIjC,EAAA;YACA,yEAAA;YACA,EAAA;YACI,IAAI,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,MAAM,KAAK,CAAC,GAAG,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM;iBAC1E,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC;;QAG5B,OAAO,IAAI;IACb,CAAC;IAED;;;;;;EAMA,GACA,YAAY,CAAC,SAAS,CAAC,kBAAkB,GAAG,SAAS,kBAAkB,CAAC,KAAK,EAAE;QAC7E,IAAI,GAAG;QAEP,IAAI,KAAK,EAAE;YACT,GAAG,GAAG,MAAM,GAAG,MAAM,GAAG,KAAK,GAAG,KAAK;YACrC,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,UAAU,CAAC,IAAI,EAAE,GAAG,CAAC;QAChD,CAAG,MAAM;YACL,IAAI,CAAC,OAAO,GAAG,IAAI,MAAM,EAAE;YAC3B,IAAI,CAAC,YAAY,GAAG,CAAC;;QAGvB,OAAO,IAAI;IACb,CAAC;IAED,EAAA;IACA,qDAAA;IACA,EAAA;IACA,YAAY,CAAC,SAAS,CAAC,GAAG,GAAG,YAAY,CAAC,SAAS,CAAC,cAAc;IAClE,YAAY,CAAC,SAAS,CAAC,WAAW,GAAG,YAAY,CAAC,SAAS,CAAC,EAAE;IAE9D,EAAA;IACA,qBAAA;IACA,EAAA;IACA,YAAY,CAAC,QAAQ,GAAG,MAAM;IAE9B,EAAA;IACA,2DAAA;IACA,EAAA;IACA,YAAY,CAAC,YAAY,GAAG,YAAY;IAExC,EAAA;IACA,qBAAA;IACA,EAAA;IACmC;QACjC,MAAA,CAAA,OAAA,GAAiB,YAAY;IAC/B,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1962, "column": 0}, "map": {"version": 3, "sources": ["file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/markers-plugin/src/index.ts", "file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/markers-plugin/src/events.ts", "file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/markers-plugin/src/MarkersButton.ts", "file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/markers-plugin/src/icons/pin.svg", "file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/markers-plugin/src/MarkersListButton.ts", "file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/markers-plugin/src/constants.ts", "file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/markers-plugin/src/icons/pin-list.svg", "file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/markers-plugin/src/MarkersPlugin.ts", "file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/markers-plugin/src/CSS3DContainer.ts", "file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/node_modules/three/examples/jsm/renderers/CSS3DRenderer.js", "file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/markers-plugin/src/MarkerType.ts", "file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/markers-plugin/src/markers/AbstractStandardMarker.ts", "file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/markers-plugin/src/markers/AbstractDomMarker.ts", "file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/markers-plugin/src/markers/Marker.ts", "file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/markers-plugin/src/markers/Marker3D.ts", "file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/shared/ChromaKeyMaterial.ts", "file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/shared/shaders/chromaKey.fragment.glsl", "file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/shared/shaders/chromaKey.vertex.glsl", "file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/shared/video-utils.ts", "file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/markers-plugin/src/utils.ts", "file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/markers-plugin/src/markers/MarkerCSS3D.ts", "file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/markers-plugin/src/markers/MarkerNormal.ts", "file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/markers-plugin/src/markers/MarkerPolygon.ts", "file:///workspaces/social-travel-trip/social-travel-trip-frontend/node_modules/%40photo-sphere-viewer/markers-plugin/src/markers/MarkerSvg.ts"], "sourcesContent": ["import { DEFAULTS, registerButton } from '@photo-sphere-viewer/core';\nimport * as events from './events';\nimport { MarkersButton } from './MarkersButton';\nimport { MarkersListButton } from './MarkersListButton';\n\nDEFAULTS.lang[MarkersButton.id] = 'Markers';\nDEFAULTS.lang[MarkersListButton.id] = 'Markers list';\nregisterButton(MarkersButton, 'caption:left');\nregisterButton(MarkersListButton, 'caption:left');\n\nexport type { Marker } from './markers/Marker';\nexport type { MarkerType } from './MarkerType';\nexport { MarkersPlugin } from './MarkersPlugin';\nexport * from './model';\nexport { events };\n\n/** @internal  */\nimport './styles/index.scss';\n", "import { TypedEvent } from '@photo-sphere-viewer/core';\nimport type { Marker } from './markers/Marker';\nimport type { MarkersPlugin } from './MarkersPlugin';\n\n/**\n * Base class for events dispatched by {@link MarkersPlugin}\n */\nexport abstract class MarkersPluginEvent extends TypedEvent<MarkersPlugin> {}\n\n/**\n * @event Triggered when the visibility of a marker changes\n */\nexport class MarkerVisibilityEvent extends MarkersPluginEvent {\n    static override readonly type = 'marker-visibility';\n    override type: 'marker-visibility';\n\n    /** @internal */\n    constructor(\n        public readonly marker: Marker,\n        public readonly visible: boolean,\n    ) {\n        super(MarkerVisibilityEvent.type);\n    }\n}\n\n/**\n * @event Triggered when the animation to a marker is done\n */\nexport class GotoMarkerDoneEvent extends MarkersPluginEvent {\n    static override readonly type = 'goto-marker-done';\n    override type: 'goto-marker-done';\n\n    /** @internal */\n    constructor(public readonly marker: Marker) {\n        super(GotoMarkerDoneEvent.type);\n    }\n}\n\n/**\n * @event Triggered when the user puts the cursor away from a marker\n */\nexport class LeaveMarkerEvent extends MarkersPluginEvent {\n    static override readonly type = 'leave-marker';\n    override type: 'leave-marker';\n\n    /** @internal */\n    constructor(public readonly marker: Marker) {\n        super(LeaveMarkerEvent.type);\n    }\n}\n\n/**\n * @event Triggered when the user puts the cursor hover a marker\n */\nexport class EnterMarkerEvent extends MarkersPluginEvent {\n    static override readonly type = 'enter-marker';\n    override type: 'enter-marker';\n\n    /** @internal */\n    constructor(public readonly marker: Marker) {\n        super(EnterMarkerEvent.type);\n    }\n}\n\n/**\n * @event Triggered when the user clicks on a marker\n */\nexport class SelectMarkerEvent extends MarkersPluginEvent {\n    static override readonly type = 'select-marker';\n    override type: 'select-marker';\n\n    /** @internal */\n    constructor(\n        public readonly marker: Marker,\n        public readonly doubleClick: boolean,\n        public readonly rightClick: boolean,\n    ) {\n        super(SelectMarkerEvent.type);\n    }\n}\n\n/**\n * @event Triggered when a marker is selected from the side panel\n */\nexport class SelectMarkerListEvent extends MarkersPluginEvent {\n    static override readonly type = 'select-marker-list';\n    override type: 'select-marker-list';\n\n    /** @internal */\n    constructor(public readonly marker: Marker) {\n        super(SelectMarkerListEvent.type);\n    }\n}\n\n/**\n * @event Triggered when a marker was selected and the user clicks elsewhere\n */\nexport class UnselectMarkerEvent extends MarkersPluginEvent {\n    static override readonly type = 'unselect-marker';\n    override type: 'unselect-marker';\n\n    /** @internal */\n    constructor(public readonly marker: Marker) {\n        super(UnselectMarkerEvent.type);\n    }\n}\n\n/**\n * @event Triggered when the markers are hidden\n */\nexport class HideMarkersEvent extends MarkersPluginEvent {\n    static override readonly type = 'hide-markers';\n    override type: 'hide-markers';\n\n    /** @internal */\n    constructor() {\n        super(HideMarkersEvent.type);\n    }\n}\n\n/**\n * @event Triggered when the markers change\n */\nexport class SetMarkersEvent extends MarkersPluginEvent {\n    static override readonly type = 'set-markers';\n    override type: 'set-markers';\n\n    /** @internal */\n    constructor(public readonly markers: Marker[]) {\n        super(SetMarkersEvent.type);\n    }\n}\n\n/**\n * @event Triggered when the markers are shown\n */\nexport class ShowMarkersEvent extends MarkersPluginEvent {\n    static override readonly type = 'show-markers';\n    override type: 'show-markers';\n\n    /** @internal */\n    constructor() {\n        super(ShowMarkersEvent.type);\n    }\n}\n\n/**\n * @event Used to alter the list of markers displayed in the side-panel\n */\nexport class RenderMarkersListEvent extends MarkersPluginEvent {\n    static override readonly type = 'render-markers-list';\n    override type: 'render-markers-list';\n\n    /** @internal */\n    constructor(\n        /** the list of markers to display, can be modified */\n        public markers: Marker[],\n    ) {\n        super(RenderMarkersListEvent.type);\n    }\n}\n\nexport type MarkersPluginEvents =\n    | MarkerVisibilityEvent\n    | GotoMarkerDoneEvent\n    | LeaveMarkerEvent\n    | EnterMarkerEvent\n    | SelectMarkerEvent\n    | SelectMarkerListEvent\n    | UnselectMarkerEvent\n    | HideMarkersEvent\n    | SetMarkersEvent\n    | ShowMarkersEvent\n    | RenderMarkersListEvent;\n", "import type { Navbar } from '@photo-sphere-viewer/core';\nimport { AbstractButton } from '@photo-sphere-viewer/core';\nimport { HideMarkersEvent, ShowMarkersEvent } from './events';\nimport type { MarkersPlugin } from './MarkersPlugin';\nimport pin from './icons/pin.svg';\n\nexport class MarkersButton extends AbstractButton {\n    static override readonly id = 'markers';\n\n    private readonly plugin: MarkersPlugin;\n\n    constructor(navbar: Navbar) {\n        super(navbar, {\n            className: 'psv-markers-button',\n            icon: pin,\n            hoverScale: true,\n            collapsable: true,\n            tabbable: true,\n        });\n\n        this.plugin = this.viewer.getPlugin('markers');\n\n        if (this.plugin) {\n            this.plugin.addEventListener(ShowMarkersEvent.type, this);\n            this.plugin.addEventListener(HideMarkersEvent.type, this);\n\n            this.toggleActive(true);\n        }\n    }\n\n    override destroy() {\n        if (this.plugin) {\n            this.plugin.removeEventListener(ShowMarkersEvent.type, this);\n            this.plugin.removeEventListener(HideMarkersEvent.type, this);\n        }\n\n        super.destroy();\n    }\n\n    override isSupported() {\n        return !!this.plugin;\n    }\n\n    handleEvent(e: Event) {\n        if (e instanceof ShowMarkersEvent) {\n            this.toggleActive(true);\n        } else if (e instanceof HideMarkersEvent) {\n            this.toggleActive(false);\n        }\n    }\n\n    onClick() {\n        this.plugin.toggleAllMarkers();\n    }\n}\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"10 9 81 81\"><path fill=\"currentColor\" d=\"M50.5 90S22.9 51.9 22.9 36.6 35.2 9 50.5 9s27.6 12.4 27.6 27.6S50.5 90 50.5 90zm0-66.3c-6.1 0-11 4.9-11 11s4.9 11 11 11 11-4.9 11-11-4.9-11-11-11z\"/><!--Created by <PERSON><PERSON><PERSON> from the Noun Project--></svg>\n", "import type { Navbar } from '@photo-sphere-viewer/core';\nimport { AbstractButton, events } from '@photo-sphere-viewer/core';\nimport { ID_PANEL_MARKERS_LIST } from './constants';\nimport type { MarkersPlugin } from './MarkersPlugin';\nimport pinList from './icons/pin-list.svg';\n\nexport class MarkersListButton extends AbstractButton {\n    static override readonly id = 'markersList';\n\n    private readonly plugin: MarkersPlugin;\n\n    constructor(navbar: Navbar) {\n        super(navbar, {\n            className: ' psv-markers-list-button',\n            icon: pinList,\n            hoverScale: true,\n            collapsable: true,\n            tabbable: true,\n        });\n\n        this.plugin = this.viewer.getPlugin('markers');\n\n        if (this.plugin) {\n            this.viewer.addEventListener(events.ShowPanelEvent.type, this);\n            this.viewer.addEventListener(events.HidePanelEvent.type, this);\n        }\n    }\n\n    override destroy() {\n        this.viewer.removeEventListener(events.ShowPanelEvent.type, this);\n        this.viewer.removeEventListener(events.HidePanelEvent.type, this);\n\n        super.destroy();\n    }\n\n    override isSupported() {\n        return !!this.plugin;\n    }\n\n    handleEvent(e: Event) {\n        if (e instanceof events.ShowPanelEvent) {\n            this.toggleActive(e.panelId === ID_PANEL_MARKERS_LIST);\n        } else if (e instanceof events.HidePanelEvent) {\n            this.toggleActive(false);\n        }\n    }\n\n    onClick() {\n        this.plugin.toggleMarkersList();\n    }\n}\n", "import { utils } from '@photo-sphere-viewer/core';\nimport type { Marker } from './markers/Marker';\nimport icon from './icons/pin-list.svg';\n\n/**\n * Namespace for SVG creation\n * @internal\n */\nexport const SVG_NS = 'http://www.w3.org/2000/svg';\n\n/**\n * Property name added to marker elements\n * @internal\n */\nexport const MARKER_DATA = 'psvMarker';\n\n/**\n * Property name added to marker elements (dash-case)\n * @internal\n */\nexport const MARKER_DATA_KEY = utils.dasherize(MARKER_DATA);\n\n/**\n * Panel identifier for marker content\n * @internal\n */\nexport const ID_PANEL_MARKER = 'marker';\n\n/**\n * Panel identifier for markers list\n * @internal\n */\nexport const ID_PANEL_MARKERS_LIST = 'markersList';\n\n/**\n * Default configuration for the \"hoverScale\" parameters\n * @internal\n */\nexport const DEFAULT_HOVER_SCALE = {\n    amount: 2,\n    duration: 100,\n    easing: 'linear',\n};\n\n/**\n * Markers list template\n * @internal\n */\nexport const MARKERS_LIST_TEMPLATE = (markers: Marker[], title: string) => `\n<div class=\"psv-panel-menu psv-panel-menu--stripped\">\n    <h1 class=\"psv-panel-menu-title\">${icon} ${title}</h1>\n    <ul class=\"psv-panel-menu-list\">\n    ${markers.map(marker => `\n        <li data-${MARKER_DATA_KEY}=\"${marker.id}\" class=\"psv-panel-menu-item\" tabindex=\"0\">\n          ${marker.type === 'image' ? `<span class=\"psv-panel-menu-item-icon\"><img src=\"${marker.definition}\"/></span>` : ''}\n          <span class=\"psv-panel-menu-item-label\">${marker.getListContent()}</span>\n        </li>\n    `).join('')}\n    </ul>\n</div>\n`;\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"9 9 81 81\"><path fill=\"currentColor\" d=\"M37.5 90S9.9 51.9 9.9 36.6 22.2 9 37.5 9s27.6 12.4 27.6 27.6S37.5 90 37.5 90zm0-66.3c-6.1 0-11 4.9-11 11s4.9 11 11 11 11-4.9 11-11-4.9-11-11-11zM86.7 55H70c-1.8 0-3.3-1.5-3.3-3.3s1.5-3.3 3.3-3.3h16.7c1.8 0 3.3 1.5 3.3 3.3S88.5 55 86.7 55zm0-25h-15a3.3 3.3 0 0 1-3.3-3.3c0-1.8 1.5-3.3 3.3-3.3h15c1.8 0 3.3 1.5 3.3 3.3 0 1.8-1.5 3.3-3.3 3.3zM56.5 73h30c1.8 0 3.3 1.5 3.3 3.3 0 1.8-1.5 3.3-3.3 3.3h-30a3.3 3.3 0 0 1-3.3-3.3 3.2 3.2 0 0 1 3.3-3.3z\"/><!--Created by Rohith <PERSON> S from the Noun Project--></svg>\n", "import type { Point, Viewer } from '@photo-sphere-viewer/core';\nimport { AbstractConfigurablePlugin, PSVError, events, utils } from '@photo-sphere-viewer/core';\nimport { Object3D } from 'three';\nimport { CSS3DContainer } from './CSS3DContainer';\nimport { getMarkerType } from './MarkerType';\nimport { MarkersButton } from './MarkersButton';\nimport { MarkersListButton } from './MarkersListButton';\nimport {\n    DEFAULT_HOVER_SCALE,\n    ID_PANEL_MARKER,\n    ID_PANEL_MARKERS_LIST,\n    MARKERS_LIST_TEMPLATE,\n    MARKER_DATA,\n    SVG_NS,\n} from './constants';\nimport {\n    EnterMarkerEvent,\n    GotoMarkerDoneEvent,\n    HideMarkersEvent,\n    LeaveMarkerEvent,\n    MarkerVisibilityEvent,\n    MarkersPluginEvents,\n    RenderMarkersListEvent,\n    SelectMarkerEvent,\n    SelectMarkerListEvent,\n    SetMarkersEvent,\n    ShowMarkersEvent,\n    UnselectMarkerEvent,\n} from './events';\nimport { AbstractStandardMarker } from './markers/AbstractStandardMarker';\nimport { Marker } from './markers/Marker';\nimport { Marker3D } from './markers/Marker3D';\nimport { MarkerCSS3D } from './markers/MarkerCSS3D';\nimport { MarkerNormal } from './markers/MarkerNormal';\nimport { MarkerPolygon } from './markers/MarkerPolygon';\nimport { MarkerSvg } from './markers/MarkerSvg';\nimport { MarkerConfig, MarkersPluginConfig, ParsedMarkersPluginConfig, UpdatableMarkersPluginConfig } from './model';\n\nconst getConfig = utils.getConfigParser<MarkersPluginConfig, ParsedMarkersPluginConfig>(\n    {\n        clickEventOnMarker: false,\n        gotoMarkerSpeed: '8rpm',\n        markers: null,\n        defaultHoverScale: null,\n    },\n    {\n        defaultHoverScale(defaultHoverScale) {\n            if (!defaultHoverScale) {\n                return null;\n            }\n            if (defaultHoverScale === true) {\n                defaultHoverScale = DEFAULT_HOVER_SCALE;\n            }\n            if (typeof defaultHoverScale === 'number') {\n                defaultHoverScale = { amount: defaultHoverScale };\n            }\n            return {\n                ...DEFAULT_HOVER_SCALE,\n                ...defaultHoverScale,\n            };\n        },\n    },\n);\n\nfunction getMarkerCtor(config: MarkerConfig): typeof Marker {\n    const type = getMarkerType(config, false);\n\n    switch (type) {\n        case 'image':\n        case 'html':\n        case 'element':\n            return MarkerNormal;\n        case 'imageLayer':\n        case 'videoLayer':\n            return Marker3D;\n        case 'elementLayer':\n            return MarkerCSS3D;\n        case 'polygon':\n        case 'polyline':\n        case 'polygonPixels':\n        case 'polylinePixels':\n            return MarkerPolygon;\n        case 'square':\n        case 'rect':\n        case 'circle':\n        case 'ellipse':\n        case 'path':\n            return MarkerSvg;\n        default:\n            throw new PSVError('invalid marker type');\n    }\n}\n\n/**\n * Displays various markers on the viewer\n */\nexport class MarkersPlugin extends AbstractConfigurablePlugin<\n    MarkersPluginConfig,\n    ParsedMarkersPluginConfig,\n    UpdatableMarkersPluginConfig,\n    MarkersPluginEvents\n> {\n    static override readonly id = 'markers';\n    static override readonly VERSION = PKG_VERSION;\n    static override readonly configParser = getConfig;\n    static override readonly readonlyOptions: Array<keyof MarkersPluginConfig> = ['markers'];\n\n    private readonly markers: Record<string, Marker> = {};\n\n    private readonly state = {\n        allVisible: true,\n        showAllTooltips: false,\n        currentMarker: null as Marker,\n        hoveringMarker: null as Marker,\n        // require a 2nd render (only the scene) when 3d markers visibility changes\n        needsReRender: false,\n        // use when updating a polygon marker in order to keep the current position\n        lastClientX: null as number,\n        lastClientY: null as number,\n    };\n\n    private readonly container: HTMLElement;\n    private readonly svgContainer: SVGElement;\n    private readonly css3DContainer: CSS3DContainer;\n\n    constructor(viewer: Viewer, config: MarkersPluginConfig) {\n        super(viewer, config);\n\n        this.container = document.createElement('div');\n        this.container.className = 'psv-markers';\n        this.viewer.container.appendChild(this.container);\n\n        this.container.addEventListener('contextmenu', e => e.preventDefault());\n\n        this.svgContainer = document.createElementNS(SVG_NS, 'svg');\n        this.svgContainer.setAttribute('class', 'psv-markers-svg-container');\n        this.container.appendChild(this.svgContainer);\n\n        this.css3DContainer = new CSS3DContainer(viewer);\n        this.container.appendChild(this.css3DContainer.element);\n\n        // Markers events via delegation\n        this.container.addEventListener('mouseenter', this, true);\n        this.container.addEventListener('mouseleave', this, true);\n        this.container.addEventListener('mousemove', this, true);\n    }\n\n    /**\n     * @internal\n     */\n    override init() {\n        super.init();\n\n        utils.checkStylesheet(this.viewer.container, 'markers-plugin');\n\n        // Viewer events\n        this.viewer.addEventListener(events.ClickEvent.type, this);\n        this.viewer.addEventListener(events.DoubleClickEvent.type, this);\n        this.viewer.addEventListener(events.RenderEvent.type, this);\n        this.viewer.addEventListener(events.ConfigChangedEvent.type, this);\n        this.viewer.addEventListener(events.ObjectEnterEvent.type, this);\n        this.viewer.addEventListener(events.ObjectHoverEvent.type, this);\n        this.viewer.addEventListener(events.ObjectLeaveEvent.type, this);\n        this.viewer.addEventListener(events.ReadyEvent.type, this, { once: true });\n    }\n\n    /**\n     * @internal\n     */\n    override destroy() {\n        this.clearMarkers(false);\n\n        this.viewer.unobserveObjects(MARKER_DATA);\n\n        this.viewer.removeEventListener(events.ClickEvent.type, this);\n        this.viewer.removeEventListener(events.DoubleClickEvent.type, this);\n        this.viewer.removeEventListener(events.RenderEvent.type, this);\n        this.viewer.removeEventListener(events.ObjectEnterEvent.type, this);\n        this.viewer.removeEventListener(events.ObjectHoverEvent.type, this);\n        this.viewer.removeEventListener(events.ObjectLeaveEvent.type, this);\n        this.viewer.removeEventListener(events.ReadyEvent.type, this);\n\n        this.css3DContainer.destroy();\n        this.viewer.container.removeChild(this.container);\n\n        super.destroy();\n    }\n\n    /**\n     * @internal\n     */\n    handleEvent(e: Event) {\n        switch (e.type) {\n            case events.ReadyEvent.type:\n                if (this.config.markers) {\n                    this.setMarkers(this.config.markers);\n                    delete this.config.markers;\n                }\n                break;\n\n            case events.RenderEvent.type:\n                this.renderMarkers();\n                break;\n\n            case events.ClickEvent.type:\n                this.__onClick(e as events.ClickEvent, false);\n                break;\n\n            case events.DoubleClickEvent.type:\n                this.__onClick(e as events.DoubleClickEvent, true);\n                break;\n\n            case events.ObjectEnterEvent.type:\n            case events.ObjectLeaveEvent.type:\n            case events.ObjectHoverEvent.type:\n                if ((e as events.ObjectEvent).userDataKey === MARKER_DATA) {\n                    const event = (e as events.ObjectEvent).originalEvent;\n                    const marker: Marker = (e as events.ObjectEvent).object.userData[MARKER_DATA];\n                    switch (e.type) {\n                        case events.ObjectEnterEvent.type:\n                            if (marker.config.style?.cursor) {\n                                this.viewer.setCursor(marker.config.style.cursor);\n                            } else if (marker.config.tooltip || marker.config.content) {\n                                this.viewer.setCursor('pointer');\n                            }\n                            this.__onEnterMarker(event, marker);\n                            break;\n                        case events.ObjectLeaveEvent.type:\n                            this.viewer.setCursor(null);\n                            this.__onLeaveMarker(marker);\n                            break;\n                        case events.ObjectHoverEvent.type:\n                            this.__onHoverMarker(event, marker);\n                            break;\n                    }\n                }\n                break;\n\n            case 'mouseenter': {\n                const marker = this.__getTargetMarker(utils.getEventTarget(e));\n                this.__onEnterMarker(e as MouseEvent, marker);\n                break;\n            }\n\n            case 'mouseleave': {\n                const marker = this.__getTargetMarker(utils.getEventTarget(e));\n                this.__onLeaveMarker(marker);\n                break;\n            }\n\n            case 'mousemove': {\n                const marker = this.__getTargetMarker(utils.getEventTarget(e), true);\n                this.__onHoverMarker(e as MouseEvent, marker);\n                break;\n            }\n        }\n    }\n\n    /**\n     * Toggles all markers\n     */\n    toggleAllMarkers() {\n        if (this.state.allVisible) {\n            this.hideAllMarkers();\n        } else {\n            this.showAllMarkers();\n        }\n    }\n\n    /**\n     * Shows all markers\n     */\n    showAllMarkers() {\n        this.state.allVisible = true;\n        Object.values(this.markers).forEach((marker) => {\n            marker.config.visible = true;\n        });\n        this.renderMarkers();\n        this.dispatchEvent(new ShowMarkersEvent());\n    }\n\n    /**\n     * Hides all markers\n     */\n    hideAllMarkers() {\n        this.state.allVisible = false;\n        Object.values(this.markers).forEach((marker) => {\n            marker.config.visible = false;\n        });\n        this.renderMarkers();\n        this.dispatchEvent(new HideMarkersEvent());\n    }\n\n    /**\n     * Toggles the visibility of all tooltips\n     */\n    toggleAllTooltips() {\n        if (this.state.showAllTooltips) {\n            this.hideAllTooltips();\n        } else {\n            this.showAllTooltips();\n        }\n    }\n\n    /**\n     *  Displays all tooltips\n     */\n    showAllTooltips() {\n        this.state.showAllTooltips = true;\n        Object.values(this.markers).forEach((marker) => {\n            marker.state.staticTooltip = true;\n            marker.showTooltip();\n        });\n    }\n\n    /**\n     * Hides all tooltips\n     */\n    hideAllTooltips() {\n        this.state.showAllTooltips = false;\n        Object.values(this.markers).forEach((marker) => {\n            marker.state.staticTooltip = false;\n            marker.hideTooltip();\n        });\n    }\n\n    /**\n     * Returns the total number of markers\n     */\n    getNbMarkers(): number {\n        return Object.keys(this.markers).length;\n    }\n\n    /**\n     * Returns all the markers\n     */\n    getMarkers(): Marker[] {\n        return Object.values(this.markers);\n    }\n\n    /**\n     * Adds a new marker to viewer\n     * @throws {@link PSVError} when the marker's id is missing or already exists\n     */\n    addMarker(config: MarkerConfig, render = true) {\n        if (this.markers[config.id]) {\n            throw new PSVError(`marker \"${config.id}\" already exists`);\n        }\n\n        // @ts-ignore\n        const marker: Marker = new (getMarkerCtor(config))(this.viewer, this, config);\n\n        if (marker.isPoly()) {\n            this.svgContainer.appendChild(marker.domElement);\n        } else if (marker.isCss3d()) {\n            this.css3DContainer.addObject(marker as MarkerCSS3D);\n        } else if (marker.is3d()) {\n            this.viewer.renderer.addObject(marker.threeElement);\n        } else {\n            this.container.appendChild(marker.domElement);\n        }\n\n        this.markers[marker.id] = marker;\n\n        if (this.state.showAllTooltips) {\n            marker.state.staticTooltip = true;\n        }\n\n        if (render) {\n            this.__afterChangeMarkers();\n        }\n    }\n\n    /**\n     * Returns the internal marker object for a marker id\n     * @throws {@link PSVError} when the marker cannot be found\n     */\n    getMarker(markerId: string | MarkerConfig): Marker {\n        const id = typeof markerId === 'object' ? markerId.id : markerId;\n\n        if (!this.markers[id]) {\n            throw new PSVError(`cannot find marker \"${id}\"`);\n        }\n\n        return this.markers[id];\n    }\n\n    /**\n     * Returns the last marker selected by the user\n     */\n    getCurrentMarker(): Marker {\n        return this.state.currentMarker;\n    }\n\n    /**\n     * Updates the existing marker with the same id\n     * Every property can be changed but you can't change its type (Eg: `image` to `html`)\n     */\n    updateMarker(config: MarkerConfig, render = true) {\n        const marker = this.getMarker(config.id);\n\n        marker.update(config);\n\n        if (render) {\n            this.__afterChangeMarkers();\n\n            if (\n                (marker === this.state.hoveringMarker && marker.config.tooltip?.trigger === 'hover')\n                || marker.state.staticTooltip\n            ) {\n                marker.showTooltip(this.state.lastClientX, this.state.lastClientY, true);\n            }\n        }\n    }\n\n    /**\n     * Removes a marker from the viewer\n     */\n    removeMarker(markerId: string | MarkerConfig, render = true) {\n        const marker = this.getMarker(markerId);\n\n        if (marker.isPoly()) {\n            this.svgContainer.removeChild(marker.domElement);\n        } else if (marker.isCss3d()) {\n            this.css3DContainer.removeObject(marker as MarkerCSS3D);\n        } else if (marker.is3d()) {\n            this.viewer.renderer.removeObject(marker.threeElement);\n        } else {\n            this.container.removeChild(marker.domElement);\n        }\n\n        if (this.state.hoveringMarker === marker) {\n            this.state.hoveringMarker = null;\n        }\n\n        if (this.state.currentMarker === marker) {\n            this.state.currentMarker = null;\n        }\n\n        marker.destroy();\n        delete this.markers[marker.id];\n\n        if (render) {\n            this.__afterChangeMarkers();\n        }\n    }\n\n    /**\n     * Removes multiple markers\n     */\n    removeMarkers(markerIds: string[], render = true) {\n        markerIds.forEach(markerId => this.removeMarker(markerId, false));\n\n        if (render) {\n            this.__afterChangeMarkers();\n        }\n    }\n\n    /**\n     * Replaces all markers\n     */\n    setMarkers(markers: MarkerConfig[] | null, render = true) {\n        this.clearMarkers(false);\n\n        markers?.forEach((marker) => {\n            this.addMarker(marker, false);\n        });\n\n        if (render) {\n            this.__afterChangeMarkers();\n        }\n    }\n\n    /**\n     * Removes all markers\n     */\n    clearMarkers(render = true) {\n        Object.keys(this.markers).forEach((markerId) => {\n            this.removeMarker(markerId, false);\n        });\n\n        if (render) {\n            this.__afterChangeMarkers();\n        }\n    }\n\n    /**\n     * Rotate the view to face the marker\n     */\n    gotoMarker(markerId: string | MarkerConfig, speed: string | number = this.config.gotoMarkerSpeed): Promise<void> {\n        const marker = this.getMarker(markerId);\n\n        if (!speed) {\n            this.viewer.rotate(marker.state.position);\n            if (!utils.isNil(marker.config.zoomLvl)) {\n                this.viewer.zoom(marker.config.zoomLvl);\n            }\n            this.dispatchEvent(new GotoMarkerDoneEvent(marker));\n            return Promise.resolve();\n        } else {\n            return this.viewer\n                .animate({\n                    ...marker.state.position,\n                    zoom: marker.config.zoomLvl,\n                    speed: speed,\n                })\n                .then(() => {\n                    this.dispatchEvent(new GotoMarkerDoneEvent(marker));\n                });\n        }\n    }\n\n    /**\n     * Hides a marker\n     */\n    hideMarker(markerId: string | MarkerConfig) {\n        this.toggleMarker(markerId, false);\n    }\n\n    /**\n     * Shows a marker\n     */\n    showMarker(markerId: string | MarkerConfig) {\n        this.toggleMarker(markerId, true);\n    }\n\n    /**\n     * Forces the display of the tooltip of a marker\n     */\n    showMarkerTooltip(markerId: string | MarkerConfig) {\n        const marker = this.getMarker(markerId);\n        marker.state.staticTooltip = true;\n        marker.showTooltip();\n    }\n\n    /**\n     * Hides the tooltip of a marker\n     */\n    hideMarkerTooltip(markerId: string | MarkerConfig) {\n        const marker = this.getMarker(markerId);\n        marker.state.staticTooltip = false;\n        marker.hideTooltip();\n    }\n\n    /**\n     * Toggles a marker visibility\n     */\n    toggleMarker(markerId: string | MarkerConfig, visible?: boolean) {\n        const marker = this.getMarker(markerId);\n        marker.config.visible = utils.isNil(visible) ? !marker.config.visible : visible;\n        this.renderMarkers();\n    }\n\n    /**\n     * Opens the panel with the content of the marker\n     */\n    showMarkerPanel(markerId: string | MarkerConfig) {\n        const marker = this.getMarker(markerId);\n\n        if (marker.config.content) {\n            this.viewer.panel.show({\n                id: ID_PANEL_MARKER,\n                content: marker.config.content,\n            });\n        } else {\n            this.hideMarkerPanel();\n        }\n    }\n\n    /**\n     * Closes the panel if currently showing the content of a marker\n     */\n    hideMarkerPanel() {\n        this.viewer.panel.hide(ID_PANEL_MARKER);\n    }\n\n    /**\n     * Toggles the visibility of the list of markers\n     */\n    toggleMarkersList() {\n        if (this.viewer.panel.isVisible(ID_PANEL_MARKERS_LIST)) {\n            this.hideMarkersList();\n        } else {\n            this.showMarkersList();\n        }\n    }\n\n    /**\n     * Opens side panel with the list of markers\n     */\n    showMarkersList() {\n        let markers: Marker[] = [];\n        Object.values(this.markers).forEach((marker) => {\n            if (marker.config.visible && !marker.config.hideList) {\n                markers.push(marker);\n            }\n        });\n\n        const e = new RenderMarkersListEvent(markers);\n        this.dispatchEvent(e);\n        markers = e.markers;\n\n        this.viewer.panel.show({\n            id: ID_PANEL_MARKERS_LIST,\n            content: MARKERS_LIST_TEMPLATE(markers, this.viewer.config.lang[MarkersButton.id]),\n            noMargin: true,\n            clickHandler: (target) => {\n                const li = utils.getClosest(target, '.psv-panel-menu-item');\n                const markerId = li ? li.dataset[MARKER_DATA] : undefined;\n\n                if (markerId) {\n                    const marker = this.getMarker(markerId);\n\n                    this.dispatchEvent(new SelectMarkerListEvent(marker));\n\n                    this.gotoMarker(marker.id);\n                    this.hideMarkersList();\n                }\n            },\n        });\n    }\n\n    /**\n     * Closes side panel if it contains the list of markers\n     */\n    hideMarkersList() {\n        this.viewer.panel.hide(ID_PANEL_MARKERS_LIST);\n    }\n\n    /**\n     * Updates the visibility and the position of all markers\n     */\n    renderMarkers() {\n        if (this.state.needsReRender) {\n            this.state.needsReRender = false;\n            return;\n        }\n\n        const zoomLevel = this.viewer.getZoomLevel();\n        const viewerPosition = this.viewer.getPosition();\n        const hoveringMarker = this.state.hoveringMarker;\n\n        Object.values(this.markers).forEach((marker) => {\n            let isVisible = marker.config.visible;\n            let visibilityChanged = false;\n            let position: Point = null;\n\n            if (isVisible) {\n                position = marker.render({ viewerPosition, zoomLevel, hoveringMarker });\n                isVisible = !!position;\n            }\n\n            visibilityChanged = marker.state.visible !== isVisible;\n            marker.state.visible = isVisible;\n            marker.state.position2D = position;\n\n            if (marker.domElement) {\n                utils.toggleClass(marker.domElement, 'psv-marker--visible', isVisible);\n            }\n\n            if (!isVisible) {\n                marker.hideTooltip();\n            } else if (marker.state.staticTooltip) {\n                marker.showTooltip();\n            } else if (marker !== this.state.hoveringMarker) {\n                marker.hideTooltip();\n            }\n\n            if (visibilityChanged) {\n                this.dispatchEvent(new MarkerVisibilityEvent(marker, isVisible));\n\n                if (marker.is3d() || marker.isCss3d()) {\n                    this.state.needsReRender = true;\n                }\n            }\n        });\n\n        if (this.state.needsReRender) {\n            this.viewer.needsUpdate();\n        }\n    }\n\n    /**\n     * Returns the marker associated to an event target\n     */\n    private __getTargetMarker(target: HTMLElement, closest?: boolean): Marker;\n    private __getTargetMarker(target: Object3D[]): Marker;\n    private __getTargetMarker(target: HTMLElement | Object3D[], closest = false): Marker {\n        if (target instanceof Node) {\n            const target2 = closest ? utils.getClosest(target, '.psv-marker') : target;\n            return target2 ? (target2 as any)[MARKER_DATA] : undefined;\n        } else if (Array.isArray(target)) {\n            return target\n                .map(o => o.userData[MARKER_DATA] as Marker)\n                .filter(m => !!m)\n                .sort((a, b) => b.config.zIndex - a.config.zIndex)[0];\n        } else {\n            return null;\n        }\n    }\n\n    /**\n     * Handles mouse enter events, show the tooltip for non polygon markers\n     */\n    private __onEnterMarker(e: MouseEvent, marker?: Marker) {\n        if (marker) {\n            this.state.hoveringMarker = marker;\n            this.state.lastClientX = e.clientX;\n            this.state.lastClientY = e.clientY;\n\n            this.dispatchEvent(new EnterMarkerEvent(marker));\n\n            if (marker instanceof AbstractStandardMarker) {\n                marker.applyScale({\n                    zoomLevel: this.viewer.getZoomLevel(),\n                    viewerPosition: this.viewer.getPosition(),\n                    mouseover: true,\n                });\n            }\n\n            if (!marker.state.staticTooltip && marker.config.tooltip?.trigger === 'hover') {\n                marker.showTooltip(e.clientX, e.clientY);\n            }\n        }\n    }\n\n    /**\n     * Handles mouse leave events, hide the tooltip\n     */\n    private __onLeaveMarker(marker?: Marker) {\n        if (marker) {\n            this.dispatchEvent(new LeaveMarkerEvent(marker));\n\n            if (marker instanceof AbstractStandardMarker) {\n                marker.applyScale({\n                    zoomLevel: this.viewer.getZoomLevel(),\n                    viewerPosition: this.viewer.getPosition(),\n                    mouseover: false,\n                });\n            }\n\n            this.state.hoveringMarker = null;\n\n            if (!marker.state.staticTooltip && marker.config.tooltip?.trigger === 'hover') {\n                marker.hideTooltip();\n            } else if (marker.state.staticTooltip) {\n                marker.showTooltip();\n            }\n        }\n    }\n\n    /**\n     * Handles mouse move events, refresh the tooltip for polygon markers\n     */\n    private __onHoverMarker(e: MouseEvent, marker?: Marker) {\n        if (marker) {\n            this.state.lastClientX = e.clientX;\n            this.state.lastClientY = e.clientY;\n\n            if (marker.isPoly() || marker.is3d() || marker.isCss3d()) {\n                if (marker.config.tooltip?.trigger === 'hover') {\n                    marker.showTooltip(e.clientX, e.clientY);\n                }\n            }\n        }\n    }\n\n    /**\n     * Handles mouse click events, select the marker and open the panel if necessary\n     */\n    private __onClick(e: events.ClickEvent | events.DoubleClickEvent, dblclick: boolean) {\n        const threeMarker = this.__getTargetMarker(e.data.objects);\n        const stdMarker = this.__getTargetMarker(e.data.target, true);\n\n        // give priority to standard markers which are always on top of Three markers\n        const marker = stdMarker || threeMarker;\n\n        if (this.state.currentMarker && this.state.currentMarker !== marker) {\n            this.dispatchEvent(new UnselectMarkerEvent(this.state.currentMarker));\n\n            this.viewer.panel.hide(ID_PANEL_MARKER);\n\n            if (!this.state.showAllTooltips && this.state.currentMarker.config.tooltip?.trigger === 'click') {\n                this.hideMarkerTooltip(this.state.currentMarker.id);\n            }\n\n            this.state.currentMarker = null;\n        }\n\n        if (marker) {\n            this.state.currentMarker = marker;\n\n            this.dispatchEvent(new SelectMarkerEvent(marker, dblclick, e.data.rightclick));\n\n            if (this.config.clickEventOnMarker) {\n                // add the marker to event data\n                e.data.marker = marker;\n            } else {\n                e.stopImmediatePropagation();\n            }\n\n            // the marker could have been deleted in an event handler\n            if (this.markers[marker.id] && !e.data.rightclick) {\n                if (marker.config.tooltip?.trigger === 'click') {\n                    if (marker.tooltip) {\n                        this.hideMarkerTooltip(marker.id);\n                    } else {\n                        this.showMarkerTooltip(marker.id);\n                    }\n                } else {\n                    this.showMarkerPanel(marker.id);\n                }\n            }\n        }\n    }\n\n    private __afterChangeMarkers() {\n        this.__refreshUi();\n        this.__checkObjectsObserver();\n        this.viewer.needsUpdate();\n        this.dispatchEvent(new SetMarkersEvent(this.getMarkers()));\n    }\n\n    /**\n     * Updates the visiblity of the panel and the buttons\n     */\n    private __refreshUi() {\n        const nbMarkers = Object.values(this.markers).filter(m => !m.config.hideList).length;\n\n        if (nbMarkers === 0) {\n            this.viewer.panel.hide(ID_PANEL_MARKER);\n            this.viewer.panel.hide(ID_PANEL_MARKERS_LIST);\n        } else {\n            if (this.viewer.panel.isVisible(ID_PANEL_MARKERS_LIST)) {\n                this.showMarkersList();\n            } else if (this.viewer.panel.isVisible(ID_PANEL_MARKER)) {\n                this.state.currentMarker ? this.showMarkerPanel(this.state.currentMarker.id) : this.viewer.panel.hide();\n            }\n        }\n\n        this.viewer.navbar.getButton(MarkersButton.id, false)?.toggle(nbMarkers > 0);\n        this.viewer.navbar.getButton(MarkersListButton.id, false)?.toggle(nbMarkers > 0);\n    }\n\n    /**\n     * Adds or remove the objects observer if there are 3D markers\n     */\n    private __checkObjectsObserver() {\n        const has3d = Object.values(this.markers).some(marker => marker.is3d());\n\n        if (has3d) {\n            this.viewer.observeObjects(MARKER_DATA);\n        } else {\n            this.viewer.unobserveObjects(MARKER_DATA);\n        }\n    }\n}\n", "import { events, type Viewer } from '@photo-sphere-viewer/core';\nimport { Scene } from 'three';\nimport { CSS3DRenderer } from 'three/examples/jsm/renderers/CSS3DRenderer.js';\nimport { MARKER_DATA } from './constants';\nimport { MarkerCSS3D } from './markers/MarkerCSS3D';\n\n/**\n * @internal\n */\nexport class CSS3DContainer {\n    element: HTMLElement;\n\n    private readonly renderer: CSS3DRenderer;\n    private readonly scene: Scene;\n    private readonly intersectionObserver: IntersectionObserver;\n\n    constructor(\n        private viewer: Viewer,\n    ) {\n        this.element = document.createElement('div');\n        this.element.className = 'psv-markers-css3d-container';\n\n        this.renderer = new CSS3DRenderer({ element: this.element });\n        this.scene = new Scene();\n\n        this.intersectionObserver = new IntersectionObserver((entries) => {\n            entries.forEach((entry) => {\n                const marker = (entry.target as any)[MARKER_DATA] as MarkerCSS3D;\n                if (marker.config.visible) {\n                    marker.viewportIntersection = entry.isIntersecting;\n                }\n            });\n        }, {\n            root: this.element,\n        });\n\n        viewer.addEventListener(events.ReadyEvent.type, this, { once: true });\n        viewer.addEventListener(events.SizeUpdatedEvent.type, this);\n        viewer.addEventListener(events.RenderEvent.type, this);\n    }\n\n    handleEvent(e: Event) {\n        switch (e.type) {\n            case events.ReadyEvent.type:\n            case events.SizeUpdatedEvent.type:\n                this.updateSize();\n                break;\n            case events.RenderEvent.type:\n                this.render();\n                break;\n        }\n    }\n\n    destroy(): void {\n        this.viewer.removeEventListener(events.ReadyEvent.type, this);\n        this.viewer.removeEventListener(events.SizeUpdatedEvent.type, this);\n        this.viewer.removeEventListener(events.RenderEvent.type, this);\n\n        this.intersectionObserver.disconnect();\n    }\n\n    private updateSize() {\n        const size = this.viewer.getSize();\n        this.renderer.setSize(size.width, size.height);\n    }\n\n    private render() {\n        this.renderer.render(this.scene, this.viewer.renderer.camera);\n    }\n\n    addObject(marker: MarkerCSS3D) {\n        this.scene.add(marker.threeElement);\n        this.intersectionObserver.observe(marker.domElement);\n    }\n\n    removeObject(marker: MarkerCSS3D) {\n        this.scene.remove(marker.threeElement);\n        this.intersectionObserver.unobserve(marker.domElement);\n    }\n}\n", "import {\n\tMatrix4,\n\tObject3<PERSON>,\n\tQuaternion,\n\tVector3\n} from 'three';\n\n// Based on http://www.emagix.net/academic/mscs-project/item/camera-sync-with-css3-and-webgl-threejs\n\nconst _position = new Vector3();\nconst _quaternion = new Quaternion();\nconst _scale = new Vector3();\n\n/**\n * The base 3D object that is supported by {@link CSS3DRenderer}.\n *\n * @augments Object3D\n */\nclass CSS3DObject extends Object3D {\n\n\t/**\n\t * Constructs a new CSS3D object.\n\t *\n\t * @param {DOMElement} [element] - The DOM element.\n\t */\n\tconstructor( element = document.createElement( 'div' ) ) {\n\n\t\tsuper();\n\n\t\t/**\n\t\t * This flag can be used for type testing.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @readonly\n\t\t * @default true\n\t\t */\n\t\tthis.isCSS3DObject = true;\n\n\t\t/**\n\t\t * The DOM element which defines the appearance of this 3D object.\n\t\t *\n\t\t * @type {DOMElement}\n\t\t * @readonly\n\t\t * @default true\n\t\t */\n\t\tthis.element = element;\n\t\tthis.element.style.position = 'absolute';\n\t\tthis.element.style.pointerEvents = 'auto';\n\t\tthis.element.style.userSelect = 'none';\n\n\t\tthis.element.setAttribute( 'draggable', false );\n\n\t\tthis.addEventListener( 'removed', function () {\n\n\t\t\tthis.traverse( function ( object ) {\n\n\t\t\t\tif (\n\t\t\t\t\tobject.element instanceof object.element.ownerDocument.defaultView.Element &&\n\t\t\t\t\tobject.element.parentNode !== null\n\t\t\t\t) {\n\n\t\t\t\t\tobject.element.remove();\n\n\t\t\t\t}\n\n\t\t\t} );\n\n\t\t} );\n\n\t}\n\n\tcopy( source, recursive ) {\n\n\t\tsuper.copy( source, recursive );\n\n\t\tthis.element = source.element.cloneNode( true );\n\n\t\treturn this;\n\n\t}\n\n}\n\n/**\n * A specialized version of {@link CSS3DObject} that represents\n * DOM elements as sprites.\n *\n * @augments CSS3DObject\n */\nclass CSS3DSprite extends CSS3DObject {\n\n\t/**\n\t * Constructs a new CSS3D sprite object.\n\t *\n\t * @param {DOMElement} [element] - The DOM element.\n\t */\n\tconstructor( element ) {\n\n\t\tsuper( element );\n\n\t\t/**\n\t\t * This flag can be used for type testing.\n\t\t *\n\t\t * @type {boolean}\n\t\t * @readonly\n\t\t * @default true\n\t\t */\n\t\tthis.isCSS3DSprite = true;\n\n\t\t/**\n\t\t * The sprite's rotation in radians.\n\t\t *\n\t\t * @type {number}\n\t\t * @default 0\n\t\t */\n\t\tthis.rotation2D = 0;\n\n\t}\n\n\tcopy( source, recursive ) {\n\n\t\tsuper.copy( source, recursive );\n\n\t\tthis.rotation2D = source.rotation2D;\n\n\t\treturn this;\n\n\t}\n\n}\n\n//\n\nconst _matrix = new Matrix4();\nconst _matrix2 = new Matrix4();\n\n/**\n * This renderer can be used to apply hierarchical 3D transformations to DOM elements\n * via the CSS3 [transform]{@link https://www.w3schools.com/cssref/css3_pr_transform.asp} property.\n * `CSS3DRenderer` is particularly interesting if you want to apply 3D effects to a website without\n * canvas based rendering. It can also be used in order to combine DOM elements with WebGLcontent.\n *\n * There are, however, some important limitations:\n *\n * - It's not possible to use the material system of *three.js*.\n * - It's also not possible to use geometries.\n * - The renderer only supports 100% browser and display zoom.\n *\n * So `CSS3DRenderer` is just focused on ordinary DOM elements. These elements are wrapped into special\n * 3D objects ({@link CSS3DObject} or {@link CSS3DSprite}) and then added to the scene graph.\n */\nclass CSS3DRenderer {\n\n\t/**\n\t * Constructs a new CSS3D renderer.\n\t *\n\t * @param {CSS3DRenderer~Parameters} [parameters] - The parameters.\n\t */\n\tconstructor( parameters = {} ) {\n\n\t\tconst _this = this;\n\n\t\tlet _width, _height;\n\t\tlet _widthHalf, _heightHalf;\n\n\t\tconst cache = {\n\t\t\tcamera: { style: '' },\n\t\t\tobjects: new WeakMap()\n\t\t};\n\n\t\tconst domElement = parameters.element !== undefined ? parameters.element : document.createElement( 'div' );\n\n\t\tdomElement.style.overflow = 'hidden';\n\n\t\t/**\n\t\t * The DOM where the renderer appends its child-elements.\n\t\t *\n\t\t * @type {DOMElement}\n\t\t */\n\t\tthis.domElement = domElement;\n\n\t\tconst viewElement = document.createElement( 'div' );\n\t\tviewElement.style.transformOrigin = '0 0';\n\t\tviewElement.style.pointerEvents = 'none';\n\t\tdomElement.appendChild( viewElement );\n\n\t\tconst cameraElement = document.createElement( 'div' );\n\n\t\tcameraElement.style.transformStyle = 'preserve-3d';\n\n\t\tviewElement.appendChild( cameraElement );\n\n\t\t/**\n\t\t * Returns an object containing the width and height of the renderer.\n\t\t *\n\t\t * @return {{width:number,height:number}} The size of the renderer.\n\t\t */\n\t\tthis.getSize = function () {\n\n\t\t\treturn {\n\t\t\t\twidth: _width,\n\t\t\t\theight: _height\n\t\t\t};\n\n\t\t};\n\n\t\t/**\n\t\t * Renders the given scene using the given camera.\n\t\t *\n\t\t * @param {Object3D} scene - A scene or any other type of 3D object.\n\t\t * @param {Camera} camera - The camera.\n\t\t */\n\t\tthis.render = function ( scene, camera ) {\n\n\t\t\tconst fov = camera.projectionMatrix.elements[ 5 ] * _heightHalf;\n\n\t\t\tif ( camera.view && camera.view.enabled ) {\n\n\t\t\t\t// view offset\n\t\t\t\tviewElement.style.transform = `translate( ${ - camera.view.offsetX * ( _width / camera.view.width ) }px, ${ - camera.view.offsetY * ( _height / camera.view.height ) }px )`;\n\n\t\t\t\t// view fullWidth and fullHeight, view width and height\n\t\t\t\tviewElement.style.transform += `scale( ${ camera.view.fullWidth / camera.view.width }, ${ camera.view.fullHeight / camera.view.height } )`;\n\n\t\t\t} else {\n\n\t\t\t\tviewElement.style.transform = '';\n\n\t\t\t}\n\n\t\t\tif ( scene.matrixWorldAutoUpdate === true ) scene.updateMatrixWorld();\n\t\t\tif ( camera.parent === null && camera.matrixWorldAutoUpdate === true ) camera.updateMatrixWorld();\n\n\t\t\tlet tx, ty;\n\n\t\t\tif ( camera.isOrthographicCamera ) {\n\n\t\t\t\ttx = - ( camera.right + camera.left ) / 2;\n\t\t\t\tty = ( camera.top + camera.bottom ) / 2;\n\n\t\t\t}\n\n\t\t\tconst scaleByViewOffset = camera.view && camera.view.enabled ? camera.view.height / camera.view.fullHeight : 1;\n\t\t\tconst cameraCSSMatrix = camera.isOrthographicCamera ?\n\t\t\t\t`scale( ${ scaleByViewOffset } )` + 'scale(' + fov + ')' + 'translate(' + epsilon( tx ) + 'px,' + epsilon( ty ) + 'px)' + getCameraCSSMatrix( camera.matrixWorldInverse ) :\n\t\t\t\t`scale( ${ scaleByViewOffset } )` + 'translateZ(' + fov + 'px)' + getCameraCSSMatrix( camera.matrixWorldInverse );\n\t\t\tconst perspective = camera.isPerspectiveCamera ? 'perspective(' + fov + 'px) ' : '';\n\n\t\t\tconst style = perspective + cameraCSSMatrix +\n\t\t\t\t'translate(' + _widthHalf + 'px,' + _heightHalf + 'px)';\n\n\t\t\tif ( cache.camera.style !== style ) {\n\n\t\t\t\tcameraElement.style.transform = style;\n\n\t\t\t\tcache.camera.style = style;\n\n\t\t\t}\n\n\t\t\trenderObject( scene, scene, camera, cameraCSSMatrix );\n\n\t\t};\n\n\t\t/**\n\t\t * Resizes the renderer to the given width and height.\n\t\t *\n\t\t * @param {number} width - The width of the renderer.\n\t\t * @param {number} height - The height of the renderer.\n\t\t */\n\t\tthis.setSize = function ( width, height ) {\n\n\t\t\t_width = width;\n\t\t\t_height = height;\n\t\t\t_widthHalf = _width / 2;\n\t\t\t_heightHalf = _height / 2;\n\n\t\t\tdomElement.style.width = width + 'px';\n\t\t\tdomElement.style.height = height + 'px';\n\n\t\t\tviewElement.style.width = width + 'px';\n\t\t\tviewElement.style.height = height + 'px';\n\n\t\t\tcameraElement.style.width = width + 'px';\n\t\t\tcameraElement.style.height = height + 'px';\n\n\t\t};\n\n\t\tfunction epsilon( value ) {\n\n\t\t\treturn Math.abs( value ) < 1e-10 ? 0 : value;\n\n\t\t}\n\n\t\tfunction getCameraCSSMatrix( matrix ) {\n\n\t\t\tconst elements = matrix.elements;\n\n\t\t\treturn 'matrix3d(' +\n\t\t\t\tepsilon( elements[ 0 ] ) + ',' +\n\t\t\t\tepsilon( - elements[ 1 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 2 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 3 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 4 ] ) + ',' +\n\t\t\t\tepsilon( - elements[ 5 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 6 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 7 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 8 ] ) + ',' +\n\t\t\t\tepsilon( - elements[ 9 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 10 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 11 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 12 ] ) + ',' +\n\t\t\t\tepsilon( - elements[ 13 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 14 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 15 ] ) +\n\t\t\t')';\n\n\t\t}\n\n\t\tfunction getObjectCSSMatrix( matrix ) {\n\n\t\t\tconst elements = matrix.elements;\n\t\t\tconst matrix3d = 'matrix3d(' +\n\t\t\t\tepsilon( elements[ 0 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 1 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 2 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 3 ] ) + ',' +\n\t\t\t\tepsilon( - elements[ 4 ] ) + ',' +\n\t\t\t\tepsilon( - elements[ 5 ] ) + ',' +\n\t\t\t\tepsilon( - elements[ 6 ] ) + ',' +\n\t\t\t\tepsilon( - elements[ 7 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 8 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 9 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 10 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 11 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 12 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 13 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 14 ] ) + ',' +\n\t\t\t\tepsilon( elements[ 15 ] ) +\n\t\t\t')';\n\n\t\t\treturn 'translate(-50%,-50%)' + matrix3d;\n\n\t\t}\n\n\t\tfunction hideObject( object ) {\n\n\t\t\tif ( object.isCSS3DObject ) object.element.style.display = 'none';\n\n\t\t\tfor ( let i = 0, l = object.children.length; i < l; i ++ ) {\n\n\t\t\t\thideObject( object.children[ i ] );\n\n\t\t\t}\n\n\t\t}\n\n\t\tfunction renderObject( object, scene, camera, cameraCSSMatrix ) {\n\n\t\t\tif ( object.visible === false ) {\n\n\t\t\t\thideObject( object );\n\n\t\t\t\treturn;\n\n\t\t\t}\n\n\t\t\tif ( object.isCSS3DObject ) {\n\n\t\t\t\tconst visible = ( object.layers.test( camera.layers ) === true );\n\n\t\t\t\tconst element = object.element;\n\t\t\t\telement.style.display = visible === true ? '' : 'none';\n\n\t\t\t\tif ( visible === true ) {\n\n\t\t\t\t\tobject.onBeforeRender( _this, scene, camera );\n\n\t\t\t\t\tlet style;\n\n\t\t\t\t\tif ( object.isCSS3DSprite ) {\n\n\t\t\t\t\t\t// http://swiftcoder.wordpress.com/2008/11/25/constructing-a-billboard-matrix/\n\n\t\t\t\t\t\t_matrix.copy( camera.matrixWorldInverse );\n\t\t\t\t\t\t_matrix.transpose();\n\n\t\t\t\t\t\tif ( object.rotation2D !== 0 ) _matrix.multiply( _matrix2.makeRotationZ( object.rotation2D ) );\n\n\t\t\t\t\t\tobject.matrixWorld.decompose( _position, _quaternion, _scale );\n\t\t\t\t\t\t_matrix.setPosition( _position );\n\t\t\t\t\t\t_matrix.scale( _scale );\n\n\t\t\t\t\t\t_matrix.elements[ 3 ] = 0;\n\t\t\t\t\t\t_matrix.elements[ 7 ] = 0;\n\t\t\t\t\t\t_matrix.elements[ 11 ] = 0;\n\t\t\t\t\t\t_matrix.elements[ 15 ] = 1;\n\n\t\t\t\t\t\tstyle = getObjectCSSMatrix( _matrix );\n\n\t\t\t\t\t} else {\n\n\t\t\t\t\t\tstyle = getObjectCSSMatrix( object.matrixWorld );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tconst cachedObject = cache.objects.get( object );\n\n\t\t\t\t\tif ( cachedObject === undefined || cachedObject.style !== style ) {\n\n\t\t\t\t\t\telement.style.transform = style;\n\n\t\t\t\t\t\tconst objectData = { style: style };\n\t\t\t\t\t\tcache.objects.set( object, objectData );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tif ( element.parentNode !== cameraElement ) {\n\n\t\t\t\t\t\tcameraElement.appendChild( element );\n\n\t\t\t\t\t}\n\n\t\t\t\t\tobject.onAfterRender( _this, scene, camera );\n\n\t\t\t\t}\n\n\t\t\t}\n\n\t\t\tfor ( let i = 0, l = object.children.length; i < l; i ++ ) {\n\n\t\t\t\trenderObject( object.children[ i ], scene, camera, cameraCSSMatrix );\n\n\t\t\t}\n\n\t\t}\n\n\t}\n\n}\n\n/**\n * Constructor parameters of `CSS3DRenderer`.\n *\n * @typedef {Object} CSS3DRenderer~Parameters\n * @property {DOMElement} [element] - A DOM element where the renderer appends its child-elements.\n * If not passed in here, a new div element will be created.\n **/\n\nexport { CSS3DObject, CSS3DSprite, CSS3DRenderer };\n", "import { PSVError } from '@photo-sphere-viewer/core';\nimport { MarkerConfig } from './model';\n\nexport enum MarkerType {\n    image = 'image',\n    html = 'html',\n    element = 'element',\n    imageLayer = 'imageLayer',\n    videoLayer = 'videoLayer',\n    elementLayer = 'elementLayer',\n    polygon = 'polygon',\n    polygonPixels = 'polygonPixels',\n    polyline = 'polyline',\n    polylinePixels = 'polylinePixels',\n    square = 'square',\n    rect = 'rect',\n    circle = 'circle',\n    ellipse = 'ellipse',\n    path = 'path',\n}\n\n/**\n * Determines the type of a marker by the available properties\n * @throws {@link PSVError} when the marker's type cannot be found\n */\nexport function getMarkerType(config: MarkerConfig, allowNone = false): MarkerType {\n    const found: MarkerType[] = [];\n\n    Object.keys(MarkerType).forEach((type) => {\n        if ((config as any)[type]) {\n            found.push(type as MarkerType);\n        }\n    });\n\n    if (found.length === 0 && !allowNone) {\n        throw new PSVError(`missing marker content, either ${Object.keys(MarkerType).join(', ')}`);\n    } else if (found.length > 1) {\n        throw new PSVError(`multiple marker content, either ${Object.keys(MarkerType).join(', ')}`);\n    }\n\n    return found[0];\n}\n", "import { CONSTANTS, Point, Position, PSVError, utils, type Viewer } from '@photo-sphere-viewer/core';\nimport { MathUtils } from 'three';\nimport { DEFAULT_HOVER_SCALE } from '../constants';\nimport { type MarkersPlugin } from '../MarkersPlugin';\nimport { MarkerType } from '../MarkerType';\nimport { MarkerConfig } from '../model';\nimport { AbstractDomMarker } from './AbstractDomMarker';\nimport { Marker } from './Marker';\n\n/**\n * Base class for standard markers\n * @internal\n */\nexport abstract class AbstractStandardMarker extends AbstractDomMarker {\n    protected needsUpdateSize: boolean;\n\n    constructor(viewer: Viewer, plugin: MarkersPlugin, config: MarkerConfig) {\n        super(viewer, plugin, config);\n    }\n\n    protected override afterCreateElement(): void {\n        super.afterCreateElement();\n\n        this.domElement.addEventListener('transitionend', () => {\n            // the transition \"scale\" is only applied manually on mouseover\n            // because it must not be present when the scale changes on zoom/move\n            this.domElement.style.transition = '';\n        });\n    }\n\n    override render({\n        viewerPosition,\n        zoomLevel,\n        hoveringMarker,\n    }: {\n        viewerPosition: Position;\n        zoomLevel: number;\n        hoveringMarker: Marker;\n    }): Point {\n        this.__updateSize();\n\n        const position = this.viewer.dataHelper.vector3ToViewerCoords(this.state.positions3D[0]);\n        position.x -= this.state.size.width * this.state.anchor.x;\n        position.y -= this.state.size.height * this.state.anchor.y;\n\n        // It tests if the point is in the general direction of the camera, then check if it's in the viewport\n        const isVisible = (\n            this.state.positions3D[0].dot(this.viewer.state.direction) > 0\n            && position.x + this.state.size.width >= 0\n            && position.x - this.state.size.width <= this.viewer.state.size.width\n            && position.y + this.state.size.height >= 0\n            && position.y - this.state.size.height <= this.viewer.state.size.height\n        );\n\n        if (isVisible) {\n            this.domElement.style.translate = `${position.x}px ${position.y}px 0px`;\n\n            this.applyScale({\n                zoomLevel,\n                viewerPosition,\n                mouseover: this === hoveringMarker,\n            });\n\n            return position;\n        } else {\n            return null;\n        }\n    }\n\n    override update(config: MarkerConfig): void {\n        super.update(config);\n\n        if (!utils.isExtendedPosition(this.config.position)) {\n            throw new PSVError(`missing marker ${this.id} position`);\n        }\n\n        // convert texture coordinates to spherical coordinates\n        try {\n            this.state.position = this.viewer.dataHelper.cleanPosition(this.config.position);\n        } catch (e) {\n            throw new PSVError(`invalid marker ${this.id} position`, e);\n        }\n\n        // compute x/y/z position\n        this.state.positions3D = [this.viewer.dataHelper.sphericalCoordsToVector3(this.state.position)];\n\n        const element = this.domElement;\n\n        element.classList.add('psv-marker--normal');\n\n        if (this.config.scale && Array.isArray(this.config.scale)) {\n            this.config.scale = { zoom: this.config.scale as any };\n        }\n        if (typeof this.config.hoverScale === 'boolean') {\n            this.config.hoverScale = this.config.hoverScale\n                ? this.plugin.config.defaultHoverScale || DEFAULT_HOVER_SCALE\n                : null;\n        } else if (typeof this.config.hoverScale === 'number') {\n            this.config.hoverScale = { amount: this.config.hoverScale } as any;\n        } else if (!this.config.hoverScale) {\n            this.config.hoverScale = this.plugin.config.defaultHoverScale;\n        }\n        if (this.config.hoverScale) {\n            this.config.hoverScale = {\n                ...this.plugin.config.defaultHoverScale,\n                ...this.config.hoverScale,\n            };\n        }\n\n        // set rotation\n        element.style.rotate = this.config.rotation.roll !== 0 ? MathUtils.radToDeg(this.config.rotation.roll) + 'deg' : null;\n\n        // set anchor\n        element.style.transformOrigin = `${this.state.anchor.x * 100}% ${this.state.anchor.y * 100}%`;\n    }\n\n    /**\n     * Computes the real size of a marker\n     * @description This is done by removing all it's transformations (if any) and making it visible\n     * before querying its bounding rect\n     */\n    private __updateSize() {\n        if (!this.needsUpdateSize) {\n            return;\n        }\n\n        const element = this.domElement;\n        const makeTransparent = !this.state.visible || !this.state.size;\n\n        if (makeTransparent) {\n            element.classList.add('psv-marker--transparent');\n        }\n\n        if (this.isSvg()) {\n            const rect = (element.firstElementChild as SVGElement).getBoundingClientRect();\n            this.state.size = {\n                width: rect.width,\n                height: rect.height,\n            };\n        } else {\n            this.state.size = {\n                width: (element as HTMLElement).offsetWidth,\n                height: (element as HTMLElement).offsetHeight,\n            };\n        }\n\n        if (makeTransparent) {\n            element.classList.remove('psv-marker--transparent');\n        }\n\n        if (this.isSvg()) {\n            // the real size must be declared on the SVG root\n            element.style.width = this.state.size.width + 'px';\n            element.style.height = this.state.size.height + 'px';\n        }\n\n        // custom element HTML marker remain dynamic\n        if (this.type !== MarkerType.element) {\n            this.needsUpdateSize = false;\n        }\n    }\n\n    /**\n     * Computes and applies the scale to the marker\n     */\n    applyScale({\n        zoomLevel,\n        viewerPosition,\n        mouseover,\n    }: {\n        zoomLevel: number;\n        viewerPosition: Position;\n        mouseover: boolean;\n    }) {\n        if (mouseover !== null && this.config.hoverScale) {\n            this.domElement.style.transition = `scale ${this.config.hoverScale.duration}ms ${this.config.hoverScale.easing}`;\n        }\n\n        let scale = 1;\n        if (typeof this.config.scale === 'function') {\n            scale = this.config.scale(zoomLevel, viewerPosition);\n        } else if (this.config.scale) {\n            if (Array.isArray(this.config.scale.zoom)) {\n                const [min, max] = this.config.scale.zoom;\n                scale *= min + (max - min) * CONSTANTS.EASINGS.inQuad(zoomLevel / 100);\n            }\n            if (Array.isArray(this.config.scale.yaw)) {\n                const [min, max] = this.config.scale.yaw;\n                const halfFov = MathUtils.degToRad(this.viewer.state.hFov) / 2;\n                const arc = Math.abs(utils.getShortestArc(this.state.position.yaw, viewerPosition.yaw));\n                scale *= max + (min - max) * CONSTANTS.EASINGS.outQuad(Math.max(0, (halfFov - arc) / halfFov));\n            }\n        }\n        if (mouseover && this.config.hoverScale) {\n            scale *= this.config.hoverScale.amount;\n        }\n\n        this.domElement.style.scale = `${scale}`;\n    }\n}\n", "import { utils, type Viewer } from '@photo-sphere-viewer/core';\nimport { MARKER_DATA } from '../constants';\nimport { type MarkersPlugin } from '../MarkersPlugin';\nimport { MarkerConfig } from '../model';\nimport { Marker } from './Marker';\n\n/**\n * Base class for markers added in the DOM\n * @internal\n */\nexport abstract class AbstractDomMarker extends Marker {\n    override get domElement(): HTMLElement | SVGElement {\n        return this.element;\n    }\n\n    constructor(viewer: Viewer, plugin: MarkersPlugin, config: MarkerConfig) {\n        super(viewer, plugin, config);\n    }\n\n    protected afterCreateElement(): void {\n        this.element[MARKER_DATA] = this;\n    }\n\n    override destroy(): void {\n        delete this.element[MARKER_DATA];\n\n        super.destroy();\n    }\n\n    override update(config: MarkerConfig): void {\n        super.update(config);\n\n        const element = this.domElement;\n\n        element.id = `psv-marker-${this.config.id}`;\n\n        // reset CSS class\n        element.setAttribute('class', 'psv-marker');\n        if (this.state.visible) {\n            element.classList.add('psv-marker--visible');\n        }\n        if (this.config.tooltip) {\n            element.classList.add('psv-marker--has-tooltip');\n        }\n        if (this.config.content) {\n            element.classList.add('psv-marker--has-content');\n        }\n        if (this.config.className) {\n            utils.addClasses(element, this.config.className);\n        }\n\n        // apply style\n        element.style.opacity = `${this.config.opacity}`;\n        element.style.zIndex = `${30 + this.config.zIndex}`; // 30 is the base z-index in the stylesheet\n        if (this.config.style) {\n            Object.assign(element.style, this.config.style);\n        }\n    }\n}\n", "import { PSVE<PERSON>r, Point, Position, Size, Tooltip, TooltipConfig, utils, type Viewer } from '@photo-sphere-viewer/core';\nimport { Object3D, Vector3 } from 'three';\nimport { MarkerType, getMarkerType } from '../MarkerType';\nimport { type MarkersPlugin } from '../MarkersPlugin';\nimport { MarkerConfig, ParsedMarkerConfig } from '../model';\n\n/**\n * Base class for all markers\n */\nexport abstract class Marker {\n    readonly type: MarkerType;\n\n    protected element: any;\n\n    /**\n     * The final description of the marker. Either text content, image, url, SVG attributes, etc.\n     */\n    definition: any;\n\n    /** @internal */\n    tooltip?: Tooltip;\n\n    config: ParsedMarkerConfig;\n\n    get id(): string {\n        return this.config.id;\n    }\n\n    get data(): any {\n        return this.config.data;\n    }\n\n    get domElement(): HTMLElement | SVGElement {\n        return null;\n    }\n\n    get threeElement(): Object3D {\n        return null;\n    }\n\n    get video(): HTMLVideoElement {\n        return null;\n    }\n\n    /** @internal */\n    readonly state = {\n        anchor: null as Point,\n        visible: false,\n        staticTooltip: false,\n        position: null as Position,\n        position2D: null as Point,\n        positions3D: null as Vector3[],\n        size: null as Size,\n    };\n\n    constructor(\n        protected viewer: Viewer,\n        protected plugin: MarkersPlugin,\n        config: MarkerConfig,\n    ) {\n        if (!config.id) {\n            throw new PSVError('missing marker id');\n        }\n\n        this.type = getMarkerType(config);\n        this.createElement();\n        this.update(config);\n    }\n\n    /**\n     * @internal\n     */\n    abstract createElement(): void;\n\n    /**\n     * @internal\n     * Returns the 2D position if the marker is visible\n     */\n    abstract render(params: {\n        viewerPosition: Position;\n        zoomLevel: number;\n        hoveringMarker: Marker;\n    }): Point;\n\n    /**\n     * @internal\n     */\n    destroy() {\n        delete this.viewer;\n        delete this.plugin;\n        delete this.element;\n\n        this.hideTooltip();\n    }\n\n    /**\n     * Checks if it is a 3D marker (imageLayer, videoLayer)\n     */\n    is3d(): boolean {\n        return false;\n    }\n\n    /**\n     * Checks if it is a normal marker (image, html, element)\n     */\n    isNormal(): boolean {\n        return false;\n    }\n\n    /**\n     * Checks if it is a polygon/polyline marker\n     */\n    isPoly(): boolean {\n        return false;\n    }\n\n    /**\n     * Checks if it is an SVG marker\n     */\n    isSvg(): boolean {\n        return false;\n    }\n\n    /**\n     * Checks if it is an CSS3D marker\n     */\n    isCss3d(): boolean {\n        return false;\n    }\n\n    /**\n     * Updates the marker with new properties\n     * @throws {@link PSVError} if the configuration is invalid\n     * @internal\n     */\n    update(config: MarkerConfig) {\n        const newType = getMarkerType(config, true);\n\n        if (newType !== undefined && newType !== this.type) {\n            throw new PSVError(`cannot change marker ${config.id} type`);\n        }\n\n        this.config = utils.deepmerge(this.config, config as any);\n        if (typeof this.config.tooltip === 'string') {\n            this.config.tooltip = { content: this.config.tooltip };\n        }\n        if (this.config.tooltip && !this.config.tooltip.trigger) {\n            this.config.tooltip.trigger = 'hover';\n        }\n        if (utils.isNil(this.config.visible)) {\n            this.config.visible = true;\n        }\n        if (utils.isNil(this.config.zIndex)) {\n            this.config.zIndex = 1;\n        }\n        if (utils.isNil(this.config.opacity)) {\n            this.config.opacity = 1;\n        }\n\n        if (this.config.rotation) {\n            const rot = this.config.rotation;\n            if (typeof rot === 'object') {\n                this.config.rotation = {\n                    yaw: rot.yaw ? utils.parseAngle(rot.yaw, true, false) : 0,\n                    pitch: rot.pitch ? utils.parseAngle(rot.pitch, true, false) : 0,\n                    roll: rot.roll ? utils.parseAngle(rot.roll, true, false) : 0,\n                };\n            } else {\n                this.config.rotation = {\n                    yaw: 0,\n                    pitch: 0,\n                    roll: utils.parseAngle(rot, true, false),\n                };\n            }\n        } else {\n            this.config.rotation = { yaw: 0, pitch: 0, roll: 0 };\n        }\n\n        this.state.anchor = utils.parsePoint(this.config.anchor);\n    }\n\n    /**\n     * Returns the markers list content for the marker, it can be either :\n     * - the `listContent`\n     * - the `tooltip`\n     * - the `html`\n     * - the `id`\n     * @internal\n     */\n    getListContent(): string {\n        if (this.config.listContent) {\n            return this.config.listContent;\n        } else if (this.config.tooltip?.content) {\n            return this.config.tooltip.content;\n        } else if (this.config.html) {\n            return this.config.html;\n        } else {\n            return this.id;\n        }\n    }\n\n    /**\n     * Display the tooltip of this marker\n     * @internal\n     */\n    showTooltip(clientX?: number, clientY?: number, forceUpdate = false) {\n        if (this.state.visible && this.config.tooltip?.content && this.state.position2D) {\n            const config: TooltipConfig = {\n                ...this.config.tooltip,\n                style: {\n                    // prevents conflicts with tooltip tracking\n                    pointerEvents: this.state.staticTooltip ? 'auto' : 'none',\n                },\n                data: this,\n                top: 0,\n                left: 0,\n            };\n\n            if (this.isPoly() || this.is3d() || this.isCss3d()) {\n                if (clientX || clientY) {\n                    const viewerPos = utils.getPosition(this.viewer.container);\n                    config.top = clientY - viewerPos.y + 10;\n                    config.left = clientX - viewerPos.x;\n                    config.box = {\n                        // separate the tooltip from the cursor\n                        width: 20,\n                        height: 20,\n                    };\n                } else {\n                    config.top = this.state.position2D.y;\n                    config.left = this.state.position2D.x;\n                }\n            } else {\n                // note: state.position2D already has the anchor applied with the default size\n                const position = this.viewer.dataHelper.vector3ToViewerCoords(this.state.positions3D[0]);\n                let width = this.state.size.width;\n                let height = this.state.size.height;\n\n                // only apply scaling for \"temporary\" tooltips\n                if (this.config.hoverScale && !this.state.staticTooltip) {\n                    width *= this.config.hoverScale.amount;\n                    height *= this.config.hoverScale.amount;\n                }\n\n                config.top = position.y - height * this.state.anchor.y + height / 2;\n                config.left = position.x - width * this.state.anchor.x + width / 2;\n                config.box = { width, height };\n            }\n\n            if (this.tooltip) {\n                if (forceUpdate) {\n                    this.tooltip.update(this.config.tooltip.content, config);\n                } else {\n                    this.tooltip.move(config);\n                }\n            } else {\n                this.tooltip = this.viewer.createTooltip(config);\n            }\n        }\n    }\n\n    /**\n     * Hides the tooltip of this marker\n     * @internal\n     */\n    hideTooltip() {\n        if (this.tooltip) {\n            this.tooltip.hide();\n            this.tooltip = null;\n        }\n    }\n}\n", "import { ExtendedPosition, PSVError, Point, Position, Size, utils, type Viewer } from '@photo-sphere-viewer/core';\nimport {\n    Group,\n    Mesh,\n    Object3D,\n    PlaneGeometry,\n    Texture,\n    Vector3,\n    VideoTexture,\n} from 'three';\nimport { ChromaKeyMaterial } from '../../../shared/ChromaKeyMaterial';\nimport { createVideo } from '../../../shared/video-utils';\nimport { MarkerType } from '../MarkerType';\nimport { type MarkersPlugin } from '../MarkersPlugin';\nimport { MARKER_DATA } from '../constants';\nimport { MarkerConfig } from '../model';\nimport { getPolygonCenter } from '../utils';\nimport { Marker } from './Marker';\n\n/**\n * @internal\n */\nexport class Marker3D extends Marker {\n    override get threeElement(): Group {\n        return this.element;\n    }\n\n    get threeMesh(): Mesh<PlaneGeometry, ChromaKeyMaterial> {\n        return this.threeElement.children[0] as any;\n    }\n\n    override get video(): HTMLVideoElement {\n        if (this.type === MarkerType.videoLayer) {\n            return this.threeMesh.material.map.image;\n        } else {\n            return null;\n        }\n    }\n\n    constructor(viewer: Viewer, plugin: MarkersPlugin, config: MarkerConfig) {\n        super(viewer, plugin, config);\n    }\n\n    override is3d(): boolean {\n        return true;\n    }\n\n    override createElement(): void {\n        const material = new ChromaKeyMaterial({ alpha: 0 });\n        const geometry = new PlaneGeometry(1, 1);\n        const mesh = new Mesh(geometry, material);\n        mesh.userData = { [MARKER_DATA]: this };\n\n        // overwrite the visible property to be tied to the Marker instance\n        // and do it without context bleed\n        Object.defineProperty(mesh, 'visible', {\n            enumerable: true,\n            get: function (this: Object3D) {\n                return (this.userData[MARKER_DATA] as Marker).config.visible;\n            },\n            set: function (this: Object3D, visible: boolean) {\n                (this.userData[MARKER_DATA] as Marker).config.visible = visible;\n            },\n        });\n\n        this.element = new Group().add(mesh);\n\n        if (this.type === MarkerType.videoLayer) {\n            this.viewer.needsContinuousUpdate(true);\n        }\n    }\n\n    override destroy(): void {\n        delete this.threeMesh.userData[MARKER_DATA];\n\n        if (this.type === MarkerType.videoLayer) {\n            this.video.pause();\n            this.viewer.needsContinuousUpdate(false);\n        }\n\n        super.destroy();\n    }\n\n    override render(): Point {\n        if (this.viewer.renderer.isObjectVisible(this.threeMesh)) {\n            return this.viewer.dataHelper.sphericalCoordsToViewerCoords(this.state.position);\n        } else {\n            return null;\n        }\n    }\n\n    override update(config: MarkerConfig): void {\n        super.update(config);\n\n        const mesh = this.threeMesh;\n        const group = mesh.parent;\n        const material = mesh.material;\n\n        if (utils.isExtendedPosition(this.config.position)) {\n            try {\n                this.state.position = this.viewer.dataHelper.cleanPosition(this.config.position);\n            } catch (e) {\n                throw new PSVError(`invalid marker ${this.id} position`, e);\n            }\n\n            if (!this.config.size) {\n                throw new PSVError(`missing marker ${this.id} size`);\n            }\n\n            this.state.size = this.config.size;\n\n            // 100 is magic number that gives a coherent size at default zoom level\n            mesh.scale.set(this.config.size.width / 100, this.config.size.height / 100, 1);\n            mesh.position.set(mesh.scale.x * (0.5 - this.state.anchor.x), mesh.scale.y * (this.state.anchor.y - 0.5), 0);\n            mesh.rotation.set(0, 0, 0);\n            this.viewer.dataHelper.sphericalCoordsToVector3(this.state.position, group.position);\n\n            group.lookAt(0, group.position.y, 0);\n            mesh.rotateY(-this.config.rotation.yaw);\n            mesh.rotateX(-this.config.rotation.pitch);\n            mesh.rotateZ(-this.config.rotation.roll);\n\n            const p = mesh.geometry.getAttribute('position');\n            this.state.positions3D = [0, 1, 3, 2].map((i) => {\n                const v3 = new Vector3();\n                v3.fromBufferAttribute(p, i);\n                return mesh.localToWorld(v3);\n            });\n        } else {\n            if (this.config.position?.length !== 4) {\n                throw new PSVError(`missing marker ${this.id} position`);\n            }\n\n            let positions: Position[];\n            try {\n                positions = this.config.position.map(p => this.viewer.dataHelper.cleanPosition(p));\n            } catch (e) {\n                throw new PSVError(`invalid marker ${this.id} position`, e);\n            }\n\n            const positions3D = positions.map(p => this.viewer.dataHelper.sphericalCoordsToVector3(p));\n\n            const centroid = getPolygonCenter(positions.map(({ yaw, pitch }) => [yaw, pitch]));\n            this.state.position = { yaw: centroid[0], pitch: centroid[1] };\n\n            this.state.positions3D = positions3D;\n\n            const p = mesh.geometry.getAttribute('position');\n            [\n                positions3D[0],\n                positions3D[1],\n                positions3D[3], // not a mistake!\n                positions3D[2],\n            ].forEach((v, i) => {\n                p.setX(i, v.x);\n                p.setY(i, v.y);\n                p.setZ(i, v.z);\n            });\n            p.needsUpdate = true;\n\n            this.__setTextureWrap(material);\n        }\n\n        switch (this.type) {\n            case MarkerType.videoLayer:\n                if (this.definition !== this.config.videoLayer) {\n                    material.map?.dispose();\n\n                    const video = createVideo({\n                        src: this.config.videoLayer,\n                        withCredentials: this.viewer.config.withCredentials,\n                        muted: true,\n                        autoplay: this.config.autoplay ?? true,\n                    });\n\n                    const texture = new VideoTexture(video);\n                    material.map = texture;\n                    material.alpha = 0;\n\n                    video.addEventListener('loadedmetadata', () => {\n                        if (!this.viewer) {\n                            return; // the marker has been removed\n                        }\n\n                        material.alpha = this.config.opacity;\n\n                        if (!utils.isExtendedPosition(this.config.position)) {\n                            mesh.material.userData[MARKER_DATA] = { width: video.videoWidth, height: video.videoHeight };\n                            this.__setTextureWrap(material);\n                        }\n                    }, { once: true });\n\n                    if (video.autoplay) {\n                        video.play();\n                    }\n\n                    this.definition = this.config.videoLayer;\n                } else {\n                    material.alpha = this.config.opacity;\n                }\n                break;\n\n            case MarkerType.imageLayer:\n                if (this.definition !== this.config.imageLayer) {\n                    material.map?.dispose();\n\n                    const texture = new Texture();\n                    material.map = texture;\n                    material.alpha = 0;\n\n                    this.viewer.textureLoader.loadImage(this.config.imageLayer).then((image) => {\n                        if (!this.viewer) {\n                            return; // the marker has been removed\n                        }\n\n                        if (!utils.isExtendedPosition(this.config.position)) {\n                            mesh.material.userData[MARKER_DATA] = { width: image.width, height: image.height };\n                            this.__setTextureWrap(material);\n                        }\n\n                        texture.image = image;\n                        texture.anisotropy = 4;\n                        texture.needsUpdate = true;\n                        material.alpha = this.config.opacity;\n\n                        this.viewer.needsUpdate();\n                    });\n\n                    this.definition = this.config.imageLayer;\n                } else {\n                    material.alpha = this.config.opacity;\n                }\n                break;\n\n            // no default\n        }\n\n        material.chromaKey = this.config.chromaKey;\n        mesh.renderOrder = 1000 + this.config.zIndex;\n        mesh.geometry.boundingBox = null; // reset box for Renderer.isObjectVisible\n    }\n\n    /**\n     * For layers positionned by corners, applies offset to the texture in order to keep its proportions\n     */\n    private __setTextureWrap(material: ChromaKeyMaterial) {\n        const imageSize: Size = material.userData[MARKER_DATA];\n\n        if (!imageSize || !imageSize.height || !imageSize.width) {\n            material.repeat.set(1, 1);\n            material.offset.set(0, 0);\n            return;\n        }\n\n        const positions = (this.config.position as ExtendedPosition[]).map((p) => {\n            return this.viewer.dataHelper.cleanPosition(p);\n        });\n\n        const w1 = utils.greatArcDistance(\n            [positions[0].yaw, positions[0].pitch],\n            [positions[1].yaw, positions[1].pitch],\n        );\n        const w2 = utils.greatArcDistance(\n            [positions[3].yaw, positions[3].pitch],\n            [positions[2].yaw, positions[2].pitch],\n        );\n        const h1 = utils.greatArcDistance(\n            [positions[1].yaw, positions[1].pitch],\n            [positions[2].yaw, positions[2].pitch],\n        );\n        const h2 = utils.greatArcDistance(\n            [positions[0].yaw, positions[0].pitch],\n            [positions[3].yaw, positions[3].pitch],\n        );\n\n        const layerRatio = (w1 + w2) / (h1 + h2);\n        const imageRatio = imageSize.width / imageSize.height;\n\n        let hMargin = 0;\n        let vMargin = 0;\n        if (layerRatio < imageRatio) {\n            hMargin = imageRatio - layerRatio;\n        } else {\n            vMargin = 1 / imageRatio - 1 / layerRatio;\n        }\n\n        material.repeat.set(1 - hMargin, 1 - vMargin);\n        material.offset.set(hMargin / 2, vMargin / 2);\n    }\n}\n", "import { Color, ColorRepresentation, ShaderMaterial, Texture, Vector2 } from 'three';\nimport chromaKeyFragment from './shaders/chromaKey.fragment.glsl';\nimport chromaKeyVertex from './shaders/chromaKey.vertex.glsl';\n\ntype ShaderUniforms = {\n    map: { value: Texture };\n    repeat: { value: Vector2 };\n    offset: { value: Vector2 };\n    alpha: { value: number };\n    keying: { value: boolean };\n    color: { value: Color };\n    similarity: { value: number };\n    smoothness: { value: number };\n    spill: { value: number };\n};\n\ntype ChromaKey = {\n    /** @default false */\n    enabled: boolean;\n    /** @default 0x00ff00 */\n    color?: ColorRepresentation | { r: number; g: number; b: number };\n    /** @default 0.2 */\n    similarity?: number;\n    /** @default 0.2 */\n    smoothness?: number;\n};\n\nexport class ChromaKeyMaterial extends ShaderMaterial {\n    override uniforms: ShaderUniforms;\n\n    get map(): Texture {\n        return this.uniforms.map.value;\n    }\n\n    set map(map: Texture) {\n        this.uniforms.map.value = map;\n    }\n\n    set alpha(alpha: number) {\n        this.uniforms.alpha.value = alpha;\n    }\n\n    get offset(): Vector2 {\n        return this.uniforms.offset.value;\n    }\n\n    get repeat(): Vector2 {\n        return this.uniforms.repeat.value;\n    }\n\n    set chromaKey(chromaKey: ChromaKey) {\n        this.uniforms.keying.value = chromaKey?.enabled === true;\n        if (chromaKey?.enabled) {\n            if (typeof chromaKey.color === 'object' && 'r' in chromaKey.color) {\n                this.uniforms.color.value.set(\n                    chromaKey.color.r / 255,\n                    chromaKey.color.g / 255,\n                    chromaKey.color.b / 255,\n                );\n            } else {\n                this.uniforms.color.value.set(chromaKey.color ?? 0x00ff00);\n            }\n            this.uniforms.similarity.value = chromaKey.similarity ?? 0.2;\n            this.uniforms.smoothness.value = chromaKey.smoothness ?? 0.2;\n        }\n    }\n\n    constructor(params?: {\n        map?: Texture;\n        alpha?: number;\n        chromaKey?: ChromaKey;\n    }) {\n        super({\n            transparent: true,\n            depthTest: false,\n            depthWrite: false,\n            uniforms: {\n                map: { value: params?.map },\n                repeat: { value: new Vector2(1, 1) },\n                offset: { value: new Vector2(0, 0) },\n                alpha: { value: params?.alpha ?? 1 },\n                keying: { value: false },\n                color: { value: new Color(0x00ff00) },\n                similarity: { value: 0.2 },\n                smoothness: { value: 0.2 },\n                spill: { value: 0.1 },\n            } satisfies ShaderUniforms,\n            vertexShader: chromaKeyVertex,\n            fragmentShader: chromaKeyFragment,\n        });\n\n        this.chromaKey = params?.chromaKey;\n    }\n}\n", "// https://www.8thwall.com/playground/chromakey-threejs\n\nuniform sampler2D map;\nuniform float alpha;\nuniform bool keying;\nuniform vec3 color;\nuniform float similarity;\nuniform float smoothness;\nuniform float spill;\n\nvarying vec2 vUv;\n\nvec2 RGBtoUV(vec3 rgb) {\n    return vec2(\n        rgb.r * -0.169 + rgb.g * -0.331 + rgb.b *  0.5    + 0.5,\n        rgb.r *  0.5   + rgb.g * -0.419 + rgb.b * -0.081  + 0.5\n    );\n}\n\nvoid main(void) {\n    gl_FragColor = texture2D(map, vUv);\n\n    if (keying) {\n        float chromaDist = distance(RGBtoUV(gl_FragColor.rgb), RGBtoUV(color));\n\n        float baseMask = chromaDist - similarity;\n        float fullMask = pow(clamp(baseMask / smoothness, 0., 1.), 1.5);\n        gl_FragColor.a *= fullMask * alpha;\n\n        float spillVal = pow(clamp(baseMask / spill, 0., 1.), 1.5);\n        float desat = clamp(gl_FragColor.r * 0.2126 + gl_FragColor.g * 0.7152 + gl_FragColor.b * 0.0722, 0., 1.);\n        gl_FragColor.rgb = mix(vec3(desat, desat, desat), gl_FragColor.rgb, spillVal);\n    } else {\n        gl_FragColor.a *= alpha;\n    }\n}\n", "varying vec2 vUv;\nuniform vec2 repeat;\nuniform vec2 offset;\n\nvoid main() {\n    vUv = uv * repeat + offset;\n    gl_Position = projectionMatrix *  modelViewMatrix * vec4( position, 1.0 );\n}\n", "/**\n * Create a standard video element\n */\nexport function createVideo({\n    src,\n    withCredentials,\n    muted,\n    autoplay,\n}: {\n    src: string | MediaStream;\n    withCredentials: boolean;\n    muted: boolean;\n    autoplay: boolean;\n}): HTMLVideoElement {\n    const video = document.createElement('video');\n    video.crossOrigin = withCredentials ? 'use-credentials' : 'anonymous';\n    video.loop = true;\n    video.playsInline = true;\n    video.autoplay = autoplay;\n    video.muted = muted;\n    video.preload = 'metadata';\n    if (src instanceof MediaStream) {\n        video.srcObject = src;\n    } else {\n        video.src = src;\n    }\n    return video;\n}\n", "import { CONSTANTS, utils } from '@photo-sphere-viewer/core';\nimport { Vector3 } from 'three';\n\n/**\n * Returns intermediary point between two points on the sphere\n * {@link http://www.movable-type.co.uk/scripts/latlong.html}\n */\nfunction greatArcIntermediaryPoint(p1: [number, number], p2: [number, number], f: number): [number, number] {\n    const [λ1, φ1] = p1;\n    const [λ2, φ2] = p2;\n\n    // note: \"r\" should be the angular distance, see \"intermediatePointTo\" in the above article\n    // but \"greatArcDistance\" gives identiqual results up to 0.00001 radians and is faster\n    const r = utils.greatArcDistance(p1, p2);\n    const a = Math.sin((1 - f) * r) / Math.sin(r);\n    const b = Math.sin(f * r) / Math.sin(r);\n    const x = a * Math.cos(φ1) * Math.cos(λ1) + b * Math.cos(φ2) * Math.cos(λ2);\n    const y = a * Math.cos(φ1) * Math.sin(λ1) + b * Math.cos(φ2) * Math.sin(λ2);\n    const z = a * Math.sin(φ1) + b * Math.sin(φ2);\n\n    return [Math.atan2(y, x), Math.atan2(z, Math.sqrt(x * x + y * y))];\n}\n\n/**\n * Given a list of spherical points, offsets yaws in order to have only coutinuous values\n * eg: [0.2, 6.08] is transformed to [0.2, -0.2]\n */\nfunction getPolygonCoherentPoints(points: Array<[number, number]>) {\n    const workPoints = [points[0]];\n\n    let k = 0;\n    for (let i = 1; i < points.length; i++) {\n        const d = points[i - 1][0] - points[i][0];\n        if (d > Math.PI) {\n            // crossed the origin left to right\n            k += 1;\n        } else if (d < -Math.PI) {\n            // crossed the origin right to left\n            k -= 1;\n        }\n        workPoints.push([points[i][0] + k * 2 * Math.PI, points[i][1]]);\n    }\n\n    return workPoints;\n}\n\n/**\n * Computes the center point of a polygon\n * @todo Get \"visual center\" (https://blog.mapbox.com/a-new-algorithm-for-finding-a-visual-center-of-a-polygon-7c77e6492fbc)\n * @internal\n */\nexport function getPolygonCenter(polygon: Array<[number, number]>): [number, number] {\n    const points = getPolygonCoherentPoints(polygon);\n\n    const sum = points.reduce((intermediary, point) => [intermediary[0] + point[0], intermediary[1] + point[1]]);\n    return [utils.parseAngle(sum[0] / polygon.length), sum[1] / polygon.length];\n}\n\n/**\n * Computes the middle point of a polyline\n * @internal\n */\nexport function getPolylineCenter(polyline: Array<[number, number]>): [number, number] {\n    const points = getPolygonCoherentPoints(polyline);\n\n    // compute each segment length + total length\n    let length = 0;\n    const lengths = [];\n\n    for (let i = 0; i < points.length - 1; i++) {\n        const l = utils.greatArcDistance(points[i], points[i + 1]) * CONSTANTS.SPHERE_RADIUS;\n\n        lengths.push(l);\n        length += l;\n    }\n\n    // iterate until length / 2\n    let consumed = 0;\n\n    for (let j = 0; j < points.length - 1; j++) {\n        // once the segment containing the middle point is found, computes the intermediary point\n        if (consumed + lengths[j] > length / 2) {\n            const r = (length / 2 - consumed) / lengths[j];\n            return greatArcIntermediaryPoint(points[j], points[j + 1], r);\n        }\n\n        consumed += lengths[j];\n    }\n\n    // this never happens\n    return points[Math.round(points.length / 2)];\n}\n\nconst C = new Vector3();\nconst N = new Vector3();\nconst V = new Vector3();\nconst X = new Vector3();\nconst Y = new Vector3();\nconst A = new Vector3();\n\n/**\n * Given one point in the same direction of the camera and one point behind the camera,\n * computes an intermediary point on the great circle delimiting the half sphere visible by the camera.\n * The point is shifted by .01 rad because the projector cannot handle points exactly on this circle.\n * @todo : does not work with fisheye view (must not use the great circle)\n * @link http://math.stackexchange.com/a/1730410/327208\n */\nexport function getGreatCircleIntersection(P1: Vector3, P2: Vector3, direction: Vector3): Vector3 {\n    C.copy(direction).normalize();\n    N.crossVectors(P1, P2).normalize();\n    V.crossVectors(N, P1).normalize();\n    X.copy(P1).multiplyScalar(-C.dot(V));\n    Y.copy(V).multiplyScalar(C.dot(P1));\n    const H = new Vector3().addVectors(X, Y).normalize();\n    A.crossVectors(H, C);\n    return H.applyAxisAngle(A, 0.01).multiplyScalar(CONSTANTS.SPHERE_RADIUS);\n}\n", "import { PSVError, Point, Position, utils, type Viewer } from '@photo-sphere-viewer/core';\nimport { Object3D } from 'three';\nimport { CSS3DObject } from 'three/examples/jsm/renderers/CSS3DRenderer.js';\nimport { type MarkersPlugin } from '../MarkersPlugin';\nimport { MARKER_DATA } from '../constants';\nimport { MarkerConfig } from '../model';\nimport { AbstractDomMarker } from './AbstractDomMarker';\nimport { Marker } from './Marker';\n\n/**\n * @internal\n */\nexport class MarkerCSS3D extends AbstractDomMarker {\n    private object: CSS3DObject;\n\n    /**\n     * @internal\n     */\n    viewportIntersection = false;\n\n    override get threeElement() {\n        return this.object;\n    }\n\n    constructor(viewer: Viewer, plugin: MarkersPlugin, config: MarkerConfig) {\n        super(viewer, plugin, config);\n    }\n\n    override isCss3d(): boolean {\n        return true;\n    }\n\n    override createElement(): void {\n        this.element = document.createElement('div');\n\n        this.object = new CSS3DObject(this.element);\n        this.object.userData = { [MARKER_DATA]: this };\n\n        // overwrite the visible property to be tied to the Marker instance\n        // and do it without context bleed\n        Object.defineProperty(this.object, 'visible', {\n            enumerable: true,\n            get: function (this: Object3D) {\n                return (this.userData[MARKER_DATA] as Marker).config.visible;\n            },\n            set: function (this: Object3D, visible: boolean) {\n                (this.userData[MARKER_DATA] as Marker).config.visible = visible;\n            },\n        });\n\n        this.afterCreateElement();\n    }\n\n    override destroy(): void {\n        delete this.object.userData[MARKER_DATA];\n        delete this.object;\n\n        super.destroy();\n    }\n\n    override render({\n        viewerPosition,\n        zoomLevel,\n    }: {\n        viewerPosition: Position;\n        zoomLevel: number;\n    }): Point {\n        const element = this.domElement;\n\n        this.state.size = {\n            width: (element as HTMLElement).offsetWidth,\n            height: (element as HTMLElement).offsetHeight,\n        };\n\n        const isVisible = this.state.positions3D[0].dot(this.viewer.state.direction) > 0 && this.viewportIntersection;\n\n        if (isVisible) {\n            const position = this.viewer.dataHelper.sphericalCoordsToViewerCoords(this.state.position);\n\n            this.config.elementLayer.updateMarker?.({\n                marker: this,\n                position,\n                viewerPosition,\n                zoomLevel,\n                viewerSize: this.viewer.state.size,\n            });\n\n            return position;\n        } else {\n            return null;\n        }\n    }\n\n    override update(config: MarkerConfig): void {\n        super.update(config);\n\n        if (!utils.isExtendedPosition(this.config.position)) {\n            throw new PSVError(`missing marker ${this.id} position`);\n        }\n\n        // convert texture coordinates to spherical coordinates\n        try {\n            this.state.position = this.viewer.dataHelper.cleanPosition(this.config.position);\n        } catch (e) {\n            throw new PSVError(`invalid marker ${this.id} position`, e);\n        }\n\n        // compute x/y/z position\n        this.state.positions3D = [this.viewer.dataHelper.sphericalCoordsToVector3(this.state.position)];\n\n        const object = this.threeElement;\n        const element = this.domElement;\n\n        element.classList.add('psv-marker--css3d');\n\n        element.childNodes.forEach(n => n.remove());\n        element.appendChild(this.config.elementLayer);\n        this.config.elementLayer.style.display = 'block';\n\n        object.position.copy(this.state.positions3D[0]).multiplyScalar(100);\n        object.lookAt(0, this.state.positions3D[0].y * 100, 0);\n        object.rotateY(-this.config.rotation.yaw);\n        object.rotateX(-this.config.rotation.pitch);\n        object.rotateZ(-this.config.rotation.roll);\n    }\n}\n", "import { PSVError, Point, Position, type Viewer } from '@photo-sphere-viewer/core';\nimport { MarkerType } from '../MarkerType';\nimport { type MarkersPlugin } from '../MarkersPlugin';\nimport { MarkerConfig } from '../model';\nimport { AbstractStandardMarker } from './AbstractStandardMarker';\nimport { Marker } from './Marker';\n\n/**\n * @internal\n */\nexport class MarkerNormal extends AbstractStandardMarker {\n    constructor(viewer: Viewer, plugin: MarkersPlugin, config: MarkerConfig) {\n        super(viewer, plugin, config);\n    }\n\n    override isNormal(): boolean {\n        return true;\n    }\n\n    override createElement(): void {\n        this.element = document.createElement('div');\n        this.afterCreateElement();\n    }\n\n    override render(params: {\n        viewerPosition: Position;\n        zoomLevel: number;\n        hoveringMarker: Marker;\n    }): Point {\n        const position = super.render(params);\n\n        if (position && this.type === MarkerType.element) {\n            this.config.element.updateMarker?.({\n                marker: this,\n                position,\n                viewerPosition: params.viewerPosition,\n                zoomLevel: params.zoomLevel,\n                viewerSize: this.viewer.state.size,\n            });\n        }\n\n        return position;\n    }\n\n    override update(config: MarkerConfig): void {\n        super.update(config);\n\n        const element = this.domElement;\n\n        if (this.config.image && !this.config.size) {\n            throw new PSVError(`missing marker ${this.id} size`);\n        }\n\n        if (this.config.size) {\n            this.needsUpdateSize = false;\n            this.state.size = this.config.size;\n            element.style.width = this.config.size.width + 'px';\n            element.style.height = this.config.size.height + 'px';\n        } else {\n            this.needsUpdateSize = true;\n        }\n\n        switch (this.type) {\n            case MarkerType.image:\n                this.definition = this.config.image;\n                element.style.backgroundImage = `url(\"${this.config.image}\")`;\n                break;\n            case MarkerType.html:\n                this.definition = this.config.html;\n                element.innerHTML = this.config.html;\n                break;\n            case MarkerType.element:\n                if (this.definition !== this.config.element) {\n                    this.definition = this.config.element;\n                    element.childNodes.forEach(n => n.remove());\n                    element.appendChild(this.config.element);\n                    this.config.element.style.display = 'block';\n                }\n                break;\n        }\n    }\n}\n", "import {\n    PSVE<PERSON>r,\n    PanoramaPosition,\n    Point,\n    Position,\n    SphericalPosition,\n    Viewer,\n    utils,\n} from '@photo-sphere-viewer/core';\nimport { Vector3 } from 'three';\nimport { MarkerType } from '../MarkerType';\nimport { MarkersPlugin } from '../MarkersPlugin';\nimport { MARKER_DATA, SVG_NS } from '../constants';\nimport { MarkerConfig } from '../model';\nimport { getGreatCircleIntersection, getPolygonCenter, getPolylineCenter } from '../utils';\nimport { AbstractDomMarker } from './AbstractDomMarker';\n\n/**\n * @internal\n */\nexport class MarkerPolygon extends AbstractDomMarker {\n    private positions3D: Vector3[][];\n\n    constructor(viewer: Viewer, plugin: MarkersPlugin, config: MarkerConfig) {\n        super(viewer, plugin, config);\n    }\n\n    override createElement(): void {\n        this.element = document.createElementNS(SVG_NS, 'path');\n        this.element[MARKER_DATA] = this;\n    }\n\n    override isPoly(): boolean {\n        return true;\n    }\n\n    /**\n     * Checks if it is a polygon/polyline using pixel coordinates\n     */\n    private get isPixels(): boolean {\n        return this.type === MarkerType.polygonPixels || this.type === MarkerType.polylinePixels;\n    }\n\n    /**\n     * Checks if it is a polygon marker\n     */\n    private get isPolygon(): boolean {\n        return this.type === MarkerType.polygon || this.type === MarkerType.polygonPixels;\n    }\n\n    /**\n     * Checks if it is a polyline marker\n     */\n    private get isPolyline(): boolean {\n        return this.type === MarkerType.polyline || this.type === MarkerType.polylinePixels;\n    }\n\n    private get coords(): Array<Array<[number, number]>> {\n        return this.definition;\n    }\n\n    override render(): Point {\n        const positions = this.__getAllPolyPositions();\n        const isVisible = positions[0].length > (this.isPolygon ? 2 : 1);\n\n        if (isVisible) {\n            const position = this.viewer.dataHelper.sphericalCoordsToViewerCoords(this.state.position);\n\n            const points = positions\n                .filter(innerPos => innerPos.length > 0)\n                .map((innerPos) => {\n                    let innerPoints = 'M';\n                    innerPoints += innerPos\n                        .map(pos => `${pos.x - position.x},${pos.y - position.y}`)\n                        .join('L');\n                    if (this.isPolygon) {\n                        innerPoints += 'Z';\n                    }\n                    return innerPoints;\n                })\n                .join(' ');\n\n            this.domElement.setAttributeNS(null, 'd', points);\n            this.domElement.setAttributeNS(null, 'transform', `translate(${position.x} ${position.y})`);\n\n            return position;\n        } else {\n            return null;\n        }\n    }\n\n    override update(config: MarkerConfig): void {\n        super.update(config);\n\n        const element = this.domElement;\n\n        element.classList.add('psv-marker--poly');\n\n        // set style\n        if (this.config.svgStyle) {\n            Object.entries(this.config.svgStyle).forEach(([prop, value]) => {\n                element.setAttributeNS(null, utils.dasherize(prop), value);\n            });\n\n            if (this.isPolyline && !this.config.svgStyle.fill) {\n                element.setAttributeNS(null, 'fill', 'none');\n            }\n        } else if (this.isPolygon) {\n            element.setAttributeNS(null, 'fill', 'rgba(0,0,0,0.5)');\n        } else if (this.isPolyline) {\n            element.setAttributeNS(null, 'fill', 'none');\n            element.setAttributeNS(null, 'stroke', 'rgb(0,0,0)');\n        }\n\n        try {\n            // (retrocompat) fold arrays: [1,2,3,4] => [[1,2],[3,4]]\n            let actualPoly: any = this.config[this.type];\n            if (!Array.isArray(actualPoly[0]) && typeof actualPoly[0] !== 'object') {\n                for (let i = 0; i < actualPoly.length; i++) {\n                    // @ts-ignore\n                    actualPoly.splice(i, 2, [actualPoly[i], actualPoly[i + 1]]);\n                }\n            }\n\n            // make nested array for holes\n            if (!Array.isArray(actualPoly[0][0]) && typeof actualPoly[0][0] !== 'object') {\n                actualPoly = [actualPoly];\n            }\n\n            if (this.isPolyline && actualPoly.length > 1) {\n                throw new PSVError(`polylines cannot have holes`);\n            }\n\n            if (this.isPixels) {\n                // convert texture coordinates to spherical coordinates\n                this.definition = (actualPoly as Array<Array<[number, number] | PanoramaPosition>>).map((coords) => {\n                    return coords.map((coord) => {\n                        let sphericalCoord: Position;\n                        if (utils.isExtendedPosition(coord)) {\n                            sphericalCoord = this.viewer.dataHelper.cleanPosition(coord);\n                        } else {\n                            sphericalCoord = this.viewer.dataHelper.textureCoordsToSphericalCoords({\n                                textureX: coord[0],\n                                textureY: coord[1],\n                            });\n                        }\n                        return [sphericalCoord.yaw, sphericalCoord.pitch];\n                    });\n                });\n            } else {\n                // clean angles\n                this.definition = (actualPoly as Array<Array<[number, number] | [string, string] | SphericalPosition>>).map((coords) => {\n                    return coords.map((coord) => {\n                        let sphericalCoord: Position;\n                        if (utils.isExtendedPosition(coord)) {\n                            sphericalCoord = this.viewer.dataHelper.cleanPosition(coord);\n                        } else {\n                            sphericalCoord = this.viewer.dataHelper.cleanPosition({\n                                yaw: coord[0],\n                                pitch: coord[1],\n                            });\n                        }\n                        return [sphericalCoord.yaw, sphericalCoord.pitch];\n                    });\n                });\n            }\n        } catch (e) {\n            throw new PSVError(`invalid marker ${this.id} position`, e);\n        }\n\n        const centroid = this.isPolygon ? getPolygonCenter(this.coords[0]) : getPolylineCenter(this.coords[0]);\n        this.state.position = { yaw: centroid[0], pitch: centroid[1] };\n\n        // compute x/y/z positions\n        this.positions3D = this.coords.map((coords) => {\n            return coords.map((coord) => {\n                return this.viewer.dataHelper.sphericalCoordsToVector3({ yaw: coord[0], pitch: coord[1] });\n            });\n        });\n\n        this.state.positions3D = this.positions3D[0];\n    }\n\n    private __getAllPolyPositions(): Point[][] {\n        return this.positions3D.map((positions) => {\n            return this.__getPolyPositions(positions);\n        });\n    }\n\n    /**\n     * Computes viewer coordinates of each point of a polygon/polyline<br>\n     * It handles points behind the camera by creating intermediary points suitable for the projector\n     */\n    private __getPolyPositions(positions: Vector3[]): Point[] {\n        const nbVectors = positions.length;\n\n        // compute if each vector is visible\n        const positions3D = positions.map((vector) => {\n            return {\n                vector: vector,\n                visible: vector.dot(this.viewer.state.direction) > 0,\n            };\n        });\n\n        // get pairs of visible/invisible vectors for each invisible vector connected to a visible vector\n        const toBeComputed: Array<{ visible: Vector3; invisible: Vector3; index: number }> = [];\n        positions3D.forEach((pos, i) => {\n            if (!pos.visible) {\n                const neighbours = [\n                    i === 0 ? positions3D[nbVectors - 1] : positions3D[i - 1],\n                    i === nbVectors - 1 ? positions3D[0] : positions3D[i + 1],\n                ];\n\n                neighbours.forEach((neighbour) => {\n                    if (neighbour.visible) {\n                        toBeComputed.push({\n                            visible: neighbour.vector,\n                            invisible: pos.vector,\n                            index: i,\n                        });\n                    }\n                });\n            }\n        });\n\n        // compute intermediary vector for each pair (the loop is reversed for splice to insert at the right place)\n        toBeComputed.reverse().forEach((pair) => {\n            positions3D.splice(pair.index, 0, {\n                vector: getGreatCircleIntersection(pair.visible, pair.invisible, this.viewer.state.direction),\n                visible: true,\n            });\n        });\n\n        // translate vectors to screen pos\n        return positions3D\n            .filter(pos => pos.visible)\n            .map(pos => this.viewer.dataHelper.vector3ToViewerCoords(pos.vector));\n    }\n}\n", "import { utils, type Viewer } from '@photo-sphere-viewer/core';\nimport { SVG_NS } from '../constants';\nimport { type MarkersPlugin } from '../MarkersPlugin';\nimport { MarkerType } from '../MarkerType';\nimport { MarkerConfig } from '../model';\nimport { AbstractStandardMarker } from './AbstractStandardMarker';\n\n/**\n * @internal\n */\nexport class MarkerSvg extends AbstractStandardMarker {\n    get svgElement(): SVGElement {\n        return this.domElement.firstElementChild as any;\n    }\n\n    constructor(viewer: Viewer, plugin: MarkersPlugin, config: MarkerConfig) {\n        super(viewer, plugin, config);\n    }\n\n    override isSvg(): boolean {\n        return true;\n    }\n\n    override createElement(): void {\n        const svgType = this.type === MarkerType.square ? 'rect' : this.type;\n        const elt = document.createElementNS(SVG_NS, svgType);\n        this.element = document.createElementNS(SVG_NS, 'svg');\n        this.element.appendChild(elt);\n        this.afterCreateElement();\n    }\n\n    override update(config: MarkerConfig): void {\n        super.update(config);\n\n        const svgElement = this.svgElement;\n\n        this.needsUpdateSize = true;\n\n        // set content\n        switch (this.type) {\n            case MarkerType.square:\n                this.definition = {\n                    x: 0,\n                    y: 0,\n                    width: this.config.square,\n                    height: this.config.square,\n                };\n                break;\n\n            case MarkerType.rect:\n                if (Array.isArray(this.config.rect)) {\n                    this.definition = {\n                        x: 0,\n                        y: 0,\n                        width: this.config.rect[0],\n                        height: this.config.rect[1],\n                    };\n                } else {\n                    this.definition = {\n                        x: 0,\n                        y: 0,\n                        width: this.config.rect.width,\n                        height: this.config.rect.height,\n                    };\n                }\n                break;\n\n            case MarkerType.circle:\n                this.definition = {\n                    cx: this.config.circle,\n                    cy: this.config.circle,\n                    r: this.config.circle,\n                };\n                break;\n\n            case MarkerType.ellipse:\n                if (Array.isArray(this.config.ellipse)) {\n                    this.definition = {\n                        cx: this.config.ellipse[0],\n                        cy: this.config.ellipse[1],\n                        rx: this.config.ellipse[0],\n                        ry: this.config.ellipse[1],\n                    };\n                } else {\n                    this.definition = {\n                        cx: this.config.ellipse.rx,\n                        cy: this.config.ellipse.ry,\n                        rx: this.config.ellipse.rx,\n                        ry: this.config.ellipse.ry,\n                    };\n                }\n                break;\n\n            case MarkerType.path:\n                this.definition = {\n                    d: this.config.path,\n                };\n                break;\n\n            // no default\n        }\n\n        Object.entries(this.definition).forEach(([prop, value]) => {\n            svgElement.setAttributeNS(null, prop, value as string);\n        });\n\n        // set style\n        if (this.config.svgStyle) {\n            Object.entries(this.config.svgStyle).forEach(([prop, value]) => {\n                svgElement.setAttributeNS(null, utils.dasherize(prop), value);\n            });\n        } else {\n            svgElement.setAttributeNS(null, 'fill', 'rgba(0,0,0,0.5)');\n        }\n    }\n}\n"], "names": ["AbstractButton", "AbstractButton", "PSVError", "events", "utils", "events", "events", "MarkerType", "PSVError", "utils", "utils", "PSVError", "utils", "PSVError", "utils", "utils", "utils", "PSVError", "PSVError", "utils", "Texture", "Vector3", "CONSTANTS", "utils", "Vector3", "utils", "PSVError", "Vector3", "p", "Texture", "PSVError", "utils", "utils", "PSVError", "PSVError", "PSVError", "PSVError", "utils", "utils", "PSVError", "utils", "utils", "utils", "PSVError", "events"], "mappings": ";;;;;;;;;AAAA,SAAS,UAAU,sBAAsB;AQCzC,SAAS,aAAa;;;;;;;;;;APDtB,IAAA,iBAAA,CAAA;AAAA,SAAA,gBAAA;IAAA,kBAAA,IAAA;IAAA,qBAAA,IAAA;IAAA,kBAAA,IAAA;IAAA,kBAAA,IAAA;IAAA,uBAAA,IAAA;IAAA,oBAAA,IAAA;IAAA,wBAAA,IAAA;IAAA,mBAAA,IAAA;IAAA,uBAAA,IAAA;IAAA,iBAAA,IAAA;IAAA,kBAAA,IAAA;IAAA,qBAAA,IAAA;AAAA;;AAOO,IAAe,qBAAf,qLAA0C,aAAA,CAA0B;AAAC;AAKrE,IAAM,yBAAN,MAAM,+BAA8B,mBAAmB;IAAA,cAAA,GAK1D,YACoB,MAAA,EACA,OAAA,CAClB;QACE,KAAA,CAAM,uBAAsB,IAAI;QAHhB,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,OAAA,GAAA;IAGpB;AACJ;AAXa,uBACgB,IAAA,GAAO;AAD7B,IAAM,wBAAN;AAgBA,IAAM,uBAAN,MAAM,6BAA4B,mBAAmB;IAAA,cAAA,GAKxD,YAA4B,MAAA,CAAgB;QACxC,KAAA,CAAM,qBAAoB,IAAI;QADN,IAAA,CAAA,MAAA,GAAA;IAE5B;AACJ;AARa,qBACgB,IAAA,GAAO;AAD7B,IAAM,sBAAN;AAaA,IAAM,oBAAN,MAAM,0BAAyB,mBAAmB;IAAA,cAAA,GAKrD,YAA4B,MAAA,CAAgB;QACxC,KAAA,CAAM,kBAAiB,IAAI;QADH,IAAA,CAAA,MAAA,GAAA;IAE5B;AACJ;AARa,kBACgB,IAAA,GAAO;AAD7B,IAAM,mBAAN;AAaA,IAAM,oBAAN,MAAM,0BAAyB,mBAAmB;IAAA,cAAA,GAKrD,YAA4B,MAAA,CAAgB;QACxC,KAAA,CAAM,kBAAiB,IAAI;QADH,IAAA,CAAA,MAAA,GAAA;IAE5B;AACJ;AARa,kBACgB,IAAA,GAAO;AAD7B,IAAM,mBAAN;AAaA,IAAM,qBAAN,MAAM,2BAA0B,mBAAmB;IAAA,cAAA,GAKtD,YACoB,MAAA,EACA,WAAA,EACA,UAAA,CAClB;QACE,KAAA,CAAM,mBAAkB,IAAI;QAJZ,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,WAAA,GAAA;QACA,IAAA,CAAA,UAAA,GAAA;IAGpB;AACJ;AAZa,mBACgB,IAAA,GAAO;AAD7B,IAAM,oBAAN;AAiBA,IAAM,yBAAN,MAAM,+BAA8B,mBAAmB;IAAA,cAAA,GAK1D,YAA4B,MAAA,CAAgB;QACxC,KAAA,CAAM,uBAAsB,IAAI;QADR,IAAA,CAAA,MAAA,GAAA;IAE5B;AACJ;AARa,uBACgB,IAAA,GAAO;AAD7B,IAAM,wBAAN;AAaA,IAAM,uBAAN,MAAM,6BAA4B,mBAAmB;IAAA,cAAA,GAKxD,YAA4B,MAAA,CAAgB;QACxC,KAAA,CAAM,qBAAoB,IAAI;QADN,IAAA,CAAA,MAAA,GAAA;IAE5B;AACJ;AARa,qBACgB,IAAA,GAAO;AAD7B,IAAM,sBAAN;AAaA,IAAM,oBAAN,MAAM,0BAAyB,mBAAmB;IAAA,cAAA,GAKrD,aAAc;QACV,KAAA,CAAM,kBAAiB,IAAI;IAC/B;AACJ;AARa,kBACgB,IAAA,GAAO;AAD7B,IAAM,mBAAN;AAaA,IAAM,mBAAN,MAAM,yBAAwB,mBAAmB;IAAA,cAAA,GAKpD,YAA4B,OAAA,CAAmB;QAC3C,KAAA,CAAM,iBAAgB,IAAI;QADF,IAAA,CAAA,OAAA,GAAA;IAE5B;AACJ;AARa,iBACgB,IAAA,GAAO;AAD7B,IAAM,kBAAN;AAaA,IAAM,oBAAN,MAAM,0BAAyB,mBAAmB;IAAA,cAAA,GAKrD,aAAc;QACV,KAAA,CAAM,kBAAiB,IAAI;IAC/B;AACJ;AARa,kBACgB,IAAA,GAAO;AAD7B,IAAM,mBAAN;AAaA,IAAM,0BAAN,MAAM,gCAA+B,mBAAmB;IAAA,cAAA,GAK3D,YAEW,OAAA,CACT;QACE,KAAA,CAAM,wBAAuB,IAAI;QAF1B,IAAA,CAAA,OAAA,GAAA;IAGX;AACJ;AAXa,wBACgB,IAAA,GAAO;AAD7B,IAAM,yBAAN;;;AErJP,IAAA,cAAA;;ADMO,IAAM,gBAAN,qLAA4B,iBAAA,CAAe;IAK9C,YAAY,MAAA,CAAgB;QACxB,KAAA,CAAM,QAAQ;YACV,WAAW;YACX,MAAM;YACN,YAAY;YACZ,aAAa;YACb,UAAU;QACd,CAAC;QAED,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,MAAA,CAAO,SAAA,CAAU,SAAS;QAE7C,IAAI,IAAA,CAAK,MAAA,EAAQ;YACb,IAAA,CAAK,MAAA,CAAO,gBAAA,CAAiB,iBAAiB,IAAA,EAAM,IAAI;YACxD,IAAA,CAAK,MAAA,CAAO,gBAAA,CAAiB,iBAAiB,IAAA,EAAM,IAAI;YAExD,IAAA,CAAK,YAAA,CAAa,IAAI;QAC1B;IACJ;IAES,UAAU;QACf,IAAI,IAAA,CAAK,MAAA,EAAQ;YACb,IAAA,CAAK,MAAA,CAAO,mBAAA,CAAoB,iBAAiB,IAAA,EAAM,IAAI;YAC3D,IAAA,CAAK,MAAA,CAAO,mBAAA,CAAoB,iBAAiB,IAAA,EAAM,IAAI;QAC/D;QAEA,KAAA,CAAM,QAAQ;IAClB;IAES,cAAc;QACnB,OAAO,CAAC,CAAC,IAAA,CAAK,MAAA;IAClB;IAEA,YAAY,CAAA,EAAU;QAClB,IAAI,aAAa,kBAAkB;YAC/B,IAAA,CAAK,YAAA,CAAa,IAAI;QAC1B,OAAA,IAAW,aAAa,kBAAkB;YACtC,IAAA,CAAK,YAAA,CAAa,KAAK;QAC3B;IACJ;IAEA,UAAU;QACN,IAAA,CAAK,MAAA,CAAO,gBAAA,CAAiB;IACjC;AACJ;AAhDa,cACgB,EAAA,GAAK;;;;AIPlC,IAAA,mBAAA;;ADQO,IAAM,SAAS;AAMf,IAAM,cAAc;AAMpB,IAAM,yLAAkB,QAAA,CAAM,SAAA,CAAU,WAAW;AAMnD,IAAM,kBAAkB;AAMxB,IAAM,wBAAwB;AAM9B,IAAM,sBAAsB;IAC/B,QAAQ;IACR,UAAU;IACV,QAAQ;AACZ;AAMO,IAAM,wBAAwB,CAAC,SAAmB,QAAkB,CAAA;;qCAAA,EAEpC,gBAAI,CAAA,CAAA,EAAI,KAAK,CAAA;;IAAA,EAE9C,QAAQ,GAAA,CAAI,CAAA,SAAU,CAAA;iBAAA,EACT,eAAe,CAAA,EAAA,EAAK,OAAO,EAAE,CAAA;UAAA,EACpC,OAAO,IAAA,KAAS,UAAU,CAAA,iDAAA,EAAoD,OAAO,UAAU,CAAA,UAAA,CAAA,GAAe,EAAE,CAAA;kDAAA,EACxE,OAAO,cAAA,CAAe,CAAC,CAAA;;IAAA,CAEtE,EAAE,IAAA,CAAK,EAAE,CAAC,CAAA;;;AAAA,CAAA;;ADnDR,IAAM,oBAAN,qLAAgCC,iBAAAA,CAAe;IAKlD,YAAY,MAAA,CAAgB;QACxB,KAAA,CAAM,QAAQ;YACV,WAAW;YACX,MAAM;YACN,YAAY;YACZ,aAAa;YACb,UAAU;QACd,CAAC;QAED,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,MAAA,CAAO,SAAA,CAAU,SAAS;QAE7C,IAAI,IAAA,CAAK,MAAA,EAAQ;YACb,IAAA,CAAK,MAAA,CAAO,gBAAA,CAAiB,gLAAA,CAAO,cAAA,CAAe,IAAA,EAAM,IAAI;YAC7D,IAAA,CAAK,MAAA,CAAO,gBAAA,CAAiB,gLAAA,CAAO,cAAA,CAAe,IAAA,EAAM,IAAI;QACjE;IACJ;IAES,UAAU;QACf,IAAA,CAAK,MAAA,CAAO,mBAAA,wKAAoB,SAAA,CAAO,cAAA,CAAe,IAAA,EAAM,IAAI;QAChE,IAAA,CAAK,MAAA,CAAO,mBAAA,wKAAoB,SAAA,CAAO,cAAA,CAAe,IAAA,EAAM,IAAI;QAEhE,KAAA,CAAM,QAAQ;IAClB;IAES,cAAc;QACnB,OAAO,CAAC,CAAC,IAAA,CAAK,MAAA;IAClB;IAEA,YAAY,CAAA,EAAU;QAClB,IAAI,aAAa,gLAAA,CAAO,cAAA,EAAgB;YACpC,IAAA,CAAK,YAAA,CAAa,EAAE,OAAA,KAAY,qBAAqB;QACzD,OAAA,IAAW,oLAAa,SAAA,CAAO,cAAA,EAAgB;YAC3C,IAAA,CAAK,YAAA,CAAa,KAAK;QAC3B;IACJ;IAEA,UAAU;QACN,IAAA,CAAK,MAAA,CAAO,iBAAA,CAAkB;IAClC;AACJ;AA5Ca,kBACgB,EAAA,GAAK;;;;;AKElC,IAAM,YAAY,sJAAI,UAAA,CAAQ;AAC9B,IAAM,cAAc,sJAAI,aAAA,CAAW;AACnC,IAAM,SAAS,sJAAI,UAAA,CAAQ;AAO3B,IAAM,cAAN,gKAA0B,WAAA,CAAS;IAAA;;;;GAAA,GAOlC,YAAa,UAAU,SAAS,aAAA,CAAe,KAAM,CAAA,CAAI;QAExD,KAAA,CAAM;QASN,IAAA,CAAK,aAAA,GAAgB;QASrB,IAAA,CAAK,OAAA,GAAU;QACf,IAAA,CAAK,OAAA,CAAQ,KAAA,CAAM,QAAA,GAAW;QAC9B,IAAA,CAAK,OAAA,CAAQ,KAAA,CAAM,aAAA,GAAgB;QACnC,IAAA,CAAK,OAAA,CAAQ,KAAA,CAAM,UAAA,GAAa;QAEhC,IAAA,CAAK,OAAA,CAAQ,YAAA,CAAc,aAAa,KAAM;QAE9C,IAAA,CAAK,gBAAA,CAAkB,WAAW,WAAY;YAE7C,IAAA,CAAK,QAAA,CAAU,SAAW,MAAA,EAAS;gBAElC,IACC,OAAO,OAAA,YAAmB,OAAO,OAAA,CAAQ,aAAA,CAAc,WAAA,CAAY,OAAA,IACnE,OAAO,OAAA,CAAQ,UAAA,KAAe,MAC7B;oBAED,OAAO,OAAA,CAAQ,MAAA,CAAO;gBAEvB;YAED,CAAE;QAEH,CAAE;IAEH;IAEA,KAAM,MAAA,EAAQ,SAAA,EAAY;QAEzB,KAAA,CAAM,KAAM,QAAQ,SAAU;QAE9B,IAAA,CAAK,OAAA,GAAU,OAAO,OAAA,CAAQ,SAAA,CAAW,IAAK;QAE9C,OAAO,IAAA;IAER;AAED;AAoDA,IAAM,UAAU,sJAAI,UAAA,CAAQ;AAC5B,IAAM,WAAW,sJAAI,UAAA,CAAQ;AAiB7B,IAAM,gBAAN,MAAoB;IAAA;;;;GAAA,GAOnB,YAAa,aAAa,CAAC,CAAA,CAAI;QAE9B,MAAM,QAAQ,IAAA;QAEd,IAAI,QAAQ;QACZ,IAAI,YAAY;QAEhB,MAAM,QAAQ;YACb,QAAQ;gBAAE,OAAO;YAAG;YACpB,SAAS,aAAA,GAAA,IAAI,QAAQ;QACtB;QAEA,MAAM,aAAa,WAAW,OAAA,KAAY,KAAA,IAAY,WAAW,OAAA,GAAU,SAAS,aAAA,CAAe,KAAM;QAEzG,WAAW,KAAA,CAAM,QAAA,GAAW;QAO5B,IAAA,CAAK,UAAA,GAAa;QAElB,MAAM,cAAc,SAAS,aAAA,CAAe,KAAM;QAClD,YAAY,KAAA,CAAM,eAAA,GAAkB;QACpC,YAAY,KAAA,CAAM,aAAA,GAAgB;QAClC,WAAW,WAAA,CAAa,WAAY;QAEpC,MAAM,gBAAgB,SAAS,aAAA,CAAe,KAAM;QAEpD,cAAc,KAAA,CAAM,cAAA,GAAiB;QAErC,YAAY,WAAA,CAAa,aAAc;QAOvC,IAAA,CAAK,OAAA,GAAU,WAAY;YAE1B,OAAO;gBACN,OAAO;gBACP,QAAQ;YACT;QAED;QAQA,IAAA,CAAK,MAAA,GAAS,SAAW,KAAA,EAAO,MAAA,EAAS;YAExC,MAAM,MAAM,OAAO,gBAAA,CAAiB,QAAA,CAAU,CAAE,CAAA,GAAI;YAEpD,IAAK,OAAO,IAAA,IAAQ,OAAO,IAAA,CAAK,OAAA,EAAU;gBAGzC,YAAY,KAAA,CAAM,SAAA,GAAY,CAAA,WAAA,EAAe,CAAE,OAAO,IAAA,CAAK,OAAA,GAAA,CAAY,SAAS,OAAO,IAAA,CAAK,KAAA,CAAQ,CAAA,IAAA,EAAQ,CAAE,OAAO,IAAA,CAAK,OAAA,GAAA,CAAY,UAAU,OAAO,IAAA,CAAK,MAAA,CAAS,CAAA,IAAA,CAAA;gBAGrK,YAAY,KAAA,CAAM,SAAA,IAAa,CAAA,OAAA,EAAW,OAAO,IAAA,CAAK,SAAA,GAAY,OAAO,IAAA,CAAK,KAAM,CAAA,EAAA,EAAM,OAAO,IAAA,CAAK,UAAA,GAAa,OAAO,IAAA,CAAK,MAAO,CAAA,EAAA,CAAA;YAEvI,OAAO;gBAEN,YAAY,KAAA,CAAM,SAAA,GAAY;YAE/B;YAEA,IAAK,MAAM,qBAAA,KAA0B,KAAO,CAAA,MAAM,iBAAA,CAAkB;YACpE,IAAK,OAAO,MAAA,KAAW,QAAQ,OAAO,qBAAA,KAA0B,KAAO,CAAA,OAAO,iBAAA,CAAkB;YAEhG,IAAI,IAAI;YAER,IAAK,OAAO,oBAAA,EAAuB;gBAElC,KAAK,CAAA,CAAI,OAAO,KAAA,GAAQ,OAAO,IAAA,IAAS;gBACxC,KAAA,CAAO,OAAO,GAAA,GAAM,OAAO,MAAA,IAAW;YAEvC;YAEA,MAAM,oBAAoB,OAAO,IAAA,IAAQ,OAAO,IAAA,CAAK,OAAA,GAAU,OAAO,IAAA,CAAK,MAAA,GAAS,OAAO,IAAA,CAAK,UAAA,GAAa;YAC7G,MAAM,kBAAkB,OAAO,oBAAA,GAC9B,CAAA,OAAA,EAAW,iBAAkB,CAAA,QAAA,CAAA,GAAkB,MAAM,gBAAqB,QAAS,EAAG,IAAI,QAAQ,QAAS,EAAG,IAAI,QAAQ,mBAAoB,OAAO,kBAAmB,IACxK,CAAA,OAAA,EAAW,iBAAkB,CAAA,aAAA,CAAA,GAAuB,MAAM,QAAQ,mBAAoB,OAAO,kBAAmB;YACjH,MAAM,cAAc,OAAO,mBAAA,GAAsB,iBAAiB,MAAM,SAAS;YAEjF,MAAM,QAAQ,cAAc,kBAC3B,eAAe,aAAa,QAAQ,cAAc;YAEnD,IAAK,MAAM,MAAA,CAAO,KAAA,KAAU,OAAQ;gBAEnC,cAAc,KAAA,CAAM,SAAA,GAAY;gBAEhC,MAAM,MAAA,CAAO,KAAA,GAAQ;YAEtB;YAEA,aAAc,OAAO,OAAO,QAAQ,eAAgB;QAErD;QAQA,IAAA,CAAK,OAAA,GAAU,SAAW,KAAA,EAAO,MAAA,EAAS;YAEzC,SAAS;YACT,UAAU;YACV,aAAa,SAAS;YACtB,cAAc,UAAU;YAExB,WAAW,KAAA,CAAM,KAAA,GAAQ,QAAQ;YACjC,WAAW,KAAA,CAAM,MAAA,GAAS,SAAS;YAEnC,YAAY,KAAA,CAAM,KAAA,GAAQ,QAAQ;YAClC,YAAY,KAAA,CAAM,MAAA,GAAS,SAAS;YAEpC,cAAc,KAAA,CAAM,KAAA,GAAQ,QAAQ;YACpC,cAAc,KAAA,CAAM,MAAA,GAAS,SAAS;QAEvC;QAEA,SAAS,QAAS,KAAA,EAAQ;YAEzB,OAAO,KAAK,GAAA,CAAK,KAAM,IAAI,QAAQ,IAAI;QAExC;QAEA,SAAS,mBAAoB,MAAA,EAAS;YAErC,MAAM,WAAW,OAAO,QAAA;YAExB,OAAO,cACN,QAAS,QAAA,CAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,CAAE,QAAA,CAAU,CAAE,CAAE,IAAI,MAC7B,QAAS,QAAA,CAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,QAAA,CAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,QAAA,CAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,CAAE,QAAA,CAAU,CAAE,CAAE,IAAI,MAC7B,QAAS,QAAA,CAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,QAAA,CAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,QAAA,CAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,CAAE,QAAA,CAAU,CAAE,CAAE,IAAI,MAC7B,QAAS,QAAA,CAAU,EAAG,CAAE,IAAI,MAC5B,QAAS,QAAA,CAAU,EAAG,CAAE,IAAI,MAC5B,QAAS,QAAA,CAAU,EAAG,CAAE,IAAI,MAC5B,QAAS,CAAE,QAAA,CAAU,EAAG,CAAE,IAAI,MAC9B,QAAS,QAAA,CAAU,EAAG,CAAE,IAAI,MAC5B,QAAS,QAAA,CAAU,EAAG,CAAE,IACzB;QAED;QAEA,SAAS,mBAAoB,MAAA,EAAS;YAErC,MAAM,WAAW,OAAO,QAAA;YACxB,MAAM,WAAW,cAChB,QAAS,QAAA,CAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,QAAA,CAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,QAAA,CAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,QAAA,CAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,CAAE,QAAA,CAAU,CAAE,CAAE,IAAI,MAC7B,QAAS,CAAE,QAAA,CAAU,CAAE,CAAE,IAAI,MAC7B,QAAS,CAAE,QAAA,CAAU,CAAE,CAAE,IAAI,MAC7B,QAAS,CAAE,QAAA,CAAU,CAAE,CAAE,IAAI,MAC7B,QAAS,QAAA,CAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,QAAA,CAAU,CAAE,CAAE,IAAI,MAC3B,QAAS,QAAA,CAAU,EAAG,CAAE,IAAI,MAC5B,QAAS,QAAA,CAAU,EAAG,CAAE,IAAI,MAC5B,QAAS,QAAA,CAAU,EAAG,CAAE,IAAI,MAC5B,QAAS,QAAA,CAAU,EAAG,CAAE,IAAI,MAC5B,QAAS,QAAA,CAAU,EAAG,CAAE,IAAI,MAC5B,QAAS,QAAA,CAAU,EAAG,CAAE,IACzB;YAEA,OAAO,yBAAyB;QAEjC;QAEA,SAAS,WAAY,MAAA,EAAS;YAE7B,IAAK,OAAO,aAAA,CAAgB,CAAA,OAAO,OAAA,CAAQ,KAAA,CAAM,OAAA,GAAU;YAE3D,IAAA,IAAU,IAAI,GAAG,IAAI,OAAO,QAAA,CAAS,MAAA,EAAQ,IAAI,GAAG,IAAO;gBAE1D,WAAY,OAAO,QAAA,CAAU,CAAE,CAAE;YAElC;QAED;QAEA,SAAS,aAAc,MAAA,EAAQ,KAAA,EAAO,MAAA,EAAQ,eAAA,EAAkB;YAE/D,IAAK,OAAO,OAAA,KAAY,OAAQ;gBAE/B,WAAY,MAAO;gBAEnB;YAED;YAEA,IAAK,OAAO,aAAA,EAAgB;gBAE3B,MAAM,UAAY,OAAO,MAAA,CAAO,IAAA,CAAM,OAAO,MAAO,MAAM;gBAE1D,MAAM,UAAU,OAAO,OAAA;gBACvB,QAAQ,KAAA,CAAM,OAAA,GAAU,YAAY,OAAO,KAAK;gBAEhD,IAAK,YAAY,MAAO;oBAEvB,OAAO,cAAA,CAAgB,OAAO,OAAO,MAAO;oBAE5C,IAAI;oBAEJ,IAAK,OAAO,aAAA,EAAgB;wBAI3B,QAAQ,IAAA,CAAM,OAAO,kBAAmB;wBACxC,QAAQ,SAAA,CAAU;wBAElB,IAAK,OAAO,UAAA,KAAe,EAAI,CAAA,QAAQ,QAAA,CAAU,SAAS,aAAA,CAAe,OAAO,UAAW,CAAE;wBAE7F,OAAO,WAAA,CAAY,SAAA,CAAW,WAAW,aAAa,MAAO;wBAC7D,QAAQ,WAAA,CAAa,SAAU;wBAC/B,QAAQ,KAAA,CAAO,MAAO;wBAEtB,QAAQ,QAAA,CAAU,CAAE,CAAA,GAAI;wBACxB,QAAQ,QAAA,CAAU,CAAE,CAAA,GAAI;wBACxB,QAAQ,QAAA,CAAU,EAAG,CAAA,GAAI;wBACzB,QAAQ,QAAA,CAAU,EAAG,CAAA,GAAI;wBAEzB,QAAQ,mBAAoB,OAAQ;oBAErC,OAAO;wBAEN,QAAQ,mBAAoB,OAAO,WAAY;oBAEhD;oBAEA,MAAM,eAAe,MAAM,OAAA,CAAQ,GAAA,CAAK,MAAO;oBAE/C,IAAK,iBAAiB,KAAA,KAAa,aAAa,KAAA,KAAU,OAAQ;wBAEjE,QAAQ,KAAA,CAAM,SAAA,GAAY;wBAE1B,MAAM,aAAa;4BAAE;wBAAa;wBAClC,MAAM,OAAA,CAAQ,GAAA,CAAK,QAAQ,UAAW;oBAEvC;oBAEA,IAAK,QAAQ,UAAA,KAAe,eAAgB;wBAE3C,cAAc,WAAA,CAAa,OAAQ;oBAEpC;oBAEA,OAAO,aAAA,CAAe,OAAO,OAAO,MAAO;gBAE5C;YAED;YAEA,IAAA,IAAU,IAAI,GAAG,IAAI,OAAO,QAAA,CAAS,MAAA,EAAQ,IAAI,GAAG,IAAO;gBAE1D,aAAc,OAAO,QAAA,CAAU,CAAE,CAAA,EAAG,OAAO,QAAQ,eAAgB;YAEpE;QAED;IAED;AAED;;AD7aO,IAAM,iBAAN,MAAqB;IAOxB,YACY,MAAA,CACV;QADU,IAAA,CAAA,MAAA,GAAA;QAER,IAAA,CAAK,OAAA,GAAU,SAAS,aAAA,CAAc,KAAK;QAC3C,IAAA,CAAK,OAAA,CAAQ,SAAA,GAAY;QAEzB,IAAA,CAAK,QAAA,GAAW,IAAI,cAAc;YAAE,SAAS,IAAA,CAAK,OAAA;QAAQ,CAAC;QAC3D,IAAA,CAAK,KAAA,GAAQ,IAAI,0JAAA,CAAM;QAEvB,IAAA,CAAK,oBAAA,GAAuB,IAAI,qBAAqB,CAAC,YAAY;YAC9D,QAAQ,OAAA,CAAQ,CAAC,UAAU;gBACvB,MAAM,SAAU,MAAM,MAAA,CAAe,WAAW,CAAA;gBAChD,IAAI,OAAO,MAAA,CAAO,OAAA,EAAS;oBACvB,OAAO,oBAAA,GAAuB,MAAM,cAAA;gBACxC;YACJ,CAAC;QACL,GAAG;YACC,MAAM,IAAA,CAAK,OAAA;QACf,CAAC;QAED,OAAO,gBAAA,wKAAiBK,SAAAA,CAAO,UAAA,CAAW,IAAA,EAAM,IAAA,EAAM;YAAE,MAAM;QAAK,CAAC;QACpE,OAAO,gBAAA,wKAAiBA,SAAAA,CAAO,gBAAA,CAAiB,IAAA,EAAM,IAAI;QAC1D,OAAO,gBAAA,wKAAiBA,SAAAA,CAAO,WAAA,CAAY,IAAA,EAAM,IAAI;IACzD;IAEA,YAAY,CAAA,EAAU;QAClB,OAAQ,EAAE,IAAA,EAAM;YACZ,4KAAKA,SAAAA,CAAO,UAAA,CAAW,IAAA;YACvB,KAAKA,gLAAAA,CAAO,gBAAA,CAAiB,IAAA;gBACzB,IAAA,CAAK,UAAA,CAAW;gBAChB;YACJ,4KAAKA,SAAAA,CAAO,WAAA,CAAY,IAAA;gBACpB,IAAA,CAAK,MAAA,CAAO;gBACZ;QACR;IACJ;IAEA,UAAgB;QACZ,IAAA,CAAK,MAAA,CAAO,mBAAA,CAAoBA,gLAAAA,CAAO,UAAA,CAAW,IAAA,EAAM,IAAI;QAC5D,IAAA,CAAK,MAAA,CAAO,mBAAA,wKAAoBA,SAAAA,CAAO,gBAAA,CAAiB,IAAA,EAAM,IAAI;QAClE,IAAA,CAAK,MAAA,CAAO,mBAAA,wKAAoBA,SAAAA,CAAO,WAAA,CAAY,IAAA,EAAM,IAAI;QAE7D,IAAA,CAAK,oBAAA,CAAqB,UAAA,CAAW;IACzC;IAEQ,aAAa;QACjB,MAAM,OAAO,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ;QACjC,IAAA,CAAK,QAAA,CAAS,OAAA,CAAQ,KAAK,KAAA,EAAO,KAAK,MAAM;IACjD;IAEQ,SAAS;QACb,IAAA,CAAK,QAAA,CAAS,MAAA,CAAO,IAAA,CAAK,KAAA,EAAO,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,MAAM;IAChE;IAEA,UAAU,MAAA,EAAqB;QAC3B,IAAA,CAAK,KAAA,CAAM,GAAA,CAAI,OAAO,YAAY;QAClC,IAAA,CAAK,oBAAA,CAAqB,OAAA,CAAQ,OAAO,UAAU;IACvD;IAEA,aAAa,MAAA,EAAqB;QAC9B,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,OAAO,YAAY;QACrC,IAAA,CAAK,oBAAA,CAAqB,SAAA,CAAU,OAAO,UAAU;IACzD;AACJ;;AE5EO,IAAK,aAAL,aAAA,GAAA,CAAA,CAAKC,gBAAL;IACHA,WAAAA,CAAA,QAAA,GAAQ;IACRA,WAAAA,CAAA,OAAA,GAAO;IACPA,WAAAA,CAAA,UAAA,GAAU;IACVA,WAAAA,CAAA,aAAA,GAAa;IACbA,WAAAA,CAAA,aAAA,GAAa;IACbA,WAAAA,CAAA,eAAA,GAAe;IACfA,WAAAA,CAAA,UAAA,GAAU;IACVA,WAAAA,CAAA,gBAAA,GAAgB;IAChBA,WAAAA,CAAA,WAAA,GAAW;IACXA,WAAAA,CAAA,iBAAA,GAAiB;IACjBA,WAAAA,CAAA,SAAA,GAAS;IACTA,WAAAA,CAAA,OAAA,GAAO;IACPA,WAAAA,CAAA,SAAA,GAAS;IACTA,WAAAA,CAAA,UAAA,GAAU;IACVA,WAAAA,CAAA,OAAA,GAAO;IAfC,OAAAA;AAAA,CAAA,EAAA,cAAA,CAAA;AAsBL,SAAS,cAAc,MAAA,EAAsB,YAAY,KAAA,EAAmB;IAC/E,MAAM,QAAsB,CAAC,CAAA;IAE7B,OAAO,IAAA,CAAK,UAAU,EAAE,OAAA,CAAQ,CAAC,SAAS;QACtC,IAAK,MAAA,CAAe,IAAI,CAAA,EAAG;YACvB,MAAM,IAAA,CAAK,IAAkB;QACjC;IACJ,CAAC;IAED,IAAI,MAAM,MAAA,KAAW,KAAK,CAAC,WAAW;QAClC,MAAM,2KAAI,WAAA,CAAS,CAAA,+BAAA,EAAkC,OAAO,IAAA,CAAK,UAAU,EAAE,IAAA,CAAK,IAAI,CAAC,EAAE;IAC7F,OAAA,IAAW,MAAM,MAAA,GAAS,GAAG;QACzB,MAAM,2KAAI,WAAA,CAAS,CAAA,gCAAA,EAAmC,OAAO,IAAA,CAAK,UAAU,EAAE,IAAA,CAAK,IAAI,CAAC,EAAE;IAC9F;IAEA,OAAO,KAAA,CAAM,CAAC,CAAA;AAClB;;;;;AGhCO,IAAe,SAAf,MAAsB;IA8CzB,YACc,MAAA,EACA,MAAA,EACV,MAAA,CACF;QAHY,IAAA,CAAA,MAAA,GAAA;QACA,IAAA,CAAA,MAAA,GAAA;QAZd,cAAA,GAAA,IAAA,CAAS,KAAA,GAAQ;YACb,QAAQ;YACR,SAAS;YACT,eAAe;YACf,UAAU;YACV,YAAY;YACZ,aAAa;YACb,MAAM;QACV;QAOI,IAAI,CAAC,OAAO,EAAA,EAAI;YACZ,MAAM,IAAIM,kLAAAA,CAAS,mBAAmB;QAC1C;QAEA,IAAA,CAAK,IAAA,GAAO,cAAc,MAAM;QAChC,IAAA,CAAK,aAAA,CAAc;QACnB,IAAA,CAAK,MAAA,CAAO,MAAM;IACtB;IA3CA,IAAI,KAAa;QACb,OAAO,IAAA,CAAK,MAAA,CAAO,EAAA;IACvB;IAEA,IAAI,OAAY;QACZ,OAAO,IAAA,CAAK,MAAA,CAAO,IAAA;IACvB;IAEA,IAAI,aAAuC;QACvC,OAAO;IACX;IAEA,IAAI,eAAyB;QACzB,OAAO;IACX;IAEA,IAAI,QAA0B;QAC1B,OAAO;IACX;IAAA;;GAAA,GA6CA,UAAU;QACN,OAAO,IAAA,CAAK,MAAA;QACZ,OAAO,IAAA,CAAK,MAAA;QACZ,OAAO,IAAA,CAAK,OAAA;QAEZ,IAAA,CAAK,WAAA,CAAY;IACrB;IAAA;;GAAA,GAKA,OAAgB;QACZ,OAAO;IACX;IAAA;;GAAA,GAKA,WAAoB;QAChB,OAAO;IACX;IAAA;;GAAA,GAKA,SAAkB;QACd,OAAO;IACX;IAAA;;GAAA,GAKA,QAAiB;QACb,OAAO;IACX;IAAA;;GAAA,GAKA,UAAmB;QACf,OAAO;IACX;IAAA;;;;GAAA,GAOA,OAAO,MAAA,EAAsB;QACzB,MAAM,UAAU,cAAc,QAAQ,IAAI;QAE1C,IAAI,YAAY,KAAA,KAAa,YAAY,IAAA,CAAK,IAAA,EAAM;YAChD,MAAM,2KAAIA,WAAAA,CAAS,CAAA,qBAAA,EAAwB,OAAO,EAAE,CAAA,KAAA,CAAO;QAC/D;QAEA,IAAA,CAAK,MAAA,0KAASC,QAAAA,CAAM,SAAA,CAAU,IAAA,CAAK,MAAA,EAAQ,MAAa;QACxD,IAAI,OAAO,IAAA,CAAK,MAAA,CAAO,OAAA,KAAY,UAAU;YACzC,IAAA,CAAK,MAAA,CAAO,OAAA,GAAU;gBAAE,SAAS,IAAA,CAAK,MAAA,CAAO,OAAA;YAAQ;QACzD;QACA,IAAI,IAAA,CAAK,MAAA,CAAO,OAAA,IAAW,CAAC,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,OAAA,EAAS;YACrD,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,OAAA,GAAU;QAClC;QACA,2KAAIA,QAAAA,CAAM,KAAA,CAAM,IAAA,CAAK,MAAA,CAAO,OAAO,GAAG;YAClC,IAAA,CAAK,MAAA,CAAO,OAAA,GAAU;QAC1B;QACA,2KAAIA,QAAAA,CAAM,KAAA,CAAM,IAAA,CAAK,MAAA,CAAO,MAAM,GAAG;YACjC,IAAA,CAAK,MAAA,CAAO,MAAA,GAAS;QACzB;QACA,2KAAIA,QAAAA,CAAM,KAAA,CAAM,IAAA,CAAK,MAAA,CAAO,OAAO,GAAG;YAClC,IAAA,CAAK,MAAA,CAAO,OAAA,GAAU;QAC1B;QAEA,IAAI,IAAA,CAAK,MAAA,CAAO,QAAA,EAAU;YACtB,MAAM,MAAM,IAAA,CAAK,MAAA,CAAO,QAAA;YACxB,IAAI,OAAO,QAAQ,UAAU;gBACzB,IAAA,CAAK,MAAA,CAAO,QAAA,GAAW;oBACnB,KAAK,IAAI,GAAA,0KAAMA,QAAAA,CAAM,UAAA,CAAW,IAAI,GAAA,EAAK,MAAM,KAAK,IAAI;oBACxD,OAAO,IAAI,KAAA,0KAAQA,QAAAA,CAAM,UAAA,CAAW,IAAI,KAAA,EAAO,MAAM,KAAK,IAAI;oBAC9D,MAAM,IAAI,IAAA,0KAAOA,QAAAA,CAAM,UAAA,CAAW,IAAI,IAAA,EAAM,MAAM,KAAK,IAAI;gBAC/D;YACJ,OAAO;gBACH,IAAA,CAAK,MAAA,CAAO,QAAA,GAAW;oBACnB,KAAK;oBACL,OAAO;oBACP,6KAAMA,QAAAA,CAAM,UAAA,CAAW,KAAK,MAAM,KAAK;gBAC3C;YACJ;QACJ,OAAO;YACH,IAAA,CAAK,MAAA,CAAO,QAAA,GAAW;gBAAE,KAAK;gBAAG,OAAO;gBAAG,MAAM;YAAE;QACvD;QAEA,IAAA,CAAK,KAAA,CAAM,MAAA,GAASA,+KAAAA,CAAM,UAAA,CAAW,IAAA,CAAK,MAAA,CAAO,MAAM;IAC3D;IAAA;;;;;;;GAAA,GAUA,iBAAyB;QACrB,IAAI,IAAA,CAAK,MAAA,CAAO,WAAA,EAAa;YACzB,OAAO,IAAA,CAAK,MAAA,CAAO,WAAA;QACvB,OAAA,IAAW,IAAA,CAAK,MAAA,CAAO,OAAA,EAAS,SAAS;YACrC,OAAO,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,OAAA;QAC/B,OAAA,IAAW,IAAA,CAAK,MAAA,CAAO,IAAA,EAAM;YACzB,OAAO,IAAA,CAAK,MAAA,CAAO,IAAA;QACvB,OAAO;YACH,OAAO,IAAA,CAAK,EAAA;QAChB;IACJ;IAAA;;;GAAA,GAMA,YAAY,OAAA,EAAkB,OAAA,EAAkB,cAAc,KAAA,EAAO;QACjE,IAAI,IAAA,CAAK,KAAA,CAAM,OAAA,IAAW,IAAA,CAAK,MAAA,CAAO,OAAA,EAAS,WAAW,IAAA,CAAK,KAAA,CAAM,UAAA,EAAY;YAC7E,MAAM,SAAwB;gBAC1B,GAAG,IAAA,CAAK,MAAA,CAAO,OAAA;gBACf,OAAO;oBAAA,2CAAA;oBAEH,eAAe,IAAA,CAAK,KAAA,CAAM,aAAA,GAAgB,SAAS;gBACvD;gBACA,MAAM,IAAA;gBACN,KAAK;gBACL,MAAM;YACV;YAEA,IAAI,IAAA,CAAK,MAAA,CAAO,KAAK,IAAA,CAAK,IAAA,CAAK,KAAK,IAAA,CAAK,OAAA,CAAQ,GAAG;gBAChD,IAAI,WAAW,SAAS;oBACpB,MAAM,mLAAYA,QAAAA,CAAM,WAAA,CAAY,IAAA,CAAK,MAAA,CAAO,SAAS;oBACzD,OAAO,GAAA,GAAM,UAAU,UAAU,CAAA,GAAI;oBACrC,OAAO,IAAA,GAAO,UAAU,UAAU,CAAA;oBAClC,OAAO,GAAA,GAAM;wBAAA,uCAAA;wBAET,OAAO;wBACP,QAAQ;oBACZ;gBACJ,OAAO;oBACH,OAAO,GAAA,GAAM,IAAA,CAAK,KAAA,CAAM,UAAA,CAAW,CAAA;oBACnC,OAAO,IAAA,GAAO,IAAA,CAAK,KAAA,CAAM,UAAA,CAAW,CAAA;gBACxC;YACJ,OAAO;gBAEH,MAAM,WAAW,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,qBAAA,CAAsB,IAAA,CAAK,KAAA,CAAM,WAAA,CAAY,CAAC,CAAC;gBACvF,IAAI,QAAQ,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,KAAA;gBAC5B,IAAI,SAAS,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,MAAA;gBAG7B,IAAI,IAAA,CAAK,MAAA,CAAO,UAAA,IAAc,CAAC,IAAA,CAAK,KAAA,CAAM,aAAA,EAAe;oBACrD,SAAS,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,MAAA;oBAChC,UAAU,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,MAAA;gBACrC;gBAEA,OAAO,GAAA,GAAM,SAAS,CAAA,GAAI,SAAS,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,CAAA,GAAI,SAAS;gBAClE,OAAO,IAAA,GAAO,SAAS,CAAA,GAAI,QAAQ,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,CAAA,GAAI,QAAQ;gBACjE,OAAO,GAAA,GAAM;oBAAE;oBAAO;gBAAO;YACjC;YAEA,IAAI,IAAA,CAAK,OAAA,EAAS;gBACd,IAAI,aAAa;oBACb,IAAA,CAAK,OAAA,CAAQ,MAAA,CAAO,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,OAAA,EAAS,MAAM;gBAC3D,OAAO;oBACH,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK,MAAM;gBAC5B;YACJ,OAAO;gBACH,IAAA,CAAK,OAAA,GAAU,IAAA,CAAK,MAAA,CAAO,aAAA,CAAc,MAAM;YACnD;QACJ;IACJ;IAAA;;;GAAA,GAMA,cAAc;QACV,IAAI,IAAA,CAAK,OAAA,EAAS;YACd,IAAA,CAAK,OAAA,CAAQ,IAAA,CAAK;YAClB,IAAA,CAAK,OAAA,GAAU;QACnB;IACJ;AACJ;;ADrQO,IAAe,oBAAf,cAAyC,OAAO;IACnD,IAAa,aAAuC;QAChD,OAAO,IAAA,CAAK,OAAA;IAChB;IAEA,YAAY,MAAA,EAAgB,MAAA,EAAuB,MAAA,CAAsB;QACrE,KAAA,CAAM,QAAQ,QAAQ,MAAM;IAChC;IAEU,qBAA2B;QACjC,IAAA,CAAK,OAAA,CAAQ,WAAW,CAAA,GAAI,IAAA;IAChC;IAES,UAAgB;QACrB,OAAO,IAAA,CAAK,OAAA,CAAQ,WAAW,CAAA;QAE/B,KAAA,CAAM,QAAQ;IAClB;IAES,OAAO,MAAA,EAA4B;QACxC,KAAA,CAAM,OAAO,MAAM;QAEnB,MAAM,UAAU,IAAA,CAAK,UAAA;QAErB,QAAQ,EAAA,GAAK,CAAA,WAAA,EAAc,IAAA,CAAK,MAAA,CAAO,EAAE,EAAA;QAGzC,QAAQ,YAAA,CAAa,SAAS,YAAY;QAC1C,IAAI,IAAA,CAAK,KAAA,CAAM,OAAA,EAAS;YACpB,QAAQ,SAAA,CAAU,GAAA,CAAI,qBAAqB;QAC/C;QACA,IAAI,IAAA,CAAK,MAAA,CAAO,OAAA,EAAS;YACrB,QAAQ,SAAA,CAAU,GAAA,CAAI,yBAAyB;QACnD;QACA,IAAI,IAAA,CAAK,MAAA,CAAO,OAAA,EAAS;YACrB,QAAQ,SAAA,CAAU,GAAA,CAAI,yBAAyB;QACnD;QACA,IAAI,IAAA,CAAK,MAAA,CAAO,SAAA,EAAW;mLACvBC,QAAAA,CAAM,UAAA,CAAW,SAAS,IAAA,CAAK,MAAA,CAAO,SAAS;QACnD;QAGA,QAAQ,KAAA,CAAM,OAAA,GAAU,GAAG,IAAA,CAAK,MAAA,CAAO,OAAO,EAAA;QAC9C,QAAQ,KAAA,CAAM,MAAA,GAAS,GAAG,KAAK,IAAA,CAAK,MAAA,CAAO,MAAM,EAAA;QACjD,IAAI,IAAA,CAAK,MAAA,CAAO,KAAA,EAAO;YACnB,OAAO,MAAA,CAAO,QAAQ,KAAA,EAAO,IAAA,CAAK,MAAA,CAAO,KAAK;QAClD;IACJ;AACJ;;AD7CO,IAAe,yBAAf,cAA8C,kBAAkB;IAGnE,YAAY,MAAA,EAAgB,MAAA,EAAuB,MAAA,CAAsB;QACrE,KAAA,CAAM,QAAQ,QAAQ,MAAM;IAChC;IAEmB,qBAA2B;QAC1C,KAAA,CAAM,mBAAmB;QAEzB,IAAA,CAAK,UAAA,CAAW,gBAAA,CAAiB,iBAAiB,MAAM;YAGpD,IAAA,CAAK,UAAA,CAAW,KAAA,CAAM,UAAA,GAAa;QACvC,CAAC;IACL;IAES,OAAO,EACZ,cAAA,EACA,SAAA,EACA,cAAA,EACJ,EAIU;QACN,IAAA,CAAK,YAAA,CAAa;QAElB,MAAM,WAAW,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,qBAAA,CAAsB,IAAA,CAAK,KAAA,CAAM,WAAA,CAAY,CAAC,CAAC;QACvF,SAAS,CAAA,IAAK,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,KAAA,GAAQ,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,CAAA;QACxD,SAAS,CAAA,IAAK,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,MAAA,GAAS,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,CAAA;QAGzD,MAAM,YACF,IAAA,CAAK,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,GAAA,CAAI,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,SAAS,IAAI,KAC1D,SAAS,CAAA,GAAI,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,KAAA,IAAS,KACtC,SAAS,CAAA,GAAI,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,KAAA,IAAS,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,IAAA,CAAK,KAAA,IAC7D,SAAS,CAAA,GAAI,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,MAAA,IAAU,KACvC,SAAS,CAAA,GAAI,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,MAAA,IAAU,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,IAAA,CAAK,MAAA;QAGrE,IAAI,WAAW;YACX,IAAA,CAAK,UAAA,CAAW,KAAA,CAAM,SAAA,GAAY,GAAG,SAAS,CAAC,CAAA,GAAA,EAAM,SAAS,CAAC,CAAA,MAAA,CAAA;YAE/D,IAAA,CAAK,UAAA,CAAW;gBACZ;gBACA;gBACA,WAAW,IAAA,KAAS;YACxB,CAAC;YAED,OAAO;QACX,OAAO;YACH,OAAO;QACX;IACJ;IAES,OAAO,MAAA,EAA4B;QACxC,KAAA,CAAM,OAAO,MAAM;QAEnB,IAAI,wKAACC,QAAAA,CAAM,kBAAA,CAAmB,IAAA,CAAK,MAAA,CAAO,QAAQ,GAAG;YACjD,MAAM,IAAIC,kLAAAA,CAAS,CAAA,eAAA,EAAkB,IAAA,CAAK,EAAE,CAAA,SAAA,CAAW;QAC3D;QAGA,IAAI;YACA,IAAA,CAAK,KAAA,CAAM,QAAA,GAAW,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,aAAA,CAAc,IAAA,CAAK,MAAA,CAAO,QAAQ;QACnF,EAAA,OAAS,GAAG;YACR,MAAM,2KAAIA,WAAAA,CAAS,CAAA,eAAA,EAAkB,IAAA,CAAK,EAAE,CAAA,SAAA,CAAA,EAAa,CAAC;QAC9D;QAGA,IAAA,CAAK,KAAA,CAAM,WAAA,GAAc;YAAC,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,wBAAA,CAAyB,IAAA,CAAK,KAAA,CAAM,QAAQ,CAAC;SAAA;QAE9F,MAAM,UAAU,IAAA,CAAK,UAAA;QAErB,QAAQ,SAAA,CAAU,GAAA,CAAI,oBAAoB;QAE1C,IAAI,IAAA,CAAK,MAAA,CAAO,KAAA,IAAS,MAAM,OAAA,CAAQ,IAAA,CAAK,MAAA,CAAO,KAAK,GAAG;YACvD,IAAA,CAAK,MAAA,CAAO,KAAA,GAAQ;gBAAE,MAAM,IAAA,CAAK,MAAA,CAAO,KAAA;YAAa;QACzD;QACA,IAAI,OAAO,IAAA,CAAK,MAAA,CAAO,UAAA,KAAe,WAAW;YAC7C,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa,IAAA,CAAK,MAAA,CAAO,UAAA,GAC/B,IAAA,CAAK,MAAA,CAAO,MAAA,CAAO,iBAAA,IAAqB,sBACxC;QACV,OAAA,IAAW,OAAO,IAAA,CAAK,MAAA,CAAO,UAAA,KAAe,UAAU;YACnD,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa;gBAAE,QAAQ,IAAA,CAAK,MAAA,CAAO,UAAA;YAAW;QAC9D,OAAA,IAAW,CAAC,IAAA,CAAK,MAAA,CAAO,UAAA,EAAY;YAChC,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa,IAAA,CAAK,MAAA,CAAO,MAAA,CAAO,iBAAA;QAChD;QACA,IAAI,IAAA,CAAK,MAAA,CAAO,UAAA,EAAY;YACxB,IAAA,CAAK,MAAA,CAAO,UAAA,GAAa;gBACrB,GAAG,IAAA,CAAK,MAAA,CAAO,MAAA,CAAO,iBAAA;gBACtB,GAAG,IAAA,CAAK,MAAA,CAAO,UAAA;YACnB;QACJ;QAGA,QAAQ,KAAA,CAAM,MAAA,GAAS,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,IAAA,KAAS,sJAAI,YAAA,CAAU,QAAA,CAAS,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,IAAI,IAAI,QAAQ;QAGjH,QAAQ,KAAA,CAAM,eAAA,GAAkB,GAAG,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,CAAA,GAAI,GAAG,CAAA,EAAA,EAAK,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,CAAA,GAAI,GAAG,CAAA,CAAA,CAAA;IAC9F;IAAA;;;;GAAA,GAOQ,eAAe;QACnB,IAAI,CAAC,IAAA,CAAK,eAAA,EAAiB;YACvB;QACJ;QAEA,MAAM,UAAU,IAAA,CAAK,UAAA;QACrB,MAAM,kBAAkB,CAAC,IAAA,CAAK,KAAA,CAAM,OAAA,IAAW,CAAC,IAAA,CAAK,KAAA,CAAM,IAAA;QAE3D,IAAI,iBAAiB;YACjB,QAAQ,SAAA,CAAU,GAAA,CAAI,yBAAyB;QACnD;QAEA,IAAI,IAAA,CAAK,KAAA,CAAM,GAAG;YACd,MAAM,OAAQ,QAAQ,iBAAA,CAAiC,qBAAA,CAAsB;YAC7E,IAAA,CAAK,KAAA,CAAM,IAAA,GAAO;gBACd,OAAO,KAAK,KAAA;gBACZ,QAAQ,KAAK,MAAA;YACjB;QACJ,OAAO;YACH,IAAA,CAAK,KAAA,CAAM,IAAA,GAAO;gBACd,OAAQ,QAAwB,WAAA;gBAChC,QAAS,QAAwB,YAAA;YACrC;QACJ;QAEA,IAAI,iBAAiB;YACjB,QAAQ,SAAA,CAAU,MAAA,CAAO,yBAAyB;QACtD;QAEA,IAAI,IAAA,CAAK,KAAA,CAAM,GAAG;YAEd,QAAQ,KAAA,CAAM,KAAA,GAAQ,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,KAAA,GAAQ;YAC9C,QAAQ,KAAA,CAAM,MAAA,GAAS,IAAA,CAAK,KAAA,CAAM,IAAA,CAAK,MAAA,GAAS;QACpD;QAGA,IAAI,IAAA,CAAK,IAAA,KAAA,UAAA,WAAA,KAA6B;YAClC,IAAA,CAAK,eAAA,GAAkB;QAC3B;IACJ;IAAA;;GAAA,GAKA,WAAW,EACP,SAAA,EACA,cAAA,EACA,SAAA,EACJ,EAIG;QACC,IAAI,cAAc,QAAQ,IAAA,CAAK,MAAA,CAAO,UAAA,EAAY;YAC9C,IAAA,CAAK,UAAA,CAAW,KAAA,CAAM,UAAA,GAAa,CAAA,MAAA,EAAS,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,QAAQ,CAAA,GAAA,EAAM,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,MAAM,EAAA;QAClH;QAEA,IAAI,QAAQ;QACZ,IAAI,OAAO,IAAA,CAAK,MAAA,CAAO,KAAA,KAAU,YAAY;YACzC,QAAQ,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,WAAW,cAAc;QACvD,OAAA,IAAW,IAAA,CAAK,MAAA,CAAO,KAAA,EAAO;YAC1B,IAAI,MAAM,OAAA,CAAQ,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,IAAI,GAAG;gBACvC,MAAM,CAAC,KAAK,GAAG,CAAA,GAAI,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,IAAA;gBACrC,SAAS,MAAA,CAAO,MAAM,GAAA,2KAAO,YAAA,CAAU,OAAA,CAAQ,MAAA,CAAO,YAAY,GAAG;YACzE;YACA,IAAI,MAAM,OAAA,CAAQ,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,GAAG,GAAG;gBACtC,MAAM,CAAC,KAAK,GAAG,CAAA,GAAI,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,GAAA;gBACrC,MAAM,UAAU,8JAAA,CAAU,QAAA,CAAS,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,IAAI,IAAI;gBAC7D,MAAM,MAAM,KAAK,GAAA,wKAAID,QAAAA,CAAM,cAAA,CAAe,IAAA,CAAK,KAAA,CAAM,QAAA,CAAS,GAAA,EAAK,eAAe,GAAG,CAAC;gBACtF,SAAS,MAAA,CAAO,MAAM,GAAA,2KAAO,YAAA,CAAU,OAAA,CAAQ,OAAA,CAAQ,KAAK,GAAA,CAAI,GAAA,CAAI,UAAU,GAAA,IAAO,OAAO,CAAC;YACjG;QACJ;QACA,IAAI,aAAa,IAAA,CAAK,MAAA,CAAO,UAAA,EAAY;YACrC,SAAS,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,MAAA;QACpC;QAEA,IAAA,CAAK,UAAA,CAAW,KAAA,CAAM,KAAA,GAAQ,GAAG,KAAK,EAAA;IAC1C;AACJ;;;;;AKvMA,IAAA,6BAAA;;ACAA,IAAA,2BAAA;;AF2BO,IAAM,oBAAN,cAAgC,mKAAA,CAAe;IAGlD,IAAI,MAAe;QACf,OAAO,IAAA,CAAK,QAAA,CAAS,GAAA,CAAI,KAAA;IAC7B;IAEA,IAAI,IAAI,GAAA,EAAc;QAClB,IAAA,CAAK,QAAA,CAAS,GAAA,CAAI,KAAA,GAAQ;IAC9B;IAEA,IAAI,MAAM,KAAA,EAAe;QACrB,IAAA,CAAK,QAAA,CAAS,KAAA,CAAM,KAAA,GAAQ;IAChC;IAEA,IAAI,SAAkB;QAClB,OAAO,IAAA,CAAK,QAAA,CAAS,MAAA,CAAO,KAAA;IAChC;IAEA,IAAI,SAAkB;QAClB,OAAO,IAAA,CAAK,QAAA,CAAS,MAAA,CAAO,KAAA;IAChC;IAEA,IAAI,UAAU,SAAA,EAAsB;QAChC,IAAA,CAAK,QAAA,CAAS,MAAA,CAAO,KAAA,GAAQ,WAAW,YAAY;QACpD,IAAI,WAAW,SAAS;YACpB,IAAI,OAAO,UAAU,KAAA,KAAU,YAAY,OAAO,UAAU,KAAA,EAAO;gBAC/D,IAAA,CAAK,QAAA,CAAS,KAAA,CAAM,KAAA,CAAM,GAAA,CACtB,UAAU,KAAA,CAAM,CAAA,GAAI,KACpB,UAAU,KAAA,CAAM,CAAA,GAAI,KACpB,UAAU,KAAA,CAAM,CAAA,GAAI;YAE5B,OAAO;gBACH,IAAA,CAAK,QAAA,CAAS,KAAA,CAAM,KAAA,CAAM,GAAA,CAAI,UAAU,KAAA,IAAS,KAAQ;YAC7D;YACA,IAAA,CAAK,QAAA,CAAS,UAAA,CAAW,KAAA,GAAQ,UAAU,UAAA,IAAc;YACzD,IAAA,CAAK,QAAA,CAAS,UAAA,CAAW,KAAA,GAAQ,UAAU,UAAA,IAAc;QAC7D;IACJ;IAEA,YAAY,MAAA,CAIT;QACC,KAAA,CAAM;YACF,aAAa;YACb,WAAW;YACX,YAAY;YACZ,UAAU;gBACN,KAAK;oBAAE,OAAO,QAAQ;gBAAI;gBAC1B,QAAQ;oBAAE,OAAO,sJAAI,UAAA,CAAQ,GAAG,CAAC;gBAAE;gBACnC,QAAQ;oBAAE,OAAO,sJAAI,UAAA,CAAQ,GAAG,CAAC;gBAAE;gBACnC,OAAO;oBAAE,OAAO,QAAQ,SAAS;gBAAE;gBACnC,QAAQ;oBAAE,OAAO;gBAAM;gBACvB,OAAO;oBAAE,OAAO,sJAAI,QAAA,CAAM,KAAQ;gBAAE;gBACpC,YAAY;oBAAE,OAAO;gBAAI;gBACzB,YAAY;oBAAE,OAAO;gBAAI;gBACzB,OAAO;oBAAE,OAAO;gBAAI;YACxB;YACA,cAAc;YACd,gBAAgB;QACpB,CAAC;QAED,IAAA,CAAK,SAAA,GAAY,QAAQ;IAC7B;AACJ;;AG1FO,SAAS,YAAY,EACxB,GAAA,EACA,eAAA,EACA,KAAA,EACA,QAAA,EACJ,EAKqB;IACjB,MAAM,QAAQ,SAAS,aAAA,CAAc,OAAO;IAC5C,MAAM,WAAA,GAAc,kBAAkB,oBAAoB;IAC1D,MAAM,IAAA,GAAO;IACb,MAAM,WAAA,GAAc;IACpB,MAAM,QAAA,GAAW;IACjB,MAAM,KAAA,GAAQ;IACd,MAAM,OAAA,GAAU;IAChB,IAAI,eAAe,aAAa;QAC5B,MAAM,SAAA,GAAY;IACtB,OAAO;QACH,MAAM,GAAA,GAAM;IAChB;IACA,OAAO;AACX;;;ACpBA,SAAS,0BAA0B,EAAA,EAAsB,EAAA,EAAsB,CAAA,EAA6B;IACxG,MAAM,CAAC,IAAI,GAAE,GAAI,CAAJ;IACb,MAAM,CAAC,IAAI,GAAE,GAAI,CAAJ;IAIb,MAAM,IAAIO,+KAAAA,CAAM,gBAAA,CAAiB,IAAI,EAAE;IACvC,MAAM,IAAI,KAAK,GAAA,CAAA,CAAK,IAAI,CAAA,IAAK,CAAC,IAAI,KAAK,GAAA,CAAI,CAAC;IAC5C,MAAM,IAAI,KAAK,GAAA,CAAI,IAAI,CAAC,IAAI,KAAK,GAAA,CAAI,CAAC;IACtC,MAAM,IAAI,IAAI,KAAK,GAAA,CAAI,MAAM,CAAJ,IAAS,GAAA,CAAI,MAAM,CAAJ,GAAQ,KAAK,GAAA,CAAI,MAAM,CAAJ,IAAS,GAAA,CAAI,OAAE;IAC1E,MAAM,IAAI,IAAI,KAAK,GAAA,CAAI,MAAM,CAAJ,IAAS,GAAA,CAAI,MAAM,CAAJ,GAAQ,KAAK,GAAA,CAAI,MAAM,CAAJ,IAAS,GAAA,CAAI,OAAE;IAC1E,MAAM,IAAI,IAAI,KAAK,GAAA,CAAI,MAAM,CAAJ,GAAQ,KAAK,GAAA,CAAI,OAAE;IAE5C,OAAO;QAAC,KAAK,KAAA,CAAM,GAAG,CAAC;QAAG,KAAK,KAAA,CAAM,GAAG,KAAK,IAAA,CAAK,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC;KAAA;AACrE;AAMA,SAAS,yBAAyB,MAAA,EAAiC;IAC/D,MAAM,aAAa;QAAC,MAAA,CAAO,CAAC,CAAC;KAAA;IAE7B,IAAI,IAAI;IACR,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,MAAA,EAAQ,IAAK;QACpC,MAAM,IAAI,MAAA,CAAO,IAAI,CAAC,CAAA,CAAE,CAAC,CAAA,GAAI,MAAA,CAAO,CAAC,CAAA,CAAE,CAAC,CAAA;QACxC,IAAI,IAAI,KAAK,EAAA,EAAI;YAEb,KAAK;QACT,OAAA,IAAW,IAAI,CAAC,KAAK,EAAA,EAAI;YAErB,KAAK;QACT;QACA,WAAW,IAAA,CAAK;YAAC,MAAA,CAAO,CAAC,CAAA,CAAE,CAAC,CAAA,GAAI,IAAI,IAAI,KAAK,EAAA;YAAI,MAAA,CAAO,CAAC,CAAA,CAAE,CAAC,CAAC;SAAC;IAClE;IAEA,OAAO;AACX;AAOO,SAAS,iBAAiB,OAAA,EAAoD;IACjF,MAAM,SAAS,yBAAyB,OAAO;IAE/C,MAAM,MAAM,OAAO,MAAA,CAAO,CAAC,cAAc,QAAU;YAAC,YAAA,CAAa,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAA;YAAG,YAAA,CAAa,CAAC,CAAA,GAAI,KAAA,CAAM,CAAC,CAAC;SAAC;IAC3G,OAAO;+KAACA,QAAAA,CAAM,UAAA,CAAW,GAAA,CAAI,CAAC,CAAA,GAAI,QAAQ,MAAM;QAAG,GAAA,CAAI,CAAC,CAAA,GAAI,QAAQ,MAAM;KAAA;AAC9E;AAMO,SAAS,kBAAkB,QAAA,EAAqD;IACnF,MAAM,SAAS,yBAAyB,QAAQ;IAGhD,IAAI,SAAS;IACb,MAAM,UAAU,CAAC,CAAA;IAEjB,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,MAAA,GAAS,GAAG,IAAK;QACxC,MAAM,2KAAIA,QAAAA,CAAM,gBAAA,CAAiB,MAAA,CAAO,CAAC,CAAA,EAAG,MAAA,CAAO,IAAI,CAAC,CAAC,2KAAID,YAAAA,CAAU,aAAA;QAEvE,QAAQ,IAAA,CAAK,CAAC;QACd,UAAU;IACd;IAGA,IAAI,WAAW;IAEf,IAAA,IAAS,IAAI,GAAG,IAAI,OAAO,MAAA,GAAS,GAAG,IAAK;QAExC,IAAI,WAAW,OAAA,CAAQ,CAAC,CAAA,GAAI,SAAS,GAAG;YACpC,MAAM,IAAA,CAAK,SAAS,IAAI,QAAA,IAAY,OAAA,CAAQ,CAAC,CAAA;YAC7C,OAAO,0BAA0B,MAAA,CAAO,CAAC,CAAA,EAAG,MAAA,CAAO,IAAI,CAAC,CAAA,EAAG,CAAC;QAChE;QAEA,YAAY,OAAA,CAAQ,CAAC,CAAA;IACzB;IAGA,OAAO,MAAA,CAAO,KAAK,KAAA,CAAM,OAAO,MAAA,GAAS,CAAC,CAAC,CAAA;AAC/C;AAEA,IAAM,IAAI,sJAAIE,UAAAA,CAAQ;AACtB,IAAM,IAAI,sJAAIA,UAAAA,CAAQ;AACtB,IAAM,IAAI,sJAAIA,UAAAA,CAAQ;AACtB,IAAM,IAAI,sJAAIA,UAAAA,CAAQ;AACtB,IAAM,IAAI,sJAAIA,UAAAA,CAAQ;AACtB,IAAM,IAAI,sJAAIA,UAAAA,CAAQ;AASf,SAAS,2BAA2B,EAAA,EAAa,EAAA,EAAa,SAAA,EAA6B;IAC9F,EAAE,IAAA,CAAK,SAAS,EAAE,SAAA,CAAU;IAC5B,EAAE,YAAA,CAAa,IAAI,EAAE,EAAE,SAAA,CAAU;IACjC,EAAE,YAAA,CAAa,GAAG,EAAE,EAAE,SAAA,CAAU;IAChC,EAAE,IAAA,CAAK,EAAE,EAAE,cAAA,CAAe,CAAC,EAAE,GAAA,CAAI,CAAC,CAAC;IACnC,EAAE,IAAA,CAAK,CAAC,EAAE,cAAA,CAAe,EAAE,GAAA,CAAI,EAAE,CAAC;IAClC,MAAM,IAAI,sJAAIA,UAAAA,CAAQ,EAAE,UAAA,CAAW,GAAG,CAAC,EAAE,SAAA,CAAU;IACnD,EAAE,YAAA,CAAa,GAAG,CAAC;IACnB,OAAO,EAAE,cAAA,CAAe,GAAG,IAAI,EAAE,cAAA,wKAAeF,YAAAA,CAAU,aAAa;AAC3E;;AL9FO,IAAM,WAAN,cAAuB,OAAO;IACjC,IAAa,eAAsB;QAC/B,OAAO,IAAA,CAAK,OAAA;IAChB;IAEA,IAAI,YAAoD;QACpD,OAAO,IAAA,CAAK,YAAA,CAAa,QAAA,CAAS,CAAC,CAAA;IACvC;IAEA,IAAa,QAA0B;QACnC,IAAI,IAAA,CAAK,IAAA,KAAA,aAAA,cAAA,KAAgC;YACrC,OAAO,IAAA,CAAK,SAAA,CAAU,QAAA,CAAS,GAAA,CAAI,KAAA;QACvC,OAAO;YACH,OAAO;QACX;IACJ;IAEA,YAAY,MAAA,EAAgB,MAAA,EAAuB,MAAA,CAAsB;QACrE,KAAA,CAAM,QAAQ,QAAQ,MAAM;IAChC;IAES,OAAgB;QACrB,OAAO;IACX;IAES,gBAAsB;QAC3B,MAAM,WAAW,IAAI,kBAAkB;YAAE,OAAO;QAAE,CAAC;QACnD,MAAM,WAAW,sJAAI,gBAAA,CAAc,GAAG,CAAC;QACvC,MAAM,OAAO,qJAAI,QAAA,CAAK,UAAU,QAAQ;QACxC,KAAK,QAAA,GAAW;YAAE,CAAC,WAAW,CAAA,EAAG,IAAA;QAAK;QAItC,OAAO,cAAA,CAAe,MAAM,WAAW;YACnC,YAAY;YACZ,KAAK,WAA0B;gBAC3B,OAAQ,IAAA,CAAK,QAAA,CAAS,WAAW,CAAA,CAAa,MAAA,CAAO,OAAA;YACzD;YACA,KAAK,SAA0B,OAAA,EAAkB;gBAC5C,IAAA,CAAK,QAAA,CAAS,WAAW,CAAA,CAAa,MAAA,CAAO,OAAA,GAAU;YAC5D;QACJ,CAAC;QAED,IAAA,CAAK,OAAA,GAAU,sJAAI,QAAA,CAAM,EAAE,GAAA,CAAI,IAAI;QAEnC,IAAI,IAAA,CAAK,IAAA,KAAA,aAAA,cAAA,KAAgC;YACrC,IAAA,CAAK,MAAA,CAAO,qBAAA,CAAsB,IAAI;QAC1C;IACJ;IAES,UAAgB;QACrB,OAAO,IAAA,CAAK,SAAA,CAAU,QAAA,CAAS,WAAW,CAAA;QAE1C,IAAI,IAAA,CAAK,IAAA,KAAA,aAAA,cAAA,KAAgC;YACrC,IAAA,CAAK,KAAA,CAAM,KAAA,CAAM;YACjB,IAAA,CAAK,MAAA,CAAO,qBAAA,CAAsB,KAAK;QAC3C;QAEA,KAAA,CAAM,QAAQ;IAClB;IAES,SAAgB;QACrB,IAAI,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,eAAA,CAAgB,IAAA,CAAK,SAAS,GAAG;YACtD,OAAO,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,6BAAA,CAA8B,IAAA,CAAK,KAAA,CAAM,QAAQ;QACnF,OAAO;YACH,OAAO;QACX;IACJ;IAES,OAAO,MAAA,EAA4B;QACxC,KAAA,CAAM,OAAO,MAAM;QAEnB,MAAM,OAAO,IAAA,CAAK,SAAA;QAClB,MAAM,QAAQ,KAAK,MAAA;QACnB,MAAM,WAAW,KAAK,QAAA;QAEtB,2KAAIG,QAAAA,CAAM,kBAAA,CAAmB,IAAA,CAAK,MAAA,CAAO,QAAQ,GAAG;YAChD,IAAI;gBACA,IAAA,CAAK,KAAA,CAAM,QAAA,GAAW,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,aAAA,CAAc,IAAA,CAAK,MAAA,CAAO,QAAQ;YACnF,EAAA,OAAS,GAAG;gBACR,MAAM,2KAAIC,WAAAA,CAAS,CAAA,eAAA,EAAkB,IAAA,CAAK,EAAE,CAAA,SAAA,CAAA,EAAa,CAAC;YAC9D;YAEA,IAAI,CAAC,IAAA,CAAK,MAAA,CAAO,IAAA,EAAM;gBACnB,MAAM,2KAAIA,WAAAA,CAAS,CAAA,eAAA,EAAkB,IAAA,CAAK,EAAE,CAAA,KAAA,CAAO;YACvD;YAEA,IAAA,CAAK,KAAA,CAAM,IAAA,GAAO,IAAA,CAAK,MAAA,CAAO,IAAA;YAG9B,KAAK,KAAA,CAAM,GAAA,CAAI,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,KAAA,GAAQ,KAAK,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,MAAA,GAAS,KAAK,CAAC;YAC7E,KAAK,QAAA,CAAS,GAAA,CAAI,KAAK,KAAA,CAAM,CAAA,GAAA,CAAK,MAAM,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,CAAA,GAAI,KAAK,KAAA,CAAM,CAAA,GAAA,CAAK,IAAA,CAAK,KAAA,CAAM,MAAA,CAAO,CAAA,GAAI,GAAA,GAAM,CAAC;YAC3G,KAAK,QAAA,CAAS,GAAA,CAAI,GAAG,GAAG,CAAC;YACzB,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,wBAAA,CAAyB,IAAA,CAAK,KAAA,CAAM,QAAA,EAAU,MAAM,QAAQ;YAEnF,MAAM,MAAA,CAAO,GAAG,MAAM,QAAA,CAAS,CAAA,EAAG,CAAC;YACnC,KAAK,OAAA,CAAQ,CAAC,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,GAAG;YACtC,KAAK,OAAA,CAAQ,CAAC,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,KAAK;YACxC,KAAK,OAAA,CAAQ,CAAC,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,IAAI;YAEvC,MAAM,IAAI,KAAK,QAAA,CAAS,YAAA,CAAa,UAAU;YAC/C,IAAA,CAAK,KAAA,CAAM,WAAA,GAAc;gBAAC;gBAAG;gBAAG;gBAAG,CAAC;aAAA,CAAE,GAAA,CAAI,CAAC,MAAM;gBAC7C,MAAM,KAAK,sJAAIC,UAAAA,CAAQ;gBACvB,GAAG,mBAAA,CAAoB,GAAG,CAAC;gBAC3B,OAAO,KAAK,YAAA,CAAa,EAAE;YAC/B,CAAC;QACL,OAAO;YACH,IAAI,IAAA,CAAK,MAAA,CAAO,QAAA,EAAU,WAAW,GAAG;gBACpC,MAAM,2KAAID,WAAAA,CAAS,CAAA,eAAA,EAAkB,IAAA,CAAK,EAAE,CAAA,SAAA,CAAW;YAC3D;YAEA,IAAI;YACJ,IAAI;gBACA,YAAY,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,GAAA,CAAI,CAAAE,KAAK,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,aAAA,CAAcA,EAAC,CAAC;YACrF,EAAA,OAAS,GAAG;gBACR,MAAM,IAAIF,kLAAAA,CAAS,CAAA,eAAA,EAAkB,IAAA,CAAK,EAAE,CAAA,SAAA,CAAA,EAAa,CAAC;YAC9D;YAEA,MAAM,cAAc,UAAU,GAAA,CAAI,CAAAE,KAAK,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,wBAAA,CAAyBA,EAAC,CAAC;YAEzF,MAAM,WAAW,iBAAiB,UAAU,GAAA,CAAI,CAAC,EAAE,GAAA,EAAK,KAAA,CAAM,CAAA,GAAM;oBAAC;oBAAK,KAAK;iBAAC,CAAC;YACjF,IAAA,CAAK,KAAA,CAAM,QAAA,GAAW;gBAAE,KAAK,QAAA,CAAS,CAAC,CAAA;gBAAG,OAAO,QAAA,CAAS,CAAC,CAAA;YAAE;YAE7D,IAAA,CAAK,KAAA,CAAM,WAAA,GAAc;YAEzB,MAAM,IAAI,KAAK,QAAA,CAAS,YAAA,CAAa,UAAU;YAC/C;gBACI,WAAA,CAAY,CAAC,CAAA;gBACb,WAAA,CAAY,CAAC,CAAA;gBACb,WAAA,CAAY,CAAC,CAAA;gBAAA,iBAAA;gBACb,WAAA,CAAY,CAAC,CAAA;aACjB,CAAE,OAAA,CAAQ,CAAC,GAAG,MAAM;gBAChB,EAAE,IAAA,CAAK,GAAG,EAAE,CAAC;gBACb,EAAE,IAAA,CAAK,GAAG,EAAE,CAAC;gBACb,EAAE,IAAA,CAAK,GAAG,EAAE,CAAC;YACjB,CAAC;YACD,EAAE,WAAA,GAAc;YAEhB,IAAA,CAAK,gBAAA,CAAiB,QAAQ;QAClC;QAEA,OAAQ,IAAA,CAAK,IAAA,EAAM;YACf,KAAA,aAAA,cAAA;gBACI,IAAI,IAAA,CAAK,UAAA,KAAe,IAAA,CAAK,MAAA,CAAO,UAAA,EAAY;oBAC5C,SAAS,GAAA,EAAK,QAAQ;oBAEtB,MAAM,QAAQ,YAAY;wBACtB,KAAK,IAAA,CAAK,MAAA,CAAO,UAAA;wBACjB,iBAAiB,IAAA,CAAK,MAAA,CAAO,MAAA,CAAO,eAAA;wBACpC,OAAO;wBACP,UAAU,IAAA,CAAK,MAAA,CAAO,QAAA,IAAY;oBACtC,CAAC;oBAED,MAAM,UAAU,IAAI,iKAAA,CAAa,KAAK;oBACtC,SAAS,GAAA,GAAM;oBACf,SAAS,KAAA,GAAQ;oBAEjB,MAAM,gBAAA,CAAiB,kBAAkB,MAAM;wBAC3C,IAAI,CAAC,IAAA,CAAK,MAAA,EAAQ;4BACd;wBACJ;wBAEA,SAAS,KAAA,GAAQ,IAAA,CAAK,MAAA,CAAO,OAAA;wBAE7B,IAAI,wKAACH,QAAAA,CAAM,kBAAA,CAAmB,IAAA,CAAK,MAAA,CAAO,QAAQ,GAAG;4BACjD,KAAK,QAAA,CAAS,QAAA,CAAS,WAAW,CAAA,GAAI;gCAAE,OAAO,MAAM,UAAA;gCAAY,QAAQ,MAAM,WAAA;4BAAY;4BAC3F,IAAA,CAAK,gBAAA,CAAiB,QAAQ;wBAClC;oBACJ,GAAG;wBAAE,MAAM;oBAAK,CAAC;oBAEjB,IAAI,MAAM,QAAA,EAAU;wBAChB,MAAM,IAAA,CAAK;oBACf;oBAEA,IAAA,CAAK,UAAA,GAAa,IAAA,CAAK,MAAA,CAAO,UAAA;gBAClC,OAAO;oBACH,SAAS,KAAA,GAAQ,IAAA,CAAK,MAAA,CAAO,OAAA;gBACjC;gBACA;YAEJ,KAAA,aAAA,cAAA;gBACI,IAAI,IAAA,CAAK,UAAA,KAAe,IAAA,CAAK,MAAA,CAAO,UAAA,EAAY;oBAC5C,SAAS,GAAA,EAAK,QAAQ;oBAEtB,MAAM,UAAU,qJAAII,WAAAA,CAAQ;oBAC5B,SAAS,GAAA,GAAM;oBACf,SAAS,KAAA,GAAQ;oBAEjB,IAAA,CAAK,MAAA,CAAO,aAAA,CAAc,SAAA,CAAU,IAAA,CAAK,MAAA,CAAO,UAAU,EAAE,IAAA,CAAK,CAAC,UAAU;wBACxE,IAAI,CAAC,IAAA,CAAK,MAAA,EAAQ;4BACd;wBACJ;wBAEA,IAAI,wKAACJ,QAAAA,CAAM,kBAAA,CAAmB,IAAA,CAAK,MAAA,CAAO,QAAQ,GAAG;4BACjD,KAAK,QAAA,CAAS,QAAA,CAAS,WAAW,CAAA,GAAI;gCAAE,OAAO,MAAM,KAAA;gCAAO,QAAQ,MAAM,MAAA;4BAAO;4BACjF,IAAA,CAAK,gBAAA,CAAiB,QAAQ;wBAClC;wBAEA,QAAQ,KAAA,GAAQ;wBAChB,QAAQ,UAAA,GAAa;wBACrB,QAAQ,WAAA,GAAc;wBACtB,SAAS,KAAA,GAAQ,IAAA,CAAK,MAAA,CAAO,OAAA;wBAE7B,IAAA,CAAK,MAAA,CAAO,WAAA,CAAY;oBAC5B,CAAC;oBAED,IAAA,CAAK,UAAA,GAAa,IAAA,CAAK,MAAA,CAAO,UAAA;gBAClC,OAAO;oBACH,SAAS,KAAA,GAAQ,IAAA,CAAK,MAAA,CAAO,OAAA;gBACjC;gBACA;QAGR;QAEA,SAAS,SAAA,GAAY,IAAA,CAAK,MAAA,CAAO,SAAA;QACjC,KAAK,WAAA,GAAc,MAAO,IAAA,CAAK,MAAA,CAAO,MAAA;QACtC,KAAK,QAAA,CAAS,WAAA,GAAc;IAChC;IAAA;;GAAA,GAKQ,iBAAiB,QAAA,EAA6B;QAClD,MAAM,YAAkB,SAAS,QAAA,CAAS,WAAW,CAAA;QAErD,IAAI,CAAC,aAAa,CAAC,UAAU,MAAA,IAAU,CAAC,UAAU,KAAA,EAAO;YACrD,SAAS,MAAA,CAAO,GAAA,CAAI,GAAG,CAAC;YACxB,SAAS,MAAA,CAAO,GAAA,CAAI,GAAG,CAAC;YACxB;QACJ;QAEA,MAAM,YAAa,IAAA,CAAK,MAAA,CAAO,QAAA,CAAgC,GAAA,CAAI,CAAC,MAAM;YACtE,OAAO,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,aAAA,CAAc,CAAC;QACjD,CAAC;QAED,MAAM,4KAAKA,QAAAA,CAAM,gBAAA,CACb;YAAC,SAAA,CAAU,CAAC,CAAA,CAAE,GAAA;YAAK,SAAA,CAAU,CAAC,CAAA,CAAE,KAAK;SAAA,EACrC;YAAC,SAAA,CAAU,CAAC,CAAA,CAAE,GAAA;YAAK,SAAA,CAAU,CAAC,CAAA,CAAE,KAAK;SAAA;QAEzC,MAAM,KAAKA,+KAAAA,CAAM,gBAAA,CACb;YAAC,SAAA,CAAU,CAAC,CAAA,CAAE,GAAA;YAAK,SAAA,CAAU,CAAC,CAAA,CAAE,KAAK;SAAA,EACrC;YAAC,SAAA,CAAU,CAAC,CAAA,CAAE,GAAA;YAAK,SAAA,CAAU,CAAC,CAAA,CAAE,KAAK;SAAA;QAEzC,MAAM,4KAAKA,QAAAA,CAAM,gBAAA,CACb;YAAC,SAAA,CAAU,CAAC,CAAA,CAAE,GAAA;YAAK,SAAA,CAAU,CAAC,CAAA,CAAE,KAAK;SAAA,EACrC;YAAC,SAAA,CAAU,CAAC,CAAA,CAAE,GAAA;YAAK,SAAA,CAAU,CAAC,CAAA,CAAE,KAAK;SAAA;QAEzC,MAAM,4KAAKA,QAAAA,CAAM,gBAAA,CACb;YAAC,SAAA,CAAU,CAAC,CAAA,CAAE,GAAA;YAAK,SAAA,CAAU,CAAC,CAAA,CAAE,KAAK;SAAA,EACrC;YAAC,SAAA,CAAU,CAAC,CAAA,CAAE,GAAA;YAAK,SAAA,CAAU,CAAC,CAAA,CAAE,KAAK;SAAA;QAGzC,MAAM,aAAA,CAAc,KAAK,EAAA,IAAA,CAAO,KAAK,EAAA;QACrC,MAAM,aAAa,UAAU,KAAA,GAAQ,UAAU,MAAA;QAE/C,IAAI,UAAU;QACd,IAAI,UAAU;QACd,IAAI,aAAa,YAAY;YACzB,UAAU,aAAa;QAC3B,OAAO;YACH,UAAU,IAAI,aAAa,IAAI;QACnC;QAEA,SAAS,MAAA,CAAO,GAAA,CAAI,IAAI,SAAS,IAAI,OAAO;QAC5C,SAAS,MAAA,CAAO,GAAA,CAAI,UAAU,GAAG,UAAU,CAAC;IAChD;AACJ;;AMrRO,IAAM,cAAN,cAA0B,kBAAkB;IAY/C,YAAY,MAAA,EAAgB,MAAA,EAAuB,MAAA,CAAsB;QACrE,KAAA,CAAM,QAAQ,QAAQ,MAAM;QAPhC;;KAAA,GAAA,IAAA,CAAA,oBAAA,GAAuB;IAQvB;IANA,IAAa,eAAe;QACxB,OAAO,IAAA,CAAK,MAAA;IAChB;IAMS,UAAmB;QACxB,OAAO;IACX;IAES,gBAAsB;QAC3B,IAAA,CAAK,OAAA,GAAU,SAAS,aAAA,CAAc,KAAK;QAE3C,IAAA,CAAK,MAAA,GAAS,IAAI,YAAY,IAAA,CAAK,OAAO;QAC1C,IAAA,CAAK,MAAA,CAAO,QAAA,GAAW;YAAE,CAAC,WAAW,CAAA,EAAG,IAAA;QAAK;QAI7C,OAAO,cAAA,CAAe,IAAA,CAAK,MAAA,EAAQ,WAAW;YAC1C,YAAY;YACZ,KAAK,WAA0B;gBAC3B,OAAQ,IAAA,CAAK,QAAA,CAAS,WAAW,CAAA,CAAa,MAAA,CAAO,OAAA;YACzD;YACA,KAAK,SAA0B,OAAA,EAAkB;gBAC5C,IAAA,CAAK,QAAA,CAAS,WAAW,CAAA,CAAa,MAAA,CAAO,OAAA,GAAU;YAC5D;QACJ,CAAC;QAED,IAAA,CAAK,kBAAA,CAAmB;IAC5B;IAES,UAAgB;QACrB,OAAO,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,WAAW,CAAA;QACvC,OAAO,IAAA,CAAK,MAAA;QAEZ,KAAA,CAAM,QAAQ;IAClB;IAES,OAAO,EACZ,cAAA,EACA,SAAA,EACJ,EAGU;QACN,MAAM,UAAU,IAAA,CAAK,UAAA;QAErB,IAAA,CAAK,KAAA,CAAM,IAAA,GAAO;YACd,OAAQ,QAAwB,WAAA;YAChC,QAAS,QAAwB,YAAA;QACrC;QAEA,MAAM,YAAY,IAAA,CAAK,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,GAAA,CAAI,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,SAAS,IAAI,KAAK,IAAA,CAAK,oBAAA;QAEzF,IAAI,WAAW;YACX,MAAM,WAAW,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,6BAAA,CAA8B,IAAA,CAAK,KAAA,CAAM,QAAQ;YAEzF,IAAA,CAAK,MAAA,CAAO,YAAA,CAAa,YAAA,GAAe;gBACpC,QAAQ,IAAA;gBACR;gBACA;gBACA;gBACA,YAAY,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,IAAA;YAClC,CAAC;YAED,OAAO;QACX,OAAO;YACH,OAAO;QACX;IACJ;IAES,OAAO,MAAA,EAA4B;QACxC,KAAA,CAAM,OAAO,MAAM;QAEnB,IAAI,wKAACO,QAAAA,CAAM,kBAAA,CAAmB,IAAA,CAAK,MAAA,CAAO,QAAQ,GAAG;YACjD,MAAM,2KAAIC,WAAAA,CAAS,CAAA,eAAA,EAAkB,IAAA,CAAK,EAAE,CAAA,SAAA,CAAW;QAC3D;QAGA,IAAI;YACA,IAAA,CAAK,KAAA,CAAM,QAAA,GAAW,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,aAAA,CAAc,IAAA,CAAK,MAAA,CAAO,QAAQ;QACnF,EAAA,OAAS,GAAG;YACR,MAAM,2KAAIA,WAAAA,CAAS,CAAA,eAAA,EAAkB,IAAA,CAAK,EAAE,CAAA,SAAA,CAAA,EAAa,CAAC;QAC9D;QAGA,IAAA,CAAK,KAAA,CAAM,WAAA,GAAc;YAAC,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,wBAAA,CAAyB,IAAA,CAAK,KAAA,CAAM,QAAQ,CAAC;SAAA;QAE9F,MAAM,SAAS,IAAA,CAAK,YAAA;QACpB,MAAM,UAAU,IAAA,CAAK,UAAA;QAErB,QAAQ,SAAA,CAAU,GAAA,CAAI,mBAAmB;QAEzC,QAAQ,UAAA,CAAW,OAAA,CAAQ,CAAA,IAAK,EAAE,MAAA,CAAO,CAAC;QAC1C,QAAQ,WAAA,CAAY,IAAA,CAAK,MAAA,CAAO,YAAY;QAC5C,IAAA,CAAK,MAAA,CAAO,YAAA,CAAa,KAAA,CAAM,OAAA,GAAU;QAEzC,OAAO,QAAA,CAAS,IAAA,CAAK,IAAA,CAAK,KAAA,CAAM,WAAA,CAAY,CAAC,CAAC,EAAE,cAAA,CAAe,GAAG;QAClE,OAAO,MAAA,CAAO,GAAG,IAAA,CAAK,KAAA,CAAM,WAAA,CAAY,CAAC,CAAA,CAAE,CAAA,GAAI,KAAK,CAAC;QACrD,OAAO,OAAA,CAAQ,CAAC,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,GAAG;QACxC,OAAO,OAAA,CAAQ,CAAC,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,KAAK;QAC1C,OAAO,OAAA,CAAQ,CAAC,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,IAAI;IAC7C;AACJ;;ACnHO,IAAM,eAAN,cAA2B,uBAAuB;IACrD,YAAY,MAAA,EAAgB,MAAA,EAAuB,MAAA,CAAsB;QACrE,KAAA,CAAM,QAAQ,QAAQ,MAAM;IAChC;IAES,WAAoB;QACzB,OAAO;IACX;IAES,gBAAsB;QAC3B,IAAA,CAAK,OAAA,GAAU,SAAS,aAAA,CAAc,KAAK;QAC3C,IAAA,CAAK,kBAAA,CAAmB;IAC5B;IAES,OAAO,MAAA,EAIN;QACN,MAAM,WAAW,KAAA,CAAM,OAAO,MAAM;QAEpC,IAAI,YAAY,IAAA,CAAK,IAAA,KAAA,UAAA,WAAA,KAA6B;YAC9C,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,YAAA,GAAe;gBAC/B,QAAQ,IAAA;gBACR;gBACA,gBAAgB,OAAO,cAAA;gBACvB,WAAW,OAAO,SAAA;gBAClB,YAAY,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,IAAA;YAClC,CAAC;QACL;QAEA,OAAO;IACX;IAES,OAAO,MAAA,EAA4B;QACxC,KAAA,CAAM,OAAO,MAAM;QAEnB,MAAM,UAAU,IAAA,CAAK,UAAA;QAErB,IAAI,IAAA,CAAK,MAAA,CAAO,KAAA,IAAS,CAAC,IAAA,CAAK,MAAA,CAAO,IAAA,EAAM;YACxC,MAAM,2KAAIE,WAAAA,CAAS,CAAA,eAAA,EAAkB,IAAA,CAAK,EAAE,CAAA,KAAA,CAAO;QACvD;QAEA,IAAI,IAAA,CAAK,MAAA,CAAO,IAAA,EAAM;YAClB,IAAA,CAAK,eAAA,GAAkB;YACvB,IAAA,CAAK,KAAA,CAAM,IAAA,GAAO,IAAA,CAAK,MAAA,CAAO,IAAA;YAC9B,QAAQ,KAAA,CAAM,KAAA,GAAQ,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,KAAA,GAAQ;YAC/C,QAAQ,KAAA,CAAM,MAAA,GAAS,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,MAAA,GAAS;QACrD,OAAO;YACH,IAAA,CAAK,eAAA,GAAkB;QAC3B;QAEA,OAAQ,IAAA,CAAK,IAAA,EAAM;YACf,KAAA,QAAA,SAAA;gBACI,IAAA,CAAK,UAAA,GAAa,IAAA,CAAK,MAAA,CAAO,KAAA;gBAC9B,QAAQ,KAAA,CAAM,eAAA,GAAkB,CAAA,KAAA,EAAQ,IAAA,CAAK,MAAA,CAAO,KAAK,CAAA,EAAA,CAAA;gBACzD;YACJ,KAAA,OAAA,QAAA;gBACI,IAAA,CAAK,UAAA,GAAa,IAAA,CAAK,MAAA,CAAO,IAAA;gBAC9B,QAAQ,SAAA,GAAY,IAAA,CAAK,MAAA,CAAO,IAAA;gBAChC;YACJ,KAAA,UAAA,WAAA;gBACI,IAAI,IAAA,CAAK,UAAA,KAAe,IAAA,CAAK,MAAA,CAAO,OAAA,EAAS;oBACzC,IAAA,CAAK,UAAA,GAAa,IAAA,CAAK,MAAA,CAAO,OAAA;oBAC9B,QAAQ,UAAA,CAAW,OAAA,CAAQ,CAAA,IAAK,EAAE,MAAA,CAAO,CAAC;oBAC1C,QAAQ,WAAA,CAAY,IAAA,CAAK,MAAA,CAAO,OAAO;oBACvC,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,KAAA,CAAM,OAAA,GAAU;gBACxC;gBACA;QACR;IACJ;AACJ;;AC7DO,IAAM,gBAAN,cAA4B,kBAAkB;IAGjD,YAAY,MAAA,EAAgB,MAAA,EAAuB,MAAA,CAAsB;QACrE,KAAA,CAAM,QAAQ,QAAQ,MAAM;IAChC;IAES,gBAAsB;QAC3B,IAAA,CAAK,OAAA,GAAU,SAAS,eAAA,CAAgB,QAAQ,MAAM;QACtD,IAAA,CAAK,OAAA,CAAQ,WAAW,CAAA,GAAI,IAAA;IAChC;IAES,SAAkB;QACvB,OAAO;IACX;IAAA;;GAAA,GAKA,IAAY,WAAoB;QAC5B,OAAO,IAAA,CAAK,IAAA,KAAA,gBAAA,iBAAA,OAAqC,IAAA,CAAK,IAAA,KAAA,iBAAA,kBAAA;IAC1D;IAAA;;GAAA,GAKA,IAAY,YAAqB;QAC7B,OAAO,IAAA,CAAK,IAAA,KAAA,UAAA,WAAA,OAA+B,IAAA,CAAK,IAAA,KAAA,gBAAA,iBAAA;IACpD;IAAA;;GAAA,GAKA,IAAY,aAAsB;QAC9B,OAAO,IAAA,CAAK,IAAA,KAAA,WAAA,YAAA,OAAgC,IAAA,CAAK,IAAA,KAAA,iBAAA,kBAAA;IACrD;IAEA,IAAY,SAAyC;QACjD,OAAO,IAAA,CAAK,UAAA;IAChB;IAES,SAAgB;QACrB,MAAM,YAAY,IAAA,CAAK,qBAAA,CAAsB;QAC7C,MAAM,YAAY,SAAA,CAAU,CAAC,CAAA,CAAE,MAAA,GAAA,CAAU,IAAA,CAAK,SAAA,GAAY,IAAI,CAAA;QAE9D,IAAI,WAAW;YACX,MAAM,WAAW,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,6BAAA,CAA8B,IAAA,CAAK,KAAA,CAAM,QAAQ;YAEzF,MAAM,SAAS,UACV,MAAA,CAAO,CAAA,WAAY,SAAS,MAAA,GAAS,CAAC,EACtC,GAAA,CAAI,CAAC,aAAa;gBACf,IAAI,cAAc;gBAClB,eAAe,SACV,GAAA,CAAI,CAAA,MAAO,GAAG,IAAI,CAAA,GAAI,SAAS,CAAC,CAAA,CAAA,EAAI,IAAI,CAAA,GAAI,SAAS,CAAC,EAAE,EACxD,IAAA,CAAK,GAAG;gBACb,IAAI,IAAA,CAAK,SAAA,EAAW;oBAChB,eAAe;gBACnB;gBACA,OAAO;YACX,CAAC,EACA,IAAA,CAAK,GAAG;YAEb,IAAA,CAAK,UAAA,CAAW,cAAA,CAAe,MAAM,KAAK,MAAM;YAChD,IAAA,CAAK,UAAA,CAAW,cAAA,CAAe,MAAM,aAAa,CAAA,UAAA,EAAa,SAAS,CAAC,CAAA,CAAA,EAAI,SAAS,CAAC,CAAA,CAAA,CAAG;YAE1F,OAAO;QACX,OAAO;YACH,OAAO;QACX;IACJ;IAES,OAAO,MAAA,EAA4B;QACxC,KAAA,CAAM,OAAO,MAAM;QAEnB,MAAM,UAAU,IAAA,CAAK,UAAA;QAErB,QAAQ,SAAA,CAAU,GAAA,CAAI,kBAAkB;QAGxC,IAAI,IAAA,CAAK,MAAA,CAAO,QAAA,EAAU;YACtB,OAAO,OAAA,CAAQ,IAAA,CAAK,MAAA,CAAO,QAAQ,EAAE,OAAA,CAAQ,CAAC,CAAC,MAAM,KAAK,CAAA,KAAM;gBAC5D,QAAQ,cAAA,CAAe,6KAAMG,QAAAA,CAAM,SAAA,CAAU,IAAI,GAAG,KAAK;YAC7D,CAAC;YAED,IAAI,IAAA,CAAK,UAAA,IAAc,CAAC,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,IAAA,EAAM;gBAC/C,QAAQ,cAAA,CAAe,MAAM,QAAQ,MAAM;YAC/C;QACJ,OAAA,IAAW,IAAA,CAAK,SAAA,EAAW;YACvB,QAAQ,cAAA,CAAe,MAAM,QAAQ,iBAAiB;QAC1D,OAAA,IAAW,IAAA,CAAK,UAAA,EAAY;YACxB,QAAQ,cAAA,CAAe,MAAM,QAAQ,MAAM;YAC3C,QAAQ,cAAA,CAAe,MAAM,UAAU,YAAY;QACvD;QAEA,IAAI;YAEA,IAAI,aAAkB,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,IAAI,CAAA;YAC3C,IAAI,CAAC,MAAM,OAAA,CAAQ,UAAA,CAAW,CAAC,CAAC,KAAK,OAAO,UAAA,CAAW,CAAC,CAAA,KAAM,UAAU;gBACpE,IAAA,IAAS,IAAI,GAAG,IAAI,WAAW,MAAA,EAAQ,IAAK;oBAExC,WAAW,MAAA,CAAO,GAAG,GAAG;wBAAC,UAAA,CAAW,CAAC,CAAA;wBAAG,UAAA,CAAW,IAAI,CAAC,CAAC;qBAAC;gBAC9D;YACJ;YAGA,IAAI,CAAC,MAAM,OAAA,CAAQ,UAAA,CAAW,CAAC,CAAA,CAAE,CAAC,CAAC,KAAK,OAAO,UAAA,CAAW,CAAC,CAAA,CAAE,CAAC,CAAA,KAAM,UAAU;gBAC1E,aAAa;oBAAC,UAAU;iBAAA;YAC5B;YAEA,IAAI,IAAA,CAAK,UAAA,IAAc,WAAW,MAAA,GAAS,GAAG;gBAC1C,MAAM,0KAAIC,YAAAA,CAAS,CAAA,2BAAA,CAA6B;YACpD;YAEA,IAAI,IAAA,CAAK,QAAA,EAAU;gBAEf,IAAA,CAAK,UAAA,GAAc,WAAiE,GAAA,CAAI,CAAC,WAAW;oBAChG,OAAO,OAAO,GAAA,CAAI,CAAC,UAAU;wBACzB,IAAI;wBACJ,2KAAID,QAAAA,CAAM,kBAAA,CAAmB,KAAK,GAAG;4BACjC,iBAAiB,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,aAAA,CAAc,KAAK;wBAC/D,OAAO;4BACH,iBAAiB,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,8BAAA,CAA+B;gCACnE,UAAU,KAAA,CAAM,CAAC,CAAA;gCACjB,UAAU,KAAA,CAAM,CAAC,CAAA;4BACrB,CAAC;wBACL;wBACA,OAAO;4BAAC,eAAe,GAAA;4BAAK,eAAe,KAAK;yBAAA;oBACpD,CAAC;gBACL,CAAC;YACL,OAAO;gBAEH,IAAA,CAAK,UAAA,GAAc,WAAqF,GAAA,CAAI,CAAC,WAAW;oBACpH,OAAO,OAAO,GAAA,CAAI,CAAC,UAAU;wBACzB,IAAI;wBACJ,2KAAIA,QAAAA,CAAM,kBAAA,CAAmB,KAAK,GAAG;4BACjC,iBAAiB,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,aAAA,CAAc,KAAK;wBAC/D,OAAO;4BACH,iBAAiB,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,aAAA,CAAc;gCAClD,KAAK,KAAA,CAAM,CAAC,CAAA;gCACZ,OAAO,KAAA,CAAM,CAAC,CAAA;4BAClB,CAAC;wBACL;wBACA,OAAO;4BAAC,eAAe,GAAA;4BAAK,eAAe,KAAK;yBAAA;oBACpD,CAAC;gBACL,CAAC;YACL;QACJ,EAAA,OAAS,GAAG;YACR,MAAM,IAAIC,kLAAAA,CAAS,CAAA,eAAA,EAAkB,IAAA,CAAK,EAAE,CAAA,SAAA,CAAA,EAAa,CAAC;QAC9D;QAEA,MAAM,WAAW,IAAA,CAAK,SAAA,GAAY,iBAAiB,IAAA,CAAK,MAAA,CAAO,CAAC,CAAC,IAAI,kBAAkB,IAAA,CAAK,MAAA,CAAO,CAAC,CAAC;QACrG,IAAA,CAAK,KAAA,CAAM,QAAA,GAAW;YAAE,KAAK,QAAA,CAAS,CAAC,CAAA;YAAG,OAAO,QAAA,CAAS,CAAC,CAAA;QAAE;QAG7D,IAAA,CAAK,WAAA,GAAc,IAAA,CAAK,MAAA,CAAO,GAAA,CAAI,CAAC,WAAW;YAC3C,OAAO,OAAO,GAAA,CAAI,CAAC,UAAU;gBACzB,OAAO,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,wBAAA,CAAyB;oBAAE,KAAK,KAAA,CAAM,CAAC,CAAA;oBAAG,OAAO,KAAA,CAAM,CAAC,CAAA;gBAAE,CAAC;YAC7F,CAAC;QACL,CAAC;QAED,IAAA,CAAK,KAAA,CAAM,WAAA,GAAc,IAAA,CAAK,WAAA,CAAY,CAAC,CAAA;IAC/C;IAEQ,wBAAmC;QACvC,OAAO,IAAA,CAAK,WAAA,CAAY,GAAA,CAAI,CAAC,cAAc;YACvC,OAAO,IAAA,CAAK,kBAAA,CAAmB,SAAS;QAC5C,CAAC;IACL;IAAA;;;GAAA,GAMQ,mBAAmB,SAAA,EAA+B;QACtD,MAAM,YAAY,UAAU,MAAA;QAG5B,MAAM,cAAc,UAAU,GAAA,CAAI,CAAC,WAAW;YAC1C,OAAO;gBACH;gBACA,SAAS,OAAO,GAAA,CAAI,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,SAAS,IAAI;YACvD;QACJ,CAAC;QAGD,MAAM,eAA+E,CAAC,CAAA;QACtF,YAAY,OAAA,CAAQ,CAAC,KAAK,MAAM;YAC5B,IAAI,CAAC,IAAI,OAAA,EAAS;gBACd,MAAM,aAAa;oBACf,MAAM,IAAI,WAAA,CAAY,YAAY,CAAC,CAAA,GAAI,WAAA,CAAY,IAAI,CAAC,CAAA;oBACxD,MAAM,YAAY,IAAI,WAAA,CAAY,CAAC,CAAA,GAAI,WAAA,CAAY,IAAI,CAAC,CAAA;iBAC5D;gBAEA,WAAW,OAAA,CAAQ,CAAC,cAAc;oBAC9B,IAAI,UAAU,OAAA,EAAS;wBACnB,aAAa,IAAA,CAAK;4BACd,SAAS,UAAU,MAAA;4BACnB,WAAW,IAAI,MAAA;4BACf,OAAO;wBACX,CAAC;oBACL;gBACJ,CAAC;YACL;QACJ,CAAC;QAGD,aAAa,OAAA,CAAQ,EAAE,OAAA,CAAQ,CAAC,SAAS;YACrC,YAAY,MAAA,CAAO,KAAK,KAAA,EAAO,GAAG;gBAC9B,QAAQ,2BAA2B,KAAK,OAAA,EAAS,KAAK,SAAA,EAAW,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,SAAS;gBAC5F,SAAS;YACb,CAAC;QACL,CAAC;QAGD,OAAO,YACF,MAAA,CAAO,CAAA,MAAO,IAAI,OAAO,EACzB,GAAA,CAAI,CAAA,MAAO,IAAA,CAAK,MAAA,CAAO,UAAA,CAAW,qBAAA,CAAsB,IAAI,MAAM,CAAC;IAC5E;AACJ;;ACpOO,IAAM,YAAN,cAAwB,uBAAuB;IAClD,IAAI,aAAyB;QACzB,OAAO,IAAA,CAAK,UAAA,CAAW,iBAAA;IAC3B;IAEA,YAAY,MAAA,EAAgB,MAAA,EAAuB,MAAA,CAAsB;QACrE,KAAA,CAAM,QAAQ,QAAQ,MAAM;IAChC;IAES,QAAiB;QACtB,OAAO;IACX;IAES,gBAAsB;QAC3B,MAAM,UAAU,IAAA,CAAK,IAAA,KAAA,SAAA,UAAA,MAA6B,SAAS,IAAA,CAAK,IAAA;QAChE,MAAM,MAAM,SAAS,eAAA,CAAgB,QAAQ,OAAO;QACpD,IAAA,CAAK,OAAA,GAAU,SAAS,eAAA,CAAgB,QAAQ,KAAK;QACrD,IAAA,CAAK,OAAA,CAAQ,WAAA,CAAY,GAAG;QAC5B,IAAA,CAAK,kBAAA,CAAmB;IAC5B;IAES,OAAO,MAAA,EAA4B;QACxC,KAAA,CAAM,OAAO,MAAM;QAEnB,MAAM,aAAa,IAAA,CAAK,UAAA;QAExB,IAAA,CAAK,eAAA,GAAkB;QAGvB,OAAQ,IAAA,CAAK,IAAA,EAAM;YACf,KAAA,SAAA,UAAA;gBACI,IAAA,CAAK,UAAA,GAAa;oBACd,GAAG;oBACH,GAAG;oBACH,OAAO,IAAA,CAAK,MAAA,CAAO,MAAA;oBACnB,QAAQ,IAAA,CAAK,MAAA,CAAO,MAAA;gBACxB;gBACA;YAEJ,KAAA,OAAA,QAAA;gBACI,IAAI,MAAM,OAAA,CAAQ,IAAA,CAAK,MAAA,CAAO,IAAI,GAAG;oBACjC,IAAA,CAAK,UAAA,GAAa;wBACd,GAAG;wBACH,GAAG;wBACH,OAAO,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,CAAC,CAAA;wBACzB,QAAQ,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,CAAC,CAAA;oBAC9B;gBACJ,OAAO;oBACH,IAAA,CAAK,UAAA,GAAa;wBACd,GAAG;wBACH,GAAG;wBACH,OAAO,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,KAAA;wBACxB,QAAQ,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,MAAA;oBAC7B;gBACJ;gBACA;YAEJ,KAAA,SAAA,UAAA;gBACI,IAAA,CAAK,UAAA,GAAa;oBACd,IAAI,IAAA,CAAK,MAAA,CAAO,MAAA;oBAChB,IAAI,IAAA,CAAK,MAAA,CAAO,MAAA;oBAChB,GAAG,IAAA,CAAK,MAAA,CAAO,MAAA;gBACnB;gBACA;YAEJ,KAAA,UAAA,WAAA;gBACI,IAAI,MAAM,OAAA,CAAQ,IAAA,CAAK,MAAA,CAAO,OAAO,GAAG;oBACpC,IAAA,CAAK,UAAA,GAAa;wBACd,IAAI,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,CAAC,CAAA;wBACzB,IAAI,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,CAAC,CAAA;wBACzB,IAAI,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,CAAC,CAAA;wBACzB,IAAI,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,CAAC,CAAA;oBAC7B;gBACJ,OAAO;oBACH,IAAA,CAAK,UAAA,GAAa;wBACd,IAAI,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,EAAA;wBACxB,IAAI,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,EAAA;wBACxB,IAAI,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,EAAA;wBACxB,IAAI,IAAA,CAAK,MAAA,CAAO,OAAA,CAAQ,EAAA;oBAC5B;gBACJ;gBACA;YAEJ,KAAA,OAAA,QAAA;gBACI,IAAA,CAAK,UAAA,GAAa;oBACd,GAAG,IAAA,CAAK,MAAA,CAAO,IAAA;gBACnB;gBACA;QAGR;QAEA,OAAO,OAAA,CAAQ,IAAA,CAAK,UAAU,EAAE,OAAA,CAAQ,CAAC,CAAC,MAAM,KAAK,CAAA,KAAM;YACvD,WAAW,cAAA,CAAe,MAAM,MAAM,KAAe;QACzD,CAAC;QAGD,IAAI,IAAA,CAAK,MAAA,CAAO,QAAA,EAAU;YACtB,OAAO,OAAA,CAAQ,IAAA,CAAK,MAAA,CAAO,QAAQ,EAAE,OAAA,CAAQ,CAAC,CAAC,MAAM,KAAK,CAAA,KAAM;gBAC5D,WAAW,cAAA,CAAe,6KAAME,QAAAA,CAAM,SAAA,CAAU,IAAI,GAAG,KAAK;YAChE,CAAC;QACL,OAAO;YACH,WAAW,cAAA,CAAe,MAAM,QAAQ,iBAAiB;QAC7D;IACJ;AACJ;;AhB7EA,IAAM,YAAYC,+KAAAA,CAAM,eAAA,CACpB;IACI,oBAAoB;IACpB,iBAAiB;IACjB,SAAS;IACT,mBAAmB;AACvB,GACA;IACI,mBAAkB,iBAAA,EAAmB;QACjC,IAAI,CAAC,mBAAmB;YACpB,OAAO;QACX;QACA,IAAI,sBAAsB,MAAM;YAC5B,oBAAoB;QACxB;QACA,IAAI,OAAO,sBAAsB,UAAU;YACvC,oBAAoB;gBAAE,QAAQ;YAAkB;QACpD;QACA,OAAO;YACH,GAAG,mBAAA;YACH,GAAG,iBAAA;QACP;IACJ;AACJ;AAGJ,SAAS,cAAc,MAAA,EAAqC;IACxD,MAAM,OAAO,cAAc,QAAQ,KAAK;IAExC,OAAQ,MAAM;QACV,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO;QACX;YACI,MAAM,2KAAIC,WAAAA,CAAS,qBAAqB;IAChD;AACJ;AAKO,IAAM,gBAAN,qLAA4B,6BAAA,CAKjC;IAwBE,YAAY,MAAA,EAAgB,MAAA,CAA6B;QACrD,KAAA,CAAM,QAAQ,MAAM;QAnBxB,IAAA,CAAiB,OAAA,GAAkC,CAAC;QAEpD,IAAA,CAAiB,KAAA,GAAQ;YACrB,YAAY;YACZ,iBAAiB;YACjB,eAAe;YACf,gBAAgB;YAAA,2EAAA;YAEhB,eAAe;YAAA,2EAAA;YAEf,aAAa;YACb,aAAa;QACjB;QASI,IAAA,CAAK,SAAA,GAAY,SAAS,aAAA,CAAc,KAAK;QAC7C,IAAA,CAAK,SAAA,CAAU,SAAA,GAAY;QAC3B,IAAA,CAAK,MAAA,CAAO,SAAA,CAAU,WAAA,CAAY,IAAA,CAAK,SAAS;QAEhD,IAAA,CAAK,SAAA,CAAU,gBAAA,CAAiB,eAAe,CAAA,IAAK,EAAE,cAAA,CAAe,CAAC;QAEtE,IAAA,CAAK,YAAA,GAAe,SAAS,eAAA,CAAgB,QAAQ,KAAK;QAC1D,IAAA,CAAK,YAAA,CAAa,YAAA,CAAa,SAAS,2BAA2B;QACnE,IAAA,CAAK,SAAA,CAAU,WAAA,CAAY,IAAA,CAAK,YAAY;QAE5C,IAAA,CAAK,cAAA,GAAiB,IAAI,eAAe,MAAM;QAC/C,IAAA,CAAK,SAAA,CAAU,WAAA,CAAY,IAAA,CAAK,cAAA,CAAe,OAAO;QAGtD,IAAA,CAAK,SAAA,CAAU,gBAAA,CAAiB,cAAc,IAAA,EAAM,IAAI;QACxD,IAAA,CAAK,SAAA,CAAU,gBAAA,CAAiB,cAAc,IAAA,EAAM,IAAI;QACxD,IAAA,CAAK,SAAA,CAAU,gBAAA,CAAiB,aAAa,IAAA,EAAM,IAAI;IAC3D;IAAA;;GAAA,GAKS,OAAO;QACZ,KAAA,CAAM,KAAK;+KAEXD,QAAAA,CAAM,eAAA,CAAgB,IAAA,CAAK,MAAA,CAAO,SAAA,EAAW,gBAAgB;QAG7D,IAAA,CAAK,MAAA,CAAO,gBAAA,uKAAiBE,UAAAA,CAAO,UAAA,CAAW,IAAA,EAAM,IAAI;QACzD,IAAA,CAAK,MAAA,CAAO,gBAAA,wKAAiBA,SAAAA,CAAO,gBAAA,CAAiB,IAAA,EAAM,IAAI;QAC/D,IAAA,CAAK,MAAA,CAAO,gBAAA,wKAAiBA,SAAAA,CAAO,WAAA,CAAY,IAAA,EAAM,IAAI;QAC1D,IAAA,CAAK,MAAA,CAAO,gBAAA,wKAAiBA,SAAAA,CAAO,kBAAA,CAAmB,IAAA,EAAM,IAAI;QACjE,IAAA,CAAK,MAAA,CAAO,gBAAA,wKAAiBA,SAAAA,CAAO,gBAAA,CAAiB,IAAA,EAAM,IAAI;QAC/D,IAAA,CAAK,MAAA,CAAO,gBAAA,wKAAiBA,SAAAA,CAAO,gBAAA,CAAiB,IAAA,EAAM,IAAI;QAC/D,IAAA,CAAK,MAAA,CAAO,gBAAA,wKAAiBA,SAAAA,CAAO,gBAAA,CAAiB,IAAA,EAAM,IAAI;QAC/D,IAAA,CAAK,MAAA,CAAO,gBAAA,wKAAiBA,SAAAA,CAAO,UAAA,CAAW,IAAA,EAAM,IAAA,EAAM;YAAE,MAAM;QAAK,CAAC;IAC7E;IAAA;;GAAA,GAKS,UAAU;QACf,IAAA,CAAK,YAAA,CAAa,KAAK;QAEvB,IAAA,CAAK,MAAA,CAAO,gBAAA,CAAiB,WAAW;QAExC,IAAA,CAAK,MAAA,CAAO,mBAAA,wKAAoBA,SAAAA,CAAO,UAAA,CAAW,IAAA,EAAM,IAAI;QAC5D,IAAA,CAAK,MAAA,CAAO,mBAAA,wKAAoBA,SAAAA,CAAO,gBAAA,CAAiB,IAAA,EAAM,IAAI;QAClE,IAAA,CAAK,MAAA,CAAO,mBAAA,wKAAoBA,SAAAA,CAAO,WAAA,CAAY,IAAA,EAAM,IAAI;QAC7D,IAAA,CAAK,MAAA,CAAO,mBAAA,wKAAoBA,SAAAA,CAAO,gBAAA,CAAiB,IAAA,EAAM,IAAI;QAClE,IAAA,CAAK,MAAA,CAAO,mBAAA,CAAoBA,gLAAAA,CAAO,gBAAA,CAAiB,IAAA,EAAM,IAAI;QAClE,IAAA,CAAK,MAAA,CAAO,mBAAA,wKAAoBA,SAAAA,CAAO,gBAAA,CAAiB,IAAA,EAAM,IAAI;QAClE,IAAA,CAAK,MAAA,CAAO,mBAAA,wKAAoBA,SAAAA,CAAO,UAAA,CAAW,IAAA,EAAM,IAAI;QAE5D,IAAA,CAAK,cAAA,CAAe,OAAA,CAAQ;QAC5B,IAAA,CAAK,MAAA,CAAO,SAAA,CAAU,WAAA,CAAY,IAAA,CAAK,SAAS;QAEhD,KAAA,CAAM,QAAQ;IAClB;IAAA;;GAAA,GAKA,YAAY,CAAA,EAAU;QAClB,OAAQ,EAAE,IAAA,EAAM;YACZ,4KAAKA,SAAAA,CAAO,UAAA,CAAW,IAAA;gBACnB,IAAI,IAAA,CAAK,MAAA,CAAO,OAAA,EAAS;oBACrB,IAAA,CAAK,UAAA,CAAW,IAAA,CAAK,MAAA,CAAO,OAAO;oBACnC,OAAO,IAAA,CAAK,MAAA,CAAO,OAAA;gBACvB;gBACA;YAEJ,4KAAKA,SAAAA,CAAO,WAAA,CAAY,IAAA;gBACpB,IAAA,CAAK,aAAA,CAAc;gBACnB;YAEJ,2KAAKA,UAAAA,CAAO,UAAA,CAAW,IAAA;gBACnB,IAAA,CAAK,SAAA,CAAU,GAAwB,KAAK;gBAC5C;YAEJ,4KAAKA,SAAAA,CAAO,gBAAA,CAAiB,IAAA;gBACzB,IAAA,CAAK,SAAA,CAAU,GAA8B,IAAI;gBACjD;YAEJ,4KAAKA,SAAAA,CAAO,gBAAA,CAAiB,IAAA;YAC7B,4KAAKA,SAAAA,CAAO,gBAAA,CAAiB,IAAA;YAC7B,KAAKA,gLAAAA,CAAO,gBAAA,CAAiB,IAAA;gBACzB,IAAK,EAAyB,WAAA,KAAgB,aAAa;oBACvD,MAAM,QAAS,EAAyB,aAAA;oBACxC,MAAM,SAAkB,EAAyB,MAAA,CAAO,QAAA,CAAS,WAAW,CAAA;oBAC5E,OAAQ,EAAE,IAAA,EAAM;wBACZ,4KAAKA,SAAAA,CAAO,gBAAA,CAAiB,IAAA;4BACzB,IAAI,OAAO,MAAA,CAAO,KAAA,EAAO,QAAQ;gCAC7B,IAAA,CAAK,MAAA,CAAO,SAAA,CAAU,OAAO,MAAA,CAAO,KAAA,CAAM,MAAM;4BACpD,OAAA,IAAW,OAAO,MAAA,CAAO,OAAA,IAAW,OAAO,MAAA,CAAO,OAAA,EAAS;gCACvD,IAAA,CAAK,MAAA,CAAO,SAAA,CAAU,SAAS;4BACnC;4BACA,IAAA,CAAK,eAAA,CAAgB,OAAO,MAAM;4BAClC;wBACJ,4KAAKA,SAAAA,CAAO,gBAAA,CAAiB,IAAA;4BACzB,IAAA,CAAK,MAAA,CAAO,SAAA,CAAU,IAAI;4BAC1B,IAAA,CAAK,eAAA,CAAgB,MAAM;4BAC3B;wBACJ,KAAKA,gLAAAA,CAAO,gBAAA,CAAiB,IAAA;4BACzB,IAAA,CAAK,eAAA,CAAgB,OAAO,MAAM;4BAClC;oBACR;gBACJ;gBACA;YAEJ,KAAK;gBAAc;oBACf,MAAM,SAAS,IAAA,CAAK,iBAAA,CAAkBF,+KAAAA,CAAM,cAAA,CAAe,CAAC,CAAC;oBAC7D,IAAA,CAAK,eAAA,CAAgB,GAAiB,MAAM;oBAC5C;gBACJ;YAEA,KAAK;gBAAc;oBACf,MAAM,SAAS,IAAA,CAAK,iBAAA,CAAkBA,+KAAAA,CAAM,cAAA,CAAe,CAAC,CAAC;oBAC7D,IAAA,CAAK,eAAA,CAAgB,MAAM;oBAC3B;gBACJ;YAEA,KAAK;gBAAa;oBACd,MAAM,SAAS,IAAA,CAAK,iBAAA,wKAAkBA,QAAAA,CAAM,cAAA,CAAe,CAAC,GAAG,IAAI;oBACnE,IAAA,CAAK,eAAA,CAAgB,GAAiB,MAAM;oBAC5C;gBACJ;QACJ;IACJ;IAAA;;GAAA,GAKA,mBAAmB;QACf,IAAI,IAAA,CAAK,KAAA,CAAM,UAAA,EAAY;YACvB,IAAA,CAAK,cAAA,CAAe;QACxB,OAAO;YACH,IAAA,CAAK,cAAA,CAAe;QACxB;IACJ;IAAA;;GAAA,GAKA,iBAAiB;QACb,IAAA,CAAK,KAAA,CAAM,UAAA,GAAa;QACxB,OAAO,MAAA,CAAO,IAAA,CAAK,OAAO,EAAE,OAAA,CAAQ,CAAC,WAAW;YAC5C,OAAO,MAAA,CAAO,OAAA,GAAU;QAC5B,CAAC;QACD,IAAA,CAAK,aAAA,CAAc;QACnB,IAAA,CAAK,aAAA,CAAc,IAAI,iBAAiB,CAAC;IAC7C;IAAA;;GAAA,GAKA,iBAAiB;QACb,IAAA,CAAK,KAAA,CAAM,UAAA,GAAa;QACxB,OAAO,MAAA,CAAO,IAAA,CAAK,OAAO,EAAE,OAAA,CAAQ,CAAC,WAAW;YAC5C,OAAO,MAAA,CAAO,OAAA,GAAU;QAC5B,CAAC;QACD,IAAA,CAAK,aAAA,CAAc;QACnB,IAAA,CAAK,aAAA,CAAc,IAAI,iBAAiB,CAAC;IAC7C;IAAA;;GAAA,GAKA,oBAAoB;QAChB,IAAI,IAAA,CAAK,KAAA,CAAM,eAAA,EAAiB;YAC5B,IAAA,CAAK,eAAA,CAAgB;QACzB,OAAO;YACH,IAAA,CAAK,eAAA,CAAgB;QACzB;IACJ;IAAA;;GAAA,GAKA,kBAAkB;QACd,IAAA,CAAK,KAAA,CAAM,eAAA,GAAkB;QAC7B,OAAO,MAAA,CAAO,IAAA,CAAK,OAAO,EAAE,OAAA,CAAQ,CAAC,WAAW;YAC5C,OAAO,KAAA,CAAM,aAAA,GAAgB;YAC7B,OAAO,WAAA,CAAY;QACvB,CAAC;IACL;IAAA;;GAAA,GAKA,kBAAkB;QACd,IAAA,CAAK,KAAA,CAAM,eAAA,GAAkB;QAC7B,OAAO,MAAA,CAAO,IAAA,CAAK,OAAO,EAAE,OAAA,CAAQ,CAAC,WAAW;YAC5C,OAAO,KAAA,CAAM,aAAA,GAAgB;YAC7B,OAAO,WAAA,CAAY;QACvB,CAAC;IACL;IAAA;;GAAA,GAKA,eAAuB;QACnB,OAAO,OAAO,IAAA,CAAK,IAAA,CAAK,OAAO,EAAE,MAAA;IACrC;IAAA;;GAAA,GAKA,aAAuB;QACnB,OAAO,OAAO,MAAA,CAAO,IAAA,CAAK,OAAO;IACrC;IAAA;;;GAAA,GAMA,UAAU,MAAA,EAAsB,SAAS,IAAA,EAAM;QAC3C,IAAI,IAAA,CAAK,OAAA,CAAQ,OAAO,EAAE,CAAA,EAAG;YACzB,MAAM,2KAAIC,WAAAA,CAAS,CAAA,QAAA,EAAW,OAAO,EAAE,CAAA,gBAAA,CAAkB;QAC7D;QAGA,MAAM,SAAiB,IAAA,CAAK,cAAc,MAAM,CAAA,EAAG,IAAA,CAAK,MAAA,EAAQ,IAAA,EAAM,MAAM;QAE5E,IAAI,OAAO,MAAA,CAAO,GAAG;YACjB,IAAA,CAAK,YAAA,CAAa,WAAA,CAAY,OAAO,UAAU;QACnD,OAAA,IAAW,OAAO,OAAA,CAAQ,GAAG;YACzB,IAAA,CAAK,cAAA,CAAe,SAAA,CAAU,MAAqB;QACvD,OAAA,IAAW,OAAO,IAAA,CAAK,GAAG;YACtB,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,SAAA,CAAU,OAAO,YAAY;QACtD,OAAO;YACH,IAAA,CAAK,SAAA,CAAU,WAAA,CAAY,OAAO,UAAU;QAChD;QAEA,IAAA,CAAK,OAAA,CAAQ,OAAO,EAAE,CAAA,GAAI;QAE1B,IAAI,IAAA,CAAK,KAAA,CAAM,eAAA,EAAiB;YAC5B,OAAO,KAAA,CAAM,aAAA,GAAgB;QACjC;QAEA,IAAI,QAAQ;YACR,IAAA,CAAK,oBAAA,CAAqB;QAC9B;IACJ;IAAA;;;GAAA,GAMA,UAAU,QAAA,EAAyC;QAC/C,MAAM,KAAK,OAAO,aAAa,WAAW,SAAS,EAAA,GAAK;QAExD,IAAI,CAAC,IAAA,CAAK,OAAA,CAAQ,EAAE,CAAA,EAAG;YACnB,MAAM,2KAAIA,WAAAA,CAAS,CAAA,oBAAA,EAAuB,EAAE,CAAA,CAAA,CAAG;QACnD;QAEA,OAAO,IAAA,CAAK,OAAA,CAAQ,EAAE,CAAA;IAC1B;IAAA;;GAAA,GAKA,mBAA2B;QACvB,OAAO,IAAA,CAAK,KAAA,CAAM,aAAA;IACtB;IAAA;;;GAAA,GAMA,aAAa,MAAA,EAAsB,SAAS,IAAA,EAAM;QAC9C,MAAM,SAAS,IAAA,CAAK,SAAA,CAAU,OAAO,EAAE;QAEvC,OAAO,MAAA,CAAO,MAAM;QAEpB,IAAI,QAAQ;YACR,IAAA,CAAK,oBAAA,CAAqB;YAE1B,IACK,WAAW,IAAA,CAAK,KAAA,CAAM,cAAA,IAAkB,OAAO,MAAA,CAAO,OAAA,EAAS,YAAY,WACzE,OAAO,KAAA,CAAM,aAAA,EAClB;gBACE,OAAO,WAAA,CAAY,IAAA,CAAK,KAAA,CAAM,WAAA,EAAa,IAAA,CAAK,KAAA,CAAM,WAAA,EAAa,IAAI;YAC3E;QACJ;IACJ;IAAA;;GAAA,GAKA,aAAa,QAAA,EAAiC,SAAS,IAAA,EAAM;QACzD,MAAM,SAAS,IAAA,CAAK,SAAA,CAAU,QAAQ;QAEtC,IAAI,OAAO,MAAA,CAAO,GAAG;YACjB,IAAA,CAAK,YAAA,CAAa,WAAA,CAAY,OAAO,UAAU;QACnD,OAAA,IAAW,OAAO,OAAA,CAAQ,GAAG;YACzB,IAAA,CAAK,cAAA,CAAe,YAAA,CAAa,MAAqB;QAC1D,OAAA,IAAW,OAAO,IAAA,CAAK,GAAG;YACtB,IAAA,CAAK,MAAA,CAAO,QAAA,CAAS,YAAA,CAAa,OAAO,YAAY;QACzD,OAAO;YACH,IAAA,CAAK,SAAA,CAAU,WAAA,CAAY,OAAO,UAAU;QAChD;QAEA,IAAI,IAAA,CAAK,KAAA,CAAM,cAAA,KAAmB,QAAQ;YACtC,IAAA,CAAK,KAAA,CAAM,cAAA,GAAiB;QAChC;QAEA,IAAI,IAAA,CAAK,KAAA,CAAM,aAAA,KAAkB,QAAQ;YACrC,IAAA,CAAK,KAAA,CAAM,aAAA,GAAgB;QAC/B;QAEA,OAAO,OAAA,CAAQ;QACf,OAAO,IAAA,CAAK,OAAA,CAAQ,OAAO,EAAE,CAAA;QAE7B,IAAI,QAAQ;YACR,IAAA,CAAK,oBAAA,CAAqB;QAC9B;IACJ;IAAA;;GAAA,GAKA,cAAc,SAAA,EAAqB,SAAS,IAAA,EAAM;QAC9C,UAAU,OAAA,CAAQ,CAAA,WAAY,IAAA,CAAK,YAAA,CAAa,UAAU,KAAK,CAAC;QAEhE,IAAI,QAAQ;YACR,IAAA,CAAK,oBAAA,CAAqB;QAC9B;IACJ;IAAA;;GAAA,GAKA,WAAW,OAAA,EAAgC,SAAS,IAAA,EAAM;QACtD,IAAA,CAAK,YAAA,CAAa,KAAK;QAEvB,SAAS,QAAQ,CAAC,WAAW;YACzB,IAAA,CAAK,SAAA,CAAU,QAAQ,KAAK;QAChC,CAAC;QAED,IAAI,QAAQ;YACR,IAAA,CAAK,oBAAA,CAAqB;QAC9B;IACJ;IAAA;;GAAA,GAKA,aAAa,SAAS,IAAA,EAAM;QACxB,OAAO,IAAA,CAAK,IAAA,CAAK,OAAO,EAAE,OAAA,CAAQ,CAAC,aAAa;YAC5C,IAAA,CAAK,YAAA,CAAa,UAAU,KAAK;QACrC,CAAC;QAED,IAAI,QAAQ;YACR,IAAA,CAAK,oBAAA,CAAqB;QAC9B;IACJ;IAAA;;GAAA,GAKA,WAAW,QAAA,EAAiC,QAAyB,IAAA,CAAK,MAAA,CAAO,eAAA,EAAgC;QAC7G,MAAM,SAAS,IAAA,CAAK,SAAA,CAAU,QAAQ;QAEtC,IAAI,CAAC,OAAO;YACR,IAAA,CAAK,MAAA,CAAO,MAAA,CAAO,OAAO,KAAA,CAAM,QAAQ;YACxC,IAAI,CAACD,+KAAAA,CAAM,KAAA,CAAM,OAAO,MAAA,CAAO,OAAO,GAAG;gBACrC,IAAA,CAAK,MAAA,CAAO,IAAA,CAAK,OAAO,MAAA,CAAO,OAAO;YAC1C;YACA,IAAA,CAAK,aAAA,CAAc,IAAI,oBAAoB,MAAM,CAAC;YAClD,OAAO,QAAQ,OAAA,CAAQ;QAC3B,OAAO;YACH,OAAO,IAAA,CAAK,MAAA,CACP,OAAA,CAAQ;gBACL,GAAG,OAAO,KAAA,CAAM,QAAA;gBAChB,MAAM,OAAO,MAAA,CAAO,OAAA;gBACpB;YACJ,CAAC,EACA,IAAA,CAAK,MAAM;gBACR,IAAA,CAAK,aAAA,CAAc,IAAI,oBAAoB,MAAM,CAAC;YACtD,CAAC;QACT;IACJ;IAAA;;GAAA,GAKA,WAAW,QAAA,EAAiC;QACxC,IAAA,CAAK,YAAA,CAAa,UAAU,KAAK;IACrC;IAAA;;GAAA,GAKA,WAAW,QAAA,EAAiC;QACxC,IAAA,CAAK,YAAA,CAAa,UAAU,IAAI;IACpC;IAAA;;GAAA,GAKA,kBAAkB,QAAA,EAAiC;QAC/C,MAAM,SAAS,IAAA,CAAK,SAAA,CAAU,QAAQ;QACtC,OAAO,KAAA,CAAM,aAAA,GAAgB;QAC7B,OAAO,WAAA,CAAY;IACvB;IAAA;;GAAA,GAKA,kBAAkB,QAAA,EAAiC;QAC/C,MAAM,SAAS,IAAA,CAAK,SAAA,CAAU,QAAQ;QACtC,OAAO,KAAA,CAAM,aAAA,GAAgB;QAC7B,OAAO,WAAA,CAAY;IACvB;IAAA;;GAAA,GAKA,aAAa,QAAA,EAAiC,OAAA,EAAmB;QAC7D,MAAM,SAAS,IAAA,CAAK,SAAA,CAAU,QAAQ;QACtC,OAAO,MAAA,CAAO,OAAA,0KAAUA,QAAAA,CAAM,KAAA,CAAM,OAAO,IAAI,CAAC,OAAO,MAAA,CAAO,OAAA,GAAU;QACxE,IAAA,CAAK,aAAA,CAAc;IACvB;IAAA;;GAAA,GAKA,gBAAgB,QAAA,EAAiC;QAC7C,MAAM,SAAS,IAAA,CAAK,SAAA,CAAU,QAAQ;QAEtC,IAAI,OAAO,MAAA,CAAO,OAAA,EAAS;YACvB,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,IAAA,CAAK;gBACnB,IAAI;gBACJ,SAAS,OAAO,MAAA,CAAO,OAAA;YAC3B,CAAC;QACL,OAAO;YACH,IAAA,CAAK,eAAA,CAAgB;QACzB;IACJ;IAAA;;GAAA,GAKA,kBAAkB;QACd,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,IAAA,CAAK,eAAe;IAC1C;IAAA;;GAAA,GAKA,oBAAoB;QAChB,IAAI,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,SAAA,CAAU,qBAAqB,GAAG;YACpD,IAAA,CAAK,eAAA,CAAgB;QACzB,OAAO;YACH,IAAA,CAAK,eAAA,CAAgB;QACzB;IACJ;IAAA;;GAAA,GAKA,kBAAkB;QACd,IAAI,UAAoB,CAAC,CAAA;QACzB,OAAO,MAAA,CAAO,IAAA,CAAK,OAAO,EAAE,OAAA,CAAQ,CAAC,WAAW;YAC5C,IAAI,OAAO,MAAA,CAAO,OAAA,IAAW,CAAC,OAAO,MAAA,CAAO,QAAA,EAAU;gBAClD,QAAQ,IAAA,CAAK,MAAM;YACvB;QACJ,CAAC;QAED,MAAM,IAAI,IAAI,uBAAuB,OAAO;QAC5C,IAAA,CAAK,aAAA,CAAc,CAAC;QACpB,UAAU,EAAE,OAAA;QAEZ,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,IAAA,CAAK;YACnB,IAAI;YACJ,SAAS,sBAAsB,SAAS,IAAA,CAAK,MAAA,CAAO,MAAA,CAAO,IAAA,CAAK,cAAc,EAAE,CAAC;YACjF,UAAU;YACV,cAAc,CAAC,WAAW;gBACtB,MAAM,4KAAKA,QAAAA,CAAM,UAAA,CAAW,QAAQ,sBAAsB;gBAC1D,MAAM,WAAW,KAAK,GAAG,OAAA,CAAQ,WAAW,CAAA,GAAI,KAAA;gBAEhD,IAAI,UAAU;oBACV,MAAM,SAAS,IAAA,CAAK,SAAA,CAAU,QAAQ;oBAEtC,IAAA,CAAK,aAAA,CAAc,IAAI,sBAAsB,MAAM,CAAC;oBAEpD,IAAA,CAAK,UAAA,CAAW,OAAO,EAAE;oBACzB,IAAA,CAAK,eAAA,CAAgB;gBACzB;YACJ;QACJ,CAAC;IACL;IAAA;;GAAA,GAKA,kBAAkB;QACd,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,IAAA,CAAK,qBAAqB;IAChD;IAAA;;GAAA,GAKA,gBAAgB;QACZ,IAAI,IAAA,CAAK,KAAA,CAAM,aAAA,EAAe;YAC1B,IAAA,CAAK,KAAA,CAAM,aAAA,GAAgB;YAC3B;QACJ;QAEA,MAAM,YAAY,IAAA,CAAK,MAAA,CAAO,YAAA,CAAa;QAC3C,MAAM,iBAAiB,IAAA,CAAK,MAAA,CAAO,WAAA,CAAY;QAC/C,MAAM,iBAAiB,IAAA,CAAK,KAAA,CAAM,cAAA;QAElC,OAAO,MAAA,CAAO,IAAA,CAAK,OAAO,EAAE,OAAA,CAAQ,CAAC,WAAW;YAC5C,IAAI,YAAY,OAAO,MAAA,CAAO,OAAA;YAC9B,IAAI,oBAAoB;YACxB,IAAI,WAAkB;YAEtB,IAAI,WAAW;gBACX,WAAW,OAAO,MAAA,CAAO;oBAAE;oBAAgB;oBAAW;gBAAe,CAAC;gBACtE,YAAY,CAAC,CAAC;YAClB;YAEA,oBAAoB,OAAO,KAAA,CAAM,OAAA,KAAY;YAC7C,OAAO,KAAA,CAAM,OAAA,GAAU;YACvB,OAAO,KAAA,CAAM,UAAA,GAAa;YAE1B,IAAI,OAAO,UAAA,EAAY;uLACnBA,QAAAA,CAAM,WAAA,CAAY,OAAO,UAAA,EAAY,uBAAuB,SAAS;YACzE;YAEA,IAAI,CAAC,WAAW;gBACZ,OAAO,WAAA,CAAY;YACvB,OAAA,IAAW,OAAO,KAAA,CAAM,aAAA,EAAe;gBACnC,OAAO,WAAA,CAAY;YACvB,OAAA,IAAW,WAAW,IAAA,CAAK,KAAA,CAAM,cAAA,EAAgB;gBAC7C,OAAO,WAAA,CAAY;YACvB;YAEA,IAAI,mBAAmB;gBACnB,IAAA,CAAK,aAAA,CAAc,IAAI,sBAAsB,QAAQ,SAAS,CAAC;gBAE/D,IAAI,OAAO,IAAA,CAAK,KAAK,OAAO,OAAA,CAAQ,GAAG;oBACnC,IAAA,CAAK,KAAA,CAAM,aAAA,GAAgB;gBAC/B;YACJ;QACJ,CAAC;QAED,IAAI,IAAA,CAAK,KAAA,CAAM,aAAA,EAAe;YAC1B,IAAA,CAAK,MAAA,CAAO,WAAA,CAAY;QAC5B;IACJ;IAOQ,kBAAkB,MAAA,EAAkC,UAAU,KAAA,EAAe;QACjF,IAAI,kBAAkB,MAAM;YACxB,MAAM,UAAU,UAAUA,+KAAAA,CAAM,UAAA,CAAW,QAAQ,aAAa,IAAI;YACpE,OAAO,UAAW,OAAA,CAAgB,WAAW,CAAA,GAAI,KAAA;QACrD,OAAA,IAAW,MAAM,OAAA,CAAQ,MAAM,GAAG;YAC9B,OAAO,OACF,GAAA,CAAI,CAAA,IAAK,EAAE,QAAA,CAAS,WAAW,CAAW,EAC1C,MAAA,CAAO,CAAA,IAAK,CAAC,CAAC,CAAC,EACf,IAAA,CAAK,CAAC,GAAG,IAAM,EAAE,MAAA,CAAO,MAAA,GAAS,EAAE,MAAA,CAAO,MAAM,CAAA,CAAE,CAAC,CAAA;QAC5D,OAAO;YACH,OAAO;QACX;IACJ;IAAA;;GAAA,GAKQ,gBAAgB,CAAA,EAAe,MAAA,EAAiB;QACpD,IAAI,QAAQ;YACR,IAAA,CAAK,KAAA,CAAM,cAAA,GAAiB;YAC5B,IAAA,CAAK,KAAA,CAAM,WAAA,GAAc,EAAE,OAAA;YAC3B,IAAA,CAAK,KAAA,CAAM,WAAA,GAAc,EAAE,OAAA;YAE3B,IAAA,CAAK,aAAA,CAAc,IAAI,iBAAiB,MAAM,CAAC;YAE/C,IAAI,kBAAkB,wBAAwB;gBAC1C,OAAO,UAAA,CAAW;oBACd,WAAW,IAAA,CAAK,MAAA,CAAO,YAAA,CAAa;oBACpC,gBAAgB,IAAA,CAAK,MAAA,CAAO,WAAA,CAAY;oBACxC,WAAW;gBACf,CAAC;YACL;YAEA,IAAI,CAAC,OAAO,KAAA,CAAM,aAAA,IAAiB,OAAO,MAAA,CAAO,OAAA,EAAS,YAAY,SAAS;gBAC3E,OAAO,WAAA,CAAY,EAAE,OAAA,EAAS,EAAE,OAAO;YAC3C;QACJ;IACJ;IAAA;;GAAA,GAKQ,gBAAgB,MAAA,EAAiB;QACrC,IAAI,QAAQ;YACR,IAAA,CAAK,aAAA,CAAc,IAAI,iBAAiB,MAAM,CAAC;YAE/C,IAAI,kBAAkB,wBAAwB;gBAC1C,OAAO,UAAA,CAAW;oBACd,WAAW,IAAA,CAAK,MAAA,CAAO,YAAA,CAAa;oBACpC,gBAAgB,IAAA,CAAK,MAAA,CAAO,WAAA,CAAY;oBACxC,WAAW;gBACf,CAAC;YACL;YAEA,IAAA,CAAK,KAAA,CAAM,cAAA,GAAiB;YAE5B,IAAI,CAAC,OAAO,KAAA,CAAM,aAAA,IAAiB,OAAO,MAAA,CAAO,OAAA,EAAS,YAAY,SAAS;gBAC3E,OAAO,WAAA,CAAY;YACvB,OAAA,IAAW,OAAO,KAAA,CAAM,aAAA,EAAe;gBACnC,OAAO,WAAA,CAAY;YACvB;QACJ;IACJ;IAAA;;GAAA,GAKQ,gBAAgB,CAAA,EAAe,MAAA,EAAiB;QACpD,IAAI,QAAQ;YACR,IAAA,CAAK,KAAA,CAAM,WAAA,GAAc,EAAE,OAAA;YAC3B,IAAA,CAAK,KAAA,CAAM,WAAA,GAAc,EAAE,OAAA;YAE3B,IAAI,OAAO,MAAA,CAAO,KAAK,OAAO,IAAA,CAAK,KAAK,OAAO,OAAA,CAAQ,GAAG;gBACtD,IAAI,OAAO,MAAA,CAAO,OAAA,EAAS,YAAY,SAAS;oBAC5C,OAAO,WAAA,CAAY,EAAE,OAAA,EAAS,EAAE,OAAO;gBAC3C;YACJ;QACJ;IACJ;IAAA;;GAAA,GAKQ,UAAU,CAAA,EAAgD,QAAA,EAAmB;QACjF,MAAM,cAAc,IAAA,CAAK,iBAAA,CAAkB,EAAE,IAAA,CAAK,OAAO;QACzD,MAAM,YAAY,IAAA,CAAK,iBAAA,CAAkB,EAAE,IAAA,CAAK,MAAA,EAAQ,IAAI;QAG5D,MAAM,SAAS,aAAa;QAE5B,IAAI,IAAA,CAAK,KAAA,CAAM,aAAA,IAAiB,IAAA,CAAK,KAAA,CAAM,aAAA,KAAkB,QAAQ;YACjE,IAAA,CAAK,aAAA,CAAc,IAAI,oBAAoB,IAAA,CAAK,KAAA,CAAM,aAAa,CAAC;YAEpE,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,IAAA,CAAK,eAAe;YAEtC,IAAI,CAAC,IAAA,CAAK,KAAA,CAAM,eAAA,IAAmB,IAAA,CAAK,KAAA,CAAM,aAAA,CAAc,MAAA,CAAO,OAAA,EAAS,YAAY,SAAS;gBAC7F,IAAA,CAAK,iBAAA,CAAkB,IAAA,CAAK,KAAA,CAAM,aAAA,CAAc,EAAE;YACtD;YAEA,IAAA,CAAK,KAAA,CAAM,aAAA,GAAgB;QAC/B;QAEA,IAAI,QAAQ;YACR,IAAA,CAAK,KAAA,CAAM,aAAA,GAAgB;YAE3B,IAAA,CAAK,aAAA,CAAc,IAAI,kBAAkB,QAAQ,UAAU,EAAE,IAAA,CAAK,UAAU,CAAC;YAE7E,IAAI,IAAA,CAAK,MAAA,CAAO,kBAAA,EAAoB;gBAEhC,EAAE,IAAA,CAAK,MAAA,GAAS;YACpB,OAAO;gBACH,EAAE,wBAAA,CAAyB;YAC/B;YAGA,IAAI,IAAA,CAAK,OAAA,CAAQ,OAAO,EAAE,CAAA,IAAK,CAAC,EAAE,IAAA,CAAK,UAAA,EAAY;gBAC/C,IAAI,OAAO,MAAA,CAAO,OAAA,EAAS,YAAY,SAAS;oBAC5C,IAAI,OAAO,OAAA,EAAS;wBAChB,IAAA,CAAK,iBAAA,CAAkB,OAAO,EAAE;oBACpC,OAAO;wBACH,IAAA,CAAK,iBAAA,CAAkB,OAAO,EAAE;oBACpC;gBACJ,OAAO;oBACH,IAAA,CAAK,eAAA,CAAgB,OAAO,EAAE;gBAClC;YACJ;QACJ;IACJ;IAEQ,uBAAuB;QAC3B,IAAA,CAAK,WAAA,CAAY;QACjB,IAAA,CAAK,sBAAA,CAAuB;QAC5B,IAAA,CAAK,MAAA,CAAO,WAAA,CAAY;QACxB,IAAA,CAAK,aAAA,CAAc,IAAI,gBAAgB,IAAA,CAAK,UAAA,CAAW,CAAC,CAAC;IAC7D;IAAA;;GAAA,GAKQ,cAAc;QAClB,MAAM,YAAY,OAAO,MAAA,CAAO,IAAA,CAAK,OAAO,EAAE,MAAA,CAAO,CAAA,IAAK,CAAC,EAAE,MAAA,CAAO,QAAQ,EAAE,MAAA;QAE9E,IAAI,cAAc,GAAG;YACjB,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,IAAA,CAAK,eAAe;YACtC,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,IAAA,CAAK,qBAAqB;QAChD,OAAO;YACH,IAAI,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,SAAA,CAAU,qBAAqB,GAAG;gBACpD,IAAA,CAAK,eAAA,CAAgB;YACzB,OAAA,IAAW,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,SAAA,CAAU,eAAe,GAAG;gBACrD,IAAA,CAAK,KAAA,CAAM,aAAA,GAAgB,IAAA,CAAK,eAAA,CAAgB,IAAA,CAAK,KAAA,CAAM,aAAA,CAAc,EAAE,IAAI,IAAA,CAAK,MAAA,CAAO,KAAA,CAAM,IAAA,CAAK;YAC1G;QACJ;QAEA,IAAA,CAAK,MAAA,CAAO,MAAA,CAAO,SAAA,CAAU,cAAc,EAAA,EAAI,KAAK,GAAG,OAAO,YAAY,CAAC;QAC3E,IAAA,CAAK,MAAA,CAAO,MAAA,CAAO,SAAA,CAAU,kBAAkB,EAAA,EAAI,KAAK,GAAG,OAAO,YAAY,CAAC;IACnF;IAAA;;GAAA,GAKQ,yBAAyB;QAC7B,MAAM,QAAQ,OAAO,MAAA,CAAO,IAAA,CAAK,OAAO,EAAE,IAAA,CAAK,CAAA,SAAU,OAAO,IAAA,CAAK,CAAC;QAEtE,IAAI,OAAO;YACP,IAAA,CAAK,MAAA,CAAO,cAAA,CAAe,WAAW;QAC1C,OAAO;YACH,IAAA,CAAK,MAAA,CAAO,gBAAA,CAAiB,WAAW;QAC5C;IACJ;AACJ;AAxvBa,cAMgB,EAAA,GAAK;AANrB,cAOgB,OAAA,GAAU;AAP1B,cAQgB,YAAA,GAAe;AAR/B,cASgB,eAAA,GAAoD;IAAC,SAAS;CAAA;;sKPpG3F,YAAA,CAAS,IAAA,CAAK,cAAc,EAAE,CAAA,GAAI;uKAClC,WAAA,CAAS,IAAA,CAAK,kBAAkB,EAAE,CAAA,GAAI;2KACtC,iBAAA,EAAe,eAAe,cAAc;2KAC5C,iBAAA,EAAe,mBAAmB,cAAc", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23], "debugId": null}}]}